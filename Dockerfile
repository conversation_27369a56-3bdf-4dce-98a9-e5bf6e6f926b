FROM harbor.12301.io/12301test/golang:1.23.1-skywalking0.5.0-0.0.1 AS build

ENV GO111MODULE=on \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct \
    GOINSECURE=gitlab.12301.test \
    GONOSUMDB=gitlab.12301.test \
    GONOPROXY=gitlab.12301.test \
    GOPRIVATE=gitlab.12301.test

ARG SKYWALKING_ENABLED=false

WORKDIR $GOPATH/src

COPY . .

RUN git config --global url."http://ci-docker:<EMAIL>".insteadOf "http://gitlab.12301.test" \
    && if [ "$SKYWALKING_ENABLED" = "true" ]; then \
        /go/bin/skywalking-go-agent -inject . \
        && go build -toolexec="/go/bin/skywalking-go-agent -config /go/src/build/skywalking-server/skywalking-go-config.yaml" -a -o ./build/ ./cmd/server ./cmd/consume ./cmd/crontab; \
    else \
        go build -a -o ./build/ ./cmd/server ./cmd/consume ./cmd/crontab; \
    fi \
    && echo -e "\033[32m Build Completed ;) \033[0m"

FROM harbor.12301.io/12301test/golang:alpine-3.14.2 AS runtime

ENV SW_AGENT_NAME=report_service \
    GIN_MODE=release

RUN addgroup -g 12301 -S pftuser \
    && adduser -D -H -u 12301 -G pftuser pftuser

COPY --from=build /go/src/build/server ./server
COPY --from=build /go/src/build/consume ./consume
COPY --from=build /go/src/build/crontab ./crontab

RUN chown -R pftuser:pftuser ./*

USER pftuser