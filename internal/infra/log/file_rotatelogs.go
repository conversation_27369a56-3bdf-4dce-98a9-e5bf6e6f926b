package log

import (
	"fmt"
	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"go.uber.org/zap/zapcore"
	"os"
	"path"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"time"
)

var FileRotateLogs = new(fileRotateLogs)

type fileRotateLogs struct{}

// GetWriteSyncer 获取 zapcore.WriteSyncer
func (r *fileRotateLogs) GetWriteSyncer(level string) (zapcore.WriteSyncer, error) {
	writeSyncers := make([]zapcore.WriteSyncer, 0)

	logDir := global.CONFIG.Zap.Director
	if logDir != "" {
		if ok, _ := utils.PathExists(global.CONFIG.Zap.Director); !ok { // 判断是否有Director文件夹
			fmt.Printf("create %v directory\n", global.CONFIG.Zap.Director)
			_ = os.Mkdir(global.CONFIG.Zap.Director, os.ModePerm)
		}
		fileWriter, err := rotatelogs.New(
			path.Join(global.CONFIG.Zap.Director, "%Y-%m-%d", level+".log"),
			rotatelogs.WithClock(rotatelogs.Local),
			rotatelogs.WithMaxAge(time.Duration(global.CONFIG.Zap.MaxAge)*24*time.Hour), // 日志留存时间
			rotatelogs.WithRotationTime(time.Hour*24),
		)
		if err != nil {
			return nil, err
		}
		writeSyncers = append(writeSyncers, zapcore.AddSync(fileWriter))
	}

	if global.CONFIG.Zap.LogInConsole {
		writeSyncers = append(writeSyncers, zapcore.AddSync(os.Stdout))
	}

	return zapcore.NewMultiWriteSyncer(writeSyncers...), nil
}
