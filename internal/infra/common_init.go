package infra

import (
	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
	"report-service/internal/global"
	"report-service/internal/infra/config"
	infrazap "report-service/internal/infra/log"
	"report-service/internal/infra/persistence"
)

func CommonInit() {
	// 初始化Viper
	global.VP = config.Init()

	// 初始化zap日志库
	global.LOG = infrazap.Init()
	zap.ReplaceGlobals(global.LOG)

	// 初始化redis
	InitRedis()

	// Api初始化
	InitApi()

	// 初始化 PKG 依赖的组件
	InitPkgUtil()

	// 初始化持久化层
	persistence.Init()

	// 初始化时间库
	carbon.SetDefault(carbon.Default{
		Layout:       carbon.DateTimeLayout,
		Timezone:     carbon.Shanghai,
		WeekStartsAt: carbon.Monday,
		Locale:       "zh-CN", // 取值范围：lang 目录下翻译文件名，不包含文件后缀
	})

	// 初始化 open telemetry
	InitOpenTelemetry()
}

func CommonClose() {
	// 关闭redis
	CloseRedis()

	// 关闭 open telemetry
	CloseOpenTelemetry()

	CloseApi()
}
