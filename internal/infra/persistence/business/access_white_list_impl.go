package business

import (
	"errors"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/business"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type AccessWhiteListModel struct {
	gormmodel.BaseModel
	MerchantId int `gorm:"column:merchant_id;not null;default:0" json:"merchant_id"` // 商户ID
	AccessType int `gorm:"column:access_type;not null;default:1" json:"access_type"` // 访问类型: 1-正常, 2-强制跳转
	Status     int `gorm:"column:status;not null;default:1" json:"status"`           // 状态: 1-启用, 0-禁用
}

func (m AccessWhiteListModel) TableName() string {
	return "r_access_white_list"
}

func (m AccessWhiteListModel) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

func (m AccessWhiteListModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m AccessWhiteListModel) Set(merchantId int, accessType int, status int) error {
	m.MerchantId = merchantId
	m.Status = status
	m.AccessType = accessType
	if m.getQuery().Where("`merchant_id` =?", merchantId).First(&m).RowsAffected == 1 && m.Id > 0 {
		if m.AccessType == accessType && m.Status == status {
			return nil
		}
		updateData := map[string]interface{}{
			"access_type": accessType,
			"status":      status,
		}
		err := m.getQuery().Where("`id` =?", m.Id).Updates(updateData).Error
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}
		return nil
	}
	err := m.getQuery().Create(&m).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (m AccessWhiteListModel) Get(merchantId int) (*business.AccessWhiteListModel, error) {
	err := m.getQuery().Where("`merchant_id` =?", merchantId).First(&m).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var result business.AccessWhiteListModel
	err = utils.JsonConvertor(m, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (m AccessWhiteListModel) GetList(params business.CommonSearch) (list []business.AccessWhiteListModel, total int, err error) {
	var count int64
	// 分页处理
	pageNum := 1
	if params.PageNum != nil && *params.PageNum > 0 {
		pageNum = *params.PageNum
	}
	pageSize := 10
	if params.PageSize != nil && *params.PageSize > 0 {
		pageSize = *params.PageSize
	}
	offset := (pageNum - 1) * pageSize
	query := m.getQuery()
	if params.AccessType != nil {
		query = query.Where("`access_type` =?", *params.AccessType)
	}
	if params.Status != nil {
		query = query.Where("`status` =?", *params.Status)
	}
	if params.MerchantIds != nil && len(params.MerchantIds) > 0 {
		query = query.Where("`merchant_id` in (?)", params.MerchantIds)
	}
	// 计算总数
	err = query.Count(&count).Error
	if err != nil {
		return
	}
	if count == 0 {
		return
	}
	err = query.Order("id asc").Offset(offset).Limit(pageSize).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		return
	}
	var result []business.AccessWhiteListModel
	err = utils.JsonConvertor(list, &result)
	if err != nil {
		return
	}
	total = int(count)
	return
}

func (m AccessWhiteListModel) GetByPage(pageNum, pageSize int) ([]business.AccessWhiteListModel, error) {
	var list []AccessWhiteListModel
	offset := (pageNum - 1) * pageSize
	err := m.getQuery().Order("id asc").Offset(offset).Limit(pageSize).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var result []business.AccessWhiteListModel
	err = utils.JsonConvertor(list, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
