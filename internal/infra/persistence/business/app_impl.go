package business

import (
	"errors"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/business"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type AppModel struct {
	gormmodel.BaseModel
	AppId     string `gorm:"column:app_id;type:varchar(16);not null;default:'';comment:'应用唯一标识'" json:"app_id"`
	AppSecret string `gorm:"column:app_secret;type:varchar(32);not null;default:'';comment:'应用密钥'" json:"app_secret"`
	AppName   string `gorm:"column:app_name;type:varchar(20);not null;default:'';comment:'应用名称'" json:"app_name"`
}

func (m AppModel) TableName() string {
	return "r_app"
}

func (m AppModel) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

func (m AppModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m AppModel) Set(appId string, appSecret string, appName string) error {
	m.AppId = appId
	m.AppSecret = appSecret
	m.AppName = appName
	if m.getQuery().Where("`app_id` =?", appId).First(&m).RowsAffected == 1 && m.Id > 0 {
		if m.AppSecret == appSecret && m.AppName == appName {
			return nil
		}
		updateData := map[string]interface{}{
			"app_secret": appSecret,
			"app_name":   appName,
		}
		err := m.getQuery().Where("`id` =?", m.Id).Updates(updateData).Error
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}
		return nil
	}
	err := m.getQuery().Create(&m).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (m AppModel) GetAppByAppId(appId string) (*business.AppModel, error) {
	err := m.getQuery().Where("`app_id` =?", appId).First(&m).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var result business.AppModel
	err = utils.JsonConvertor(m, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
