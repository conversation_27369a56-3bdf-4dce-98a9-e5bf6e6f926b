package config

import (
	"encoding/json"
	"errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
)

type ConfigGormModel struct {
	gormmodel.BaseModel
	Key     string         `gorm:"column:key;type:varchar(64);not null;default:'';index:idx_key_merchant_id;comment:'配置键'" json:"key"`
	Payload datatypes.JSON `gorm:"column:payload;type:json;not null;default:'{}';comment:'扩展数据'" json:"payload"`
}

func (c ConfigGormModel) TableName() string {
	return "r_config"
}

func (c ConfigGormModel) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

func (c ConfigGormModel) getQuery() *gorm.DB {
	return global.Databases[c.GetDatabaseName()].Table(c.<PERSON>())
}

func (c ConfigGormModel) Get(key string) (interface{}, error) {
	err := c.getQuery().Where("`key` =?", key).First(&c).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, szerrors.NewDataPersistenceError(err)
	}
	return c.Payload, nil
}

func (c ConfigGormModel) Set(key string, value interface{}) error {
	c.Key = key
	payload, err := json.Marshal(value)
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	c.Payload = payload
	if c.getQuery().Where("`key` =?", key).First(&c).RowsAffected == 1 && c.Id > 0 {
		err = c.getQuery().Where("`id` =?", c.Id).Update("payload", payload).Error
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}
		return nil
	}
	err = c.getQuery().Create(&c).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}
