package config

import (
	"encoding/json"
	"errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/config"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type MerchantConfigGormModel struct {
	gormmodel.BaseModel
	Key        string         `gorm:"column:key;type:varchar(64);not null;default:'';index:idx_key_merchant_id;comment:'配置键'" json:"key"`
	MerchantId int            `gorm:"column:merchant_id;type:bigint unsigned;not null;index:idx_key_merchant_id;comment:'商户ID'" json:"merchant_id"`
	MemberId   int            `gorm:"column:member_id;type:bigint unsigned;not null;default:0;comment:'用户ID'" json:"member_id"`
	Payload    datatypes.JSON `gorm:"column:payload;type:json;not null;default:'{}';comment:'扩展数据'" json:"payload"`
}

func (m MerchantConfigGormModel) TableName() string {
	return "r_merchant_config"
}

func (m MerchantConfigGormModel) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

func (m MerchantConfigGormModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m MerchantConfigGormModel) FindManyByKey(key string) ([]config.MerchantConfig, error) {
	var list []MerchantConfigGormModel
	err := m.getQuery().Where("`key` =?", key).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
	}
	var result []config.MerchantConfig
	err = utils.JsonConvertor(list, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (m MerchantConfigGormModel) FindManyByKeyAndMerchantIds(key string, merchantIds []int) ([]config.MerchantConfig, error) {
	var list []MerchantConfigGormModel
	err := m.getQuery().Where("`key` =? AND `merchant_id` IN (?)", key, merchantIds).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
	}
	var result []config.MerchantConfig
	err = utils.JsonConvertor(list, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (m MerchantConfigGormModel) FindManyByKeyAndMerchantIdsAndMemberIds(key string, merchantIds []int, memberIds []int) ([]config.MerchantConfig, error) {
	var list []MerchantConfigGormModel
	err := m.getQuery().Where("`key` =? AND `merchant_id` IN (?) AND `member_id` IN (?)", key, merchantIds, memberIds).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
	}
	var result []config.MerchantConfig
	err = utils.JsonConvertor(list, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (m MerchantConfigGormModel) PaginateByKey(key string, pageNum int, pageSize int) ([]config.MerchantConfig, error) {
	var list []MerchantConfigGormModel
	offset := (pageNum - 1) * pageSize
	err := m.getQuery().Where("`key` =?", key).Order("id asc").Offset(offset).Limit(pageSize).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
	}
	var result []config.MerchantConfig
	err = utils.JsonConvertor(list, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (m MerchantConfigGormModel) Set(key string, merchantId int, memberId int, value interface{}) error {
	m.Key = key
	m.MerchantId = merchantId
	m.MemberId = memberId
	payload, err := json.Marshal(value)
	if err != nil {
		return err
	}
	m.Payload = payload
	if m.getQuery().Where("`key` =? and `merchant_id`=? and `member_id`=?", key, merchantId, memberId).First(&m).RowsAffected == 1 && m.Id > 0 {
		err = m.getQuery().Where("`id` =?", m.Id).Update("payload", payload).Error
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}
		return nil
	}
	err = m.getQuery().Create(&m).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}
