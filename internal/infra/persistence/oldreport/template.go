package oldreport

import (
	"gorm.io/gorm"
	"report-service/internal/domain/repository/oldreport"
	"report-service/internal/global"
	"report-service/internal/infra/db"
)

type PftReportSearchConfig struct {
	Id           int    `gorm:"column:id;primaryKey" json:"id"`
	Fid          int    `gorm:"column:fid" json:"fid"`
	MemberId     int    `gorm:"column:member_id" json:"member_id"`
	Item         string `gorm:"column:item" json:"item"`
	Target       string `gorm:"column:target" json:"target"`
	Name         string `gorm:"column:name" json:"name"`
	Type         int    `gorm:"column:type" json:"type"`
	Status       int    `gorm:"column:status" json:"status"`
	DateType     int    `gorm:"column:date_type" json:"date_type"`
	ItemValue    string `gorm:"column:item_value" json:"item_value"`
	IsShare      int    `gorm:"column:is_share" json:"is_share"`
	TempType     int    `gorm:"column:temp_type" json:"temp_type"`
	ItemNotValue string `gorm:"column:item_not_value" json:"item_not_value"`
	ApplyToStaff string `gorm:"column:apply_to_staff" json:"apply_to_staff"`
	ExtConfig    string `gorm:"column:ext_config" json:"ext_config"`
}

func (t PftReportSearchConfig) GetDatabaseName() string {
	return db.DatabaseNameSummary
}

func (t PftReportSearchConfig) TableName() string {
	return "pft_report_search_config"
}

func (t PftReportSearchConfig) getQuery() *gorm.DB {
	return global.Databases[t.GetDatabaseName()].Table(t.TableName())
}

func (t PftReportSearchConfig) Paginate(fid []int, reportType int, page, pageSize int) ([]oldreport.PftReportSearchConfig, error) {
	query := t.getQuery()
	//生效中的模板
	query = query.Where("status = ?", 1)
	if len(fid) > 0 {
		query = query.Where("fid in ?", fid)
	}
	if reportType > 0 {
		query = query.Where("type = ?", reportType)
	}
	var result []oldreport.PftReportSearchConfig
	err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}
