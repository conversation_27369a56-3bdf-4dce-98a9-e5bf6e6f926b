package oldreport

import (
	"gorm.io/gorm"
	"report-service/internal/domain/repository/oldreport"
	"report-service/internal/global"
	"report-service/internal/infra/db"
)

type PftReportTouristSourceAreaConfig struct {
	ID         int    `gorm:"primaryKey;column:id" json:"id"`
	SupplierID int    `gorm:"column:supplier_id" json:"supplier_id"`
	Type       int    `gorm:"column:type" json:"type"`
	Content    string `gorm:"column:content" json:"content"`
	CreateTime int    `gorm:"column:create_time" json:"create_time"`
	UpdateTime int    `gorm:"column:update_time" json:"update_time"`
}

func (t PftReportTouristSourceAreaConfig) GetDatabaseName() string {
	return db.DatabaseNameSummary
}

func (t PftReportTouristSourceAreaConfig) TableName() string {
	return "pft_report_tourist_source_area_config"
}

func (t PftReportTouristSourceAreaConfig) getQuery() *gorm.DB {
	return global.Databases[t.GetDatabaseName()].Table(t.TableName())
}

func (t PftReportTouristSourceAreaConfig) Paginate(fid []int, isGroupFid bool, page, pageSize int) ([]oldreport.PftReportTouristSourceAreaConfig, error) {
	query := t.getQuery()
	if len(fid) > 0 {
		query = query.Where("supplier_id in ?", fid)
	}
	if isGroupFid {
		query = query.Select("supplier_id").Group("supplier_id")
	}
	var result []oldreport.PftReportTouristSourceAreaConfig
	err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}
