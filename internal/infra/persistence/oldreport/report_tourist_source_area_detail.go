package oldreport

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/oldreport"
	"report-service/internal/global"
	"report-service/internal/infra/db"
)

type PftReportTouristSourceAreaDetail struct {
	DateHour        int    `gorm:"column:date_hour"`
	DateDay         int    `gorm:"column:date_day"`
	DateMonth       int    `gorm:"column:date_month"`
	DateYear        int    `gorm:"column:date_year"`
	SupplierID      int    `gorm:"column:supplier_id"`
	ProductLID      int    `gorm:"column:product_lid"`
	ProductTID      int    `gorm:"column:product_tid"`
	OpType          int    `gorm:"column:op_type"`
	OpTime          int    `gorm:"column:op_time"`
	ProductRuleType int    `gorm:"column:product_rule_type"`
	LevelTop        int    `gorm:"column:level_top"`
	Level1          int    `gorm:"column:level_1"`
	Level2          int    `gorm:"column:level_2"`
	Level3          int    `gorm:"column:level_3"`
	TouristNum      int    `gorm:"column:tourist_num"`
	OrderNum        string `gorm:"column:ordernum"`
	ChkCode         string `gorm:"column:chk_code"`
	IDType          int    `gorm:"column:id_type"`
	IDNumber        string `gorm:"column:id_number"`
	Idx             int    `gorm:"column:idx"`
	TouristOpInfo   string `gorm:"column:tourist_op_info"`
	CreateTime      int    `gorm:"column:create_time"`
}

func (t PftReportTouristSourceAreaDetail) GetDatabaseName() string {
	return db.DatabaseNameSummary
}

func (t PftReportTouristSourceAreaDetail) TableName(fid int) string {
	return fmt.Sprintf("pft_report_tourist_source_area_detail_%d", fid%10)
}

func (t PftReportTouristSourceAreaDetail) getQuery(fid int) *gorm.DB {
	return global.Databases[t.GetDatabaseName()].Table(t.TableName(fid))
}
func (t PftReportTouristSourceAreaDetail) Paginate(startTime carbon.Carbon, fid int, page, pageSize int) ([]oldreport.PftReportTouristSourceAreaDetail, error) {
	query := t.getQuery(fid)
	query = query.Where("date_hour =?", startTime.Format("YmdH"))
	if fid > 0 {
		query = query.Where("supplier_id =?", fid)
	}
	var result []oldreport.PftReportTouristSourceAreaDetail
	err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}
