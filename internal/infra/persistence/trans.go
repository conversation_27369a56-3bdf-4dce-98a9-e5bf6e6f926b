package persistence

import (
	"gorm.io/gorm"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	"report-service/pkg/szerrors"
)

type TransImpl struct{}

func (t TransImpl) Begin(dbInstances []repository.DatabaseInstance) (tx interface{}, err error) {
	if len(dbInstances) == 0 {
		return nil, szerrors.NewDataPersistenceErrorWithText("No database instance found")
	}
	dbName := dbInstances[0].GetDatabaseName()
	for _, dbInstance := range dbInstances {
		if dbInstance.GetDatabaseName() != dbName {
			return nil, szerrors.NewDataPersistenceErrorWithText("All database instances should be of the same database")
		}
		dbName = dbInstance.GetDatabaseName()
	}
	if _, ok := global.Databases[dbName]; !ok {
		return nil, szerrors.NewDataPersistenceErrorWithText("Database [" + dbName + "] not found in global.Databases")
	}
	return global.Databases[dbName].Begin(), nil
}

func (t TransImpl) Commit(tx interface{}) error {
	return tx.(*gorm.DB).Commit().Error
}

func (t TransImpl) Rollback(tx interface{}) error {
	return tx.(*gorm.DB).Rollback().Error
}
