package common

import (
	"report-service/internal/domain/repository/dwm"
	"report-service/pkg/utils"
	"strings"

	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

func (m Model) Insert(records []dwm.CommonModel) error {
	var insetRecords []Model
	utils.JsonConvertOrPanic(records, &insetRecords)
	// 避免报错 sql: expected 47248 arguments, got 506000
	// 原因：gorm.Model 包含了很多字段，导致生成的SQL语句参数过多
	// 直接执行原生 SQL 语句即可
	return m.getQuery().Exec(m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Create(&insetRecords)
	})).Error
}

func (m Model) GetStatSql(startTime, endTime carbon.Carbon, merchantId int, dateSelectFields []string, dateGroupFields []string, sumFields []string) (sql string) {
	selectFields := dateSelectFields
	selectFields = append(selectFields, groupByFields...)
	selectFields = append(selectFields, sumFields...)
	selectFields = append(selectFields, "CURRENT_TIMESTAMP() as created_at")
	groupFields := dateGroupFields
	groupFields = append(groupFields, groupByFields...)
	group := strings.Join(groupFields, ",")
	sql = m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("operated_at BETWEEN ? AND ?", startTime.ToDateTimeString(), endTime.ToDateTimeString())
		if merchantId > 0 {
			tx = tx.Where("merchant_id = ?", merchantId)
		}
		return tx.Select(selectFields).Group(group).Order(group).Find(&[]dwm.CommonModel{})
	})
	return sql
}

func (m Model) GetByBusinessNos(businessNos []int) (result []dwm.CommonModel, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("business_no IN (?)", businessNos).Find(&result)
	})
	err = m.getQuery().Raw(sql).Scan(&result).Error
	return result, err
}

// GetInsertColumns 获取插入语句的列名列表
func (m Model) GetInsertColumns(dateFields []string, sumFields []string) []string {
	// 其他字段
	otherFields := []string{"created_at"}

	// 合并所有字段
	var columns []string
	columns = append(columns, dateFields...)
	columns = append(columns, groupByFields...)
	columns = append(columns, sumFields...)
	columns = append(columns, otherFields...)

	return columns
}
