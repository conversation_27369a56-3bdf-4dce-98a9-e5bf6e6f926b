package common

import (
	"gorm.io/gorm"
	"report-service/pkg/szerrors"
)
import "report-service/internal/domain/repository/dwm"

func getQueryByFilterFields(fields dwm.CommonFilterFields, tx *gorm.DB) *gorm.DB {
	if len(fields.OperateTypes) > 0 {
		tx = tx.Where("operate_type IN (?)", fields.OperateTypes)
	}
	if fields.OperatedAtStart != nil {
		tx = tx.Where("operated_at >= ?", fields.OperatedAtStart.ToDateTimeString())
	}
	if fields.OperatedAtEnd != nil {
		tx = tx.Where("operated_at <= ?", fields.OperatedAtEnd.ToDateTimeString())
	}
	if fields.MerchantId != nil {
		tx = tx.Where("merchant_id = ?", *fields.MerchantId)
	}
	if len(fields.DistributorIds) > 0 {
		tx = tx.Where("distributor_id IN (?)", fields.DistributorIds)
	}
	if len(fields.PoiIds) > 0 {
		tx = tx.Where("poi_id IN (?)", fields.PoiIds)
	}
	if len(fields.SpuIds) > 0 {
		tx = tx.Where("spu_id IN (?)", fields.SpuIds)
	}
	if len(fields.SkuIds) > 0 {
		tx = tx.Where("sku_id IN (?)", fields.SkuIds)
	}
	if len(fields.SaleChannels) > 0 {
		tx = tx.Where("sale_channel IN (?)", fields.SaleChannels)
	}
	if len(fields.OperateChannels) > 0 {
		tx = tx.Where("operate_channel IN (?)", fields.OperateChannels)
	}
	if len(fields.CostPayModes) > 0 {
		tx = tx.Where("cost_pay_mode IN (?)", fields.CostPayModes)
	}
	if len(fields.SalePayModes) > 0 {
		tx = tx.Where("sale_pay_mode IN (?)", fields.SalePayModes)
	}
	if len(fields.SellOperatorIds) > 0 {
		tx = tx.Where("sell_operator_id IN (?)", fields.SellOperatorIds)
	}
	if len(fields.OperatorIds) > 0 {
		tx = tx.Where("operator_id IN (?)", fields.OperatorIds)
	}
	if len(fields.SellSiteIds) > 0 {
		tx = tx.Where("sell_site_id IN (?)", fields.SellSiteIds)
	}
	if len(fields.OperateSiteIds) > 0 {
		tx = tx.Where("operate_site_id IN (?)", fields.OperateSiteIds)
	}
	if len(fields.PackTypes) > 0 {
		tx = tx.Where("pack_type IN (?)", fields.PackTypes)
	}
	if len(fields.ShowBindTypes) > 0 {
		tx = tx.Where("show_bind_type IN (?)", fields.ShowBindTypes)
	}
	if len(fields.AnnualCardTypes) > 0 {
		tx = tx.Where("annual_card_type IN (?)", fields.AnnualCardTypes)
	}
	if len(fields.ExchangeCouponTypes) > 0 {
		tx = tx.Where("exchange_coupon_type IN (?)", fields.ExchangeCouponTypes)
	}
	if len(fields.NotSpuIds) > 0 {
		tx = tx.Where("spu_id NOT IN (?)", fields.NotSpuIds)
	}
	if len(fields.TargetAudience) > 0 {
		tx = tx.Where("target_audience IN (?)", fields.TargetAudience)
	}
	if len(fields.GroupMemberIds) > 0 {
		tx = tx.Where("merchant_id IN (?)", fields.GroupMemberIds)
	}
	return tx
}

func (m Model) PaginateByLastSortKey(filter dwm.CommonFilterFields, lastSortKey *string, pageSize int) (list []dwm.CommonModel, nextSortKey string, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = getQueryByFilterFields(filter, tx)
		if lastSortKey != nil && *lastSortKey != "" {
			tx = tx.Where("sort_key > ?", lastSortKey)
		}
		return tx.Order("sort_key asc").Limit(pageSize).Find(&list)
	})
	// 此处要使用 Raw 而非 Exec，否则 SQL 会被覆盖
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if len(list) > 0 {
		nextSortKey = list[len(list)-1].SortKey
	}
	return list, nextSortKey, nil
}
