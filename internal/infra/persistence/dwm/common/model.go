package common

import (
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"

	"gorm.io/gorm"
)

type Model struct {
	// 操作唯一信息
	OperatedAt        string `json:"operated_at" gorm:"column:operated_at;comment:操作时间"`
	MerchantId        int    `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	BusinessNo        int    `json:"business_no" gorm:"column:business_no;comment:业务单号"`
	SortKey           string `json:"sort_key" gorm:"column:sort_key;comment:排序字段"`
	OrderNo           string `json:"order_no" gorm:"column:order_no;comment:订单号"`
	TradeNo           string `json:"trade_no" gorm:"column:trade_no;comment:交易单号"`
	ExternalOperateNo string `json:"external_operate_no" gorm:"column:external_operate_no;comment:外部操作单号"`
	ParentOrderNo     string `json:"parent_order_no" gorm:"column:parent_order_no;comment:主订单号"`
	// 维度
	TargetAudience     string `json:"target_audience" gorm:"column:target_audience;comment:适用人群标签代码"`
	ProductType        string `json:"product_type" gorm:"column:product_type;comment:产品类型"`
	SubType            int    `json:"sub_type" gorm:"column:sub_type;comment:子类型"`
	ParentMerchantId   int    `json:"parent_merchant_id" gorm:"column:parent_merchant_id;comment:上级商户ID"`
	DistributorId      int    `json:"distributor_id" gorm:"column:distributor_id;comment:下级商户ID"`
	OperateType        int    `json:"operate_type" gorm:"column:operate_type;comment:操作类型：1支付&加票 2核销&完结 3取消 4撤销 5售后"`
	PoiId              int    `json:"poi_id" gorm:"column:poi_id;comment:PoiID"`
	SpuId              int    `json:"spu_id" gorm:"column:spu_id;comment:SpuID"`
	SkuId              int    `json:"sku_id" gorm:"column:sku_id;comment:SkuID"`
	SaleChannel        int    `json:"sale_channel" gorm:"column:sale_channel;comment:销售渠道"`
	OperateChannel     int    `json:"operate_channel" gorm:"column:operate_channel;comment:操作渠道"`
	CostPayMode        int    `json:"cost_pay_mode" gorm:"column:cost_pay_mode;comment:采购支付方式"`
	SalePayMode        int    `json:"sale_pay_mode" gorm:"column:sale_pay_mode;comment:销售支付方式"`
	SellOperatorId     int    `json:"sell_operator_id" gorm:"column:sell_operator_id;comment:售票员ID"`
	OperatorId         int    `json:"operator_id" gorm:"column:operator_id;comment:操作人ID"`
	SellSiteId         int    `json:"sell_site_id" gorm:"column:sell_site_id;comment:售票站点ID"`
	OperateSiteId      int    `json:"operate_site_id" gorm:"column:operate_site_id;comment:操作站点ID"`
	CostUnitPrice      int    `json:"cost_unit_price" gorm:"column:cost_unit_price;comment:采购单价"`
	SaleUnitPrice      int    `json:"sale_unit_price" gorm:"column:sale_unit_price;comment:销售单价"`
	PackType           int    `json:"pack_type" gorm:"column:pack_type;comment:套票规则，0默认  1是主票 2是子票"`
	ShowBindType       int    `json:"show_bind_type" gorm:"column:show_bind_type;comment:捆绑票规则，0默认  1是主票 2是子票"`
	AnnualCardType     int    `json:"annual_card_type" gorm:"column:annual_card_type;comment:年卡规则，0默认  1是年卡 2是特权"`
	ExchangeCouponType int    `json:"exchange_coupon_type" gorm:"column:exchange_coupon_type;comment:预售券规则，0默认  1是预售券 2是权益兑换订单"`
	// 指标
	PayCount                int `json:"pay_count" gorm:"column:pay_count;comment:预订数量"`
	PayCostPrice            int `json:"pay_cost_price" gorm:"column:pay_cost_price;comment:预订采购金额"`
	PayCostDiscountPrice    int `json:"pay_cost_discount_price" gorm:"column:pay_cost_discount_price;comment:预订采购优惠金额"`
	PaySalePrice            int `json:"pay_sale_price" gorm:"column:pay_sale_price;comment:预订销售金额"`
	PaySaleDiscountPrice    int `json:"pay_sale_discount_price" gorm:"column:pay_sale_discount_price;comment:预订销售优惠金额"`
	VerifyCount             int `json:"verify_count" gorm:"column:verify_count;comment:核销数量"`
	VerifyCostPrice         int `json:"verify_cost_price" gorm:"column:verify_cost_price;comment:核销采购金额"`
	VerifyCostDiscountPrice int `json:"verify_cost_discount_price" gorm:"column:verify_cost_discount_price;comment:核销采购优惠金额"`
	VerifySalePrice         int `json:"verify_sale_price" gorm:"column:verify_sale_price;comment:核销销售金额"`
	VerifySaleDiscountPrice int `json:"verify_sale_discount_price" gorm:"column:verify_sale_discount_price;comment:核销销售优惠金额"`
	CancelCount             int `json:"cancel_count" gorm:"column:cancel_count;comment:取消数量"`
	CancelCostPrice         int `json:"cancel_cost_price" gorm:"column:cancel_cost_price;comment:取消采购金额"`
	CancelCostDiscountPrice int `json:"cancel_cost_discount_price" gorm:"column:cancel_cost_discount_price;comment:取消采购优惠金额"`
	CancelCostFee           int `json:"cancel_cost_fee" gorm:"column:cancel_cost_fee;comment:取消采购手续费"`
	CancelSalePrice         int `json:"cancel_sale_price" gorm:"column:cancel_sale_price;comment:取消销售金额"`
	CancelSaleDiscountPrice int `json:"cancel_sale_discount_price" gorm:"column:cancel_sale_discount_price;comment:取消销售优惠金额"`
	CancelSaleFee           int `json:"cancel_sale_fee" gorm:"column:cancel_sale_fee;comment:取消销售手续费"`
	RevokeCount             int `json:"revoke_count" gorm:"column:revoke_count;comment:撤销数量"`
	RevokeCostPrice         int `json:"revoke_cost_price" gorm:"column:revoke_cost_price;comment:撤销采购金额"`
	RevokeCostDiscountPrice int `json:"revoke_cost_discount_price" gorm:"column:revoke_cost_discount_price;comment:撤销采购优惠金额"`
	RevokeCostFee           int `json:"revoke_cost_fee" gorm:"column:revoke_cost_fee;comment:撤销采购手续费"`
	RevokeSalePrice         int `json:"revoke_sale_price" gorm:"column:revoke_sale_price;comment:撤销销售金额"`
	RevokeSaleDiscountPrice int `json:"revoke_sale_discount_price" gorm:"column:revoke_sale_discount_price;comment:撤销销售优惠金额"`
	RevokeSaleFee           int `json:"revoke_sale_fee" gorm:"column:revoke_sale_fee;comment:撤销销售手续费"`
	AfterSaleCount          int `json:"after_sale_count" gorm:"column:after_sale_count;comment:售后数量"`
	AfterCostPrice          int `json:"after_cost_price" gorm:"column:after_cost_price;comment:售后采购金额"`
	AfterSalePrice          int `json:"after_sale_price" gorm:"column:after_sale_price;comment:售后销售金额"`
	CollectCount            int `json:"collect_count" gorm:"column:collect_count;comment:取票数量"`
	ReprintCount            int `json:"reprint_count" gorm:"column:reprint_count;comment:重打印数量"`
}

func (m Model) TableName() string {
	return gormmodel.GetTableName("r_report_dwm_common")
}

func (m Model) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m Model) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

var groupByFields = []string{
	"merchant_id",
	"parent_merchant_id",
	"distributor_id",
	"poi_id",
	"spu_id",
	"sku_id",
	"sale_channel",
	"cost_pay_mode",
	"sale_pay_mode",
	"sell_operator_id",
	"operator_id",
	"sell_site_id",
	"cost_unit_price",
	"sale_unit_price",
	"pack_type",
	"show_bind_type",
	"annual_card_type",
	"exchange_coupon_type",
	"target_audience",
}
