package tourist

import (
	"gorm.io/gorm"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
)

type Model struct {
	// 操作唯一信息
	OperatedAt         string `json:"operated_at" gorm:"column:operated_at;comment:操作时间"`
	MerchantId         int    `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	BusinessNo         int    `json:"business_no" gorm:"column:business_no;comment:业务单号"`
	SortKey            string `json:"sort_key" gorm:"column:sort_key;comment:排序key时间+业务单号"`
	Region             string `json:"region" gorm:"column:region;comment:地域顶级0未知境内外"`
	Country            string `json:"country" gorm:"column:country;comment:地域一级0未知国家"`
	Province           string `json:"province" gorm:"column:province;comment:地域二级0未知省"`
	City               string `json:"city" gorm:"column:city;comment:地域三级0未知市"`
	District           string `json:"district" gorm:"column:district;comment:地域四级0未知区"`
	Age                int    `json:"age" gorm:"column:age;comment:年龄"`
	Gender             int    `json:"gender" gorm:"column:gender;comment:性别"`
	PoiId              int    `json:"poi_id" gorm:"column:poi_id;comment:PoiID"`
	SpuId              int    `json:"spu_id" gorm:"column:spu_id;comment:SpuID"`
	SkuId              int    `json:"sku_id" gorm:"column:sku_id;comment:SkuID"`
	DistributorId      int    `json:"distributor_id" gorm:"column:distributor_id;comment:下级商户ID"`
	OrderNo            string `json:"order_no" gorm:"column:order_no;comment:订单号"`
	OperateType        int    `json:"operate_type" gorm:"column:operate_type;comment:操作类型：1支付&加票 2核销&完结 3取消 4撤销 5售后"`
	PackType           int    `json:"pack_type" gorm:"column:pack_type;comment:套票规则，0默认 1是主票 2是子票"`
	ShowBindType       int    `json:"show_bind_type" gorm:"column:show_bind_type;comment:捆绑票规则，0默认 1是主票 2是子票"`
	AnnualCardType     int    `json:"annual_card_type" gorm:"column:annual_card_type;comment:年卡规则，0默认 1是年卡 2是特权"`
	ExchangeCouponType int    `json:"exchange_coupon_type" gorm:"column:exchange_coupon_type;comment:预售券规则，0默认 1是预售券 2是权益兑换订单"`
	BusinessIdx        int    `json:"business_idx" gorm:"column:business_idx;comment:业务序号(门票序号1起)"`
	BusinessCode       string `json:"business_code" gorm:"column:business_code;comment:业务凭证码（门票码）"`
	IdNumber           string `json:"id_number" gorm:"column:id_number;comment:证件号"`
	IdType             int    `json:"id_type" gorm:"column:id_type;comment:证件号"`
	OperatorId         int    `json:"operator_id" gorm:"column:operator_id;comment:操作人"`
	OperateSiteId      int    `json:"operate_site_id" gorm:"column:operate_site_id;comment:操作站点"`
	OperateChannel     int    `json:"operate_channel" gorm:"column:operate_channel;comment:操作渠道"`
	OrderChannel       int    `json:"order_channel" gorm:"column:order_channel;comment:订单渠道"`
	Nickname           string `json:"nickname" gorm:"column:nickname;comment:游客名称"`
	Mobile             string `json:"mobile" gorm:"column:mobile;comment:游客手机号"`
	// 指标
	Count int `json:"count" gorm:"column:count;comment:游客人数"`
}

func (m Model) TableName() string {
	return gormmodel.GetTableName("r_report_dwm_tourist")
}

func (m Model) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m Model) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

var groupByFields = []string{
	"merchant_id",
	"region",
	"country",
	"province",
	"city",
	"district",
	"age",
	"gender",
	"poi_id",
	"spu_id",
	"sku_id",
	"operate_type",
	"pack_type",
	"show_bind_type",
	"annual_card_type",
	"exchange_coupon_type",
}
