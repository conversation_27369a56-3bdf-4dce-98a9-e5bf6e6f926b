package tourist

import (
	"gorm.io/gorm"
	"report-service/internal/domain/repository/dwm"
	"report-service/pkg/szerrors"
)

func getQueryByFilterFields(fields dwm.TouristFilterFields, tx *gorm.DB) *gorm.DB {
	if fields.OperatedAtStart != nil {
		tx = tx.Where("operated_at >= ?", fields.OperatedAtStart.ToDateTimeString())
	}
	if fields.OperatedAtEnd != nil {
		tx = tx.Where("operated_at <= ?", fields.OperatedAtEnd.ToDateTimeString())
	}
	if fields.MerchantId != nil {
		tx = tx.Where("merchant_id = ?", *fields.MerchantId)
	}
	if len(fields.OperateTypes) > 0 {
		tx = tx.Where("operate_type IN (?)", fields.OperateTypes)
	}
	if len(fields.PoiIds) > 0 {
		tx = tx.Where("poi_id IN (?)", fields.PoiIds)
	}
	if len(fields.SpuIds) > 0 {
		tx = tx.Where("spu_id IN (?)", fields.SpuIds)
	}
	if len(fields.SkuIds) > 0 {
		tx = tx.Where("sku_id IN (?)", fields.SkuIds)
	}
	if len(fields.OperateChannels) > 0 {
		tx = tx.Where("operate_channel IN (?)", fields.OperateChannels)
	}
	if len(fields.OperatorIds) > 0 {
		tx = tx.Where("operator_id IN (?)", fields.OperatorIds)
	}
	if len(fields.OperateSiteIds) > 0 {
		tx = tx.Where("operate_site_id IN (?)", fields.OperateSiteIds)
	}
	if len(fields.PackTypes) > 0 {
		tx = tx.Where("pack_type IN (?)", fields.PackTypes)
	}
	if len(fields.ShowBindTypes) > 0 {
		tx = tx.Where("show_bind_type IN (?)", fields.ShowBindTypes)
	}
	if len(fields.AnnualCardTypes) > 0 {
		tx = tx.Where("annual_card_type IN (?)", fields.AnnualCardTypes)
	}
	if len(fields.ExchangeCouponTypes) > 0 {
		tx = tx.Where("exchange_coupon_type IN (?)", fields.ExchangeCouponTypes)
	}
	if len(fields.RegionIds) > 0 {
		tx = tx.Where("region IN (?)", fields.RegionIds)
	}
	if len(fields.CountryIds) > 0 {
		tx = tx.Where("country IN (?)", fields.CountryIds)
	}
	if len(fields.ProvinceIds) > 0 {
		tx = tx.Where("province IN (?)", fields.ProvinceIds)
	}
	if len(fields.CityIds) > 0 {
		tx = tx.Where("city IN (?)", fields.CityIds)
	}
	if len(fields.DistrictIds) > 0 {
		tx = tx.Where("district IN (?)", fields.DistrictIds)
	}
	if len(fields.Ages) > 0 {
		tx = tx.Where("age IN (?)", fields.Ages)
	}
	if len(fields.Genders) > 0 {
		tx = tx.Where("gender IN (?)", fields.Genders)
	}
	if len(fields.NotSpuIds) > 0 {
		tx = tx.Where("spu_id NOT IN (?)", fields.NotSpuIds)
	}
	if len(fields.OrderNos) > 0 {
		tx = tx.Where("order_no IN (?)", fields.OrderNos)
	}
	if len(fields.BusinessCodes) > 0 {
		tx = tx.Where("business_code IN (?)", fields.BusinessCodes)
	}
	if len(fields.SaleChannels) > 0 {
		tx = tx.Where("order_channel IN (?)", fields.SaleChannels)
	}
	if len(fields.Nicknames) > 0 {
		tx = tx.Where("nickname IN (?)", fields.Nicknames)
	}
	if len(fields.Mobiles) > 0 {
		tx = tx.Where("mobile IN (?)", fields.Mobiles)
	}
	if len(fields.IdTypes) > 0 {
		tx = tx.Where("id_type IN (?)", fields.IdTypes)
	}
	if len(fields.IdNumbers) > 0 {
		tx = tx.Where("id_number IN (?)", fields.IdNumbers)
	}
	return tx
}

func (m Model) PaginateByLastSortKey(filter dwm.TouristFilterFields, lastSortKey *string, pageSize int) (list []dwm.TouristModel, nextSortKey string, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = getQueryByFilterFields(filter, tx)
		if lastSortKey != nil && *lastSortKey != "" {
			tx = tx.Where("sort_key > ?", lastSortKey)
		}
		return tx.Order("sort_key asc").Limit(pageSize).Find(&list)
	})
	// 此处要使用 Raw 而非 Exec，否则 SQL 会被覆盖
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if len(list) > 0 {
		nextSortKey = list[len(list)-1].SortKey
	}
	return list, nextSortKey, nil
}

func (m Model) PaginateCount(filter dwm.TouristFilterFields) (total int, err error) {
	var count int64
	totalSql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return getQueryByFilterFields(filter, tx).Select("count(1)").Find(&count)
	})
	err = m.getQuery().Raw(totalSql).Scan(&count).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if count == 0 {
		return
	}
	total = int(count)

	return total, nil
}

func (m Model) Statistics(filter dwm.TouristFilterFields) (statistic dwm.TouristStatistics, err error) {
	totalSql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return getQueryByFilterFields(filter, tx).Select("sum(count) as count").Find(&statistic)
	})
	err = m.getQuery().Raw(totalSql).Scan(&statistic).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}

	return statistic, nil
}

func (m Model) GetPrevSortKey(filter dwm.TouristFilterFields, lastSortKey *string, pageSize int) (prevSortKey string, err error) {
	var list []dwm.TouristModel
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = getQueryByFilterFields(filter, tx)
		if lastSortKey != nil && *lastSortKey != "" {
			tx = tx.Where("sort_key < ?", lastSortKey)
		}
		return tx.Order("sort_key desc").Limit(pageSize).Find(&list)
	})
	// 此处要使用 Raw 而非 Exec，否则 SQL 会被覆盖
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if len(list) == pageSize {
		prevSortKey = list[pageSize-1].SortKey
	}
	return prevSortKey, nil
}
