package paymodemoney

import (
	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/dwm/customized"
	"report-service/pkg/utils"
	"strings"
)

func (m Model) Insert(records []customized.PayModeMoneyModel) error {
	var insetRecords []Model
	utils.JsonConvertOrPanic(records, &insetRecords)
	// 避免报错 sql: expected 47248 arguments, got 506000
	// 原因：gorm.Model 包含了很多字段，导致生成的SQL语句参数过多
	// 直接执行原生 SQL 语句即可
	return m.getQuery().Exec(m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Create(&insetRecords)
	})).Error
}

func (m Model) GetStatSql(startTime, endTime carbon.Carbon, merchantId int, projectId int, dateSelectFields []string, dateGroupFields []string, sumFields []string) (sql string) {
	selectFields := dateSelectFields
	selectFields = append(selectFields, groupByFields...)
	selectFields = append(selectFields, sumFields...)
	selectFields = append(selectFields, "CURRENT_TIMESTAMP() as created_at")
	groupFields := dateGroupFields
	groupFields = append(groupFields, groupByFields...)
	group := strings.Join(groupFields, ",")
	sql = m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("operated_at BETWEEN ? AND ?", startTime.ToDateTimeString(), endTime.ToDateTimeString())
		if merchantId > 0 {
			tx = tx.Where("merchant_id = ?", merchantId)
		}
		if projectId > 0 {
			tx = tx.Where("project_id = ?", projectId)
		}
		return tx.Select(selectFields).Group(group).Order(group).Find(&[]customized.PayModeMoneyModel{})
	})
	return sql
}

func (m Model) GetByBusinessNos(businessNos []int64) (result []customized.PayModeMoneyModel, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("business_no IN (?)", businessNos).Find(&result)
	})
	err = m.getQuery().Raw(sql).Scan(&result).Error
	return result, err
}
