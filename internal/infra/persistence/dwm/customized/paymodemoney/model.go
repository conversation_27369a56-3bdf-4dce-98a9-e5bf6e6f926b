package paymodemoney

import (
	"gorm.io/gorm"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
)

type Model struct {
	OperatedAt                  string `json:"operated_at" gorm:"column:operated_at;type:datetime;comment:操作时间"`
	MerchantID                  int    `json:"merchant_id" gorm:"column:merchant_id;type:BIGINT;comment:商户ID"`
	DataSource                  int    `json:"data_source" gorm:"column:data_source;type:INT;comment:数据来源"`
	BusinessNo                  int    `json:"business_no" gorm:"column:business_no;type:BIGINT;comment:业务单号"`
	ExternalNo                  string `json:"external_no" gorm:"column:external_no;type:TEXT;comment:外部单号"`
	ProjectID                   int    `json:"project_id" gorm:"column:project_id;type:BIGINT;comment:项目ID"`
	TimeType                    int    `json:"time_type" gorm:"column:time_type;type:INT;comment:时间类型"`
	OperatingRevenue            int    `json:"operating_revenue" gorm:"column:operating_revenue;type:INT;comment:营收金额 单位：元"`
	DiscountAmount              int    `json:"discount_amount" gorm:"column:discount_amount;type:INT;comment:折扣金额 单位：元"`
	AccountsReceivablePayment   int    `json:"accounts_receivable_payment" gorm:"column:accounts_receivable_payment;type:INT;comment:挂帐金额 单位：元"`
	EntertainmentExpensePayment int    `json:"entertainment_expense_payment" gorm:"column:entertainment_expense_payment;type:INT;comment:招待金额 单位：元"`
	CashPayment                 int    `json:"cash_payment" gorm:"column:cash_payment;type:INT;comment:现金支付金额 单位：元"`
	UnionPayPayment             int    `json:"union_pay_payment" gorm:"column:union_pay_payment;type:INT;comment:银联支付金额 单位：元"`
	StoredValueCardPayment      int    `json:"stored_value_card_payment" gorm:"column:stored_value_card_payment;type:INT;comment:储蓄卡支付金额 单位：元"`
	YinbaoPayPayment            int    `json:"yinbao_pay_payment" gorm:"column:yinbao_pay_payment;type:INT;comment:银豹付支付金额 单位：元"`
	RuralCommercialBankPayment  int    `json:"rural_commercial_bank_payment" gorm:"column:rural_commercial_bank_payment;type:INT;comment:农商行收银宝金额 单位：元"`
	CreditPayment               int    `json:"credit_payment" gorm:"column:credit_payment;type:INT;comment:授信支付金额 单位：元"`
	YibaoPayment                int    `json:"yibao_payment" gorm:"column:yibao_payment;type:INT;comment:易宝金额 单位：元"`
	AlipayPayment               int    `json:"alipay_payment" gorm:"column:alipay_payment;type:INT;comment:支付宝金额 单位：元"`
	WechatPayment               int    `json:"wechat_payment" gorm:"column:wechat_payment;type:INT;comment:微信金额 单位：元"`
	PrepaidCardPayment          int    `json:"prepaid_card_payment" gorm:"column:prepaid_card_payment;type:INT;comment:预付卡金额 单位：元"`
	MeituanCouponPayment        int    `json:"meituan_coupon_payment" gorm:"column:meituan_coupon_payment;type:INT;comment:美团优惠券金额 单位：元"`
	OtherPayment                int    `json:"other_payment" gorm:"column:other_payment;type:INT;comment:其他金额 单位：元"`
	TotalIncome                 int    `json:"total_income" gorm:"column:total_income;type:INT;comment:收入合计金额（不含营收、折扣） 单位：元"`
}

func (m Model) TableName() string {
	return gormmodel.GetTableName("r_report_dwm_pay_mode_money")
}

func (m Model) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m Model) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

var groupByFields = []string{
	"merchant_id",
	"data_source",
	"time_type",
	"project_id",
}
