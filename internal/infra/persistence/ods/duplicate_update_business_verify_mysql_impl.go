package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessVerifyGormModel struct {
	GormModelBase
	VerifyNo   int       `gorm:"column:verify_no;type:bigint;not null;comment:验证单号" json:"verify_no"`
	VerifiedAt time.Time `gorm:"column:verified_at;type:timestamp;not null;comment:验证时间" json:"verified_at"`
}

func (m *DuplicateUpdateBusinessVerifyGormModel) GetGormModel() BusinessVerifyGormModel {
	return BusinessVerifyGormModel{}
}

func (m *DuplicateUpdateBusinessVerifyGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.VerifiedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessVerifyGormModel) validate() error {
	if m.VerifyNo == 0 {
		return errors.New("model business verify result set duplicate update verify_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessVerifyGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "verify_no", Value: m.VerifyNo},
		{Name: "verified_at", Value: m.VerifiedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessVerifyGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "verified_at", Value: m.VerifiedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessVerifyArchiveLabel []*DuplicateUpdateBusinessVerifyGormModel

func (m *DuplicateUpdateBusinessVerifyArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessVerifyGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessVerifyGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.VerifiedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
