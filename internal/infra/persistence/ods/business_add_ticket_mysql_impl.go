package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessAddTicketGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	AddTicketNo   int       `gorm:"column:add_ticket_no;type:bigint;not null;comment:加票单号" json:"add_ticket_no"`
	AddedTicketAt time.Time `gorm:"column:added_ticket_at;type:timestamp;not null;comment:加票时间" json:"added_ticket_at"`
}

func (m BusinessAddTicketGormModel) TableName() string {
	return "r_business_fact_add_ticket"
}
func (m BusinessAddTicketGormModel) TableComment() string {
	return "业务-加票事实表"
}

type BusinessAddTicketMysqlImpl struct{}

func (r BusinessAddTicketMysqlImpl) InsertMore(records []ods.BusinessAddTicketModelData) error {
	var insertRecords []*DuplicateUpdateBusinessAddTicketGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessAddTicketArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
