package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessAddTicketGormModel struct {
	GormModelBase
	AddTicketNo   int       `gorm:"column:add_ticket_no;type:bigint;not null;comment:加票单号" json:"add_ticket_no"`
	AddedTicketAt time.Time `gorm:"column:added_ticket_at;type:timestamp;not null;comment:加票时间" json:"added_ticket_at"`
}

func (m *DuplicateUpdateBusinessAddTicketGormModel) GetGormModel() BusinessAddTicketGormModel {
	return BusinessAddTicketGormModel{}
}

func (m *DuplicateUpdateBusinessAddTicketGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.AddedTicketAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessAddTicketGormModel) validate() error {
	if m.AddTicketNo == 0 {
		return errors.New("model business add ticket result set duplicate update add_ticket_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessAddTicketGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "add_ticket_no", Value: m.AddTicketNo},
		{Name: "added_ticket_at", Value: m.AddedTicketAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessAddTicketGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "added_ticket_at", Value: m.AddedTicketAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessAddTicketArchiveLabel []*DuplicateUpdateBusinessAddTicketGormModel

func (m *DuplicateUpdateBusinessAddTicketArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessAddTicketGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessAddTicketGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.AddedTicketAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
