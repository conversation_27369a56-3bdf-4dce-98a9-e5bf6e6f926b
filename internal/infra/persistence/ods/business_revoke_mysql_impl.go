package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessRevokeGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	RevokeNo  int       `gorm:"column:revoke_no;type:bigint;not null;comment:撤销撤改单号" json:"revoke_no"`
	RevokedAt time.Time `gorm:"column:revoked_at;type:timestamp;not null;comment:撤销撤改时间" json:"revoked_at"`
}

func (m BusinessRevokeGormModel) TableName() string {
	return "r_business_fact_revoke"
}
func (m BusinessRevokeGormModel) TableComment() string {
	return "业务-撤销撤改事实表"
}

type BusinessRevokeMysqlImpl struct{}

func (r BusinessRevokeMysqlImpl) InsertMore(records []ods.BusinessRevokeModelData) error {
	var insertRecords []*DuplicateUpdateBusinessRevokeGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessRevokeArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
