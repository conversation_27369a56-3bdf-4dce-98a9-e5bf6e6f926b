package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessPayGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	PayNo  int       `gorm:"column:pay_no;type:bigint;not null;comment:支付单号" json:"pay_no"`
	PaidAt time.Time `gorm:"column:paid_at;type:timestamp;not null;comment:支付时间" json:"paid_at"`
}

func (m BusinessPayGormModel) TableName() string {
	return "r_business_fact_pay"
}
func (m BusinessPayGormModel) TableComment() string {
	return "业务-预订事实表"
}

type BusinessPayMysqlImpl struct{}

func (r BusinessPayMysqlImpl) InsertMore(records []ods.BusinessPayModelData) error {
	var insertRecords []*DuplicateUpdateBusinessPayGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessPayArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
