package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessCancelGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	CancelNo    int       `gorm:"column:cancel_no;type:bigint;not null;comment:取消单号" json:"cancel_no"`
	CancelledAt time.Time `gorm:"column:cancelled_at;type:timestamp;not null;comment:取消时间" json:"cancelled_at"`
}

func (m BusinessCancelGormModel) TableName() string {
	return "r_business_fact_cancel"
}
func (m BusinessCancelGormModel) TableComment() string {
	return "业务-取消事实表"
}

type BusinessCancelMysqlImpl struct{}

func (r BusinessCancelMysqlImpl) InsertMore(records []ods.BusinessCancelModelData) error {
	var insertRecords []*DuplicateUpdateBusinessCancelGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessCancelArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
