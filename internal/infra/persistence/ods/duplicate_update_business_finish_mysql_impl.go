package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessFinishGormModel struct {
	GormModelBase
	FinishNo   int       `gorm:"column:finish_no;type:bigint;not null;comment:完结单号" json:"finish_no"`
	FinishedAt time.Time `gorm:"column:finished_at;type:timestamp;not null;comment:完结时间" json:"finished_at"`
}

func (m *DuplicateUpdateBusinessFinishGormModel) GetGormModel() BusinessFinishGormModel {
	return BusinessFinishGormModel{}
}

func (m *DuplicateUpdateBusinessFinishGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.FinishedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessFinishGormModel) validate() error {
	if m.FinishNo == 0 {
		return errors.New("model business finish result set duplicate update finish_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessFinishGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "finish_no", Value: m.FinishNo},
		{Name: "finished_at", Value: m.FinishedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessFinishGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "finished_at", Value: m.FinishedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessFinishArchiveLabel []*DuplicateUpdateBusinessFinishGormModel

func (m *DuplicateUpdateBusinessFinishArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessFinishGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessFinishGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.FinishedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
