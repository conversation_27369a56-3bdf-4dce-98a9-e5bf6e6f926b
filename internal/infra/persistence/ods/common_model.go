package ods

import (
	"fmt"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"report-service/internal/global"
	"report-service/internal/infra/db"
	"time"
)

const (
	TimestampLayoutMake = "2006-01-02 15:04:05"
)

type GormModelBase struct {
	OrderNo           string         `gorm:"column:order_no;type:varchar;not null;comment:订单号" json:"order_no"`
	PoiId             int            `gorm:"column:poi_id;type:bigint;not null;comment:PoiID" json:"poi_id"`
	SpuId             int            `gorm:"column:spu_id;type:bigint;not null;comment:SpuID" json:"spu_id"`
	SkuId             int            `gorm:"column:sku_id;type:bigint;not null;comment:SkuID" json:"sku_id"`
	SaleChannel       int            `gorm:"column:sale_channel;type:int;not null;comment:销售渠道" json:"sale_channel"`
	ApplyDid          int            `gorm:"column:apply_did;type:bigint;not null;comment:商家ID" json:"apply_did"`
	OperatorId        int            `gorm:"column:operator_id;type:bigint;not null;comment:操作人ID" json:"operator_id"`
	SellOperatorId    int            `gorm:"column:sell_operator_id;type:bigint;not null;comment:售票员ID" json:"sell_operator_id"`
	ProductType       string         `gorm:"column:product_type;type:varchar;not null;comment:产品线类型" json:"product_type"`                //产品线类型
	SubType           int            `gorm:"column:sub_type;type:int;not null;comment:产品线子类型" json:"sub_type"`                           //产品线子类型
	Count             int            `gorm:"column:count;type:int;not null;comment:数量" json:"count"`                                     //数量
	OperateChannel    int            `gorm:"column:operate_channel;type:int;not null;comment:操作渠道" json:"operate_channel"`               //操作渠道
	SellSiteId        int            `gorm:"column:sell_site_id;type:int;not null;comment:售票站点ID" json:"sell_site_id"`                   //售票站点ID
	OperateSiteId     int            `gorm:"column:operate_site_id;type:int;not null;comment:操作站点ID" json:"operate_site_id"`             //操作站点ID
	ExternalOperateNo string         `gorm:"column:external_operate_no;type:varchar;not null;comment:外部操作单号" json:"external_operate_no"` //外部操作单号
	Payload           datatypes.JSON `gorm:"column:payload;type:json;not null;comment:扩展数据" json:"payload"`
	TradeNo           string         `gorm:"column:trade_no;type:varchar;not null;comment:交易单号" json:"trade_no"` // 交易单号
}

func (m GormModelBase) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

// 生成归档标识：上半年或下半年
func (m GormModelBase) generateArchiveLabel(t time.Time) string {
	//重置
	if t.IsZero() {
		t = time.Now()
	}
	month := t.Month()
	if month <= 6 {
		return fmt.Sprintf("%d0%d", t.Year(), 1)
	}
	return fmt.Sprintf("%d0%d", t.Year(), 2)
}

func (m GormModelBase) GenerateArchiveLabel(tableName string) string {
	return fmt.Sprintf("%s_%s", tableName, m.generateArchiveLabel(time.Now()))
}

func (m GormModelBase) getQuery(tableName string) *gorm.DB {
	table := m.GenerateArchiveLabel(tableName)
	return global.Databases[m.GetDatabaseName()].Table(table)
}
