package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessCollectGormModel struct {
	GormModelBase
	CollectNo   int       `gorm:"column:collect_no;type:bigint;not null;comment:取票单号" json:"collect_no"`
	CollectedAt time.Time `gorm:"column:collected_at;type:timestamp;not null;comment:取票时间" json:"collected_at"`
}

func (m *DuplicateUpdateBusinessCollectGormModel) GetGormModel() BusinessCollectGormModel {
	return BusinessCollectGormModel{}
}

func (m *DuplicateUpdateBusinessCollectGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.CollectedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessCollectGormModel) validate() error {
	if m.CollectNo == 0 {
		return errors.New("model business collect result set duplicate update collect_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessCollectGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "collect_no", Value: m.CollectNo},
		{Name: "collected_at", Value: m.CollectedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessCollectGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "collected_at", Value: m.CollectedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessCollectArchiveLabel []*DuplicateUpdateBusinessCollectGormModel

func (m *DuplicateUpdateBusinessCollectArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessCollectGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessCollectGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.CollectedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
