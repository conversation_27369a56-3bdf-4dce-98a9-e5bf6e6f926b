package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessCollectGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	CollectNo   int       `gorm:"column:collect_no;type:bigint;not null;comment:取票单号" json:"collect_no"`
	CollectedAt time.Time `gorm:"column:collected_at;type:timestamp;not null;comment:取票时间" json:"collected_at"`
}

func (m BusinessCollectGormModel) TableName() string {
	return "r_business_fact_collect"
}
func (m BusinessCollectGormModel) TableComment() string {
	return "业务-取票事实表"
}

type BusinessCollectMysqlImpl struct{}

func (r BusinessCollectMysqlImpl) InsertMore(records []ods.BusinessCollectModelData) error {
	var insertRecords []*DuplicateUpdateBusinessCollectGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessCollectArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
