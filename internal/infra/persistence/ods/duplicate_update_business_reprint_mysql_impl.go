package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessReprintGormModel struct {
	GormModelBase
	ReprintNo   int       `gorm:"column:reprint_no;type:bigint;not null;comment:重打印单号" json:"reprint_no"`
	ReprintedAt time.Time `gorm:"column:reprinted_at;type:timestamp;not null;comment:重打印时间" json:"reprinted_at"`
}

func (m *DuplicateUpdateBusinessReprintGormModel) GetGormModel() BusinessReprintGormModel {
	return BusinessReprintGormModel{}
}

func (m *DuplicateUpdateBusinessReprintGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.ReprintedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessReprintGormModel) validate() error {
	if m.ReprintNo == 0 {
		return errors.New("model business reprint result set duplicate update reprint_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessReprintGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "reprint_no", Value: m.ReprintNo},
		{Name: "reprinted_at", Value: m.ReprintedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessReprintGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "reprinted_at", Value: m.ReprintedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessReprintArchiveLabel []*DuplicateUpdateBusinessReprintGormModel

func (m *DuplicateUpdateBusinessReprintArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessReprintGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessReprintGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.ReprintedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
