package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessAfterSaleGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	AfterSaleNo  int       `gorm:"column:after_sale_no;type:bigint;not null;comment:售后单号" json:"after_sale_no"`
	AfterSaledAt time.Time `gorm:"column:after_saled_at;type:timestamp;not null;comment:售后时间" json:"after_saled_at"`
}

func (m BusinessAfterSaleGormModel) TableName() string {
	return "r_business_fact_after_sale"
}
func (m BusinessAfterSaleGormModel) TableComment() string {
	return "业务-售后事实表"
}

type BusinessAfterSaleMysqlImpl struct{}

func (r BusinessAfterSaleMysqlImpl) InsertMore(records []ods.BusinessAfterSaleModelData) error {
	var insertRecords []*DuplicateUpdateBusinessAfterSaleGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessAfterSaleArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
