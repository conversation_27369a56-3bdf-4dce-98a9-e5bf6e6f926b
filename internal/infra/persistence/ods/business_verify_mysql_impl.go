package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessVerifyGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	VerifyNo   int       `gorm:"column:verify_no;type:bigint;not null;comment:验证单号" json:"verify_no"`
	VerifiedAt time.Time `gorm:"column:verified_at;type:timestamp;not null;comment:验证时间" json:"verified_at"`
}

func (m BusinessVerifyGormModel) TableName() string {
	return "r_business_fact_verify"
}
func (m BusinessVerifyGormModel) TableComment() string {
	return "业务-验证事实表"
}

type BusinessVerifyMysqlImpl struct{}

func (r BusinessVerifyMysqlImpl) InsertMore(records []ods.BusinessVerifyModelData) error {
	var insertRecords []*DuplicateUpdateBusinessVerifyGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessVerifyArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
