package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessFinishGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	FinishNo   int       `gorm:"column:finish_no;type:bigint;not null;comment:完结单号" json:"finish_no"`
	FinishedAt time.Time `gorm:"column:finished_at;type:timestamp;not null;comment:完结时间" json:"finished_at"`
}

func (m BusinessFinishGormModel) TableName() string {
	return "r_business_fact_finish"
}
func (m BusinessFinishGormModel) TableComment() string {
	return "业务-完结事实表"
}

type BusinessFinishMysqlImpl struct{}

func (r BusinessFinishMysqlImpl) InsertMore(records []ods.BusinessFinishModelData) error {
	var insertRecords []*DuplicateUpdateBusinessFinishGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessFinishArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
