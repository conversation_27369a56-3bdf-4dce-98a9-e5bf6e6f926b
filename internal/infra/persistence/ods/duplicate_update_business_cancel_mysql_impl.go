package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessCancelGormModel struct {
	GormModelBase
	CancelNo    int       `gorm:"column:cancel_no;type:bigint;not null;comment:取消单号" json:"cancel_no"`
	CancelledAt time.Time `gorm:"column:cancelled_at;type:timestamp;not null;comment:取消时间" json:"cancelled_at"`
}

func (m *DuplicateUpdateBusinessCancelGormModel) GetGormModel() BusinessCancelGormModel {
	return BusinessCancelGormModel{}
}

func (m *DuplicateUpdateBusinessCancelGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.CancelledAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessCancelGormModel) validate() error {
	if m.CancelNo == 0 {
		return errors.New("model business cancel result set duplicate update cancel_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessCancelGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "cancel_no", Value: m.CancelNo},
		{Name: "cancelled_at", Value: m.CancelledAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessCancelGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "cancelled_at", Value: m.CancelledAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessCancelArchiveLabel []*DuplicateUpdateBusinessCancelGormModel

func (m *DuplicateUpdateBusinessCancelArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessCancelGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessCancelGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.CancelledAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
