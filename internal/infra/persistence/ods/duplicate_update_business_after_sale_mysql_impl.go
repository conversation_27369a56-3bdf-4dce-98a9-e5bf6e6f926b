package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessAfterSaleGormModel struct {
	GormModelBase
	AfterSaleNo  int       `gorm:"column:after_sale_no;type:bigint;not null;comment:售后单号" json:"after_sale_no"`
	AfterSaledAt time.Time `gorm:"column:after_saled_at;type:timestamp;not null;comment:售后时间" json:"after_saled_at"`
}

func (m *DuplicateUpdateBusinessAfterSaleGormModel) GetGormModel() BusinessAfterSaleGormModel {
	return BusinessAfterSaleGormModel{}
}

func (m *DuplicateUpdateBusinessAfterSaleGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.AfterSaledAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessAfterSaleGormModel) validate() error {
	if m.AfterSaleNo == 0 {
		return errors.New("model business after sale result set duplicate update after_sale_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessAfterSaleGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "after_sale_no", Value: m.AfterSaleNo},
		{Name: "after_saled_at", Value: m.AfterSaledAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessAfterSaleGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "after_saled_at", Value: m.AfterSaledAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessAfterSaleArchiveLabel []*DuplicateUpdateBusinessAfterSaleGormModel

func (m *DuplicateUpdateBusinessAfterSaleArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessAfterSaleGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessAfterSaleGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.AfterSaledAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
