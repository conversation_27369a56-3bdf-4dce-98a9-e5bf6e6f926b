package ods

import (
	"report-service/internal/domain/repository/ods"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type BusinessReprintGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	ReprintNo   int       `gorm:"column:reprint_no;type:bigint;not null;comment:重打印单号" json:"reprint_no"`
	ReprintedAt time.Time `gorm:"column:reprinted_at;type:timestamp;not null;comment:重打印时间" json:"reprinted_at"`
}

func (m BusinessReprintGormModel) TableName() string {
	return "r_business_fact_reprint"
}
func (m BusinessReprintGormModel) TableComment() string {
	return "业务-重打印事实表"
}

type BusinessReprintMysqlImpl struct{}

func (r BusinessReprintMysqlImpl) InsertMore(records []ods.BusinessReprintModelData) error {
	var insertRecords []*DuplicateUpdateBusinessReprintGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateBusinessReprintArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
