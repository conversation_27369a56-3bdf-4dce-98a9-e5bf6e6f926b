package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessRevokeGormModel struct {
	GormModelBase
	RevokeNo  int       `gorm:"column:revoke_no;type:bigint;not null;comment:撤销撤改单号" json:"revoke_no"`
	RevokedAt time.Time `gorm:"column:revoked_at;type:timestamp;not null;comment:撤销撤改时间" json:"revoked_at"`
}

func (m *DuplicateUpdateBusinessRevokeGormModel) GetGormModel() BusinessRevokeGormModel {
	return BusinessRevokeGormModel{}
}

func (m *DuplicateUpdateBusinessRevokeGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.RevokedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessRevokeGormModel) validate() error {
	if m.RevokeNo == 0 {
		return errors.New("model business revoke result set duplicate update revoke_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessRevokeGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "revoke_no", Value: m.RevokeNo},
		{Name: "revoked_at", Value: m.RevokedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessRevokeGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "revoked_at", Value: m.RevokedAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessRevokeArchiveLabel []*DuplicateUpdateBusinessRevokeGormModel

func (m *DuplicateUpdateBusinessRevokeArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessRevokeGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessRevokeGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.RevokedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
