package ods

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateBusinessPayGormModel struct {
	GormModelBase
	PayNo  int       `gorm:"column:pay_no;type:bigint;not null;comment:支付单号" json:"pay_no"`
	PaidAt time.Time `gorm:"column:paid_at;type:timestamp;not null;comment:支付时间" json:"paid_at"`
}

func (m *DuplicateUpdateBusinessPayGormModel) GetGormModel() BusinessPayGormModel {
	return BusinessPayGormModel{}
}

func (m *DuplicateUpdateBusinessPayGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.PaidAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateBusinessPayGormModel) validate() error {
	if m.PayNo == 0 {
		return errors.New("model business pay result set duplicate update pay_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateBusinessPayGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "pay_no", Value: m.PayNo},
		{Name: "paid_at", Value: m.PaidAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateBusinessPayGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "paid_at", Value: m.PaidAt.Format(TimestampLayoutMake)},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiId},
		{Name: "spu_id", Value: m.SpuId},
		{Name: "sku_id", Value: m.SkuId},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "apply_did", Value: m.ApplyDid},
		{Name: "operator_id", Value: m.OperatorId},
		{Name: "sell_operator_id", Value: m.SellOperatorId},
		{Name: "product_type", Value: m.ProductType},
		{Name: "sub_type", Value: m.SubType},
		{Name: "count", Value: m.Count},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "payload", Value: m.Payload},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateBusinessPayArchiveLabel []*DuplicateUpdateBusinessPayGormModel

func (m *DuplicateUpdateBusinessPayArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateBusinessPayGormModel {
	list := make(map[string][]*DuplicateUpdateBusinessPayGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.PaidAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
