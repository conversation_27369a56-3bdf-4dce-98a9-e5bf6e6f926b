package dimensionscheme

import (
	"fmt"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/global"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
)

type GroupRelationModel struct {
	SchemeId       int `json:"scheme_id" gorm:"column:scheme_id;comment:方案ID"`
	GroupId        int `json:"group_id" gorm:"column:group_id;comment:分组ID"`
	DimensionValue int `json:"dimension_value" gorm:"column:dimension_value;comment:维度值"`
}

func (GroupRelationModel) TableName() string {
	return "r_dimension_group_relation"
}

func (GroupRelationModel) TableComment() string {
	return "维度分组关系表"
}

type GroupRelationImpl struct{}

func (c GroupRelationImpl) BatchSaveWithCleanBySchemeId(schemeId int, dimensionGroupRelations []dimensionscheme.PoGroupRelation) error {
	var err error
	err = global.Databases[db.DatabaseNameDefault].Where("scheme_id = ?", schemeId).Delete(&GroupRelationModel{}).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}

	err = global.Databases[db.DatabaseNameSelectDB].Exec(fmt.Sprintf("DELETE FROM r_dimension_group_relation WHERE scheme_id = %d", schemeId)).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}

	var groupRelationModels []GroupRelationModel
	for _, item := range dimensionGroupRelations {
		groupRelationModels = append(groupRelationModels, GroupRelationModel{
			SchemeId:       item.SchemeId,
			GroupId:        item.GroupId,
			DimensionValue: item.DimensionValue,
		})
	}

	err = global.Databases[db.DatabaseNameDefault].Model(GroupRelationModel{}).Create(&groupRelationModels).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}

	err = global.Databases[db.DatabaseNameSelectDB].Exec(global.Databases[db.DatabaseNameSelectDB].Model(GroupRelationModel{}).ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Create(&groupRelationModels)
	})).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (c GroupRelationImpl) DeleteBySchemeId(schemeId int) error {
	err := global.Databases[db.DatabaseNameDefault].Where("scheme_id = ?", schemeId).Delete(&GroupRelationModel{}).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}

	err = global.Databases[db.DatabaseNameSelectDB].Exec(fmt.Sprintf("DELETE FROM r_dimension_group_relation WHERE scheme_id = %d", schemeId)).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (c GroupRelationImpl) ListBySchemeId(schemeId int) ([]dimensionscheme.PoGroupRelation, error) {
	var groupRelations []GroupRelationModel
	err := global.Databases[db.DatabaseNameDefault].Where("scheme_id = ?", schemeId).Find(&groupRelations).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var poGroupRelations []dimensionscheme.PoGroupRelation
	for _, item := range groupRelations {
		poGroupRelations = append(poGroupRelations, dimensionscheme.PoGroupRelation{
			SchemeId:       item.SchemeId,
			GroupId:        item.GroupId,
			DimensionValue: item.DimensionValue,
		})
	}
	return poGroupRelations, nil
}

func (c GroupRelationImpl) ListBySchemeIdAndDimensionValues(schemeId int, dimensionValue []int) ([]dimensionscheme.PoGroupRelation, error) {
	var groupRelations []GroupRelationModel
	err := global.Databases[db.DatabaseNameDefault].Where("scheme_id = ? AND dimension_value IN (?)", schemeId, dimensionValue).Find(&groupRelations).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var poGroupRelations []dimensionscheme.PoGroupRelation
	for _, item := range groupRelations {
		poGroupRelations = append(poGroupRelations, dimensionscheme.PoGroupRelation{
			SchemeId:       item.SchemeId,
			GroupId:        item.GroupId,
			DimensionValue: item.DimensionValue,
		})
	}
	return poGroupRelations, nil
}
