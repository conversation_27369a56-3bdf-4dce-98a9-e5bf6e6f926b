package dimensionscheme

import (
	"report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/global"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
	"time"
)

type GroupModel struct {
	Id        int       `json:"id" gorm:"column:id;comment:ID"`
	SchemeId  int       `json:"scheme_id" gorm:"column:scheme_id;comment:方案ID"`
	Name      string    `json:"name" gorm:"column:name;comment:名称"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
}

func (GroupModel) TableName() string {
	return "r_dimension_group"
}

func (GroupModel) TableComment() string {
	return "维度分组表"
}

type GroupImpl struct{}

func (s GroupImpl) Insert(schemeId int, name string) (int, error) {
	item := GroupModel{
		SchemeId: schemeId,
		Name:     name,
	}
	err := global.Databases[db.DatabaseNameDefault].Model(GroupModel{}).Create(&item).Error
	if err != nil {
		return 0, szerrors.NewDataPersistenceError(err)
	}
	return item.Id, nil
}

func (s GroupImpl) Delete(id int) error {
	err := global.Databases[db.DatabaseNameDefault].Model(GroupModel{}).Where("id = ?", id).Delete(&GroupModel{}).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (s GroupImpl) DeleteBySchemeId(schemeId int) error {
	err := global.Databases[db.DatabaseNameDefault].Model(GroupModel{}).Where("scheme_id = ?", schemeId).Delete(&GroupModel{}).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (s GroupImpl) Modify(id int, name string) error {
	err := global.Databases[db.DatabaseNameDefault].Model(GroupModel{}).Where("id = ?", id).Update("name", name).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (s GroupImpl) BatchSave(groups []*dimensionscheme.PoGroup) error {
	var groupModels []GroupModel
	for _, group := range groups {
		groupModels = append(groupModels, GroupModel{
			SchemeId: group.SchemeId,
			Name:     group.Name,
		})
	}
	err := global.Databases[db.DatabaseNameDefault].Model(GroupModel{}).Create(&groupModels).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	for index, group := range groups {
		group.Id = groupModels[index].Id
	}
	return nil
}

func (s GroupImpl) Pagination(schemeId int, page int, pageSize int) ([]dimensionscheme.PoGroup, error) {
	var groupModels []*GroupModel
	err := global.Databases[db.DatabaseNameDefault].Where("scheme_id = ?", schemeId).Offset((page - 1) * pageSize).Limit(pageSize).Find(&groupModels).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var groups []dimensionscheme.PoGroup
	for _, groupModel := range groupModels {
		groups = append(groups, dimensionscheme.PoGroup{
			Id:       groupModel.Id,
			SchemeId: groupModel.SchemeId,
			Name:     groupModel.Name,
		})
	}
	return groups, nil
}

func (s GroupImpl) Detail(id int) (*dimensionscheme.PoGroup, error) {
	var groupModel *GroupModel
	err := global.Databases[db.DatabaseNameDefault].Where("id = ?", id).Find(&groupModel).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	return &dimensionscheme.PoGroup{
		Id:       groupModel.Id,
		SchemeId: groupModel.SchemeId,
		Name:     groupModel.Name,
	}, nil
}

func (s GroupImpl) Count(schemeId int) (int, error) {
	var total int64
	err := global.Databases[db.DatabaseNameDefault].Model(GroupModel{}).
		Where("scheme_id = ?", schemeId).
		Count(&total).Error
	if err != nil {
		return 0, szerrors.NewDataPersistenceError(err)
	}
	return int(total), nil
}

func (s GroupImpl) ListByIds(ids []int) ([]dimensionscheme.PoGroup, error) {
	var groupModels []*GroupModel
	err := global.Databases[db.DatabaseNameDefault].Where("id in ?", ids).Find(&groupModels).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	var groups []dimensionscheme.PoGroup
	for _, groupModel := range groupModels {
		groups = append(groups, dimensionscheme.PoGroup{
			Id:       groupModel.Id,
			SchemeId: groupModel.SchemeId,
			Name:     groupModel.Name,
		})
	}
	return groups, nil
}
