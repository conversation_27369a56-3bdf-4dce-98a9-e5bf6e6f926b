package dimensionscheme

import (
	"fmt"
	repoDimensionscheme "report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/global"
	"report-service/internal/infra/db"

	"gorm.io/gorm"
)

type TagRelationModel struct {
	MerchantId   int    `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	Source       int    `json:"source" gorm:"column:source;comment:来源"`
	TagGroupCode string `json:"tag_group_code" gorm:"column:tag_group_code;comment:标签组ID"`
	TagCode      string `json:"tag_code" gorm:"column:tag_code;comment:标签ID"`
	SubjectId    int    `json:"subject_id" gorm:"column:subject_id;comment:维度值"`
}

func (TagRelationModel) TableName() string {
	return "r_dimension_tag_relation"
}

func (TagRelationModel) TableComment() string {
	return "维度标签关系表"
}

func (TagRelationModel) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

func (m TagRelationModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m TagRelationModel) InsertAll(tx interface{}, tagRelations []repoDimensionscheme.PoTagRelation) error {
	if tx == nil {
		tx = m.getQuery()
	}
	// 检查是否有数据需要插入
	if len(tagRelations) == 0 {
		return nil
	}

	// 使用原生SQL语句进行插入
	sql := fmt.Sprintf("INSERT INTO %s (merchant_id, `source`, tag_group_code, tag_code, subject_id) VALUES %s",
		m.TableName(), generateInsertValues(tagRelations))
	return tx.(*gorm.DB).Exec(sql).Error
}

// 辅助函数，为InsertAll生成INSERT语句的VALUES部分
func generateInsertValues(tagRelations []repoDimensionscheme.PoTagRelation) string {
	if len(tagRelations) == 0 {
		return "(0, 0, '', '', 0)" // 防止空值情况，提供默认值
	}

	// 根据PoTagRelation结构生成VALUES语句
	values := ""
	for i, relation := range tagRelations {
		if i > 0 {
			values += ", "
		}
		values += fmt.Sprintf("(%d, %d, '%s', '%s', %d)",
			relation.MerchantId,
			relation.Source,
			relation.TagGroupCode,
			relation.TagCode,
			relation.SubjectId)
	}
	return values
}

func (m TagRelationModel) DeleteByTagGroupCode(tx interface{}, merchantId int, tagGroupCode string, source int) error {
	if tx == nil {
		tx = m.getQuery()
	}
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("merchant_id = ? and tag_group_code = ? and source = ?", merchantId, tagGroupCode, source).Delete(&TagRelationModel{})
	})
	return tx.(*gorm.DB).Exec(sql).Error
}

func (m TagRelationModel) DeleteByTagCode(merchantId int, tagCode string, source int) error {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("merchant_id = ? and tag_code = ? and source = ?", merchantId, tagCode, source).Delete(&TagRelationModel{})
	})
	return m.getQuery().Exec(sql).Error
}

// FindTagGroupsByMerchantId 根据商户ID查询所有标签组
func (m TagRelationModel) FindTagGroupsByMerchantId(merchantId int, source int) ([]string, error) {
	var tagGroups []string
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Model(&TagRelationModel{}).
			Select("DISTINCT tag_group_code").
			Where("merchant_id = ? and source = ?", merchantId, source).
			Find(&tagGroups)
	})
	err := m.getQuery().Raw(sql).Pluck("tag_group_code", &tagGroups).Error
	return tagGroups, err
}

// DeleteByMerchantIdAndTagGroupCodes 根据商户ID和标签组编码列表删除标签关联
func (m TagRelationModel) DeleteByMerchantIdAndTagGroupCodes(merchantId int, tagGroupCodes []string, source int) error {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("merchant_id = ? AND tag_group_code IN ? and source = ?", merchantId, tagGroupCodes, source).
			Delete(&TagRelationModel{})
	})
	return m.getQuery().Exec(sql).Error
}

// GetSubjectIdListByMerchantId 根据商户ID获取标签关联的维度值列表
func (m TagRelationModel) GetSubjectIdListByMerchantId(merchantId int, source int, tagGroupCode string, tagCode string) ([]repoDimensionscheme.PoTagRelation, error) {
	var list []repoDimensionscheme.PoTagRelation
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("merchant_id = ?", merchantId)
		if source != 0 {
			tx = tx.Where("source = ?", source)
		}
		if tagGroupCode != "" {
			tx = tx.Where("tag_group_code = ?", tagGroupCode)
		}
		if tagCode != "" {
			tx = tx.Where("tag_code = ?", tagCode)
		}
		return tx.Find(&list)
	})
	err := m.getQuery().Raw(sql).Find(&list).Error
	return list, err
}
