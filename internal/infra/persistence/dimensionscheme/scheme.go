package dimensionscheme

import (
	"report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/global"
	"report-service/internal/infra/db"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
	"time"
)

type SchemeModel struct {
	Id            int       `json:"id" db:"id"`
	MerchantID    int       `json:"merchant_id" db:"merchant_id"`
	DimensionType int       `json:"dimension_type" db:"dimension_type"` // 类型：1spu 2sku 3支付方式 4订单渠道
	Name          string    `json:"name" db:"name"`                     // 名称
	CreatedAt     time.Time `json:"created_at" db:"created_at"`         // 创建时间
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`         // 更新时间
}

func (SchemeModel) TableName() string {
	return "r_dimension_scheme"
}

func (SchemeModel) TableComment() string {
	return "维度方案表"
}

type SchemeImpl struct{}

func (s SchemeImpl) Insert(merchantId int, dimensionType int, name string) (int, error) {
	item := SchemeModel{
		MerchantID:    merchantId,
		DimensionType: dimensionType,
		Name:          name,
	}
	err := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{}).Create(&item).Error
	if err != nil {
		return 0, szerrors.NewDataPersistenceError(err)
	}
	return item.Id, nil
}

func (s SchemeImpl) Modify(id int, name string) error {
	err := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{}).Where("id = ?", id).Updates(map[string]interface{}{
		"name": name,
	}).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}
func (s SchemeImpl) Delete(id int) error {
	err := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{}).Where("id = ?", id).Delete(&SchemeModel{}).Error
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

func (s SchemeImpl) Detail(id int) (*dimensionscheme.PoScheme, error) {
	var item SchemeModel
	err := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{}).Where("id = ?", id).First(&item).Error
	if utils.IsRecordNotFoundErr(err) {
		return nil, szerrors.NewInvalidParamErrorWithText("方案ID不存在")
	}
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	return &dimensionscheme.PoScheme{
		Id:            item.Id,
		MerchantId:    item.MerchantID,
		DimensionType: item.DimensionType,
		Name:          item.Name,
	}, nil
}

func (s SchemeImpl) Pagination(merchantId int, dimensionType int, page int, pageSize int) ([]dimensionscheme.PoScheme, error) {
	var poSchemes []dimensionscheme.PoScheme
	query := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{})
	if merchantId > 0 {
		query = query.Where("merchant_id = ?", merchantId)
	}
	if dimensionType > 0 {
		query = query.Where("dimension_type = ?", dimensionType)
	}
	err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&poSchemes).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	return poSchemes, nil
}

func (s SchemeImpl) Count(merchantId int, dimensionType int) (int, error) {
	var total int64
	err := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{}).
		Where("merchant_id = ?", merchantId).
		Where("dimension_type = ?", dimensionType).
		Count(&total).Error
	if err != nil {
		return 0, szerrors.NewDataPersistenceError(err)
	}
	return int(total), nil
}

func (s SchemeImpl) ListByIds(ids []int) ([]dimensionscheme.PoScheme, error) {
	var poSchemes []dimensionscheme.PoScheme
	err := global.Databases[db.DatabaseNameDefault].Model(SchemeModel{}).Where("id in (?)", ids).Find(&poSchemes).Error
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	return poSchemes, nil
}
