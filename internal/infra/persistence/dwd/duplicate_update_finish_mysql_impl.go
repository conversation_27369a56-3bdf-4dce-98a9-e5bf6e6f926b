package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateFinishGormModel struct {
	GormModelBase
	GormModelDiscount
	FinishNo   int       `gorm:"column:finish_no;type:int;not null;comment:完结单号" json:"finish_no"`
	FinishedAt time.Time `gorm:"column:finished_at;type:timestamp;not null;comment:完结时间" json:"finished_at"`
}

func (m *DuplicateUpdateFinishGormModel) GetGormModel() FinishGormModel {
	return FinishGormModel{}
}

func (m *DuplicateUpdateFinishGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.FinishedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateFinishGormModel) validate() error {
	if m.FinishNo == 0 {
		return errors.New("model report finish result set duplicate update finish_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateFinishGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "finish_no", Value: m.FinishNo},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "finished_at", Value: m.FinishedAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateFinishGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "finished_at", Value: m.FinishedAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateFinishArchiveLabel []*DuplicateUpdateFinishGormModel

func (m *DuplicateUpdateFinishArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateFinishGormModel {
	list := make(map[string][]*DuplicateUpdateFinishGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.FinishedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
