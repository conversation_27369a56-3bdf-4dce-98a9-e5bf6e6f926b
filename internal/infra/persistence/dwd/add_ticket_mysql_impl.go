package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type AddTicketGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	AddTicketNo   int       `gorm:"column:add_ticket_no;type:int;not null;comment:加票单号" json:"add_ticket_no"`
	AddedTicketAt time.Time `gorm:"column:added_ticket_at;type:timestamp;not null;comment:加票时间" json:"added_ticket_at"`
}

func (m AddTicketGormModel) TableName() string {
	return "r_report_fact_add_ticket"
}
func (m AddTicketGormModel) TableComment() string {
	return "报表-加票事实表"
}

type AddTicketMysqlImpl struct{}

func (r AddTicketMysqlImpl) Insert(records []dwd.ReportFactAddTicketModel) error {
	var insertRecords []*DuplicateUpdateAddTicketGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateAddTicketArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
