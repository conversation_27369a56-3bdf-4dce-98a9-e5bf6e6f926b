package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateCollectGormModel struct {
	GormModelBase
	GormModelDiscount
	CollectNo   int       `gorm:"column:collect_no;type:bigint;not null;comment:取票单号" json:"collect_no"`
	CollectedAt time.Time `gorm:"column:collected_at;type:timestamp;not null;comment:取票时间" json:"collected_at"`
}

func (m *DuplicateUpdateCollectGormModel) GetGormModel() ReportFactCollectGormModel {
	return ReportFactCollectGormModel{}
}

func (m *DuplicateUpdateCollectGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.CollectedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateCollectGormModel) validate() error {
	if m.CollectNo == 0 {
		return errors.New("model report collect result set duplicate update collect_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateCollectGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "collect_no", Value: m.CollectNo},
		{Name: "collected_at", Value: m.CollectedAt.Format(TimestampLayoutMake)},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "trade_no", Value: m.TradeNo},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
	}, nil
}

func (m *DuplicateUpdateCollectGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "collected_at", Value: m.CollectedAt.Format(TimestampLayoutMake)},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "trade_no", Value: m.TradeNo},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
	}, nil
}

type DuplicateUpdateCollectArchiveLabel []*DuplicateUpdateCollectGormModel

func (m *DuplicateUpdateCollectArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateCollectGormModel {
	list := make(map[string][]*DuplicateUpdateCollectGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.CollectedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
