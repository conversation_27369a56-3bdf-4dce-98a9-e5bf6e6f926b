package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type AfterSaleGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	AfterSaleNo  int       `gorm:"column:after_sale_no;type:int;not null;comment:售后单号" json:"after_sale_no"`
	AfterSaledAt time.Time `gorm:"column:after_saled_at;type:timestamp;not null;comment:售后时间" json:"after_saled_at"`
}

func (m AfterSaleGormModel) TableName() string {
	return "r_report_fact_after_sale"
}
func (m AfterSaleGormModel) TableComment() string {
	return "报表-售后事实表"
}

type AfterSaleMysqlImpl struct{}

func (r AfterSaleMysqlImpl) Insert(records []dwd.ReportFactAfterSaleModel) error {
	var insertRecords []*DuplicateUpdateAfterSaleGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateAfterSaleArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
