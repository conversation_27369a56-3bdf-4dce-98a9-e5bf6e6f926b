package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type VerifyGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	VerifyNo   int       `gorm:"column:verify_no;type:int;not null;comment:核销单号" json:"verify_no"`
	VerifiedAt time.Time `gorm:"column:verified_at;type:timestamp;not null;comment:核销时间" json:"verified_at"`
}

func (m VerifyGormModel) TableName() string {
	return "r_report_fact_verify"
}
func (m VerifyGormModel) TableComment() string {
	return "报表-核销事实表"
}

type VerifyMysqlImpl struct{}

func (r VerifyMysqlImpl) Insert(records []dwd.ReportFactVerifyModel) error {
	var insertRecords []*DuplicateUpdateVerifyGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateVerifyArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
