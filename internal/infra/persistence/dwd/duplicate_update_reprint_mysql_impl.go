package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateReprintGormModel struct {
	GormModelBase
	GormModelDiscount
	ReprintNo   int       `gorm:"column:reprint_no;type:bigint;not null;comment:重打印单号" json:"reprint_no"`
	ReprintedAt time.Time `gorm:"column:reprinted_at;type:timestamp;not null;comment:重打印时间" json:"reprinted_at"`
}

func (m *DuplicateUpdateReprintGormModel) GetGormModel() ReportFactReprintGormModel {
	return ReportFactReprintGormModel{}
}

func (m *DuplicateUpdateReprintGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.ReprintedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateReprintGormModel) validate() error {
	if m.ReprintNo == 0 {
		return errors.New("model report reprint result set duplicate update reprint_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateReprintGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "reprint_no", Value: m.ReprintNo},
		{Name: "reprinted_at", Value: m.ReprintedAt.Format(TimestampLayoutMake)},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "trade_no", Value: m.TradeNo},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
	}, nil
}

func (m *DuplicateUpdateReprintGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "reprinted_at", Value: m.ReprintedAt.Format(TimestampLayoutMake)},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "trade_no", Value: m.TradeNo},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
	}, nil
}

type DuplicateUpdateReprintArchiveLabel []*DuplicateUpdateReprintGormModel

func (m *DuplicateUpdateReprintArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateReprintGormModel {
	list := make(map[string][]*DuplicateUpdateReprintGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.ReprintedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
