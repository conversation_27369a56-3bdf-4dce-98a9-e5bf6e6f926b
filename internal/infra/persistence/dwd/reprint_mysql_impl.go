package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type ReportFactReprintGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	ReprintNo   int       `gorm:"column:reprint_no;type:bigint;not null;comment:重打印单号" json:"reprint_no"`
	Payload     string    `gorm:"column:payload;type:json;not null;comment:扩展数据" json:"payload"`
	ReprintedAt time.Time `gorm:"column:reprinted_at;type:timestamp;not null;comment:重打印时间" json:"reprinted_at"`
}

func (m ReportFactReprintGormModel) TableName() string {
	return "r_report_fact_reprint"
}

func (m ReportFactReprintGormModel) TableComment() string {
	return "报表-重打印事实表"
}

type ReprintMysqlImpl struct{}

func (r ReprintMysqlImpl) Insert(records []dwd.ReportFactReprintModel) error {
	var insertRecords []*DuplicateUpdateReprintGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateReprintArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
