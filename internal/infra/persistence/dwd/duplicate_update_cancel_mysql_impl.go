package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateCancelGormModel struct {
	GormModelBase
	GormModelDiscount
	GormModelFee
	CancelNo    int       `gorm:"column:cancel_no;type:int;not null;comment:取消单号" json:"cancel_no"`
	CancelledAt time.Time `gorm:"column:cancelled_at;type:timestamp;not null;comment:取消时间" json:"cancelled_at"`
}

func (m *DuplicateUpdateCancelGormModel) GetGormModel() CancelGormModel {
	return CancelGormModel{}
}

func (m *DuplicateUpdateCancelGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.CancelledAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateCancelGormModel) validate() error {
	if m.CancelNo == 0 {
		return errors.New("model report cancel result set duplicate update cancel_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateCancelGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "cancel_no", Value: m.CancelNo},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "cancelled_at", Value: m.CancelledAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "cost_fee", Value: m.CostFee},
		{Name: "sale_fee", Value: m.SaleFee},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateCancelGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "cancelled_at", Value: m.CancelledAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "cost_fee", Value: m.CostFee},
		{Name: "sale_fee", Value: m.SaleFee},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateCancelArchiveLabel []*DuplicateUpdateCancelGormModel

func (m *DuplicateUpdateCancelArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateCancelGormModel {
	list := make(map[string][]*DuplicateUpdateCancelGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.CancelledAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
