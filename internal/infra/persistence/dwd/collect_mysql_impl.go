package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type ReportFactCollectGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	CollectNo   int       `gorm:"column:collect_no;type:bigint;not null;comment:取票单号" json:"collect_no"`
	Payload     string    `gorm:"column:payload;type:json;not null;comment:扩展数据" json:"payload"`
	CollectedAt time.Time `gorm:"column:collected_at;type:timestamp;not null;comment:取票时间" json:"collected_at"`
}

func (m ReportFactCollectGormModel) TableName() string {
	return "r_report_fact_collect"
}

func (m ReportFactCollectGormModel) TableComment() string {
	return "报表-取票事实表"
}

type CollectMysqlImpl struct{}

func (r CollectMysqlImpl) Insert(records []dwd.ReportFactCollectModel) error {
	var insertRecords []*DuplicateUpdateCollectGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateCollectArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
