package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type RevokeGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	GormModelFee
	RevokeNo  int       `gorm:"column:revoke_no;type:int;not null;comment:撤销撤改单号" json:"revoke_no"`
	RevokedAt time.Time `gorm:"column:revoked_at;type:timestamp;not null;comment:撤销撤改时间" json:"revoked_at"`
}

func (m RevokeGormModel) TableName() string {
	return "r_report_fact_revoke"
}
func (m RevokeGormModel) TableComment() string {
	return "报表-撤销撤改事实表"
}

type RevokeMysqlImpl struct{}

func (r RevokeMysqlImpl) Insert(records []dwd.ReportFactRevokeModel) error {
	var insertRecords []*DuplicateUpdateRevokeGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateRevokeArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
