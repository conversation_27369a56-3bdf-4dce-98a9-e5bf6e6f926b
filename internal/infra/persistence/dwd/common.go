package dwd

import (
	"fmt"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"report-service/internal/global"
	"report-service/internal/infra/db"
	"time"
)

const (
	TimestampLayoutMake = "2006-01-02 15:04:05"
)

type GormModelBase struct {
	MerchantID        int            `gorm:"column:merchant_id;type:bigint;not null;comment:商户ID" json:"merchant_id"`                    // 商户ID
	ParentMerchantID  int            `gorm:"column:parent_merchant_id;type:bigint;not null;comment:上级商户ID" json:"parent_merchant_id"`    // 上级商户ID
	DistributorID     int            `gorm:"column:distributor_id;type:bigint;not null;comment:下级商户ID" json:"distributor_id"`            // 下级商户ID
	OrderNo           string         `gorm:"column:order_no;type:varchar;not null;comment:订单号" json:"order_no"`                          // 订单号
	PoiID             int            `gorm:"column:poi_id;type:bigint;not null;comment:PoiID" json:"poi_id"`                             // PoiID
	SpuID             int            `gorm:"column:spu_id;type:bigint;not null;comment:SpuID" json:"spu_id"`                             // SpuID
	SkuID             int            `gorm:"column:sku_id;type:bigint;not null;comment:SkuID" json:"sku_id"`                             // SkuID
	SaleChannel       int            `gorm:"column:sale_channel;type:int;not null;comment:销售渠道" json:"sale_channel"`                     // 销售渠道
	OperateChannel    int            `gorm:"column:operate_channel;type:int;not null;comment:操作渠道" json:"operate_channel"`               //操作渠道
	CostPayMode       int            `gorm:"column:cost_pay_mode;type:int;not null;comment:采购支付方式" json:"cost_pay_mode"`                 // 采购支付方式
	SalePayMode       int            `gorm:"column:sale_pay_mode;type:int;not null;comment:销售支付方式" json:"sale_pay_mode"`                 // 销售支付方式
	SellOperatorID    int            `gorm:"column:sell_operator_id;type:bigint;not null;comment:售票员ID" json:"sell_operator_id"`         // 售票员ID
	OperatorID        int            `gorm:"column:operator_id;type:bigint;not null;comment:操作人ID" json:"operator_id"`                   // 操作人ID
	Count             int            `gorm:"column:count;type:int;not null;comment:数量" json:"count"`                                     // 数量
	CostUnitPrice     int            `gorm:"column:cost_unit_price;type:int;not null;comment:采购单价" json:"cost_unit_price"`               // 采购单价（链路原始采购单价）
	SaleUnitPrice     int            `gorm:"column:sale_unit_price;type:int;not null;comment:销售单价" json:"sale_unit_price"`               // 销售单价（链路原始销售单价）
	CostPrice         int            `gorm:"column:cost_price;type:int;not null;comment:采购金额" json:"cost_price"`                         // 采购金额（原始订单采购金额）
	SalePrice         int            `gorm:"column:sale_price;type:int;not null;comment:销售金额" json:"sale_price"`                         // 销售金额（原始订单销售金额）
	SellSiteId        int            `gorm:"column:sell_site_id;type:int;not null;comment:售票站点ID" json:"sell_site_id"`                   //售票站点ID
	OperateSiteId     int            `gorm:"column:operate_site_id;type:int;not null;comment:操作站点ID" json:"operate_site_id"`             //操作站点ID
	ExternalOperateNo string         `gorm:"column:external_operate_no;type:varchar;not null;comment:外部操作单号" json:"external_operate_no"` //外部操作单号
	Payload           datatypes.JSON `gorm:"column:payload;type:json;not null;comment:扩展数据" json:"payload"`
	TradeNo           string         `gorm:"column:trade_no;type:varchar;not null;comment:交易单号" json:"trade_no"` // 交易单号
}

type GormModelDiscount struct {
	CostDiscountPrice int `gorm:"column:cost_discount_price;type:int;not null;comment:采购优惠金额" json:"cost_discount_price"` // 采购优惠金额
	SaleDiscountPrice int `gorm:"column:sale_discount_price;type:int;not null;comment:采购优惠金额" json:"sale_discount_price"` // 销售优惠金额
}

type GormModelFee struct {
	CostFee int `gorm:"column:cost_fee;type:int;not null;comment:采购手续费" json:"cost_fee"` // 采购手续费
	SaleFee int `gorm:"column:sale_fee;type:int;not null;comment:销售手续费" json:"sale_fee"` // 销售手续费
}

func (m GormModelBase) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

// 生成归档标识：上半年或下半年
func (m GormModelBase) generateArchiveLabel(t time.Time) string {
	month := t.Month()
	if month <= 6 {
		return fmt.Sprintf("%d0%d", t.Year(), 1)
	}
	return fmt.Sprintf("%d0%d", t.Year(), 2)
}

func (m GormModelBase) GenerateArchiveLabel(tableName string) string {
	return fmt.Sprintf("%s_%s", tableName, m.generateArchiveLabel(time.Now()))
}

func (m GormModelBase) getQuery(tableName string) *gorm.DB {
	table := m.GenerateArchiveLabel(tableName)
	return global.Databases[m.GetDatabaseName()].Table(table)
}

func (m GormModelBase) getDuplicateDataSql(tableName string, groupStr string, offset int, size int) string {
	table := m.GenerateArchiveLabel(tableName)
	return fmt.Sprintf("SELECT %s,count(id) as total,GROUP_CONCAT(id ORDER  BY id SEPARATOR ',') as str FROM %s group BY %s limit %d,%d",
		groupStr, table, groupStr, offset, size)
}
