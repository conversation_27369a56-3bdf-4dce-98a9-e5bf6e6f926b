package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type FinishGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	FinishNo   int       `gorm:"column:finish_no;type:int;not null;comment:完结单号" json:"finish_no"`
	FinishedAt time.Time `gorm:"column:finished_at;type:timestamp;not null;comment:完结时间" json:"finished_at"`
}

func (m FinishGormModel) TableName() string {
	return "r_report_fact_finish"
}
func (m FinishGormModel) TableComment() string {
	return "报表-完结事实表"
}

type FinishMysqlImpl struct{}

func (r FinishMysqlImpl) Insert(records []dwd.ReportFactFinishModel) error {
	var insertRecords []*DuplicateUpdateFinishGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateFinishArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
