package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateAddTicketGormModel struct {
	GormModelBase
	AddTicketNo   int       `gorm:"column:add_ticket_no;type:int;not null;comment:加票单号" json:"add_ticket_no"`
	AddedTicketAt time.Time `gorm:"column:added_ticket_at;type:timestamp;not null;comment:加票时间" json:"added_ticket_at"`
}

func (m *DuplicateUpdateAddTicketGormModel) GetGormModel() AddTicketGormModel {
	return AddTicketGormModel{}
}

func (m *DuplicateUpdateAddTicketGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.AddedTicketAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateAddTicketGormModel) validate() error {
	if m.AddTicketNo == 0 {
		return errors.New("model report add_ticket result set duplicate update add_ticket_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateAddTicketGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "add_ticket_no", Value: m.AddTicketNo},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "added_ticket_at", Value: m.AddedTicketAt.Format(TimestampLayoutMake)},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateAddTicketGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "added_ticket_at", Value: m.AddedTicketAt.Format(TimestampLayoutMake)},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateAddTicketArchiveLabel []*DuplicateUpdateAddTicketGormModel

func (m *DuplicateUpdateAddTicketArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateAddTicketGormModel {
	list := make(map[string][]*DuplicateUpdateAddTicketGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.AddedTicketAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
