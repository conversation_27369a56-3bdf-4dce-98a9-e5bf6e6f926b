package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateRevokeGormModel struct {
	GormModelBase
	GormModelDiscount
	GormModelFee
	RevokeNo  int       `gorm:"column:revoke_no;type:int;not null;comment:撤销撤改单号" json:"revoke_no"`
	RevokedAt time.Time `gorm:"column:revoked_at;type:timestamp;not null;comment:撤销撤改时间" json:"revoked_at"`
}

func (m *DuplicateUpdateRevokeGormModel) GetGormModel() RevokeGormModel {
	return RevokeGormModel{}
}

func (m *DuplicateUpdateRevokeGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.RevokedAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateRevokeGormModel) validate() error {
	if m.RevokeNo == 0 {
		return errors.New("model report revoke result set duplicate update revoke_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateRevokeGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "revoke_no", Value: m.RevokeNo},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "revoked_at", Value: m.RevokedAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "cost_fee", Value: m.CostFee},
		{Name: "sale_fee", Value: m.SaleFee},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateRevokeGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "revoked_at", Value: m.RevokedAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "cost_fee", Value: m.CostFee},
		{Name: "sale_fee", Value: m.SaleFee},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateRevokeArchiveLabel []*DuplicateUpdateRevokeGormModel

func (m *DuplicateUpdateRevokeArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateRevokeGormModel {
	list := make(map[string][]*DuplicateUpdateRevokeGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.RevokedAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
