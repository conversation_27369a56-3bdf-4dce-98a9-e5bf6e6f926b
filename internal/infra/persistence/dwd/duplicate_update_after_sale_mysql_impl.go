package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdateAfterSaleGormModel struct {
	GormModelBase
	AfterSaleNo  int       `gorm:"column:after_sale_no;type:int;not null;comment:售后单号" json:"after_sale_no"`
	AfterSaledAt time.Time `gorm:"column:after_saled_at;type:timestamp;not null;comment:售后时间" json:"after_saled_at"`
}

func (m *DuplicateUpdateAfterSaleGormModel) GetGormModel() AfterSaleGormModel {
	return AfterSaleGormModel{}
}

func (m *DuplicateUpdateAfterSaleGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.AfterSaledAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdateAfterSaleGormModel) validate() error {
	if m.AfterSaleNo == 0 {
		return errors.New("model report after_sale result set duplicate update after_sale_no is zero")
	}
	return nil
}

func (m *DuplicateUpdateAfterSaleGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "after_sale_no", Value: m.AfterSaleNo},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "after_saled_at", Value: m.AfterSaledAt.Format(TimestampLayoutMake)},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdateAfterSaleGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "after_saled_at", Value: m.AfterSaledAt.Format(TimestampLayoutMake)},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdateAfterSaleArchiveLabel []*DuplicateUpdateAfterSaleGormModel

func (m *DuplicateUpdateAfterSaleArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdateAfterSaleGormModel {
	list := make(map[string][]*DuplicateUpdateAfterSaleGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.AfterSaledAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
