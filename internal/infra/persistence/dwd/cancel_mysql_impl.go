package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type CancelGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	GormModelFee
	CancelNo    int       `gorm:"column:cancel_no;type:int;not null;comment:取消单号" json:"cancel_no"`
	CancelledAt time.Time `gorm:"column:cancelled_at;type:timestamp;not null;comment:取消时间" json:"cancelled_at"`
}

func (m CancelGormModel) TableName() string {
	return "r_report_fact_cancel"
}
func (m CancelGormModel) TableComment() string {
	return "报表-取消事实表"
}

type CancelMysqlImpl struct{}

func (r CancelMysqlImpl) Insert(records []dwd.ReportFactCancelModel) error {
	var insertRecords []*DuplicateUpdateCancelGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdateCancelArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
