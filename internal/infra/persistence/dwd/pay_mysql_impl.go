package dwd

import (
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global/gormmodel"
	"report-service/pkg/utils"
	"time"
)

type PayGormModel struct {
	gormmodel.BaseModel
	GormModelBase
	GormModelDiscount
	PayNo  int       `gorm:"column:pay_no;type:varchar;not null;comment:支付单号" json:"pay_no"`
	PaidAt time.Time `gorm:"column:paid_at;type:timestamp;not null;comment:支付时间" json:"paid_at"`
}

func (m PayGormModel) TableName() string {
	return "r_report_fact_pay"
}
func (m PayGormModel) TableComment() string {
	return "报表-支付事实表"
}

type PayMysqlImpl struct{}

func (r PayMysqlImpl) Insert(records []dwd.ReportFactPayModel) error {
	var insertRecords []*DuplicateUpdatePayGormModel
	utils.JsonConvertOrPanic(records, &insertRecords)
	var list DuplicateUpdatePayArchiveLabel = insertRecords
	listData := list.InsertFieldValueMap()
	for _, data := range listData {
		if len(data) <= 0 {
			continue
		}
		sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(data)
		if err != nil {
			return err
		}
		err = GormModelBase{}.getQuery(data[0].TableName()).Exec(sql, values...).Error
		if err != nil {
			return err
		}
	}
	return nil
}
