package dwd

import (
	"errors"
	"fmt"
	"report-service/pkg/utils"
	"time"
)

type DuplicateUpdatePayGormModel struct {
	GormModelBase
	GormModelDiscount
	PayNo  int       `gorm:"column:pay_no;type:varchar;not null;comment:支付单号" json:"pay_no"`
	PaidAt time.Time `gorm:"column:paid_at;type:timestamp;not null;comment:支付时间" json:"paid_at"`
}

func (m *DuplicateUpdatePayGormModel) GetGormModel() PayGormModel {
	return PayGormModel{}
}

func (m *DuplicateUpdatePayGormModel) TableName() string {
	atMake := m.generateArchiveLabel(m.PaidAt)
	return fmt.Sprintf("%s_%s", m.GetGormModel().TableName(), atMake)
}

func (m *DuplicateUpdatePayGormModel) validate() error {
	if m.PayNo == 0 {
		return errors.New("model report pay result set duplicate update pay_no is zero")
	}
	return nil
}

func (m *DuplicateUpdatePayGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "pay_no", Value: m.PayNo},
		{Name: "merchant_id", Value: m.MerchantID},
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "paid_at", Value: m.PaidAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

func (m *DuplicateUpdatePayGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "parent_merchant_id", Value: m.ParentMerchantID},
		{Name: "distributor_id", Value: m.DistributorID},
		{Name: "order_no", Value: m.OrderNo},
		{Name: "poi_id", Value: m.PoiID},
		{Name: "spu_id", Value: m.SpuID},
		{Name: "sku_id", Value: m.SkuID},
		{Name: "sale_channel", Value: m.SaleChannel},
		{Name: "operate_channel", Value: m.OperateChannel},
		{Name: "cost_pay_mode", Value: m.CostPayMode},
		{Name: "sale_pay_mode", Value: m.SalePayMode},
		{Name: "sell_operator_id", Value: m.SellOperatorID},
		{Name: "operator_id", Value: m.OperatorID},
		{Name: "count", Value: m.Count},
		{Name: "cost_unit_price", Value: m.CostUnitPrice},
		{Name: "sale_unit_price", Value: m.SaleUnitPrice},
		{Name: "cost_price", Value: m.CostPrice},
		{Name: "sale_price", Value: m.SalePrice},
		{Name: "payload", Value: m.Payload},
		{Name: "paid_at", Value: m.PaidAt.Format(TimestampLayoutMake)},
		{Name: "cost_discount_price", Value: m.CostDiscountPrice},
		{Name: "sale_discount_price", Value: m.SaleDiscountPrice},
		{Name: "operate_site_id", Value: m.OperateSiteId},
		{Name: "sell_site_id", Value: m.SellSiteId},
		{Name: "external_operate_no", Value: m.ExternalOperateNo},
		{Name: "trade_no", Value: m.TradeNo},
	}, nil
}

type DuplicateUpdatePayArchiveLabel []*DuplicateUpdatePayGormModel

func (m *DuplicateUpdatePayArchiveLabel) InsertFieldValueMap() map[string][]*DuplicateUpdatePayGormModel {
	list := make(map[string][]*DuplicateUpdatePayGormModel)
	for _, model := range *m {
		atMake := model.generateArchiveLabel(model.PaidAt)
		list[atMake] = append(list[atMake], model)
	}
	return list
}
