package persistence

import (
	"report-service/internal/domain/repository"
	"report-service/internal/infra/persistence/business"
	"report-service/internal/infra/persistence/config"
	"report-service/internal/infra/persistence/dimensionscheme"
	dmcommon "report-service/internal/infra/persistence/dm/common"
	dmPaymodemoney "report-service/internal/infra/persistence/dm/customized/paymodemoney"
	"report-service/internal/infra/persistence/dm/customized/reportvisitsstatistics"
	"report-service/internal/infra/persistence/dm/oldreport"
	dmtourist "report-service/internal/infra/persistence/dm/tourist"
	"report-service/internal/infra/persistence/dwd"
	dwmcommon "report-service/internal/infra/persistence/dwm/common"
	dwmPaymodemoney "report-service/internal/infra/persistence/dwm/customized/paymodemoney"
	dwmtourist "report-service/internal/infra/persistence/dwm/tourist"
	"report-service/internal/infra/persistence/ods"
	oldreport2 "report-service/internal/infra/persistence/oldreport"
	"report-service/internal/infra/persistence/report/template"
)

func Init() {
	repository.TransRepo = TransImpl{}

	repository.ConfigRepository = config.ConfigGormModel{}
	repository.MerchantConfigRepository = config.MerchantConfigGormModel{}

	repository.ReportDwmCommonRepository = dwmcommon.Model{}
	repository.ReportDmCommonDateRepository = dmcommon.DayModel{}
	repository.ReportDmCommonHourRepository = dmcommon.HourModel{}
	repository.ReportDmCommonRealtimeRepository = dmcommon.RealtimeModel{}

	repository.OldReportOrderTwoRepo = oldreport.OrderTwo{}
	repository.OldReportOrderTwoHourRepo = oldreport.OrderTwoHour{}
	repository.OldReportCheckTwoRepo = oldreport.CheckTwo{}
	repository.OldReportCheckTwoHourRepo = oldreport.CheckTwoHour{}

	repository.SchemeRepository = dimensionscheme.SchemeImpl{}
	repository.GroupRepository = dimensionscheme.GroupImpl{}
	repository.GroupRelationRepository = dimensionscheme.GroupRelationImpl{}
	repository.TagRelationRepository = dimensionscheme.TagRelationModel{}

	repository.ReportTemplateConfigRepository = template.MysqlImpl{}
	repository.OdsBusinessPayRepository = ods.BusinessPayMysqlImpl{}
	repository.OdsBusinessCancelRepository = ods.BusinessCancelMysqlImpl{}
	repository.OdsBusinessVerifyRepository = ods.BusinessVerifyMysqlImpl{}
	repository.OdsBusinessRevokeRepository = ods.BusinessRevokeMysqlImpl{}
	repository.OdsBusinessAfterSaleRepository = ods.BusinessAfterSaleMysqlImpl{}
	repository.OdsBusinessAddTicketRepository = ods.BusinessAddTicketMysqlImpl{}
	repository.OdsBusinessFinishRepository = ods.BusinessFinishMysqlImpl{}
	repository.OdsBusinessCollectRepository = ods.BusinessCollectMysqlImpl{}
	repository.OdsBusinessReprintRepository = ods.BusinessReprintMysqlImpl{}

	repository.DwdPayRepository = dwd.PayMysqlImpl{}
	repository.DwdRevokeRepository = dwd.RevokeMysqlImpl{}
	repository.DwdVerifyRepository = dwd.VerifyMysqlImpl{}
	repository.DwdFinishRepository = dwd.FinishMysqlImpl{}
	repository.DwdAddTicketRepository = dwd.AddTicketMysqlImpl{}
	repository.DwdCancelRepository = dwd.CancelMysqlImpl{}
	repository.DwdAfterSaleRepository = dwd.AfterSaleMysqlImpl{}
	repository.DwdCollectRepository = dwd.CollectMysqlImpl{}
	repository.DwdReprintRepository = dwd.ReprintMysqlImpl{}

	//游客指标表
	repository.ReportDmTouristHourRepository = dmtourist.HourModelImpl{}
	repository.ReportDmTouristDayRepository = dmtourist.DayModelImpl{}
	repository.ReportDwmTouristRepository = dwmtourist.Model{}

	repository.PftReportSearchConfigResult = oldreport2.PftReportSearchConfig{}
	repository.PftReportTouristSourceAreaDetailResult = oldreport2.PftReportTouristSourceAreaDetail{}
	repository.PftReportTouristSourceAreaConfigResult = oldreport2.PftReportTouristSourceAreaConfig{}

	//定制报表-支付方式汇总金额报表明细表
	repository.ReportDwmPayModeMoneyRepository = dwmPaymodemoney.Model{}
	repository.ReportDmPayModeMoneyRepository = dmPaymodemoney.HourModelImpl{}

	//json-rpc应用配置
	repository.BusinessAppRepository = business.AppModel{}

	//访问白名单限制
	repository.BusinessAccessWhiteListRepository = business.AccessWhiteListModel{}

	//报表访问统计指标表
	repository.ReportDmVisitsStatisticsDayRepository = reportvisitsstatistics.VisitsStatisticsDayModel{}
}
