package reportvisitsstatistics

import (
	"errors"
	"report-service/pkg/utils"
)

type DuplicateUpdateDateGormModel struct {
	VisitsStatisticsDayModel
}

func (m *DuplicateUpdateDateGormModel) TableName() string {
	return m.VisitsStatisticsDayModel.TableName()
}

func (m *DuplicateUpdateDateGormModel) validate() error {
	if m.MerchantId == 0 || m.Date == "" {
		return errors.New("model visits statistics day set duplicate update params is error")
	}
	return nil
}

func (m *DuplicateUpdateDateGormModel) InsertFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "year", Value: m.Year},
		{Name: "month", Value: m.Month},
		{Name: "date", Value: m.Date},
		{Name: "merchant_id", Value: m.MerchantId},
		{Name: "old_pay_count", Value: m.OldPayCount},
		{Name: "new_pay_count", Value: m.NewPayCount},
		{Name: "old_verify_count", Value: m.OldVerifyCount},
		{Name: "new_verify_count", Value: m.NewVerifyCount},
		{Name: "old_total_count", Value: m.OldTotalCount},
		{Name: "new_total_count", Value: m.NewTotalCount},
	}, nil
}

func (m *DuplicateUpdateDateGormModel) UpdateFieldValueMap() ([]utils.DuplicateUpdateField, error) {
	err := m.validate()
	if err != nil {
		return nil, err
	}
	return []utils.DuplicateUpdateField{
		{Name: "old_pay_count", Value: m.OldPayCount, IsIncrement: true},
		{Name: "new_pay_count", Value: m.NewPayCount, IsIncrement: true},
		{Name: "old_verify_count", Value: m.OldVerifyCount, IsIncrement: true},
		{Name: "new_verify_count", Value: m.NewVerifyCount, IsIncrement: true},
		{Name: "old_total_count", Value: m.OldTotalCount, IsIncrement: true},
		{Name: "new_total_count", Value: m.NewTotalCount, IsIncrement: true},
	}, nil
}
