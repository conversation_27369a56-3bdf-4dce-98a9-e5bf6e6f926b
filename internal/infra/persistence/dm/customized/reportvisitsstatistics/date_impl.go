package reportvisitsstatistics

import (
	"fmt"
	"gorm.io/gorm"
	"report-service/internal/domain/repository/dm/customized"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/pkg/utils"
	"strings"
)

type VisitsStatisticsDayModel struct {
	gormmodel.BaseModel
	Year           int    `json:"year" gorm:"column:year;comment:年"`
	Month          int    `json:"month" gorm:"column:month;comment:月"`
	Date           string `json:"date" gorm:"column:date;comment:日期"`
	MerchantId     int    `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	OldPayCount    int    `json:"old_pay_count" gorm:"column:old_pay_count;comment:旧报表-预订-访问数"`
	OldVerifyCount int    `json:"old_verify_count" gorm:"column:old_verify_count;comment:旧报表-验证-访问数"`
	NewPayCount    int    `json:"new_pay_count" gorm:"column:new_pay_count;comment:新报表-预订-访问数"`
	NewVerifyCount int    `json:"new_verify_count" gorm:"column:new_verify_count;comment:新报表-验证-访问数"`
	OldTotalCount  int    `json:"old_total_count" gorm:"column:old_total_count;comment:旧报表-预订验证-总数"`
	NewTotalCount  int    `json:"new_total_count" gorm:"column:new_total_count;comment:新报表-预订验证-总数"`
}

func (m VisitsStatisticsDayModel) TableName() string {
	return gormmodel.GetTableName("r_report_visits_statistics_day")
}
func (m VisitsStatisticsDayModel) TableComment() string {
	return "报表访问量统计指标表"
}

func (m VisitsStatisticsDayModel) GetDatabaseName() string {
	return db.DatabaseNameDefault
}

func (m VisitsStatisticsDayModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m VisitsStatisticsDayModel) Save(records []customized.VisitsStatisticsDayModel) error {
	var saveRecords []*DuplicateUpdateDateGormModel
	utils.JsonConvertOrPanic(records, &saveRecords)
	sql, values, err := utils.ParseDuplicateUpdateSqlAndValues(saveRecords)
	if err != nil {
		return err
	}
	err = m.getQuery().Exec(sql, values...).Error
	if err != nil {
		return err
	}
	return nil
}

// PageQuery 分页查询访问统计数据，支持商户ID和时间范围过滤，按新旧报表访问总数降序排列
func (m VisitsStatisticsDayModel) PageQuery(params customized.CommonSearch) (list []customized.VisitsStatisticsDayModel, total int, err error) {
	var count int64
	query := m.getQuery().Where("date BETWEEN ? AND ?", params.StartTime.ToDateString(), params.EndTime.ToDateString())
	// 添加商户ID过滤条件
	if params.MerchantId != nil && *params.MerchantId > 0 {
		query = query.Where("merchant_id = ?", *params.MerchantId)
	}
	//按商户ID分组
	groupFields := []string{"merchant_id"}
	// 添加求和字段
	selectFields := append(groupFields, sumFields...)
	// 添加求和字段 和 分组字段
	query = query.Group(strings.Join(groupFields, ",")).Select(selectFields)
	// 计算总数
	err = query.Count(&count).Error
	if err != nil {
		return
	}
	if count == 0 {
		return
	}
	// 分页处理
	pageNum := 1
	if params.PageNum != nil && *params.PageNum > 0 {
		pageNum = *params.PageNum
	}
	pageSize := 10
	if params.PageSize != nil && *params.PageSize > 0 {
		pageSize = *params.PageSize
	}
	orderByFiled := ConvertOrderByFiled(params.OrderBy)
	// 分页查询，按新旧报表访问总数降序排列
	err = query.Order(fmt.Sprintf("%s DESC", orderByFiled)).Offset((pageNum - 1) * pageSize).Limit(pageSize).Find(&list).Error
	if err != nil {
		return
	}
	total = int(count)
	return
}
