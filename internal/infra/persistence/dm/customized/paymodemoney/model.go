package paymodemoney

// Model 支付方式金额指标表日表
type Model struct {
	MerchantID int `gorm:"column:merchant_id;type:BIGINT" comment:"商户ID"`
	DataSource int `gorm:"column:data_source;type:INT" comment:"数据来源"`
	TimeType   int `gorm:"column:time_type;type:INT" comment:"时间类型"`
	ProjectID  int `gorm:"column:project_id;type:BIGINT" comment:"项目ID"`
	//指标数据
	OperatingRevenue            int `gorm:"column:operating_revenue;type:INT" comment:"营收金额 单位：元"`
	DiscountAmount              int `gorm:"column:discount_amount;type:INT" comment:"折扣金额 单位：元"`
	AccountsReceivablePayment   int `gorm:"column:accounts_receivable_payment;type:INT" comment:"挂帐金额 单位：元"`
	EntertainmentExpensePayment int `gorm:"column:entertainment_expense_payment;type:INT" comment:"招待金额 单位：元"`
	CashPayment                 int `gorm:"column:cash_payment;type:INT" comment:"现金支付金额 单位：元"`
	UnionPayPayment             int `gorm:"column:union_pay_payment;type:INT" comment:"银联支付金额 单位：元"`
	StoredValueCardPayment      int `gorm:"column:stored_value_card_payment;type:INT" comment:"储蓄卡支付金额 单位：元"`
	YinbaoPayPayment            int `gorm:"column:yinbao_pay_payment;type:INT" comment:"银豹付支付金额 单位：元"`
	RuralCommercialBankPayment  int `gorm:"column:rural_commercial_bank_payment;type:INT" comment:"农商行收银宝金额 单位：元"`
	CreditPayment               int `gorm:"column:credit_payment;type:INT" comment:"授信支付金额 单位：元"`
	YibaoPayment                int `gorm:"column:yibao_payment;type:INT" comment:"易宝金额 单位：元"`
	AlipayPayment               int `gorm:"column:alipay_payment;type:INT" comment:"支付宝金额 单位：元"`
	WechatPayment               int `gorm:"column:wechat_payment;type:INT" comment:"微信金额 单位：元"`
	PrepaidCardPayment          int `gorm:"column:prepaid_card_payment;type:INT" comment:"预付卡金额 单位：元"`
	MeituanCouponPayment        int `gorm:"column:meituan_coupon_payment;type:INT" comment:"美团优惠券金额 单位：元"`
	OtherPayment                int `gorm:"column:other_payment;type:INT" comment:"其他金额 单位：元"`
	TotalIncome                 int `gorm:"column:total_income;type:INT" comment:"收入合计金额（不含营收、折扣） 单位：元"`
}
