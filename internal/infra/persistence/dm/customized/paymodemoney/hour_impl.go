package paymodemoney

import (
	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	repoDm "report-service/internal/domain/repository/dm"
	"report-service/internal/domain/repository/dm/customized"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/internal/infra/persistence/dm"
	"report-service/internal/infra/persistence/dwm/customized/paymodemoney"
	"report-service/pkg/szerrors"
)

type HourModelImpl struct {
	DateHour string `json:"date_hour" gorm:"column:date_hour;comment:日期小时"`
	Model
}

func (m HourModelImpl) TableName() string {
	return gormmodel.GetTableName("r_report_dm_pay_mode_money_hour")
}

func (m HourModelImpl) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

func (m HourModelImpl) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m HourModelImpl) InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int, projectId int) error {
	selectSql := paymodemoney.Model{}.GetStatSql(startTime, endTime, merchantId, projectId, []string{
		"date_format(operated_at, '%Y-%m-%d %H:00:00')",
	}, []string{
		"date_format(operated_at, '%Y-%m-%d %H:00:00')",
	}, sumFields)
	return m.getQuery().Exec("INSERT INTO " + m.TableName() + " " + selectSql).Error
}

func (m HourModelImpl) DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int, projectId int) error {
	return m.getQuery().Exec(m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("date_hour BETWEEN ? AND ?", startTime.ToDateTimeString(), endTime.ToDateTimeString())
		if merchantId > 0 {
			tx = tx.Where("merchant_id = ?", merchantId)
		}
		if projectId > 0 {
			tx = tx.Where("project_id = ?", projectId)
		}
		return tx.Delete(&HourModelImpl{})
	})).Error
}

func (m HourModelImpl) AggregateData(commonSearch repoDm.CommonSearch) (list []customized.PayModeMoney, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		filterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return filterModel.GetSummaryQuery("project_id ASC").Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}
