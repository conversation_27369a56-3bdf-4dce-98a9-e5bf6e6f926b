package oldreport

import (
	"gitee.com/golang-module/carbon/v2"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"report-service/internal/global"
	"report-service/internal/infra/db"
)

type OrderTwoHour struct {
	Id                   int            `json:"id" gorm:"column:id;primary_key;auto_increment;comment:id"`
	DateHour             int            `json:"date_hour" gorm:"column:date_hour;not null;comment:日期 2017101000"`
	Fid                  int            `json:"fid" gorm:"column:fid;not null;comment:用户ID"`
	ResellerId           int            `json:"reseller_id" gorm:"column:reseller_id;not null;comment:分销商ID"`
	Lid                  int            `json:"lid" gorm:"column:lid;not null;comment:景区ID"`
	Tid                  int            `json:"tid" gorm:"column:tid;not null;comment:门票ID"`
	Pid                  int            `json:"pid" gorm:"column:pid;not null;comment:产品ID"`
	Lvl                  int            `json:"lvl" gorm:"column:lvl;not null;comment:分销链层级"`
	OperateId            int            `json:"operate_id" gorm:"column:operate_id;not null;comment:操作人ID"`
	OrderNum             int            `json:"order_num" gorm:"column:order_num;not null;comment:预订订单数"`
	OrderTicket          int            `json:"order_ticket" gorm:"column:order_ticket;not null;comment:预订门票数"`
	CancelNum            int            `json:"cancel_num" gorm:"column:cancel_num;not null;comment:取消订单数"`
	CancelTicket         int            `json:"cancel_ticket" gorm:"column:cancel_ticket;not null;comment:取消门票数"`
	RevokeNum            int            `json:"revoke_num" gorm:"column:revoke_num;not null;comment:撤销订单数"`
	RevokeTicket         int            `json:"revoke_ticket" gorm:"column:revoke_ticket;not null;comment:撤销门票数"`
	CostMoney            int            `json:"cost_money" gorm:"column:cost_money;not null;comment:预订花费金额"`
	SaleMoney            int            `json:"sale_money" gorm:"column:sale_money;not null;comment:预定金额"`
	CancelCostMoney      int            `json:"cancel_cost_money" gorm:"column:cancel_cost_money;not null;comment:取消花费金额"`
	CancelSaleMoney      int            `json:"cancel_sale_money" gorm:"column:cancel_sale_money;not null;comment:取消金额"`
	RevokeCostMoney      int            `json:"revoke_cost_money" gorm:"column:revoke_cost_money;not null;comment:撤销花费金额"`
	RevokeSaleMoney      int            `json:"revoke_sale_money" gorm:"column:revoke_sale_money;not null;comment:撤销金额"`
	ServiceMoney         int            `json:"service_money" gorm:"column:service_money;not null;comment:退款服务费"`
	OrdersInfo           datatypes.JSON `json:"orders_info" gorm:"column:orders_info;null;comment:订单信息"`
	CancelOrdersInfo     datatypes.JSON `json:"cancel_orders_info" gorm:"column:cancel_orders_info;null;comment:取消订单信息"`
	RevokeOrdersInfo     datatypes.JSON `json:"revoke_orders_info" gorm:"column:revoke_orders_info;null;comment:撤销订单信息"`
	PayWay               int            `json:"pay_way" gorm:"column:pay_way;not null;comment:支付方式"`
	Channel              int            `json:"channel" gorm:"column:channel;not null;comment:销售渠道"`
	UpdateTime           int            `json:"update_time" gorm:"column:update_time;not null;comment:更新时间"`
	SiteId               int            `json:"site_id" gorm:"column:site_id;not null;comment:站点ID"`
	PrintNum             int            `json:"print_num" gorm:"column:print_num;default:0;not null;comment:取票数量"`
	PrintOrdersInfo      datatypes.JSON `json:"print_orders_info" gorm:"column:print_orders_info;null;comment:取票订单信息"`
	MainTid              int            `json:"main_tid" gorm:"column:main_tid;default:0;not null;comment:主订单tid"`
	SubMerchantId        int            `json:"sub_merchant_id" gorm:"column:sub_merchant_id;default:0;not null;comment:子商户id"`
	MainType             string         `json:"main_type" gorm:"column:main_type;default:'';not null;comment:主订单产品类型"`
	AfterSaleTicketNum   int            `json:"after_sale_ticket_num" gorm:"column:after_sale_ticket_num;default:0;not null;comment:售后数量"`
	AfterSaleRefundMoney int            `json:"after_sale_refund_money" gorm:"column:after_sale_refund_money;default:0;not null;comment:售后退回金额"`
	AfterSaleIncomeMoney int            `json:"after_sale_income_money" gorm:"column:after_sale_income_money;default:0;not null;comment:售后收入金额"`
	AfterSaleInfo        datatypes.JSON `json:"after_sale_info" gorm:"column:after_sale_info;null;comment:售后订单信息 0.订单号 1.售后数量 2.编号 3.退回单价 4.收入单价"`
}

func (t OrderTwoHour) GetDatabaseName() string {
	return db.DatabaseNameSummary
}

func (t OrderTwoHour) TableName() string {
	return "pft_report_order_two_hour"
}

func (t OrderTwoHour) getQuery() *gorm.DB {
	return global.Databases[t.GetDatabaseName()].Table(t.TableName())
}

type OrderTwoHourGroupTid struct {
	Tid int `json:"tid"` // 门票ID
}

func (t OrderTwoHour) GetStatisticTicketIds(startTime, endTime carbon.Carbon, fids []int) ([]int, error) {
	query := t.getQuery()
	if startTime.Format("YmdH") == endTime.Format("YmdH") {
		query = query.Where("date_hour=?", endTime.Format("YmdH"))
	} else {
		query = query.Where("date_hour >=? AND date_hour <=?", startTime.Format("YmdH"), endTime.Format("YmdH"))
	}
	if len(fids) > 0 {
		query = query.Where("fid in ?", fids)
	}
	query = query.Select("tid").Group("tid")
	var result []OrderTwoHourGroupTid
	err := query.Find(&result).Error
	if err != nil {
		return nil, err
	}
	data := make([]int, len(result))
	for i, v := range result {
		data[i] = v.Tid
	}
	return data, nil
}
