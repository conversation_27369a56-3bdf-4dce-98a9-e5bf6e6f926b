package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	repoDm "report-service/internal/domain/repository/dm"
	repoDmTourist "report-service/internal/domain/repository/dm/tourist"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/internal/infra/persistence/dm"
	dwmTourist "report-service/internal/infra/persistence/dwm/tourist"
	"report-service/pkg/szerrors"
)

type DayModelImpl struct {
	Year  int    `json:"year" gorm:"column:year;comment:年"`
	Month int    `json:"month" gorm:"column:month;comment:月"`
	Date  string `json:"date" gorm:"column:date;comment:日期"`
	Model
}

func (m DayModelImpl) TableName() string {
	return gormmodel.GetTableName("r_report_dm_tourist_day")
}

func (m DayModelImpl) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

func (m DayModelImpl) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m DayModelImpl) InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int) error {
	selectSql := dwmTourist.Model{}.GetStatSql(startTime, endTime, merchantId, []string{
		"substr(date_format(operated_at, '%Y-%m-%d'), 1, 4)",
		"substr(date_format(operated_at, '%Y-%m-%d'), 6, 2)",
		"date_format(operated_at, '%Y-%m-%d')",
	}, []string{
		"date_format(operated_at, '%Y-%m-%d')",
	}, sumFields)
	return m.getQuery().Exec("INSERT INTO " + m.TableName() + " " + selectSql).Error
}

func (m DayModelImpl) DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error {
	return m.getQuery().Exec(m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("date BETWEEN ? AND ?", startTime.ToDateString(), endTime.ToDateString())
		if merchantId > 0 {
			tx = tx.Where("merchant_id = ?", merchantId)
		}
		return tx.Delete(&DayModelImpl{})
	})).Error
}

func (m DayModelImpl) Paginate(
	merchantId int,
	startTime, endTime carbon.Carbon,
	dimensions []string,
	dimensionScheme map[string]int,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule repoDm.CommonSearchModelSpecialProductRule,
	commonSearchModelFilter repoDm.CommonSearchModelFilter,
	page, pageSize int,
) (list []repoDmTourist.PaginationItem, total int, err error) {
	var count int64
	totalSql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, dimensions,
			dimensionScheme, nil, dimensionRange, &commonSearchModelCommonRule, nil,
			nil, sumFields, &commonSearchModelFilter).GetTotalQuery().Find(&count)
	})
	err = m.getQuery().Raw(totalSql).Scan(&count).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if count == 0 {
		return
	}
	total = int(count)

	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, dimensions,
			dimensionScheme, nil, dimensionRange, &commonSearchModelCommonRule, nil,
			nil, sumFields, &commonSearchModelFilter).GetPaginateQuery(page, pageSize, "count DESC", "ASC").Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}

func (m DayModelImpl) Statistics(
	merchantId int,
	startTime, endTime carbon.Carbon,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule repoDm.CommonSearchModelSpecialProductRule,
	commonSearchModelFilter repoDm.CommonSearchModelFilter,
) (statistic repoDmTourist.CommonModelIndicator, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, nil,
			nil, nil, dimensionRange, &commonSearchModelCommonRule,
			nil, nil, sumFields, &commonSearchModelFilter).Statistics().Find(&statistic)
	})
	err = m.getQuery().Raw(sql).Scan(&statistic).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}

	return
}

func (m DayModelImpl) Summary(
	merchantId int,
	startTime, endTime carbon.Carbon,
	dimensions []string,
	dimensionScheme map[string]int,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule repoDm.CommonSearchModelSpecialProductRule,
	commonSearchModelFilter repoDm.CommonSearchModelFilter,
) (list []repoDmTourist.PaginationItem, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, dimensions,
			dimensionScheme, nil, dimensionRange, &commonSearchModelCommonRule, nil,
			nil, sumFields, &commonSearchModelFilter).GetSummaryQuery("count DESC", "ASC").Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}
