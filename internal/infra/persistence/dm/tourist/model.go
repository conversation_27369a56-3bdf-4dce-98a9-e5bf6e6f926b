package tourist

type Model struct {
	MerchantId         int    `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	Region             string `json:"region" gorm:"column:region;comment:地域顶级0未知境内外"`
	Country            string `json:"country" gorm:"column:country;comment:地域一级0未知国家"`
	Province           string `json:"province" gorm:"column:province;comment:地域二级0未知省"`
	City               string `json:"city" gorm:"column:city;comment:地域三级0未知市"`
	District           string `json:"district" gorm:"column:district;comment:地域四级0未知区"`
	Age                int    `json:"age" gorm:"column:age;comment:年龄"`
	Gender             int    `json:"gender" gorm:"column:gender;comment:性别"`
	PoiId              int    `json:"poi_id" gorm:"column:poi_id;comment:PoiID"`
	SpuId              int    `json:"spu_id" gorm:"column:spu_id;comment:SpuID"`
	SkuId              int    `json:"sku_id" gorm:"column:sku_id;comment:SkuID"`
	OperateType        int    `json:"operate_type" gorm:"column:operate_type;comment:操作类型：1支付&加票 2核销&完结 3取消 4撤销 5售后"`
	PackType           int    `json:"pack_type" gorm:"column:pack_type;comment:套票规则，0默认 1是主票 2是子票"`
	ShowBindType       int    `json:"show_bind_type" gorm:"column:show_bind_type;comment:捆绑票规则，0默认 1是主票 2是子票"`
	AnnualCardType     int    `json:"annual_card_type" gorm:"column:annual_card_type;comment:年卡规则，0默认 1是年卡 2是特权"`
	ExchangeCouponType int    `json:"exchange_coupon_type" gorm:"column:exchange_coupon_type;comment:预售券规则，0默认 1是预售券 2是权益兑换订单"`
	// 指标
	Count int `json:"count" gorm:"column:count;comment:游客人数"`
}
