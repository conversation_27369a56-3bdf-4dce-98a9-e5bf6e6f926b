package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	repoDm "report-service/internal/domain/repository/dm"
	repoDmTourist "report-service/internal/domain/repository/dm/tourist"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/internal/infra/persistence/dm"
	dwmTourist "report-service/internal/infra/persistence/dwm/tourist"
	"report-service/pkg/szerrors"
)

type HourModelImpl struct {
	DateHour string `json:"date_hour" gorm:"column:date_hour;comment:日期小时"`
	Model
}

func (m HourModelImpl) TableName() string {
	return gormmodel.GetTableName("r_report_dm_tourist_hour")
}

func (m HourModelImpl) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

func (m HourModelImpl) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m HourModelImpl) InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int) error {
	selectSql := dwmTourist.Model{}.GetStatSql(startTime, endTime, merchantId, []string{
		"date_format(operated_at, '%Y-%m-%d %H:00:00')",
	}, []string{
		"date_format(operated_at, '%Y-%m-%d %H:00:00')",
	}, sumFields)
	return m.getQuery().Exec("INSERT INTO " + m.TableName() + " " + selectSql).Error
}

func (m HourModelImpl) DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error {
	return m.getQuery().Exec(m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("date_hour BETWEEN ? AND ?", startTime.ToDateTimeString(), endTime.ToDateTimeString())
		if merchantId > 0 {
			tx = tx.Where("merchant_id = ?", merchantId)
		}
		return tx.Delete(&HourModelImpl{})
	})).Error
}

func (m HourModelImpl) Paginate(
	merchantId int,
	startTime, endTime carbon.Carbon,
	dimensions []string,
	dimensionScheme map[string]int,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule repoDm.CommonSearchModelSpecialProductRule,
	commonSearchModelFilter repoDm.CommonSearchModelFilter,
	page, pageSize int,
) (list []repoDmTourist.PaginationItem, total int, err error) {
	var count int64
	totalSql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, dimensions,
			dimensionScheme, nil, dimensionRange, &commonSearchModelCommonRule, nil,
			nil, sumFields, &commonSearchModelFilter).GetTotalQuery().Find(&count)
	})
	err = m.getQuery().Raw(totalSql).Scan(&count).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if count == 0 {
		return
	}
	total = int(count)

	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, dimensions,
			dimensionScheme, nil, dimensionRange, &commonSearchModelCommonRule, nil,
			nil, sumFields, &commonSearchModelFilter).GetPaginateQuery(page, pageSize, "count DESC", "ASC").Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}

func (m HourModelImpl) Statistics(
	merchantId int,
	startTime, endTime carbon.Carbon,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule repoDm.CommonSearchModelSpecialProductRule,
	commonSearchModelFilter repoDm.CommonSearchModelFilter,
) (statistic repoDmTourist.CommonModelIndicator, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, nil,
			nil, nil, dimensionRange, &commonSearchModelCommonRule,
			nil, nil, sumFields, &commonSearchModelFilter).Statistics().Find(&statistic)
	})
	err = m.getQuery().Raw(sql).Scan(&statistic).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}

	return
}

func (m HourModelImpl) Summary(
	merchantId int,
	startTime, endTime carbon.Carbon,
	dimensions []string,
	dimensionScheme map[string]int,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule repoDm.CommonSearchModelSpecialProductRule,
	commonSearchModelFilter repoDm.CommonSearchModelFilter,
) (list []repoDmTourist.PaginationItem, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		return dm.NewFilterModel(tx, m.TableName(), merchantId, startTime, endTime, nil, dimensions,
			dimensionScheme, nil, dimensionRange, &commonSearchModelCommonRule, nil,
			nil, sumFields, &commonSearchModelFilter).GetSummaryQuery("count DESC", "ASC").Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}
