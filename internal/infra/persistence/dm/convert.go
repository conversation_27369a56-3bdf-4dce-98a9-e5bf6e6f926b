package dm

import "report-service/internal/domain/enum"

func ConvertDimensionField(dimension string) string {
	switch dimension {
	case enum.DimensionSaleChannel:
		return "sale_channel"
	case enum.DimensionDistributor:
		return "distributor_id"
	case enum.DimensionSpu:
		return "spu_id"
	case enum.DimensionSku:
		return "sku_id"
	case enum.DimensionPayMode:
		return "sale_pay_mode"
	case enum.DimensionOperator:
		return "operator_id"
	case enum.DimensionSellOperator:
		return "sell_operator_id"
	case enum.DimensionSellSite:
		return "sell_site_id"
	case enum.DimensionAgeGroup:
		return "age"
	case enum.DimensionGroupAccount:
		return "merchant_id"
	case enum.DimensionGroupMember:
		return "merchant_id"
	}
	return dimension
}
