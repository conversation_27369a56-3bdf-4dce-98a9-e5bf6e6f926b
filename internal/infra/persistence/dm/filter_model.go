package dm

import (
	"fmt"
	"report-service/internal/domain/enum"
	repoDm "report-service/internal/domain/repository/dm"
	"report-service/internal/infra/persistence/dimensionscheme"
	"strings"

	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type FilterModel struct {
	Tx        *gorm.DB
	TableName string
	// 必填参数
	StartTime, EndTime carbon.Carbon
	// 可选参数
	MerchantId              *int
	TimeGroupType           *int
	Dimensions              []string
	DimensionScheme         map[string]int
	DimensionSchemeGroupIds map[string][]int
	DimensionTagGroup       map[string]string
	DimensionTagCodes       map[string][]string
	DimensionRange          map[string][]int
	ModelCommonRule         *repoDm.CommonSearchModelSpecialProductRule
	OperateTypes            []int
	DistributeRange         *repoDm.DistributorRange
	ModelFilter             *repoDm.CommonSearchModelFilter
	//标签关联参数
	DimensionTagMerchantId *int
	// 汇总字段
	sumFields []string
	// 组装 Query 用到的字段
	selectFields []string
	groupFields  []string
}

func ConvertRepoCommonSearchToFilterModel(tx *gorm.DB, tableName string, commonSearch repoDm.CommonSearch, sumFields []string) *FilterModel {
	return &FilterModel{
		Tx:                      tx,
		TableName:               tableName,
		MerchantId:              commonSearch.MerchantId,
		StartTime:               commonSearch.StartTime,
		EndTime:                 commonSearch.EndTime,
		TimeGroupType:           commonSearch.TimeGroupType,
		Dimensions:              commonSearch.Dimensions,
		DimensionScheme:         commonSearch.DimensionScheme,
		DimensionSchemeGroupIds: commonSearch.DimensionSchemeGroupIds,
		DimensionTagGroup:       commonSearch.DimensionTagGroup,
		DimensionTagCodes:       commonSearch.DimensionTagCodes,
		DimensionTagMerchantId:  commonSearch.DimensionTagMerchantId,
		DimensionRange:          commonSearch.DimensionRange,
		ModelCommonRule:         &commonSearch.SpecialProductRule,
		OperateTypes:            commonSearch.OperateTypes,
		DistributeRange:         &commonSearch.DistributorRange,
		sumFields:               sumFields,
		ModelFilter:             &commonSearch.CommonSearchModelFilter,
	}
}

func NewFilterModel(
	tx *gorm.DB,
	tableName string,
	merchantId int,
	startTime, endTime carbon.Carbon,
	timeGroupType *int,
	dimensions []string,
	dimensionScheme map[string]int,
	dimensionSchemeGroupIds map[string][]int,
	dimensionRange map[string][]int,
	commonSearchModelCommonRule *repoDm.CommonSearchModelSpecialProductRule,
	operateTypes []int,
	distributeRange *repoDm.DistributorRange,
	sumFields []string,
	commonSearchModelFilter *repoDm.CommonSearchModelFilter,
) *FilterModel {
	return &FilterModel{
		Tx:                      tx,
		TableName:               tableName,
		MerchantId:              &merchantId,
		StartTime:               startTime,
		EndTime:                 endTime,
		TimeGroupType:           timeGroupType,
		Dimensions:              dimensions,
		DimensionScheme:         dimensionScheme,
		DimensionSchemeGroupIds: dimensionSchemeGroupIds,
		DimensionRange:          dimensionRange,
		ModelCommonRule:         commonSearchModelCommonRule,
		ModelFilter:             commonSearchModelFilter,
		OperateTypes:            operateTypes,
		DistributeRange:         distributeRange,
		sumFields:               sumFields,
		selectFields:            make([]string, 0),
		groupFields:             make([]string, 0),
	}
}

func (f *FilterModel) GetTotalQuery() *gorm.DB {
	f.getCommonQuery()

	group := strings.Join(f.groupFields, ",")
	return f.Tx.Select("COUNT(DISTINCT CONCAT_WS('-', " + group + ")) AS total")
}

func (f *FilterModel) GetPaginateQuery(page, pageSize int, params ...interface{}) *gorm.DB {
	f.getCommonQuery()
	orderBy := ""
	if len(params) > 0 {
		orderBy = cast.ToString(params[0])
	}
	groupOrder := ""
	if len(params) > 1 {
		groupOrder = cast.ToString(params[1])
	}
	group := strings.Join(f.groupFields, ",")
	offset := (page - 1) * pageSize
	//支持下自定义排序
	order := ""
	if orderBy != "" {
		order = orderBy
		if groupOrder != "" {
			order = orderBy + ", " + strings.Join(f.groupFields, fmt.Sprintf(" %s,", groupOrder)) + fmt.Sprintf(" %s", groupOrder)
		}
	} else {
		order = strings.Join(f.groupFields, " DESC,") + " DESC"
	}

	return f.Tx.Select(f.selectFields).Group(group).Order(order).Offset(offset).Limit(pageSize)
}

func (f *FilterModel) Statistics() *gorm.DB {
	f.getCommonQuery()

	return f.Tx.Select(f.sumFields).Find(&[]repoDm.CommonStatistic{})
}

func (f *FilterModel) GetSummaryQuery(params ...interface{}) *gorm.DB {
	f.getCommonQuery()
	orderBy := ""
	if len(params) > 0 {
		orderBy = cast.ToString(params[0])
	}
	groupOrder := ""
	if len(params) > 1 {
		groupOrder = cast.ToString(params[1])
	}
	group := strings.Join(f.groupFields, ",")
	//支持下自定义排序
	order := ""
	if orderBy != "" {
		order = orderBy
		if groupOrder != "" {
			order = orderBy + ", " + strings.Join(f.groupFields, fmt.Sprintf(" %s,", groupOrder)) + fmt.Sprintf(" %s", groupOrder)
		}
	} else {
		order = strings.Join(f.groupFields, " DESC,") + " DESC"
	}

	return f.Tx.Select(f.selectFields).Group(group).Order(order)
}

func (f *FilterModel) getCommonQuery() *gorm.DB {
	if f.MerchantId != nil {
		f.Tx = f.Tx.Where(f.TableName+".merchant_id = ?", f.MerchantId)
	}
	if f.isHourReport() {
		f.Tx = f.Tx.Where("date_hour BETWEEN ? AND ?", f.StartTime.ToDateTimeString(), f.EndTime.ToDateTimeString())
	} else {
		f.Tx = f.Tx.Where("date BETWEEN ? AND ?", f.StartTime.ToDateString(), f.EndTime.ToDateString())
	}

	// 转换维度字段和维度方案
	f.relateDimensionSchemaAndFilterDimension()

	// 汇总字段
	f.selectFields = append(f.selectFields, f.sumFields...)

	// 维度范围
	f.filterDimensionRange()

	// 特殊产品规则
	f.whereSpecialProductRule()

	// 排除未发生操作的记录
	f.excludeNoOperationRecord()

	// 过滤分销商范围
	f.filterDistributorRange()

	// 通用过滤条件
	f.whereCommonFilter()

	return f.Tx
}

func (f *FilterModel) isHourReport() bool {
	return strings.Contains(f.TableName, "hour")
}

// relateDimensionSchemaAndFilterDimension 关联维度方案并过滤维度字段
func (f *FilterModel) relateDimensionSchemaAndFilterDimension() {
	// 1. 维度字段 & 维度方案 都存在的维度，按维度方案分组
	// 2. 维度字段存在，维度方案不存在的维度，按维度字段分组
	// 3. 维度方案存在，维度字段不存在的维度，不分组
	if len(f.Dimensions) == 0 {
		return
	}
	for _, dimension := range f.Dimensions {
		// 存在维度标签，进行关联查询
		scene := dimension
		// 存在标签中心维度先转换下，非标签中心维度，直接使用即可（同维度key）
		if _, ok := enum.DimensionToTagCenterSceneMap[dimension]; ok {
			scene = enum.DimensionToTagCenterSceneMap[dimension]
		}
		// 存在标签组，及标签归属商户id，进行关联查询
		if tagGroup, ok := f.DimensionTagGroup[scene]; ok && f.DimensionTagMerchantId != nil {
			tagCodes := make([]string, 0)
			if f.DimensionTagCodes != nil {
				tagCodes = f.DimensionTagCodes[scene]
			}
			f.relationTagGroup(*f.DimensionTagMerchantId, dimension, tagGroup, tagCodes)
			continue
		}
		// 存在维度方案，进行关联查询
		if schemeId, ok := f.DimensionScheme[dimension]; ok {
			groupIds := make([]int, 0)
			if f.DimensionSchemeGroupIds != nil {
				groupIds = f.DimensionSchemeGroupIds[dimension]
			}
			f.relationGroup(dimension, schemeId, groupIds)
			continue
		}
		// 时间分组特殊处理
		if dimension == enum.DimensionDate {
			f.addDateField()
			continue
		}
		dimensionField := ConvertDimensionField(dimension)
		if dimensionField == enum.DimensionMerchantId {
			dimensionField = f.TableName + "." + dimensionField
		}
		f.selectFields = append(f.selectFields, dimensionField)
		f.groupFields = append(f.groupFields, dimensionField)
	}
}

func (f *FilterModel) addDateField() {
	isHourReport := strings.Contains(f.TableName, "hour")
	if isHourReport {
		if f.TimeGroupType == nil {
			f.selectFields = append(f.selectFields, "date_hour as date")
			f.groupFields = append(f.groupFields, "date_hour")
			return
		}
		switch *f.TimeGroupType {
		case enum.ReportTimeGroupTypeHour:
			f.selectFields = append(f.selectFields, "date_hour as date")
			f.groupFields = append(f.groupFields, "date_hour")
		case enum.ReportTimeGroupTypeDay:
			f.selectFields = append(f.selectFields, "date_format(date_hour, '%Y-%m-%d') as date")
			f.groupFields = append(f.groupFields, "date_format(date_hour, '%Y-%m-%d')")
		}
		return
	}
	if f.TimeGroupType == nil {
		f.selectFields = append(f.selectFields, "date")
		f.groupFields = append(f.groupFields, "date")
		return
	}
	switch *f.TimeGroupType {
	case enum.ReportTimeGroupTypeDay:
		f.selectFields = append(f.selectFields, "date")
		f.groupFields = append(f.groupFields, "date")
	case enum.ReportTimeGroupTypeMonth:
		f.selectFields = append(f.selectFields, "concat(year, '-', month, '-01') as date")
		f.groupFields = append(f.groupFields, "year")
		f.groupFields = append(f.groupFields, "month")
	case enum.ReportTimeGroupTypeYear:
		f.selectFields = append(f.selectFields, "year as date")
		f.groupFields = append(f.groupFields, "year")
	}
}

// whereSpecialProductRule 特殊产品规则
func (f *FilterModel) whereSpecialProductRule() {
	if len(f.ModelCommonRule.PackType) > 0 {
		f.Tx = f.Tx.Where("pack_type in (?)", f.ModelCommonRule.PackType)
	}
	if len(f.ModelCommonRule.ShowBindType) > 0 {
		f.Tx = f.Tx.Where("show_bind_type in (?)", f.ModelCommonRule.ShowBindType)
	}
	if len(f.ModelCommonRule.AnnualCardType) > 0 {
		f.Tx = f.Tx.Where("annual_card_type in (?)", f.ModelCommonRule.AnnualCardType)
	}
	if len(f.ModelCommonRule.ExchangeCouponType) > 0 {
		f.Tx = f.Tx.Where("exchange_coupon_type in (?)", f.ModelCommonRule.ExchangeCouponType)
	}
}

// relationGroup 关联维度查询
func (f *FilterModel) relationGroup(dimension string, schemeId int, groupIds []int) {
	dimensionField := ConvertDimensionField(dimension)
	f.Tx = f.Tx.Joins(fmt.Sprintf(
		"LEFT JOIN %s type_%s ON type_%s.scheme_id = %d and type_%s.dimension_value = %s.%s",
		dimensionscheme.GroupRelationModel{}.TableName(),
		dimension, dimension, schemeId, dimension, f.TableName, dimensionField,
	))
	f.selectFields = append(f.selectFields, fmt.Sprintf("type_%s.group_id as %s_group_id", dimension, dimension))
	f.groupFields = append(f.groupFields, fmt.Sprintf("type_%s.group_id", dimension))

	if len(groupIds) > 0 {
		f.Tx = f.Tx.Where(fmt.Sprintf("type_%s.group_id IN (?)", dimension), groupIds)
	}
}

// relationTagGroup 关联维度标签查询
func (f *FilterModel) relationTagGroup(tagMerchantId int, dimension string, tagGroup string, tagCodes []string) {
	dimensionField := ConvertDimensionField(dimension)
	// 维度映射标签来源 1.标签中心 2.集团 3.年龄段
	if tagSource, ok := enum.DimensionToTagSourceMap[dimension]; ok {
		f.Tx = f.Tx.Joins(fmt.Sprintf(
			"LEFT JOIN %s type_%s ON type_%s.tag_group_code = '%s' and type_%s.subject_id = %s.%s and type_%s.merchant_id = %d and type_%s.source = %d",
			dimensionscheme.TagRelationModel{}.TableName(), dimension, dimension, tagGroup, dimension, f.TableName, dimensionField, dimension, tagMerchantId, dimension, tagSource,
		))
	} else {
		//没有映射标签来源，则是全部来源关联查询
		f.Tx = f.Tx.Joins(fmt.Sprintf(
			"LEFT JOIN %s type_%s ON type_%s.tag_group_code = '%s' and type_%s.subject_id = %s.%s and type_%s.merchant_id = %d",
			dimensionscheme.TagRelationModel{}.TableName(), dimension, dimension, tagGroup, dimension, f.TableName, dimensionField, dimension, tagMerchantId,
		))
	}
	//如果商户条件为空并且关联商户条件不为空，则关联商户条件，避免非关联全部查询
	if f.MerchantId == nil && tagMerchantId != 0 {
		f.Tx = f.Tx.Where(fmt.Sprintf("type_%s.merchant_id = %d", dimension, tagMerchantId))
	}
	f.selectFields = append(f.selectFields, fmt.Sprintf("type_%s.tag_code as %s_tag_code", dimension, dimension))
	f.groupFields = append(f.groupFields, fmt.Sprintf("type_%s.tag_code", dimension))

	if len(tagCodes) > 0 {
		f.Tx = f.Tx.Where(fmt.Sprintf("type_%s.tag_code IN (?)", dimension), tagCodes)
	}
}

// excludeNoOperationRecord 排除未发生操作的记录
func (f *FilterModel) excludeNoOperationRecord() {
	subQuery := make([]string, 0)
	for _, operateType := range f.OperateTypes {
		switch operateType {
		case enum.DWMOperateTypePay:
			subQuery = append(subQuery, "pay_count = 0")
		case enum.DWMOperateTypeVerify:
			subQuery = append(subQuery, "verify_count = 0")
		case enum.DWMOperateTypeCancel:
			subQuery = append(subQuery, "cancel_count = 0")
		case enum.DWMOperateTypeRevoke:
			subQuery = append(subQuery, "revoke_count = 0")
		case enum.DWMOperateTypeAfterSale:
			subQuery = append(subQuery, "after_sale_count = 0")
		}
	}
	f.Tx = f.Tx.Not(strings.Join(subQuery, " AND "))
}

// filterDimensionRange 过滤维度范围
func (f *FilterModel) filterDimensionRange() {
	for specialDimensionType, specialDimensionValues := range f.DimensionRange {
		if len(specialDimensionValues) > 0 {
			dimensionField := ConvertDimensionField(specialDimensionType)
			if dimensionField == enum.DimensionMerchantId {
				dimensionField = f.TableName + "." + dimensionField
			}
			f.Tx = f.Tx.Where(fmt.Sprintf("%s IN (?)", dimensionField), specialDimensionValues)
		}
	}
}

func (f *FilterModel) filterDistributorRange() {
	if f.DistributeRange == nil {
		return
	}
	if len(f.DistributeRange.IncludeIds) > 0 {
		f.Tx = f.Tx.Where("distributor_id IN (?)", f.DistributeRange.IncludeIds)
	}
	if len(f.DistributeRange.ExcludeIds) > 0 {
		f.Tx = f.Tx.Where("distributor_id NOT IN (?)", f.DistributeRange.ExcludeIds)
	}
}

func (f *FilterModel) whereCommonFilter() {
	if f.ModelFilter == nil {
		return
	}
	if len(f.ModelFilter.OperateType) > 0 {
		f.Tx = f.Tx.Where("operate_type in (?)", f.ModelFilter.OperateType)
	}
	if len(f.ModelFilter.NotSpuIds) > 0 {
		f.Tx = f.Tx.Where("spu_id not in (?)", f.ModelFilter.NotSpuIds)
	}
	if len(f.ModelFilter.RegionIds) > 0 {
		f.Tx = f.Tx.Where("region in (?)", f.ModelFilter.RegionIds)
	}
	if len(f.ModelFilter.CountryIds) > 0 {
		f.Tx = f.Tx.Where("country in (?)", f.ModelFilter.CountryIds)
	}
	if len(f.ModelFilter.ProvinceIds) > 0 {
		f.Tx = f.Tx.Where("province in (?)", f.ModelFilter.ProvinceIds)
	}
	if len(f.ModelFilter.CityIds) > 0 {
		f.Tx = f.Tx.Where("city in (?)", f.ModelFilter.CityIds)
	}
	if len(f.ModelFilter.DistrictIds) > 0 {
		f.Tx = f.Tx.Where("district in (?)", f.ModelFilter.DistrictIds)
	}
	if len(f.ModelFilter.TargetAudience) > 0 {
		f.Tx = f.Tx.Where("target_audience in (?)", f.ModelFilter.TargetAudience)
	}
}
