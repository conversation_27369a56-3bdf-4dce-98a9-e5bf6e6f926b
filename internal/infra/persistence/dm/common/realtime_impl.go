package common

import (
	repoDm "report-service/internal/domain/repository/dm"
	repoDmCommon "report-service/internal/domain/repository/dm/common"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/internal/infra/persistence/dm"
	dwmCommon "report-service/internal/infra/persistence/dwm/common"
	"report-service/pkg/szerrors"

	"gorm.io/gorm"
)

type RealtimeModel struct {
	DateHour string `json:"date_hour" gorm:"column:date_hour;comment:日期小时"`
	dwmCommon.Model
}

func (m RealtimeModel) TableName() string {
	return gormmodel.GetTableName("r_report_dwm_common")
}

func (m RealtimeModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m RealtimeModel) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

func (m RealtimeModel) Paginate(commonSearch repoDm.CommonSearch, page, pageSize int) (list []repoDmCommon.PaginationItem, total int, err error) {
	var count int64
	totalSql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		totalFilterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return totalFilterModel.GetTotalQuery().Find(&count)
	})
	err = m.getQuery().Raw(totalSql).Scan(&count).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if count == 0 {
		return
	}
	total = int(count)

	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		filterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return filterModel.GetPaginateQuery(page, pageSize).Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}

	return
}

func (m RealtimeModel) Statistics(commonSearch repoDm.CommonSearch) (statistic repoDm.CommonStatistic, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		filterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return filterModel.Statistics().Find(&statistic)
	})
	err = m.getQuery().Raw(sql).Scan(&statistic).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}
