package common

type Model struct {
	MerchantId              int `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	ParentMerchantId        int `json:"parent_merchant_id" gorm:"column:parent_merchant_id;comment:上级商户ID"`
	DistributorId           int `json:"distributor_id" gorm:"column:distributor_id;comment:下级商户ID"`
	PoiId                   int `json:"poi_id" gorm:"column:poi_id;comment:PoiID"`
	SpuId                   int `json:"spu_id" gorm:"column:spu_id;comment:SpuID"`
	SkuId                   int `json:"sku_id" gorm:"column:sku_id;comment:SkuID"`
	SaleChannel             int `json:"sale_channel" gorm:"column:sale_channel;comment:销售渠道"`
	CostPayMode             int `json:"cost_pay_mode" gorm:"column:cost_pay_mode;comment:采购支付方式"`
	SalePayMode             int `json:"sale_pay_mode" gorm:"column:sale_pay_mode;comment:销售支付方式"`
	SellOperatorId          int `json:"sell_operator_id" gorm:"column:sell_operator_id;comment:售票员ID"`
	OperatorId              int `json:"operator_id" gorm:"column:operator_id;comment:操作人ID"`
	CostUnitPrice           int `json:"cost_unit_price" gorm:"column:cost_unit_price;comment:采购单价"`
	SaleUnitPrice           int `json:"sale_unit_price" gorm:"column:sale_unit_price;comment:销售单价"`
	PackType                int `json:"pack_type" gorm:"column:pack_type;comment:套票规则，0默认 1是主票 2是子票"`
	ShowBindType            int `json:"show_bind_type" gorm:"column:show_bind_type;comment:捆绑票规则，0默认 1是主票 2是子票"`
	AnnualCardType          int `json:"annual_card_type" gorm:"column:annual_card_type;comment:年卡规则，0默认 1是年卡 2是特权"`
	ExchangeCouponType      int `json:"exchange_coupon_type" gorm:"column:exchange_coupon_type;comment:预售券规则，0默认 1是预售券 2是权益兑换订单"`
	PayCount                int `json:"pay_count" gorm:"column:pay_count;comment:预订数量"`
	PayCostPrice            int `json:"pay_cost_price" gorm:"column:pay_cost_price;comment:预订采购金额"`
	PayCostDiscountPrice    int `json:"pay_cost_discount_price" gorm:"column:pay_cost_discount_price;comment:预订采购优惠金额"`
	PaySalePrice            int `json:"pay_sale_price" gorm:"column:pay_sale_price;comment:预订销售金额"`
	PaySaleDiscountPrice    int `json:"pay_sale_discount_price" gorm:"column:pay_sale_discount_price;comment:预订销售优惠金额"`
	VerifyCount             int `json:"verify_count" gorm:"column:verify_count;comment:核销数量"`
	VerifyCostPrice         int `json:"verify_cost_price" gorm:"column:verify_cost_price;comment:核销采购金额"`
	VerifyCostDiscountPrice int `json:"verify_cost_discount_price" gorm:"column:verify_cost_discount_price;comment:核销采购优惠金额"`
	VerifySalePrice         int `json:"verify_sale_price" gorm:"column:verify_sale_price;comment:核销销售金额"`
	VerifySaleDiscountPrice int `json:"verify_sale_discount_price" gorm:"column:verify_sale_discount_price;comment:核销销售优惠金额"`
	CancelCount             int `json:"cancel_count" gorm:"column:cancel_count;comment:取消数量"`
	CancelCostPrice         int `json:"cancel_cost_price" gorm:"column:cancel_cost_price;comment:取消采购金额"`
	CancelCostDiscountPrice int `json:"cancel_cost_discount_price" gorm:"column:cancel_cost_discount_price;comment:取消采购优惠金额"`
	CancelCostFee           int `json:"cancel_cost_fee" gorm:"column:cancel_cost_fee;comment:取消采购手续费"`
	CancelSalePrice         int `json:"cancel_sale_price" gorm:"column:cancel_sale_price;comment:取消销售金额"`
	CancelSaleDiscountPrice int `json:"cancel_sale_discount_price" gorm:"column:cancel_sale_discount_price;comment:取消销售优惠金额"`
	CancelSaleFee           int `json:"cancel_sale_fee" gorm:"column:cancel_sale_fee;comment:取消销售手续费"`
	RevokeCount             int `json:"revoke_count" gorm:"column:revoke_count;comment:撤销数量"`
	RevokeCostPrice         int `json:"revoke_cost_price" gorm:"column:revoke_cost_price;comment:撤销采购金额"`
	RevokeCostDiscountPrice int `json:"revoke_cost_discount_price" gorm:"column:revoke_cost_discount_price;comment:撤销采购优惠金额"`
	RevokeCostFee           int `json:"revoke_cost_fee" gorm:"column:revoke_cost_fee;comment:撤销采购手续费"`
	RevokeSalePrice         int `json:"revoke_sale_price" gorm:"column:revoke_sale_price;comment:撤销销售金额"`
	RevokeSaleDiscountPrice int `json:"revoke_sale_discount_price" gorm:"column:revoke_sale_discount_price;comment:撤销销售优惠金额"`
	RevokeSaleFee           int `json:"revoke_sale_fee" gorm:"column:revoke_sale_fee;comment:撤销销售手续费"`
	AfterSaleCount          int `json:"after_sale_count" gorm:"column:after_sale_count;comment:售后数量"`
	AfterCostPrice          int `json:"after_cost_price" gorm:"column:after_cost_price;comment:售后采购金额"`
	AfterSalePrice          int `json:"after_sale_price" gorm:"column:after_sale_price;comment:售后销售金额"`
	CollectCount            int `json:"collect_count" gorm:"column:collect_count;comment:取票数量"`
	ReprintCount            int `json:"reprint_count" gorm:"column:reprint_count;comment:重打印数量"`
}
