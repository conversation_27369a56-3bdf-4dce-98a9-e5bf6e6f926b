package common

import "strings"

var sumFields = []string{
	"sum(pay_count) as pay_count",
	"sum(pay_cost_price) as pay_cost_price",
	"sum(pay_cost_discount_price) as pay_cost_discount_price",
	"sum(pay_sale_price) as pay_sale_price",
	"sum(pay_sale_discount_price) as pay_sale_discount_price",
	"sum(verify_count) as verify_count",
	"sum(verify_cost_price) as verify_cost_price",
	"sum(verify_cost_discount_price) as verify_cost_discount_price",
	"sum(verify_sale_price) as verify_sale_price",
	"sum(verify_sale_discount_price) as verify_sale_discount_price",
	"sum(cancel_count) as cancel_count",
	"sum(cancel_cost_price) as cancel_cost_price",
	"sum(cancel_cost_discount_price) as cancel_cost_discount_price",
	"sum(cancel_cost_fee) as cancel_cost_fee",
	"sum(cancel_sale_price) as cancel_sale_price",
	"sum(cancel_sale_discount_price) as cancel_sale_discount_price",
	"sum(cancel_sale_fee) as cancel_sale_fee",
	"sum(revoke_count) as revoke_count",
	"sum(revoke_cost_price) as revoke_cost_price",
	"sum(revoke_cost_discount_price) as revoke_cost_discount_price",
	"sum(revoke_cost_fee) as revoke_cost_fee",
	"sum(revoke_sale_price) as revoke_sale_price",
	"sum(revoke_sale_discount_price) as revoke_sale_discount_price",
	"sum(revoke_sale_fee) as revoke_sale_fee",
	"sum(after_sale_count) as after_sale_count",
	"sum(after_sale_price) as after_sale_price",
	"sum(after_cost_price) as after_cost_price",
	"sum(collect_count) as collect_count",
	"sum(reprint_count) as reprint_count",
}

var groupByFields = []string{
	"merchant_id",
	"parent_merchant_id",
	"distributor_id",
	"poi_id",
	"spu_id",
	"sku_id",
	"sale_channel",
	"cost_pay_mode",
	"sale_pay_mode",
	"sell_operator_id",
	"operator_id",
	"sell_site_id",
	"cost_unit_price",
	"sale_unit_price",
	"pack_type",
	"show_bind_type",
	"annual_card_type",
	"exchange_coupon_type",
	"target_audience",
}

// parseSumFields 解析 sumFields 获取字段名
func parseSumFields(sumFields []string) []string {
	var fields []string
	for _, field := range sumFields {
		// 从 "sum(field_name) as field_name" 中提取 field_name
		if strings.Contains(field, " as ") {
			parts := strings.Split(field, " as ")
			if len(parts) == 2 {
				fields = append(fields, strings.TrimSpace(parts[1]))
			}
		}
	}
	return fields
}
