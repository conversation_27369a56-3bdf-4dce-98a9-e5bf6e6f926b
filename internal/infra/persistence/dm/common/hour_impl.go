package common

import (
	"strings"

	repoDm "report-service/internal/domain/repository/dm"
	repoDmCommon "report-service/internal/domain/repository/dm/common"
	"report-service/internal/global"
	"report-service/internal/global/gormmodel"
	"report-service/internal/infra/db"
	"report-service/internal/infra/persistence/dm"
	dwmCommon "report-service/internal/infra/persistence/dwm/common"
	"report-service/pkg/szerrors"

	"gitee.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type HourModel struct {
	DateHour string `json:"date_hour" gorm:"column:date_hour;comment:日期小时"`
	Model
}

func (m HourModel) TableName() string {
	return gormmodel.GetTableName("r_report_dm_common_hour")
}

func (m HourModel) getQuery() *gorm.DB {
	return global.Databases[m.GetDatabaseName()].Table(m.TableName())
}

func (m HourModel) InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int) error {
	selectSql := dwmCommon.Model{}.GetStatSql(startTime, endTime, merchantId, []string{
		"date_format(operated_at, '%Y-%m-%d %H:00:00')",
	}, []string{
		"date_format(operated_at, '%Y-%m-%d %H:00:00')",
	}, sumFields)

	columnsStr := "(" + strings.Join(m.GetInsertColumns(), ",") + ")"
	return m.getQuery().Exec("INSERT INTO " + m.TableName() + " " + columnsStr + " " + selectSql).Error
}

func (m HourModel) GetDatabaseName() string {
	return db.DatabaseNameSelectDB
}

// GetInsertColumns 获取插入语句的列名列表
func (m HourModel) GetInsertColumns() []string {
	// 时间字段
	timeFields := []string{"date_hour"}

	// 从全局变量 sumFields 解析获取聚合字段名
	sumFieldNames := parseSumFields(sumFields)

	// 其他字段
	otherFields := []string{"created_at"}

	// 合并所有字段
	var columns []string
	columns = append(columns, timeFields...)
	columns = append(columns, groupByFields...)
	columns = append(columns, sumFieldNames...)
	columns = append(columns, otherFields...)

	return columns
}

func (m HourModel) DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error {
	return m.getQuery().Exec(m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		tx = tx.Where("date_hour BETWEEN ? AND ?", startTime.ToDateTimeString(), endTime.ToDateTimeString())
		if merchantId > 0 {
			tx = tx.Where("merchant_id = ?", merchantId)
		}
		return tx.Delete(&HourModel{})
	})).Error
}

func (m HourModel) Paginate(commonSearch repoDm.CommonSearch, page, pageSize int) (list []repoDmCommon.PaginationItem, total int, err error) {
	var count int64
	totalSql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		totalFilterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return totalFilterModel.GetTotalQuery().Find(&count)
	})
	err = m.getQuery().Raw(totalSql).Scan(&count).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	if count == 0 {
		return
	}
	total = int(count)

	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		filterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return filterModel.GetPaginateQuery(page, pageSize).Find(&list)
	})
	err = m.getQuery().Raw(sql).Scan(&list).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}

	return
}

func (m HourModel) Statistics(commonSearch repoDm.CommonSearch) (statistic repoDm.CommonStatistic, err error) {
	sql := m.getQuery().ToSQL(func(tx *gorm.DB) *gorm.DB {
		filterModel := dm.ConvertRepoCommonSearchToFilterModel(tx, m.TableName(), commonSearch, sumFields)
		return filterModel.Statistics().Find(&statistic)
	})
	err = m.getQuery().Raw(sql).Scan(&statistic).Error
	if err != nil {
		err = szerrors.NewDataPersistenceError(err)
		return
	}
	return
}
