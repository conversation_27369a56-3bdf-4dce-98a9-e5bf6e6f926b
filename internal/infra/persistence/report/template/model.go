package template

import (
	"gorm.io/datatypes"
	"report-service/internal/global/gormmodel"
)

type GormModel struct {
	gormmodel.BaseModel
	MerchantId int            `gorm:"column:merchant_id;type:bigint;not null;default:0;comment:商户ID" json:"merchant_id"`
	MemberId   int            `gorm:"column:member_id;type:bigint;not null;default:0;comment:用户ID" json:"member_id"`
	Category   int            `gorm:"column:category;type:tinyint;not null;default:0;comment:分类" json:"category"`
	Name       string         `gorm:"column:name;type:varchar;not null;default:'';comment:名称" json:"name"`
	Payload    datatypes.JSON `gorm:"column:payload;type:json;not null;comment:扩展数据" json:"payload"`
	OperatorId int            `gorm:"column:operator_id;type:bigint;not null;default:0;comment:操作人ID;index:idx_operator_id" json:"operator_id"`
}

func (m GormModel) TableName() string {
	return "r_template"
}

func (m GormModel) TableComment() string {
	return "报表模板表"
}
