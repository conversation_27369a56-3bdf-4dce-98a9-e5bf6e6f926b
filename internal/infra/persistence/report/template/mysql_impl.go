package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/repository/report"
	"report-service/internal/global"
	"report-service/internal/infra/db"
	"report-service/pkg/utils"

	"gorm.io/gorm"
)

type MysqlImpl struct{}

func (r MysqlImpl) Create(info report.TemplateCreate) (int, error) {
	var createData GormModel

	utils.JsonConvertOrPanic(info, &createData)
	err := global.Databases[db.DatabaseNameDefault].Model(GormModel{}).Create(&createData).Error
	if err != nil {
		return 0, err
	}
	return createData.Id, nil
}

func (r MysqlImpl) Delete(deleteInfo report.TemplateDelete) error {
	err := global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Where("merchant_id = ? and member_id = ? and id = ?", deleteInfo.MerchantId, deleteInfo.MemberId, deleteInfo.Id).
		Delete(GormModel{}).Error
	if err != nil {
		return err
	}
	return nil
}

func (r MysqlImpl) Update(updateInfo report.TemplateUpdate) error {
	payload, err := json.Marshal(updateInfo.Payload)
	if err != nil {
		return err
	}

	err = global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Where("merchant_id = ? and member_id = ? and id = ?", updateInfo.MerchantId, updateInfo.MemberId, updateInfo.Id).
		Updates(GormModel{
			Name:       updateInfo.Name,
			Payload:    payload,
			OperatorId: updateInfo.OperatorId,
		}).Error
	if err != nil {
		return err
	}
	return nil
}

func (r MysqlImpl) MerchantFirst(findInfo report.TemplateMerchantAndIdFind) (*report.TemplateListItem, error) {
	var info GormModel
	err := global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Where("merchant_id = ? and member_id = ? and id = ?", findInfo.MerchantId, findInfo.MemberId, findInfo.Id).First(&info).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("模板不存在，请重试")
		}
		return nil, err
	}
	var result report.TemplateListItem
	err = utils.JsonConvertor(info, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r MysqlImpl) MainMerchantFirst(findInfo report.TemplateMerchantAndIdFind) (*report.TemplateListItem, error) {
	var info GormModel
	err := global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Where("merchant_id = ? and id = ?", findInfo.MerchantId, findInfo.Id).First(&info).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("模板不存在，请重试")
		}
		return nil, err
	}
	var result report.TemplateListItem
	err = utils.JsonConvertor(info, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r MysqlImpl) MerchantFind(findInfo report.TemplateMerchantFind) ([]report.TemplateListItem, error) {
	var list []GormModel
	err := global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Where("merchant_id = ? and member_id = ? and category = ?", findInfo.MerchantId, findInfo.MemberId, findInfo.Category).Find(&list).Error
	if err != nil {
		return nil, err
	}

	var results []report.TemplateListItem
	err = utils.JsonConvertor(list, &results)
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r MysqlImpl) MerchantFindAll(merchantId int) ([]report.TemplateListItem, error) {
	var list []GormModel
	err := global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Where("merchant_id = ?", merchantId).Find(&list).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	var results []report.TemplateListItem
	err = utils.JsonConvertor(list, &results)
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r MysqlImpl) BatchMerchantUpdate(data map[int]interface{}) error {
	var chunkData = make(map[int]interface{})
	chunkSize := 50
	for id, item := range data {
		//写入
		payload, err := json.Marshal(item)
		if err != nil {
			return err
		}
		chunkData[id] = string(payload)
		if len(chunkData) == chunkSize {
			err := batchUpdate(chunkData, GormModel{}.TableName(), "id, payload", "payload")
			if err != nil {
				return err
			}
			//重置
			chunkData = map[int]interface{}{}
		}
	}
	if len(chunkData) > 0 {
		//写入
		err := batchUpdate(chunkData, GormModel{}.TableName(), "id, payload", "payload")
		if err != nil {
			return err
		}
		//重置
		chunkData = map[int]interface{}{}
	}
	return nil
}

func batchUpdate(data map[int]interface{}, table string, fields string, updateField string) error {
	sqlStr := "INSERT INTO %s (%s) VALUES %s ON DUPLICATE KEY UPDATE %s = VALUES(%s)"
	valStr := ""
	for id, item := range data {
		if valStr != "" {
			valStr = valStr + ","
		}
		valStr = valStr + fmt.Sprintf("(%d, '%s')", id, item)
	}
	sql := fmt.Sprintf(sqlStr, table, fields, valStr, updateField, updateField)
	err := global.Databases[db.DatabaseNameDefault].Exec(sql).Error
	if err != nil {
		return err
	}
	return nil
}

func (r MysqlImpl) GetAllMerchantIds() ([]int, error) {
	var merchantIds []int

	// 直接通过GROUP BY在数据库层获取去重后的商户ID
	err := global.Databases[db.DatabaseNameDefault].Model(&GormModel{}).
		Distinct("merchant_id").
		Pluck("merchant_id", &merchantIds).Error

	if err != nil {
		return nil, err
	}

	return merchantIds, nil
}
