package types

type Databases struct {
	Default          GeneralDB         `mapstructure:"default"`
	SelectDB         GeneralDB         `mapstructure:"select-db"`
	Summary          GeneralDB         `mapstructure:"summary"`
	TableNameMapping map[string]string `mapstructure:"table-name-mapping"`
}

type GeneralDB struct {
	Path         string `mapstructure:"path"`           // 服务器地址:端口
	Port         string `mapstructure:"port"`           // :端口
	DbName       string `mapstructure:"db-name"`        // 数据库名
	Username     string `mapstructure:"username"`       // 数据库用户名
	Password     string `mapstructure:"password"`       // 数据库密码
	Prefix       string `mapstructure:"prefix"`         // 全局表前缀，单独定义TableName则不生效
	Singular     bool   `mapstructure:"singular"`       // 是否开启全局禁用复数，true表示开启
	Engine       string `mapstructure:"engine"`         // 数据库引擎，默认InnoDB
	MaxIdleConns int    `mapstructure:"max-idle-conns"` // 空闲中的最大连接数
	MaxOpenConns int    `mapstructure:"max-open-conns"` // 打开到数据库的最大连接数
	LogMode      string `mapstructure:"log-mode"`       // 是否开启Gorm全局日志
	LogZap       bool   `mapstructure:"log-zap"`        // 是否通过zap写入日志文件
}
