package types

type KafkaConfig struct {
	Producers []KafkaProduceConfig  `mapstructure:"producers"`
	Consumers []KafkaConsumerConfig `mapstructure:"consumers"`
}

type KafkaProduceConfig struct {
	Name    string   `mapstructure:"name"`
	Brokers []string `mapstructure:"brokers"`
}

type KafkaConsumerConfig struct {
	Brokers []string                  `mapstructure:"brokers"`
	Items   []KafkaConsumerItemConfig `mapstructure:"items"`
}

// KafkaConsumerItemConfig 配置参考 pkg/szkafka/consumer.go
type KafkaConsumerItemConfig struct {
	Topic             string   `mapstructure:"topic"`
	GroupTopics       []string `mapstructure:"group-topics"`
	GroupId           string   `mapstructure:"group-id"`
	BatchSize         int      `mapstructure:"batch-size"`
	MessageHandlerKey string   `mapstructure:"message-handler-key"`
	PoolSize          int      `mapstructure:"pool-size"`
	MinBytes          int      `mapstructure:"min-bytes"`
	MaxBytes          int      `mapstructure:"max-bytes"`
	MaxWaitMs         int      `mapstructure:"max-wait-ms"`
	CommitIntervalMs  int      `mapstructure:"commit-interval-ms"`
	StartOffset       int64    `mapstructure:"start-offset"`
}
