package types

type Config struct {
	System        System              `mapstructure:"system"`
	Databases     Databases           `mapstructure:"databases"`
	Redis         Redis               `mapstructure:"redis"`
	Zap           Zap                 `mapstructure:"zap"`
	Timer         Timer               `mapstructure:"timer"`
	ExternalApi   ExternalApi         `mapstructure:"external-api"`
	Kafka         KafkaConfig         `mapstructure:"kafka"`
	OpenTelemetry OpenTelemetryConfig `mapstructure:"open-telemetry"`
}
