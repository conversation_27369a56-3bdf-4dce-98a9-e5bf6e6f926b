package types

import (
	"report-service/pkg/sdk/infrastructure"
)

type ExternalApi struct {
	JavaApi              JavaApi               `mapstructure:"java-api"`
	Infrastructure       infrastructure.CONFIG `mapstructure:"infrastructure"`
	DownloadCenter       DownloadCenter        `mapstructure:"download-center"`
	PlatformApi          PlatformApi           `mapstructure:"platform-api"`
	OdasGRpc             OdasGRpc              `mapstructure:"odas-grpc"`
	OpenCooperateEcology OpenCooperateEcology  `mapstructure:"open-cooperate-ecology"`
	DownloadCenterApi    DownloadCenterApi     `mapstructure:"download-center-api"`
}

type JavaApi struct {
	BaseUri    string `mapstructure:"base-uri"`
	GateWayUri string `mapstructure:"gateway-uri"`
}

type DownloadCenter struct {
	BaseUri string `mapstructure:"base-uri"`
	System  string `mapstructure:"system"`
	AuthKey string `mapstructure:"auth-key"`
}

type DownloadCenterApi struct {
	BaseUri string `mapstructure:"base-uri"`
}

type PlatformApi struct {
	RpcApi PlatformApiRpcApi `mapstructure:"rpc-api"`
}

type PlatformApiRpcApi struct {
	Uri     string `mapstructure:"uri"`
	System  string `mapstructure:"system"`
	AuthKey string `mapstructure:"auth-key"`
}

type OdasGRpc struct {
	Addr      string `mapstructure:"addr"`
	AppId     string `mapstructure:"app-id"`
	AppSecret string `mapstructure:"app-secret"`
	Nonce     string `mapstructure:"nonce"`
}

type OpenCooperateEcology struct {
	Addr string `mapstructure:"addr"`
}
