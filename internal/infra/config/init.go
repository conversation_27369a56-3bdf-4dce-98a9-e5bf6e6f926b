package config

import (
	"flag"
	"fmt"
	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"report-service/internal/global"
)

var (
	DefaultFile = "config.yaml"
)

// Init 优先级: 命令行 > 环境变量 > 默认值
func Init() *viper.Viper {
	var config string

	flag.StringVar(&config, "c", "", "choose config file.")
	flag.Parse()
	// 判断命令行参数是否为空，为空则使用默认值
	if config == "" {
		config = DefaultFile
	}

	v := viper.New()
	v.SetConfigFile(config)
	v.SetConfigType("yaml")
	err := v.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("fatal error config file: %s", err))
	}
	v.WatchConfig()

	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("config file changed:", e.Name)
		if err = v.Unmarshal(&global.CONFIG); err != nil {
			panic(fmt.Errorf("Fatal error config file: %s \n", err))
		}
	})
	if err = v.Unmarshal(&global.CONFIG); err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}

	return v
}
