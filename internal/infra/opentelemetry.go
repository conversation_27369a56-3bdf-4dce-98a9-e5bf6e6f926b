package infra

import (
	"context"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/utils"
	"time"

	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp"
	otelmetric "go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
)

const (
	OpenTelemetryMeterApiLatency             = "report_service_api_latency"
	OpenTelemetryMeterOrderTrackMessageCount = "report_service_order_track_message_count"
	OpenTelemetryMeterDwmDetailLatency       = "report_service_dwm_detail_latency"
)

var meterProvider *metric.MeterProvider

func InitOpenTelemetry() {
	res, err := resource.Merge(resource.Default(), resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceName(utils.GetHostName()),
		semconv.ServiceVersion("0.0.1"),
	))
	if err != nil {
		panic(err)
	}

	metricExporter, err := otlpmetrichttp.New(
		context.Background(),
		otlpmetrichttp.WithEndpoint(global.CONFIG.OpenTelemetry.HttpEndpoint), // HTTP 端点
		otlpmetrichttp.WithInsecure(),                                         // 允许不安全连接
	)
	if err != nil {
		panic(err)
	}
	meterProvider = metric.NewMeterProvider(
		metric.WithResource(res),
		metric.WithReader(metric.NewPeriodicReader(
			metricExporter,
			metric.WithInterval(time.Duration(global.CONFIG.OpenTelemetry.IntervalMs)*time.Millisecond),
		)),
	)
	meter := meterProvider.Meter("report-service-meter")
	registerOpenTelemetryMeter(meter)
}

func registerOpenTelemetryMeter(meter otelmetric.Meter) {
	var err error
	global.OpenTelemetry.ApiLatencyHistogram, err = meter.Float64Histogram(
		global.CONFIG.OpenTelemetry.KeyPrefix+OpenTelemetryMeterApiLatency,
		otelmetric.WithDescription("API请求延迟"),
		otelmetric.WithUnit("seconds"),
		otelmetric.WithExplicitBucketBoundaries(0.1, 0.5, 1, 3, 5, 10, 30),
	)
	if err != nil {
		panic(err)
	}

	global.OpenTelemetry.OrderTrackCounter, err = meter.Int64Counter(
		global.CONFIG.OpenTelemetry.KeyPrefix+OpenTelemetryMeterOrderTrackMessageCount,
		otelmetric.WithDescription("订单轨迹消息处理条数"),
		otelmetric.WithUnit("{条}"),
	)
	if err != nil {
		panic(err)
	}

	global.OpenTelemetry.DwmDetailLatencyHistogram, err = meter.Float64Histogram(
		global.CONFIG.OpenTelemetry.KeyPrefix+OpenTelemetryMeterDwmDetailLatency,
		otelmetric.WithDescription("DWM明细层消费延迟"),
		otelmetric.WithUnit("seconds"),
		otelmetric.WithExplicitBucketBoundaries(10, 30, 60, 120, 300, 600, 1800),
	)
	if err != nil {
		panic(err)
	}
}

func CloseOpenTelemetry() {
	err := meterProvider.Shutdown(context.Background())
	if err != nil {
		globalNotice.Error("Close OpenTelemetry error", err.Error())
	}
}
