package infra

import (
	"context"
	"fmt"
	"github.com/go-redis/redis/v8"
	"report-service/internal/global"
)

func InitRedis() {
	redisCfg := global.CONFIG.Redis
	client := redis.NewClient(&redis.Options{
		Addr:     redisCfg.Addr,
		Password: redisCfg.Password,
		DB:       redisCfg.DB,
	})
	_, err := client.Ping(context.Background()).Result()
	if err != nil {
		panic(fmt.Sprintf("redis connect ping failed, err: %v", err))
	}
	global.REDIS = client
}

func CloseRedis() {
	_ = global.REDIS.Close()
}
