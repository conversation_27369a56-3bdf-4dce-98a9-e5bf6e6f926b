package infra

import (
	"report-service/internal/global"
	"report-service/pkg/sdk/api"
	"report-service/pkg/sdk/infrastructure"
	"report-service/pkg/sdk/odas/grpc/client"
	"report-service/pkg/utils/httputil"

	internalApi "gitlab.12301.test/gopkg/internal-api-sdk-go"
)

type Request struct{}

func (r Request) Request(method, uri string, headers map[string]string, body interface{}, response interface{}) (err error) {
	return httputil.Request(method, uri, headers, body, response)
}

func InitApi() {
	// SDK初始化
	infrastructure.Config = global.CONFIG.ExternalApi.Infrastructure
	api.BaseUri = global.CONFIG.ExternalApi.JavaApi.BaseUri
	api.GateWayUri = global.CONFIG.ExternalApi.JavaApi.GateWayUri
	api.DownloadCenter = api.DownloadCenterConfig{
		BaseUri: global.CONFIG.ExternalApi.DownloadCenter.BaseUri,
		System:  global.CONFIG.ExternalApi.DownloadCenter.System,
		AuthKey: global.CONFIG.ExternalApi.DownloadCenter.AuthKey,
	}
	api.DownloadCenterApi = api.DownloadCenterApiConfig{
		BaseUri: global.CONFIG.ExternalApi.DownloadCenterApi.BaseUri,
	}
	api.OpenCooperateEcology = api.OpenCooperateEcologyConfig{
		Addr: global.CONFIG.ExternalApi.OpenCooperateEcology.Addr,
	}

	// go.mod 的 SDK
	internalApi.RequestInstance = Request{}
	internalApi.JavaService = internalApi.JavaServiceConfig{
		BaseUri:    global.CONFIG.ExternalApi.JavaApi.BaseUri,
		GateWayUri: global.CONFIG.ExternalApi.JavaApi.GateWayUri,
	}
	api.PlatformApi.RpcApi = api.PlatformApiConfigRpcApi{
		Uri:     global.CONFIG.ExternalApi.PlatformApi.RpcApi.Uri,
		System:  global.CONFIG.ExternalApi.PlatformApi.RpcApi.System,
		AuthKey: global.CONFIG.ExternalApi.PlatformApi.RpcApi.AuthKey,
	}

	// 注册 ODAS GRPC 接口
	err := client.Register(&client.ConfigType{
		Addr:      global.CONFIG.ExternalApi.OdasGRpc.Addr,
		AppId:     global.CONFIG.ExternalApi.OdasGRpc.AppId,
		AppSecret: global.CONFIG.ExternalApi.OdasGRpc.AppSecret,
		Nonce:     global.CONFIG.ExternalApi.OdasGRpc.Nonce,
	}, global.LOG)
	if err != nil {
		panic(err)
	}
}

func CloseApi() {
	err := client.Close()
	if err != nil {
		panic(err)
	}
}
