package db

import (
	"context"
	"fmt"
	"report-service/internal/global/notice"
	"time"

	"gorm.io/gorm/logger"
)

type SlowQueryLogger struct {
	logger.Interface
	threshold time.Duration
}

func NewSlowQueryLogger(baseLogger logger.Interface, threshold time.Duration) *SlowQueryLogger {
	return &SlowQueryLogger{
		Interface: baseLogger,
		threshold: threshold,
	}
}

func (l *SlowQueryLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	sql, rows := fc()
	elapsed := time.Since(begin)

	l.Interface.Trace(ctx, begin, fc, err)

	if elapsed >= l.threshold {
		// 排除 insert into 且超长的 SQL
		if sql[:6] == "INSERT" && len(sql) > 2000 {
			return
		}

		notice.Error(
			"慢SQL告警:",
			fmt.Sprintf("执行时间: %.2f秒", elapsed.Seconds()),
			fmt.Sprintf("影响行数: %d", rows),
			fmt.Sprintf("SQL: %s", sql),
		)
	}
}
