package db

import (
	"gorm.io/gorm/schema"
	"log"
	"os"
	"report-service/internal/infra/config/types"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Config(config types.GeneralDB) *gorm.Config {
	c := &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   config.Prefix,
			SingularTable: config.Singular,
		},
		DisableForeignKeyConstraintWhenMigrating: true,
	}
	_default := logger.New(NewWriter(log.New(os.Stdout, "", log.LstdFlags)), logger.Config{
		SlowThreshold:             time.Second,
		LogLevel:                  logger.Warn,
		IgnoreRecordNotFoundError: true,
	})

	var baseLogger logger.Interface
	switch config.LogMode {
	case "silent", "Silent":
		baseLogger = _default.LogMode(logger.Silent)
	case "error", "Error":
		baseLogger = _default.LogMode(logger.Error)
	case "warn", "Warn":
		baseLogger = _default.LogMode(logger.Warn)
	case "info", "Info":
		baseLogger = _default.LogMode(logger.Info)
	default:
		baseLogger = _default.LogMode(logger.Info)
	}

	c.Logger = NewSlowQueryLogger(baseLogger, time.Second)
	return c
}
