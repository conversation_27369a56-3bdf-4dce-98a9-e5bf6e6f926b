package db

import (
	"fmt"
	"report-service/internal/infra/config/types"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// Mysql 初始化Mysql数据库
func Mysql(config types.GeneralDB) *gorm.DB {
	if config.DbName == "" {
		return nil
	}
	mysqlConfig := mysql.Config{
		DSN:                       config.Username + ":" + config.Password + "@tcp(" + config.Path + ":" + config.Port + ")/" + config.DbName + "?charset=utf8mb4&parseTime=True&loc=Local",
		DefaultStringSize:         255,   // string 类型字段的默认长度
		SkipInitializeWithVersion: false, // 根据版本自动配置
	}
	db, err := gorm.Open(mysql.New(mysqlConfig), Config(config))
	if err != nil {
		panic(fmt.Sprintf("mysql connect error: %v", err))
	}
	db.InstanceSet("gorm:table_options", "ENGINE="+config.Engine)

	sqlDB, err := db.DB()
	if err != nil {
		panic(fmt.Sprintf("mysql get db error: %v", err))
	}
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Second * 120) // 设置连接的最大存活时间

	return db
}
