package db

import (
	"go.uber.org/zap"
	"gorm.io/gorm"
	"report-service/internal/global"
)

const (
	DatabaseNameDefault  = "default"
	DatabaseNameSelectDB = "select-db"
	DatabaseNameSummary  = "summary"
)

func Init() {
	global.Databases = make(map[string]*gorm.DB)
	global.Databases[DatabaseNameDefault] = Mysql(global.CONFIG.Databases.Default)
	global.Databases[DatabaseNameSelectDB] = Doris(global.CONFIG.Databases.SelectDB)
	global.Databases[DatabaseNameSummary] = Mysql(global.CONFIG.Databases.Summary)
}

func Close() {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("安全关闭数据库连接时发生错误", zap.Any("err", err))
		}
	}()
	global.LOG.Info("关闭数据库连接")
	for _, db := range global.Databases {
		conn, err := db.DB()
		if err != nil {
			global.LOG.Error("获取数据库连接失败", zap.Any("err", err))
			continue
		}
		_ = conn.Close()
	}
	global.LOG.Info("数据库连接已关闭")
}
