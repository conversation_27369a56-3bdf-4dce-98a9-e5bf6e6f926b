package db

import (
	"fmt"
	"gorm.io/gorm/logger"
	"report-service/internal/global"
)

type Writer struct {
	logger.Writer
}

// NewWriter writer 构造函数
func NewWriter(w logger.Writer) *Writer {
	return &Writer{Writer: w}
}

// Printf 格式化打印日志
func (w *Writer) Printf(message string, data ...interface{}) {
	if global.CONFIG.Databases.Default.LogZap {
		global.LOG.Info(fmt.Sprintf(message, data...))
	} else {
		w.Writer.Printf(message, data...)
	}
}
