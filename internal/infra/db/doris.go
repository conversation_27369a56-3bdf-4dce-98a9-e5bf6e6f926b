package db

import (
	"fmt"
	"report-service/internal/infra/config/types"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func Doris(config types.GeneralDB) *gorm.DB {
	if config.DbName == "" {
		return nil
	}
	mysqlConfig := mysql.Config{
		DSN: config.Username + ":" + config.Password + "@tcp(" + config.Path + ":" + config.Port + ")/" + config.DbName + "?charset=utf8mb4&parseTime=True&loc=Local",
	}
	options := Config(config)
	options.PrepareStmt = false
	db, err := gorm.Open(mysql.New(mysqlConfig), options)
	if err != nil {
		panic(fmt.Sprintf("doris connect error: %v", err))
	}

	db.Exec("SET group_commit = async_mode;") // 异步提交模式

	sqlDB, err := db.DB()
	if err != nil {
		panic(fmt.Sprintf("doris get db error: %v", err))
	}
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Second * 28800) // 设置连接的最大存活时间

	return db
}
