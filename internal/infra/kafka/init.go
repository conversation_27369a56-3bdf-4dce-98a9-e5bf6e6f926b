package kafka

import (
	"report-service/internal/global"
	"report-service/pkg/szkafka"
	"time"
)

func Init(enableConsume bool) {
	szkafka.Logger = Logger{}

	kafkaProducerConfigs := make([]szkafka.KafkaProducerConfig, 0)
	for _, config := range global.CONFIG.Kafka.Producers {
		kafkaProducerConfigs = append(kafkaProducerConfigs, szkafka.KafkaProducerConfig{
			Name: config.Name,
			ProducerOptions: &szkafka.ProducerOptions{
				Brokers: config.Brokers,
			},
		})
	}
	if !enableConsume {
		szkafka.New(kafkaProducerConfigs)
		return
	}

	consumersOptions := make([]*szkafka.ConsumerOptions, 0)
	for _, config := range global.CONFIG.Kafka.Consumers {
		for _, item := range config.Items {
			consumersOptions = append(consumersOptions, &szkafka.ConsumerOptions{
				Brokers:        config.Brokers,
				Topic:          item.Topic,
				GroupTopics:    item.GroupTopics,
				GroupId:        item.GroupId,
				BatchSize:      item.BatchSize,
				MessageHandler: getBatchHandler(item.MessageHandlerKey),
				PoolSize:       item.PoolSize,
				MinBytes:       item.MinBytes,
				MaxBytes:       item.MaxBytes,
				MaxWait:        time.Duration(item.MaxWaitMs) * time.Millisecond,
				CommitInterval: time.Duration(item.CommitIntervalMs) * time.Millisecond,
				StartOffset:    item.StartOffset,
			})
		}
	}
	szkafka.New(kafkaProducerConfigs, consumersOptions...)
}

func Release() {
	szkafka.Release()
}
