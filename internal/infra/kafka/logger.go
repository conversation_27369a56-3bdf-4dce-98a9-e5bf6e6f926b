package kafka

import (
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type Logger struct{}

func (l Logger) Info(msg string, fields ...zap.Field) {
	global.LOG.Info(msg, fields...)
}

func (l Logger) Debug(msg string, fields ...zap.Field) {
	global.LOG.Debug(msg, fields...)
}

func (l Logger) Error(msg string, fields ...zap.Field) {
	global.LOG.Error(msg, fields...)
	for _, field := range fields {
		if field.Type == zapcore.ErrorType {
			msg += "\nErr: " + field.Interface.(error).Error()
			continue
		}
		msg += "\n" + field.Key + ": " + field.String
	}
	globalNotice.Error(msg)
}
