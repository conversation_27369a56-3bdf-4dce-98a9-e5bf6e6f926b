package kafka

import (
	"report-service/cmd/consume/domain/logic/dimension"
	"report-service/cmd/consume/domain/logic/syncdata"
	"report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/dwd"
	dwmcommon "report-service/internal/domain/logic/dwm/common"
	"report-service/internal/domain/service/ods"
	"report-service/internal/domain/service/permission/module"
	"report-service/pkg/szkafka"
)

var batchHandlerMapping = map[string]szkafka.MessageHandler{
	"dwm_common_batch_insert":                     dwmcommon.BatchHandle,
	"ods_handle_event_dispatch":                   ods.HandleEventOrderTrack,
	"ods_handle_event_pay":                        ods.EventBatchHandleActionPay,
	"ods_handle_event_revoke":                     ods.EventBatchHandleActionRevoke,
	"ods_handle_event_verify":                     ods.EventBatchHandleActionVerify,
	"ods_handle_event_finish":                     ods.EventBatchHandleActionFinish,
	"ods_handle_event_add_ticket":                 ods.EventBatchHandleActionAddTicket,
	"ods_handle_event_cancel":                     ods.EventBatchHandleActionCancel,
	"ods_handle_event_after_sale":                 ods.EventBatchHandleActionAfterSale,
	"ods_handle_event_collect":                    ods.EventBatchHandleActionCollect,
	"ods_handle_event_reprint":                    ods.EventBatchHandleActionReprint,
	"dwd_handle_event_pay":                        dwd.BatchHandleEventPay,
	"dwd_handle_event_revoke":                     dwd.BatchHandleEventRevoke,
	"dwd_handle_event_verify":                     dwd.BatchHandleEventVerify,
	"dwd_handle_event_finish":                     dwd.BatchHandleEventFinish,
	"dwd_handle_event_add_ticket":                 dwd.BatchHandleEventAddTicket,
	"dwd_handle_event_cancel":                     dwd.BatchHandleEventCancel,
	"dwd_handle_event_after_sale":                 dwd.BatchHandleEventAfterSale,
	"dwd_handle_event_collect":                    dwd.BatchHandleEventCollect,
	"dwd_handle_event_reprint":                    dwd.BatchHandleEventReprint,
	"external_module_change":                      module.HandleModuleChange,
	"dwm_tourist_complete":                        tourist.BatchHandle,
	"dimension_refresh_tag_relation":              dimension.HandleRefreshTagRelation,
	"tag_center_tag_group_delete":                 dimension.HandleTagGroupDelete,
	"dimension_group_member_refresh_tag_relation": dimension.HandleRefreshMemberRelation,
	"open_ytt_cooperate_sync_data":                syncdata.HandleOpenYttCooperateSyncData,
	"light_app_service_data_volume_statistics":    syncdata.DataVolumeStatistics,
}

func getBatchHandler(key string) szkafka.MessageHandler {
	if handler, ok := batchHandlerMapping[key]; ok {
		return handler
	}
	panic("kafka no message handler found for key: " + key)
}
