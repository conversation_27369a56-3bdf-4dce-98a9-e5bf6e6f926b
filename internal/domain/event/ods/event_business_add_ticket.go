package ods

const TopicEventBusinessAddTicket = "odsEventBusinessAddTicket" // 业务事实加票事件

type EventBusinessAddTicket struct {
	OrderNo       string                     `json:"order_no"`        //订单号
	AddTicketNo   int                        `json:"add_ticket_no"`   //加票单号
	Type          int                        `json:"type"`            //类型：1新增 2修改 9删除
	AddedTicketAt string                     `json:"added_ticket_at"` //加票时间
	Data          EventBusinessAddTicketData `json:"data"`            //等同于数据库字段
}

type EventBusinessAddTicketData struct {
	EventBusinessDataBase
	AddTicketNo   int                               `json:"add_ticket_no"`   //加票单号
	Payload       EventBusinessAddTicketDataPayload `json:"payload"`         //扩展数据
	AddedTicketAt string                            `json:"added_ticket_at"` //加票时间
}

type EventBusinessAddTicketDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessDataPayloadDistributionChain `json:"distribution_chain"`
}

// EventBusinessAddTicketList 存储事件消息
type EventBusinessAddTicketList struct {
	EventData []EventBusinessAddTicket `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessAddTicketList) GetOdsTopic() string {
	return TopicEventBusinessAddTicket
}

// GetEventData 获取全部事件消息
func (b *EventBusinessAddTicketList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessAddTicketList) GetOperateNo(index int) int {
	return b.EventData[index].Data.AddTicketNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessAddTicketList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].AddTicketNo = b.EventData[index].Data.AddTicketNo
	b.EventData[index].AddedTicketAt = b.EventData[index].Data.AddedTicketAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessAddTicketList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
