package ods

const TopicEventBusinessCancel = "odsEventBusinessCancel" // 业务事实取消事件

type EventBusinessCancel struct {
	OrderNo     string                  `json:"order_no"`     //订单号
	CancelNo    int                     `json:"cancel_no"`    //取消单号
	Type        int                     `json:"type"`         //类型：1新增 2修改 9删除
	CancelledAt string                  `json:"cancelled_at"` //取消时间
	Data        EventBusinessCancelData `json:"data"`         //等同于数据库字段
}

type EventBusinessCancelData struct {
	EventBusinessDataBase
	CancelNo    int                            `json:"cancel_no"`    //取消单号
	Payload     EventBusinessCancelDataPayload `json:"payload"`      //扩展数据
	CancelledAt string                         `json:"cancelled_at"` //取消时间
}

type EventBusinessCancelDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessCancelDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessCancelDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息
	EventBusinessDataPayloadDistributionChainDiscount
	//手续费
	EventBusinessDataPayloadDistributionChainFee
}

// EventBusinessCancelList 存储事件消息
type EventBusinessCancelList struct {
	EventData []EventBusinessCancel `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessCancelList) GetOdsTopic() string {
	return TopicEventBusinessCancel
}

// GetEventData 获取全部事件消息
func (b *EventBusinessCancelList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessCancelList) GetOperateNo(index int) int {
	return b.EventData[index].Data.CancelNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessCancelList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].CancelNo = b.EventData[index].Data.CancelNo
	b.EventData[index].CancelledAt = b.EventData[index].Data.CancelledAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessCancelList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
