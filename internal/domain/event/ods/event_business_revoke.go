package ods

const TopicEventBusinessRevoke = "odsEventBusinessRevoke" // 业务事实撤销撤改事件

type EventBusinessRevoke struct {
	OrderNo   string                  `json:"order_no"`   //订单号
	RevokeNo  int                     `json:"revoke_no"`  //撤销撤改单号
	Type      int                     `json:"type"`       //类型：1新增 2修改 9删除
	RevokedAt string                  `json:"revoked_at"` //撤销撤改时间
	Data      EventBusinessRevokeData `json:"data"`       //等同于数据库字段
}

type EventBusinessRevokeData struct {
	EventBusinessDataBase
	RevokeNo  int                            `json:"revoke_no"`  //撤销撤改单号
	Payload   EventBusinessRevokeDataPayload `json:"payload"`    //扩展数据
	RevokedAt string                         `json:"revoked_at"` //撤销撤改时间
}

type EventBusinessRevokeDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessRevokeDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessRevokeDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息
	EventBusinessDataPayloadDistributionChainDiscount
	//手续费
	EventBusinessDataPayloadDistributionChainFee
}

// EventBusinessRevokeList 存储事件消息
type EventBusinessRevokeList struct {
	EventData []EventBusinessRevoke `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessRevokeList) GetOdsTopic() string {
	return TopicEventBusinessRevoke
}

// GetEventData 获取全部事件消息
func (b *EventBusinessRevokeList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessRevokeList) GetOperateNo(index int) int {
	return b.EventData[index].Data.RevokeNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessRevokeList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].RevokeNo = b.EventData[index].Data.RevokeNo
	b.EventData[index].RevokedAt = b.EventData[index].Data.RevokedAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessRevokeList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
