package ods

const TopicEventBusinessAfterSale = "odsEventBusinessAfterSale" // 业务事实售后事件

type EventBusinessAfterSale struct {
	OrderNo      string                     `json:"order_no"`       //订单号
	AfterSaleNo  int                        `json:"after_sale_no"`  //售后单号
	Type         int                        `json:"type"`           //类型：1新增 2修改 9删除
	AfterSaledAt string                     `json:"after_saled_at"` //售后时间
	Data         EventBusinessAfterSaleData `json:"data"`           //等同于数据库字段
}

type EventBusinessAfterSaleData struct {
	EventBusinessDataBase
	AfterSaleNo  int                               `json:"after_sale_no"`  //售后单号
	Payload      EventBusinessAfterSaleDataPayload `json:"payload"`        //扩展数据
	AfterSaledAt string                            `json:"after_saled_at"` //售后时间
}

type EventBusinessAfterSaleDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessAfterSaleDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessAfterSaleDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//售后信息
	EventBusinessDataPayloadDistributionChainAfterSale
	//分销链优惠信息
	//EventBusinessDataPayloadDistributionChainDiscount
}

// EventBusinessAfterSaleList 存储事件消息
type EventBusinessAfterSaleList struct {
	EventData []EventBusinessAfterSale `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessAfterSaleList) GetOdsTopic() string {
	return TopicEventBusinessAfterSale
}

// GetEventData 获取全部事件消息
func (b *EventBusinessAfterSaleList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessAfterSaleList) GetOperateNo(index int) int {
	return b.EventData[index].Data.AfterSaleNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessAfterSaleList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].AfterSaleNo = b.EventData[index].Data.AfterSaleNo
	b.EventData[index].AfterSaledAt = b.EventData[index].Data.AfterSaledAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessAfterSaleList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
