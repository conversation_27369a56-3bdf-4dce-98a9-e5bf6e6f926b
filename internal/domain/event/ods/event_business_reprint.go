package ods

const TopicEventBusinessReprint = "odsEventBusinessReprint" // 业务事实重打印事件

type EventBusinessReprint struct {
	OrderNo     string                   `json:"order_no"`     //订单号
	ReprintNo   int                      `json:"reprint_no"`   //重打印单号
	Type        int                      `json:"type"`         //类型：1新增 2修改 9删除
	ReprintedAt string                   `json:"reprinted_at"` //重打印时间
	Data        EventBusinessReprintData `json:"data"`         //等同于数据库字段
}

type EventBusinessReprintData struct {
	EventBusinessDataBase
	ReprintNo   int                             `json:"reprint_no"`   //重打印单号
	Payload     EventBusinessReprintDataPayload `json:"payload"`      //扩展数据
	ReprintedAt string                          `json:"reprinted_at"` //重打印时间
}

type EventBusinessReprintDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessReprintDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessReprintDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息
	EventBusinessDataPayloadDistributionChainDiscount
}

type EventBusinessReprintList struct {
	EventData []EventBusinessReprint `json:"event_data"`
}

func (e *EventBusinessReprintList) GetTopic() string {
	return TopicEventBusinessReprint
}

// GetOdsTopic 获取事件topic
func (e *EventBusinessReprintList) GetOdsTopic() string {
	return TopicEventBusinessReprint
}

// GetEventData 获取全部事件消息
func (e *EventBusinessReprintList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range e.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (e *EventBusinessReprintList) GetOperateNo(index int) int {
	return e.EventData[index].Data.ReprintNo
}

// SetEventInfo 设置事件消息头
func (e *EventBusinessReprintList) SetEventInfo(index int) {
	e.EventData[index].OrderNo = e.EventData[index].Data.OrderNo
	e.EventData[index].ReprintNo = e.EventData[index].Data.ReprintNo
	e.EventData[index].ReprintedAt = e.EventData[index].Data.ReprintedAt
	return
}

// SetEventType 设置事件消息类型
func (e *EventBusinessReprintList) SetEventType(index int, eventType int) {
	e.EventData[index].Type = eventType
	return
}
