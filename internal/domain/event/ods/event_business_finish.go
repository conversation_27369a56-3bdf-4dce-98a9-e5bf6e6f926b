package ods

const TopicEventBusinessFinish = "odsEventBusinessFinish" // 业务事实完结事件

type EventBusinessFinish struct {
	OrderNo    string                  `json:"order_no"`    //订单号
	FinishNo   int                     `json:"finish_no"`   //完结单号
	Type       int                     `json:"type"`        //类型：1新增 2修改 9删除
	FinishedAt string                  `json:"finished_at"` //完结时间
	Data       EventBusinessFinishData `json:"data"`        //等同于数据库字段
}

type EventBusinessFinishData struct {
	EventBusinessDataBase
	FinishNo   int                            `json:"finish_no"`   //完结单号
	Payload    EventBusinessFinishDataPayload `json:"payload"`     //扩展数据
	FinishedAt string                         `json:"finished_at"` //完结时间
}

type EventBusinessFinishDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessFinishDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessFinishDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息
	EventBusinessDataPayloadDistributionChainDiscount
}

// EventBusinessFinishList 存储事件消息
type EventBusinessFinishList struct {
	EventData []EventBusinessFinish `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessFinishList) GetOdsTopic() string {
	return TopicEventBusinessFinish
}

// GetEventData 获取全部事件消息
func (b *EventBusinessFinishList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessFinishList) GetOperateNo(index int) int {
	return b.EventData[index].Data.FinishNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessFinishList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].FinishNo = b.EventData[index].Data.FinishNo
	b.EventData[index].FinishedAt = b.EventData[index].Data.FinishedAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessFinishList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
