package ods

const TopicEventBusinessVerify = "odsEventBusinessVerify" // 业务事实核销事件

type EventBusinessVerify struct {
	OrderNo    string                  `json:"order_no"`    //订单号
	VerifyNo   int                     `json:"verify_no"`   //核销单号
	Type       int                     `json:"type"`        //类型：1新增 2修改 9删除
	VerifiedAt string                  `json:"verified_at"` //核销时间
	Data       EventBusinessVerifyData `json:"data"`        //等同于数据库字段
}

type EventBusinessVerifyData struct {
	EventBusinessDataBase
	VerifyNo   int                            `json:"verify_no"`   //核销单号
	Payload    EventBusinessVerifyDataPayload `json:"payload"`     //扩展数据
	VerifiedAt string                         `json:"verified_at"` //核销时间
}

type EventBusinessVerifyDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []BusinessVerifyModelDataPayloadDistributionChain `json:"distribution_chain"`
}

type BusinessVerifyModelDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息
	EventBusinessDataPayloadDistributionChainDiscount
}

// EventBusinessVerifyList 存储事件消息
type EventBusinessVerifyList struct {
	EventData []EventBusinessVerify `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessVerifyList) GetOdsTopic() string {
	return TopicEventBusinessVerify
}

// GetEventData 获取全部事件消息
func (b *EventBusinessVerifyList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessVerifyList) GetOperateNo(index int) int {
	return b.EventData[index].Data.VerifyNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessVerifyList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].VerifyNo = b.EventData[index].Data.VerifyNo
	b.EventData[index].VerifiedAt = b.EventData[index].Data.VerifiedAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessVerifyList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
