package ods

const TopicEventBusinessCollect = "odsEventBusinessCollect" // 业务事实取票事件

type EventBusinessCollect struct {
	OrderNo     string                   `json:"order_no"`     //订单号
	CollectNo   int                      `json:"collect_no"`   //取票单号
	Type        int                      `json:"type"`         //类型：1新增 2修改 9删除
	CollectedAt string                   `json:"collected_at"` //取票时间
	Data        EventBusinessCollectData `json:"data"`         //等同于数据库字段
}

type EventBusinessCollectData struct {
	EventBusinessDataBase
	CollectNo   int                             `json:"collect_no"`   //取票单号
	Payload     EventBusinessCollectDataPayload `json:"payload"`      //扩展数据
	CollectedAt string                          `json:"collected_at"` //取票时间
}

type EventBusinessCollectDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessCollectDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessCollectDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息，只有旧版优惠券优惠信息
	CouponPrice int `json:"coupon_price"` //旧版优惠券优惠金额
}

type EventBusinessCollectList struct {
	EventData []EventBusinessCollect `json:"event_data"`
}

func (e *EventBusinessCollectList) GetTopic() string {
	return TopicEventBusinessCollect
}

// GetOdsTopic 获取事件topic
func (e *EventBusinessCollectList) GetOdsTopic() string {
	return TopicEventBusinessCollect
}

// GetEventData 获取全部事件消息
func (e *EventBusinessCollectList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range e.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (e *EventBusinessCollectList) GetOperateNo(index int) int {
	return e.EventData[index].Data.CollectNo
}

// SetEventInfo 设置事件消息头
func (e *EventBusinessCollectList) SetEventInfo(index int) {
	e.EventData[index].OrderNo = e.EventData[index].Data.OrderNo
	e.EventData[index].CollectNo = e.EventData[index].Data.CollectNo
	e.EventData[index].CollectedAt = e.EventData[index].Data.CollectedAt
	return
}

// SetEventType 设置事件消息类型
func (e *EventBusinessCollectList) SetEventType(index int, eventType int) {
	e.EventData[index].Type = eventType
	return
}
