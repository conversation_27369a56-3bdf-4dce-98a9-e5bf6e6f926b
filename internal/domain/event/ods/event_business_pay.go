package ods

const TopicEventBusinessPay = "odsEventBusinessPay" // 业务事实支付事件

type EventBusinessPay struct {
	OrderNo string               `json:"order_no"` //订单号
	PayNo   int                  `json:"pay_no"`   //支付单号
	Type    int                  `json:"type"`     //类型：1新增 2修改 9删除
	PaidAt  string               `json:"paid_at"`  //支付时间
	Data    EventBusinessPayData `json:"data"`     //等同于数据库字段
}

type EventBusinessPayData struct {
	EventBusinessDataBase
	PayNo   int                         `json:"pay_no"`  //支付单号
	Payload EventBusinessPayDataPayload `json:"payload"` //扩展数据
	PaidAt  string                      `json:"paid_at"` //支付时间
}

type EventBusinessPayDataPayload struct {
	//分组标签信息
	EventBusinessDataGroupsTag
	//套票信息
	EventBusinessDataPayloadPackTicket
	DistributionChain []EventBusinessPayDataPayloadDistributionChain `json:"distribution_chain"`
}

type EventBusinessPayDataPayloadDistributionChain struct {
	//分销链信息
	EventBusinessDataPayloadDistributionChain
	//分销链优惠信息
	EventBusinessDataPayloadDistributionChainDiscount
}

// EventBusinessPayList 存储事件消息
type EventBusinessPayList struct {
	EventData []EventBusinessPay `json:"event_data"`
}

// GetOdsTopic 获取事件topic
func (b *EventBusinessPayList) GetOdsTopic() string {
	return TopicEventBusinessPay
}

// GetEventData 获取全部事件消息
func (b *EventBusinessPayList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}

// GetOperateNo 获取事件操作编号
func (b *EventBusinessPayList) GetOperateNo(index int) int {
	return b.EventData[index].Data.PayNo
}

// SetEventInfo 设置事件消息头
func (b *EventBusinessPayList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].PayNo = b.EventData[index].Data.PayNo
	b.EventData[index].PaidAt = b.EventData[index].Data.PaidAt
	return
}

// SetEventType 设置事件消息类型
func (b *EventBusinessPayList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}
