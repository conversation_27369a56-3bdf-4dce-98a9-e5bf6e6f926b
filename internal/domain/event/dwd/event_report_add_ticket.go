package dwd

const TopicEventReportAddTicket = "dwdEventReportAddTicket"

type EventReportAddTicket struct {
	OrderNo     string                   `json:"order_no"`      //订单号
	AddTicketNo int                      `json:"add_ticket_no"` //加票单号
	MerchantId  int                      `json:"merchant_id"`   //商户ID
	Type        int                      `json:"type"`          //类型：1新增 2修改 9删除
	AddTicketAt string                   `json:"add_ticket_at"` //加票时间
	Data        EventReportAddTicketData `json:"data"`          //等同于数据库字段
}

type EventReportAddTicketData struct {
	EventReportDataBase
	AddTicketNo int                             `json:"add_ticket_no"` //加票单号
	Payload     EventReportAddTicketDataPayload `json:"payload"`       //扩展数据
	AddTicketAt string                          `json:"add_ticket_at"` //加票时间
}

type EventReportAddTicketDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
}
type EventReportAddTicketList struct {
	EventData []EventReportAddTicket `json:"event_data"`
}

func (b *EventReportAddTicketList) GetDwdTopic() string {
	return TopicEventReportAddTicket
}

func (b *EventReportAddTicketList) GetOperateNo(index int) int {
	return b.EventData[index].Data.AddTicketNo
}

func (b *EventReportAddTicketList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportAddTicketList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].AddTicketNo = b.EventData[index].Data.AddTicketNo
	b.EventData[index].AddTicketAt = b.EventData[index].Data.AddTicketAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportAddTicketList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
