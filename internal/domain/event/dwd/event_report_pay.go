package dwd

const TopicEventReportPay = "dwdEventReportPay" // 支付事件

type EventReportPay struct {
	OrderNo    string             `json:"order_no"`    //订单号
	PayNo      int                `json:"pay_no"`      //支付单号
	MerchantId int                `json:"merchant_id"` //商户ID
	Type       int                `json:"type"`        //类型：1新增 2修改 9删除
	PaidAt     string             `json:"paid_at"`     //支付时间
	Data       EventReportPayData `json:"data"`        //等同于数据库字段
}

type EventReportPayData struct {
	EventReportDataBase
	EventReportDataDiscount
	PayNo   int                       `json:"pay_no"`  //支付单号
	Payload EventReportPayDataPayload `json:"payload"` //扩展数据
	PaidAt  string                    `json:"paid_at"` //支付时间
}

type EventReportPayDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}

type EventReportPayList struct {
	EventData []EventReportPay `json:"event_data"`
}

func (b *EventReportPayList) GetDwdTopic() string {
	return TopicEventReportPay
}

func (b *EventReportPayList) GetOperateNo(index int) int {
	return b.EventData[index].Data.PayNo
}

func (b *EventReportPayList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportPayList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].PayNo = b.EventData[index].Data.PayNo
	b.EventData[index].PaidAt = b.EventData[index].Data.PaidAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportPayList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
