package dwd

const TopicEventReportCollect = "dwdEventReportCollect"

type EventReportCollect struct {
	OrderNo     string                 `json:"order_no"`     //订单号
	CollectNo   int                    `json:"collect_no"`   //取票单号
	MerchantId  int                    `json:"merchant_id"`  //商户ID
	Type        int                    `json:"type"`         //类型：1新增 2修改 9删除
	CollectedAt string                 `json:"collected_at"` //取票时间
	Data        EventReportCollectData `json:"data"`         //等同于数据库字段
}

type EventReportCollectData struct {
	EventReportDataBase
	EventReportDataDiscount
	CollectNo   int                           `json:"collect_no"`   //取票单号
	Payload     EventReportCollectDataPayload `json:"payload"`      //扩展数据
	CollectedAt string                        `json:"collected_at"` //取票时间
}

type EventReportCollectDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}

type EventReportCollectList struct {
	EventData []EventReportCollect `json:"event_data"`
}

func (b *EventReportCollectList) GetDwdTopic() string {
	return TopicEventReportCollect
}

func (b *EventReportCollectList) GetOperateNo(index int) int {
	return b.EventData[index].Data.CollectNo
}

func (b *EventReportCollectList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
}

func (b *EventReportCollectList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].CollectNo = b.EventData[index].Data.CollectNo
	b.EventData[index].CollectedAt = b.EventData[index].Data.CollectedAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
}

func (b *EventReportCollectList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
