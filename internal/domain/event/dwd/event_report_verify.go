package dwd

const TopicEventReportVerify = "dwdEventReportVerify"

type EventReportVerify struct {
	OrderNo    string                `json:"order_no"`    //订单号
	VerifyNo   int                   `json:"verify_no"`   //核销单号
	MerchantId int                   `json:"merchant_id"` //商户ID
	Type       int                   `json:"type"`        //类型：1新增 2修改 9删除
	VerifiedAt string                `json:"verified_at"` //核销时间
	Data       EventReportVerifyData `json:"data"`        //等同于数据库字段
}

type EventReportVerifyData struct {
	EventReportDataBase
	EventReportDataDiscount
	VerifyNo   int                          `json:"verify_no"`   //核销单号
	Payload    EventReportVerifyDataPayload `json:"payload"`     //扩展数据
	VerifiedAt string                       `json:"verified_at"` //核销时间
}

type EventReportVerifyDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}

type EventReportVerifyList struct {
	EventData []EventReportVerify `json:"event_data"`
}

func (b *EventReportVerifyList) GetDwdTopic() string {
	return TopicEventReportVerify
}

func (b *EventReportVerifyList) GetOperateNo(index int) int {
	return b.EventData[index].Data.VerifyNo
}

func (b *EventReportVerifyList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportVerifyList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].VerifyNo = b.EventData[index].Data.VerifyNo
	b.EventData[index].VerifiedAt = b.EventData[index].Data.VerifiedAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportVerifyList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
