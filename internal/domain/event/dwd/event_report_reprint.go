package dwd

const TopicEventReportReprint = "dwdEventReportReprint"

type EventReportReprint struct {
	OrderNo     string                 `json:"order_no"`     //订单号
	ReprintNo   int                    `json:"reprint_no"`   //重打印单号
	MerchantId  int                    `json:"merchant_id"`  //商户ID
	Type        int                    `json:"type"`         //类型：1新增 2修改 9删除
	ReprintedAt string                 `json:"reprinted_at"` //重打印时间
	Data        EventReportReprintData `json:"data"`         //等同于数据库字段
}

type EventReportReprintData struct {
	EventReportDataBase
	EventReportDataDiscount
	ReprintNo   int                           `json:"reprint_no"`   //重打印单号
	Payload     EventReportReprintDataPayload `json:"payload"`      //扩展数据
	ReprintedAt string                        `json:"reprinted_at"` //重打印时间
}

type EventReportReprintDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}

type EventReportReprintList struct {
	EventData []EventReportReprint `json:"event_data"`
}

func (b *EventReportReprintList) GetDwdTopic() string {
	return TopicEventReportReprint
}

func (b *EventReportReprintList) GetOperateNo(index int) int {
	return b.EventData[index].Data.ReprintNo
}

func (b *EventReportReprintList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
}

func (b *EventReportReprintList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].ReprintNo = b.EventData[index].Data.ReprintNo
	b.EventData[index].ReprintedAt = b.EventData[index].Data.ReprintedAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
}

func (b *EventReportReprintList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
