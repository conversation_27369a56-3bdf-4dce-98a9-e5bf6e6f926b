package dwd

const TopicEventReportCancel = "dwdEventReportCancel" // 取消事件

type EventReportCancel struct {
	OrderNo     string                `json:"order_no"`    //订单号
	CancelNo    int                   `json:"cancel_no"`   //取消单号
	MerchantId  int                   `json:"merchant_id"` //商户ID
	Type        int                   `json:"type"`        //类型：1新增 2修改 9删除
	CancelledAt string                `json:"canceled_at"` //取消时间
	Data        EventReportCancelData `json:"data"`        //等同于数据库字段
}

type EventReportCancelData struct {
	EventReportDataBase
	EventReportDataDiscount
	EventReportDataFee
	CancelNo    int                          `json:"cancel_no"`    //取消单号
	Payload     EventReportCancelDataPayload `json:"payload"`      //扩展数据
	CancelledAt string                       `json:"cancelled_at"` //取消时间
}

type EventReportCancelDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}

type EventReportCancelList struct {
	EventData []EventReportCancel `json:"event_data"`
}

func (b *EventReportCancelList) GetDwdTopic() string {
	return TopicEventReportCancel
}

func (b *EventReportCancelList) GetOperateNo(index int) int {
	return b.EventData[index].Data.CancelNo
}

func (b *EventReportCancelList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportCancelList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].CancelNo = b.EventData[index].Data.CancelNo
	b.EventData[index].CancelledAt = b.EventData[index].Data.CancelledAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportCancelList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
