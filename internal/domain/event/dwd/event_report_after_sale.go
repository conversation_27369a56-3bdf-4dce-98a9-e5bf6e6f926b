package dwd

const TopicEventReportAfterSale = "dwdEventReportAfterSale"

type EventReportAfterSale struct {
	OrderNo      string                   `json:"order_no"`       //订单号
	AfterSaleNo  int                      `json:"after_sale_no"`  //售后单号
	MerchantId   int                      `json:"merchant_id"`    //商户ID
	Type         int                      `json:"type"`           //类型：1新增 2修改 9删除
	AfterSaledAt string                   `json:"after_saled_at"` //售后时间
	Data         EventReportAfterSaleData `json:"data"`           //等同于数据库字段
}

type EventReportAfterSaleData struct {
	EventReportDataBase
	AfterSaleNo  int                             `json:"after_sale_no"`  //售后单号
	Payload      EventReportAfterSaleDataPayload `json:"payload"`        //扩展数据
	AfterSaledAt string                          `json:"after_saled_at"` //售后时间
}

type EventReportAfterSaleDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDistributionChainAfterSale
}
type EventReportAfterSaleList struct {
	EventData []EventReportAfterSale `json:"event_data"`
}

func (b *EventReportAfterSaleList) GetDwdTopic() string {
	return TopicEventReportAfterSale
}

func (b *EventReportAfterSaleList) GetOperateNo(index int) int {
	return b.EventData[index].Data.AfterSaleNo
}

func (b *EventReportAfterSaleList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportAfterSaleList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].AfterSaleNo = b.EventData[index].Data.AfterSaleNo
	b.EventData[index].AfterSaledAt = b.EventData[index].Data.AfterSaledAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportAfterSaleList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
