package dwd

const TopicEventReportFinish = "dwdEventReportFinish"

type EventReportFinish struct {
	OrderNo    string                `json:"order_no"`    //订单号
	FinishNo   int                   `json:"finish_no"`   //完结单号
	MerchantId int                   `json:"merchant_id"` //商户ID
	Type       int                   `json:"type"`        //类型：1新增 2修改 9删除
	FinishedAt string                `json:"finished_at"` //完成时间
	Data       EventReportFinishData `json:"data"`        //等同于数据库字段
}

type EventReportFinishData struct {
	EventReportDataBase
	EventReportDataDiscount
	FinishNo   int                          `json:"finish_no"`   //完结单号
	Payload    EventReportFinishDataPayload `json:"payload"`     //扩展数据
	FinishedAt string                       `json:"finished_at"` //完结时间
}

type EventReportFinishDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}

type EventReportFinishList struct {
	EventData []EventReportFinish `json:"event_data"`
}

func (b *EventReportFinishList) GetDwdTopic() string {
	return TopicEventReportFinish
}

func (b *EventReportFinishList) GetOperateNo(index int) int {
	return b.EventData[index].Data.FinishNo
}

func (b *EventReportFinishList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportFinishList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].FinishNo = b.EventData[index].Data.FinishNo
	b.EventData[index].FinishedAt = b.EventData[index].Data.FinishedAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportFinishList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
