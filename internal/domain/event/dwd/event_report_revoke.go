package dwd

const TopicEventReportRevoke = "dwdEventReportRevoke" // 撤销事件

type EventReportRevoke struct {
	OrderNo    string                `json:"order_no"`    //订单号
	RevokeNo   int                   `json:"revoke_no"`   //撤销单号
	MerchantId int                   `json:"merchant_id"` //商户ID
	Type       int                   `json:"type"`        //类型：1新增 2修改 9删除
	RevokedAt  string                `json:"revoked_at"`  //撤销时间
	Data       EventReportRevokeData `json:"data"`        //等同于数据库字段
}

type EventReportRevokeData struct {
	EventReportDataBase
	EventReportDataDiscount
	EventReportDataFee
	RevokeNo  int                          `json:"revoke_no"`  //撤销单号
	Payload   EventReportRevokeDataPayload `json:"payload"`    //扩展数据
	RevokedAt string                       `json:"revoked_at"` //撤销时间
}

type EventReportRevokeDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscountDetail
	EventReportDataPayloadOrderCoupon
}
type EventReportRevokeList struct {
	EventData []EventReportRevoke `json:"event_data"`
}

func (b *EventReportRevokeList) GetDwdTopic() string {
	return TopicEventReportRevoke
}

func (b *EventReportRevokeList) GetOperateNo(index int) int {
	return b.EventData[index].Data.RevokeNo
}

func (b *EventReportRevokeList) SetEventType(index int, eventType int) {
	b.EventData[index].Type = eventType
	return
}

func (b *EventReportRevokeList) SetEventInfo(index int) {
	b.EventData[index].OrderNo = b.EventData[index].Data.OrderNo
	b.EventData[index].RevokeNo = b.EventData[index].Data.RevokeNo
	b.EventData[index].RevokedAt = b.EventData[index].Data.RevokedAt
	b.EventData[index].MerchantId = b.EventData[index].Data.MerchantId
	return
}

func (b *EventReportRevokeList) GetEventData() []interface{} {
	var data []interface{}
	for _, v := range b.EventData {
		data = append(data, v)
	}
	return data
}
