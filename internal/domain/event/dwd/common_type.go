package dwd

type EventReportDataPayload struct {
	EventReportDataPayloadGroupsTag
	EventReportDataPayloadBase
	EventReportDataPayloadDiscount
}

type EventReportDataBase struct {
	MerchantId        int    `json:"merchant_id"`         // 商户ID
	ParentMerchantId  int    `json:"parent_merchant_id"`  // 上级商户ID
	DistributorId     int    `json:"distributor_id"`      // 下级商户ID
	OrderNo           string `json:"order_no"`            // 订单号
	TradeNo           string `json:"trade_no"`            // 交易单号
	ExternalOperateNo string `json:"external_operate_no"` // 外部操作单号
	PoiId             int    `json:"poi_id"`              // PoiID
	SpuId             int    `json:"spu_id"`              // SpuID
	SkuId             int    `json:"sku_id"`              // SkuID
	SaleChannel       int    `json:"sale_channel"`        // 销售渠道
	OperateChannel    int    `json:"operate_channel"`     // 操作渠道
	CostPayMode       int    `json:"cost_pay_mode"`       // 采购支付方式
	SalePayMode       int    `json:"sale_pay_mode"`       // 销售支付方式
	SellOperatorId    int    `json:"sell_operator_id"`    // 售票员ID
	OperatorId        int    `json:"operator_id"`         // 操作人ID
	SellSiteId        int    `json:"sell_site_id"`        // 售票站点ID
	OperateSiteId     int    `json:"operate_site_id"`     // 操作站点
	Count             int    `json:"count"`               // 数量
	CostUnitPrice     int    `json:"cost_unit_price"`     // 采购单价
	SaleUnitPrice     int    `json:"sale_unit_price"`     // 销售单价
	CostPrice         int    `json:"cost_price"`          // 采购金额
	SalePrice         int    `json:"sale_price"`          // 销售金额
}

type EventReportDataDiscount struct {
	CostDiscountPrice int `json:"cost_discount_price"` // 采购优惠金额
	SaleDiscountPrice int `json:"sale_discount_price"` // 销售优惠金额
}

type EventReportDataPayloadBase struct {
	PackType           int    `json:"pack_type"`            // 套票规则 0默认 1是主票 2是子票
	ShowBindType       int    `json:"show_bind_type"`       // 捆绑票规则 0默认 1是主票 2是子票
	AnnualCardType     int    `json:"annual_card_type"`     // 年卡规则 0默认 1是年卡 2是特权
	ExchangeCouponType int    `json:"exchange_coupon_type"` // 预售券规则 0默认 1是预售券 2是权益兑换订单
	ProductType        string `json:"product_type"`         //产品线类型
	SubType            int    `json:"sub_type"`             //产品线子类型
	ParentOrderNo      string `json:"parent_order_no"`      //主订单号
}

type EventReportDataPayloadDiscount struct {
	CostDiscountDetail []EventReportDataPayloadDiscountDetailItem `json:"cost_discount_detail"` // 采购优惠明细
	SaleDiscountDetail []EventReportDataPayloadDiscountDetailItem `json:"sale_discount_detail"` // 销售优惠明细
	CouponPrice        int                                        `json:"coupon_price"`         // 优惠金额
}

type EventReportDataPayloadDiscountDetail struct {
	CostDiscountDetail []EventReportDataPayloadDiscountDetailItem `json:"cost_discount_detail"` // 采购优惠明细
	SaleDiscountDetail []EventReportDataPayloadDiscountDetailItem `json:"sale_discount_detail"` // 销售优惠明细
}

type EventReportDataPayloadOrderCoupon struct {
	SaleCouponPrice int `json:"sale_coupon_price"` // 优惠券金额
	CostCouponPrice int `json:"cost_coupon_price"` // 优惠券金额
}

type EventReportDataPayloadDistributionChainAfterSale struct {
	AfterSaleCode        string `json:"after_sale_code"`         //售后编号
	AfterSaleNum         int    `json:"after_sale_num"`          //售后票数
	AfterSaleIncomePrice int    `json:"after_sale_income_price"` //售后收入金额
	AfterSaleRefundPrice int    `json:"after_sale_refund_price"` //售后退回金额
}

type EventReportDataFee struct {
	CostFee int `json:"cost_fee"` //采购手续费
	SaleFee int `json:"sale_fee"` //销售手续费
}

type EventReportDataPayloadDiscountDetailItem struct {
	Key   int `json:"key"`   //优惠序号
	Type  int `json:"type"`  //优惠类型
	Price int `json:"price"` //优惠金额
}

// 分组标签信息
type EventReportDataPayloadGroupsTag struct {
	//标签分组信息
	GroupsTag GroupsTag `json:"groups_tag"`
}

// 标签信息
type GroupsTag struct {
	//适用人群标签标识code
	TargetAudience string `json:"target_audience"`
}

type EventProcessor interface {
	GetDwdTopic() string
	GetOperateNo(index int) int
	SetEventInfo(index int)
	SetEventType(index int, eventType int)
	GetEventData() []interface{}
}
