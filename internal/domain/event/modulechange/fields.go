package modulechange

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"time"
)

const (
	ModuleActionOpen  = 1
	ModuleActionClose = 2
)

func (c ChangeInfo) GetChangeTime() carbon.Carbon {
	nanoseconds := cast.ToInt64(c.ChangeTime) * int64(time.Millisecond)
	t := time.Unix(0, nanoseconds)
	date := t.Format(enum.TimestampLayoutMake)
	return carbon.Parse(date)
}
