package myuuordertrack

type EventMyuuOrderTrackParams struct {
	AfterList  EventMyuuOrderTrackParamsAfterList `json:"afterList"`
	BeforeList interface{}                        `json:"beforeList"`
	EventType  string                             `json:"eventType"`
	SchemaName string                             `json:"schemaName"`
	TableName  string                             `json:"tableName"`
}

type EventMyuuOrderTrackParamsAfterList struct {
	ApplyDid       int    `json:"apply_did"`
	Tnum           int    `json:"tnum"`
	BranchTerminal int    `json:"branchTerminal"`
	IdCard         string `json:"id_card"`
	OperMember     int    `json:"oper_member"`
	OrdreMonth     int    `json:"ordre_month"`
	Ordernum       string `json:"ordernum"`
	SalerID        int    `json:"SalerID"`
	SyncState      int    `json:"sync_state"`
	Source         int    `json:"source"`
	Terminal       int    `json:"terminal"`
	ExtContent     string `json:"ext_content"`
	Tid            int    `json:"tid"`
	InsertTime     string `json:"insertTime"`
	UpdateTime     int    `json:"update_time"`
	Action         int    `json:"action"`
	Id             int    `json:"id"`
	LeftNum        int    `json:"left_num"`
}

type EventMyuuOrderTrackExtContent struct {
	SerialNumber  string `json:"serial_number"`
	AfterSaleCode string `json:"afterSaleCode"`
	AfterSaleNum  int    `json:"afterSaleNum"`
}

const (
	EventMyuuOrderTrackActionModify       = 1  // 修改
	EventMyuuOrderTrackActionCancel       = 2  // 取消
	EventMyuuOrderTrackActionCollect      = 3  // 取票
	EventMyuuOrderTrackActionPay          = 4  // 支付
	EventMyuuOrderTrackActionVerify       = 5  // 验证
	EventMyuuOrderTrackActionRevokeCancel = 6  // 撤销
	EventMyuuOrderTrackActionRevokeModify = 7  // 撤改
	EventMyuuOrderTrackActionReprint      = 8  // 重打印
	EventMyuuOrderTrackActionAddTicket    = 16 // 加票
	EventMyuuOrderTrackActionFinish       = 17 // 完结
	EventMyuuOrderTrackActionAfterSale    = 38 // 售后
)

var EventMyuuOrderTrackAction = []int{
	EventMyuuOrderTrackActionModify,
	EventMyuuOrderTrackActionCancel,
	EventMyuuOrderTrackActionCollect,
	EventMyuuOrderTrackActionPay,
	EventMyuuOrderTrackActionVerify,
	EventMyuuOrderTrackActionRevokeCancel,
	EventMyuuOrderTrackActionRevokeModify,
	EventMyuuOrderTrackActionReprint,
	EventMyuuOrderTrackActionAddTicket,
	EventMyuuOrderTrackActionFinish,
	EventMyuuOrderTrackActionAfterSale,
}

// 限制消息类型
const EventAllowEventType = "INSERT"

type EventBusinessDispatchItem struct {
	OrderNo   string                    `json:"order_no"`   //订单号
	Type      int                       `json:"type"`       //类型：1新增 2修改 9删除
	OperateAt string                    `json:"operate_at"` //操作时间
	OperateNo int                       `json:"operate_no"` //操作编号
	Data      EventMyuuOrderTrackParams `json:"data"`       //等同于数据库字段
}
