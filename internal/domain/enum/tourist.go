package enum

const (
	CommonUnknownKey = 0    //通用未知
	GenderUnknownKey = 99   //性别未知
	RegionWithinKey  = 1    //境内
	RegionAbroadKey  = 2    //境外
	CountryCnKey     = 1    //国家-中国
	GenderMaleKey    = 1    //性别男
	GenderFemaleKey  = 0    //性别女
	AgeUnknownKey    = 9999 //年龄未知
)

var RegionKeyNameMap = map[int]string{
	CommonUnknownKey: "未知",
	RegionWithinKey:  "境内",
	RegionAbroadKey:  "境外",
}
var RegionKeySort = []int{RegionWithinKey, RegionAbroadKey, CommonUnknownKey}

var GenderKeyNameMap = map[int]string{
	GenderFemaleKey:  "女",
	GenderMaleKey:    "男",
	GenderUnknownKey: "未知",
}
var GenderKeySort = []int{GenderMaleKey, GenderFemaleKey, GenderUnknownKey}

var AgeKeyNameMap = map[int]string{
	AgeUnknownKey: "未知",
}

const (
	SearchFieldSelfSupplySpu = "self_supply_spu"
	SearchFieldSelfSupplySku = "self_supply_sku"
)
