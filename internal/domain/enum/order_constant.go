package enum

// 销售渠道
var OrderModeMap = map[int]string{
	0:  "正常分销商下单",
	1:  "普通用户支付",
	2:  "用户手机支付",
	9:  "会员卡购票",
	10: "云票务",
	11: "微商城",
	12: "自助机",
	13: "二级店铺",
	14: "闸机购票",
	15: "智能终端",
	16: "计调下单",
	17: "淘宝码商(飞猪)",
	18: "年卡",
	19: "微平台",
	20: "OTA",
	21: "兑换码购票",
	22: "一卡通",
	23: "套票子票下单",
	24: "团队订单",
	33: "e福州",
	34: "拼团",
	35: "积分商城",
	36: "POS支付",
	38: "招行",
	39: "银联商务",
	40: "银联pos支付",
	41: "威富通",
	42: "易宝",
	43: "微信-微票房",
	44: "报团计调下单",
	45: "云闪付下单",
	46: "断网离线订单",
	47: "员工卡",
	48: "手牌",
	49: "桌面云票务",
	50: "APP",
	55: "特殊团队预约",
	56: "小程序",
	57: "抖音-微票房",
	58: "阿里供销",
	59: "团购导码",
	60: "京津冀年卡",
	61: "水韵江苏年卡",
	62: "武汉老年卡",
	63: "宁镇扬游园卡",
	64: "预售券兑换",
	65: "景旅纵横动态码",
	66: "丝路风情年卡",
}

// 操作渠道
var TrackSourceMap = map[int]string{
	0:  "终端机",
	1:  "软终端",
	2:  "自助机",
	3:  "外部通知更新",
	4:  "云票务",
	5:  "云闸机",
	6:  "PC-支付宝",
	7:  "手机支付宝",
	8:  "支付宝刷卡",
	9:  "支付宝扫码",
	10: "微信支付",
	11: "微信刷卡",
	12: "微信扫码",
	13: "PC-银联",
	14: "手机-银联",
	15: "PC-环迅",
	16: "内部接口",
	17: "外部接口",
	18: "undefined",
	19: "自运行服务",
	20: "安卓智能终端机",
	21: "验证服务器",
	22: "微信商城",
	23: "二级店铺",
	24: "OTA",
	25: "会员购票",
	26: "年卡特权",
	27: "平安银行",
	28: "内部接口",
	29: "微信",
	30: "一卡通",
	31: "套票子票",
	32: "子景点验证",
	33: "市民卡",
	34: "拼团",
	35: "法利兰一卡通",
	36: "mini景区端",
	37: "积分商城",
	38: "招行",
	39: "银联商务",
	40: "银联pos支付",
	41: "威富通",
	42: "易宝",
	43: "云闪付",
	44: "报团计调下单",
	45: "断网离线订单",
	46: "强制核销",
	47: "农行聚合",
	48: "建行聚合",
	49: "丰收互联",
	50: "app",
	51: "桌面云票务",
	52: "桌面云票务子景点验证",
	53: "子景点过期自动验证",
	54: "子景点快速验证",
	55: "江西农商行",
	56: "银联会员卡",
	57: "特殊团队预约",
	58: "盛世通码支付",
	59: "玩聚",
	60: "青海银行",
	61: "团购导码",
	62: "OTA-阿里供销",
	63: "玩聚-抖音担保交易支付",
	64: "招商银行聚合",
	65: "百度聚合支付",
	66: "线下手工补单",
	67: "中国银行",
	68: "易宝独立收款",
	69: "手持机(人脸)",
	70: "工商银行",
	71: "预付卡",
	72: "农行(云BMP)",
	73: "安徽农商行",
	74: "易宝平台收款",
	75: "绿聚支付",
	76: "福建农信",
	77: "聚富通",
	78: "农行(创识)",
	79: "通联支付",
	80: "富友支付",
	81: "平台",
	82: "酷享支付",
	83: "自贡银行",
	84: "慧徕店支付",
	85: "预售券兑换",
	86: "预存码预约",
	87: "星沙农商",
	88: "网联商务",
	89: "金华银行",
	90: "悦航支付",
	91: "拉卡拉支付",
	92: "茶卡自有支付",
}

// 支付方式
var PaymodeMap = map[int]string{
	10000: "成本",
	10001: "自采",
	0:     "余额支付",
	1:     "支付宝",
	2:     "授信支付",
	3:     "自供自销",
	4:     "现场支付",
	5:     "微信支付",
	6:     "会员卡支付",
	7:     "银联支付",
	8:     "环迅支付",
	9:     "现金支付",
	10:    "会员卡",
	11:    "拉卡拉（商户）",
	12:    "拉卡拉（平台）",
	13:    "特权支付（年卡）",
	14:    "微信（聚合）",
	15:    "平安银行",
	16:    "支付宝（聚合）",
	17:    "会员卡支付",
	18:    "计时卡支付",
	25:    "招行一网通支付",
	26:    "银联商务支付",
	27:    "银联商务POS支付",
	28:    "威富通支付",
	29:    "易宝云企付",
	30:    "银联云闪付",
	31:    "农行",
	32:    "建行",
	33:    "博思支付",
	35:    "积分支付",
	36:    "POS支付",
	37:    "身份证一卡通",
	38:    "预存余额支付",
	34:    "丰收互联",
	39:    "江西农商行",
	40:    "银联会员卡",
	41:    "盛世通支付",
	42:    "玩聚",
	43:    "青海银行",
	44:    "玩聚-抖音担保交易支付",
	45:    "招商银行聚合支付",
	46:    "百度聚合支付",
	47:    "线下手工补单",
	48:    "中国银行",
	49:    "易宝独立收款",
	50:    "工商银行",
	51:    "预付卡",
	52:    "农行(云BMP)",
	53:    "预付卡",
	54:    "安徽农商行",
	55:    "易宝平台收款",
	56:    "绿聚支付",
	58:    "福建农信",
	59:    "聚富通",
	60:    "农行(创识)",
	61:    "通联支付",
	62:    "富友支付",
	63:    "酷享支付",
	64:    "自贡银行",
	65:    "预售券权益支付",
	66:    "慧徕店支付",
	67:    "预存码权益支付",
	68:    "星沙农行",
	69:    "网联商务",
	70:    "金华银行",
	71:    "悦航支付",
	72:    "拉卡拉支付",
	73:    "茶卡自有支付",
	74:    "无需支付",
	75:    "TLinx",
	//76~85这10个是自定义线下支付方式预留 如果商家有配置会通过java接口获取到后与这个合并
	76:  "线下收款1",  //线下支付方式1
	77:  "线下收款2",  //线下支付方式2
	78:  "线下收款3",  //线下支付方式3
	79:  "线下收款4",  //线下支付方式4
	80:  "线下收款5",  //线下支付方式5
	81:  "线下收款6",  //线下支付方式6
	82:  "线下收款7",  //线下支付方式7
	83:  "线下收款8",  //线下支付方式8
	84:  "线下收款9",  //线下支付方式9
	85:  "线下收款10", //线下支付方式10
	255: "未知",
}
var OrderStatusMap = map[int]string{
	0:  "未使用",
	1:  "已使用",
	2:  "已过期",
	3:  "被取消",
	4:  "待确认",
	5:  "撤改",
	6:  "撤销",
	7:  "部分使用",
	8:  "完结",
	9:  "被删除",
	10: "待预约",
	11: "待出票",
	80: "待发货",
	81: "已发货",
}

// 操作类型
const (
	TouristTrackActionPay       = 6 // 支付&加票
	TouristTrackActionCancel    = 2 // 取消
	TouristTrackActionVerify    = 1 // 验证&完结
	TouristTrackActionRevoke    = 5 // 撤销
	TouristTrackActionAfterSale = 7 // 售后
)

const (
	PayModePrepaidCardOne = 51
	PayModePrepaidCardTwo = 52
	PayModeCash           = 9
	PayModeCredit         = 2
	PayModeYiBao          = 55
	PayModeAlipay         = 1
	PayModeWechat         = 5
)

var PayModePrepaidCard = []int{
	PayModePrepaidCardOne,
	PayModePrepaidCardTwo,
}
