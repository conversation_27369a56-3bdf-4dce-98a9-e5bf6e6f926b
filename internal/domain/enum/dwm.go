package enum

// 操作类型
const (
	DWMOperateTypePay       = 1 // 支付&加票
	DWMOperateTypeVerify    = 2 // 验证&完结
	DWMOperateTypeCancel    = 3 // 取消
	DWMOperateTypeRevoke    = 4 // 撤销
	DWMOperateTypeAfterSale = 5 // 售后
	DWMOperateTypeCollect   = 6 // 取票
	DWMOperateTypeReprint   = 7 // 重打印
)

var DWMOperateTypeMap = map[int]string{
	DWMOperateTypePay:       "支付",
	DWMOperateTypeVerify:    "验证",
	DWMOperateTypeCancel:    "取消",
	DWMOperateTypeRevoke:    "撤销",
	DWMOperateTypeAfterSale: "售后",
	DWMOperateTypeCollect:   "取票",
	DWMOperateTypeReprint:   "重打印",
}

// 游客操作类型映射报表操作类型
var TouristTrackActionToOperateTypeMap = map[int]int{
	TouristTrackActionPay:       DWMOperateTypePay,
	TouristTrackActionCancel:    DWMOperateTypeCancel,
	TouristTrackActionVerify:    DWMOperateTypeVerify,
	TouristTrackActionRevoke:    DWMOperateTypeRevoke,
	TouristTrackActionAfterSale: DWMOperateTypeAfterSale,
}

// 证件类型
const (
	DWMTouristVoucherTypeIdCard                       = 1  //身份证
	DWMTouristVoucherTypePassport                     = 2  //护照
	DWMTouristVoucherTypeMilitaryId                   = 3  //军官证
	DWMTouristVoucherTypeHomeReturnPermit             = 4  //回乡证
	DWMTouristVoucherTypeTwCompatriotIdCard           = 5  //台胞证
	DWMTouristVoucherTypeFPResidencePermit            = 6  //外国人永久居留证
	DWMTouristVoucherTypeHKAndMacaoExitAndEntryPermit = 7  //港澳通行证
	DWMTouristVoucherTypeTwExitAndEntryPermit         = 8  //台湾通行证
	DWMTouristVoucherTypeHKAndMacaoResidencePermit    = 9  //港澳居民居住证
	DWMTouristVoucherTypeTwResidencePermit            = 10 //台湾居民居住证
	DWMTouristVoucherTypeOther                        = 99 //其他
)

var DWMTouristVoucherTypeMap = map[int]string{
	DWMTouristVoucherTypeIdCard:                       "身份证",
	DWMTouristVoucherTypePassport:                     "护照",
	DWMTouristVoucherTypeMilitaryId:                   "军官证",
	DWMTouristVoucherTypeHomeReturnPermit:             "回乡证",
	DWMTouristVoucherTypeTwCompatriotIdCard:           "台胞证",
	DWMTouristVoucherTypeFPResidencePermit:            "外国人永久居留证",
	DWMTouristVoucherTypeHKAndMacaoExitAndEntryPermit: "港澳通行证",
	DWMTouristVoucherTypeTwExitAndEntryPermit:         "台湾通行证",
	DWMTouristVoucherTypeHKAndMacaoResidencePermit:    "港澳居民居住证",
	DWMTouristVoucherTypeTwResidencePermit:            "台湾居民居住证",
	DWMTouristVoucherTypeOther:                        "其他",
}
