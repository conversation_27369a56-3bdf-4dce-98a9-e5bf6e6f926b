package enum

// 时间分组类型
const (
	ReportTimeGroupTypeHour  = 1 // 小时
	ReportTimeGroupTypeDay   = 2 // 天
	ReportTimeGroupTypeMonth = 3 // 月
	ReportTimeGroupTypeYear  = 4 // 年
)

const (
	ReportTypeHour     = 1 // 小时报表
	ReportTypeDay      = 2 // 日报表
	ReportTypeDetail   = 3 // 明细报表
	ReportTypeRealTime = 4 // 实时报表
)

// 游客统计对象类型
const (
	TouristSubjectTypeRegion      = 1  //地域统计
	TouristSubjectTypeCountry     = 2  //国家统计
	TouristSubjectTypeProvince    = 3  //省统计
	TouristSubjectTypeCity        = 4  //城市统计
	TouristSubjectTypeDistrict    = 5  //区县统计
	TouristSubjectTypeAge         = 6  //年龄统计
	TouristSubjectTypeGender      = 7  //性别统计
	TouristSubjectTypeAreaChart   = 8  //客源地图表
	TouristSubjectTypeGenderChart = 9  //性别图表
	TouristSubjectTypeAgeChart    = 10 //年龄图表
	TouristSubjectTypeRegionAll   = 11 //地域国家省市区导出Excel
)

// 报表模板维度字段定义
const (
	DimensionDate           = "date"            // 日期
	DimensionSaleChannel    = "sale_channel"    // 订单渠道
	DimensionDistributor    = "distributor"     // 分销商
	DimensionSpu            = "spu"             // 产品
	DimensionSku            = "sku"             // 票种
	DimensionPayMode        = "pay_mode"        // 支付方式
	DimensionOperator       = "operator"        // 操作人
	DimensionSellOperator   = "sell_operator"   // 售票员
	DimensionSellSite       = "sell_site"       // 售票站点
	DimensionCostUnitPrice  = "cost_unit_price" // 采购单价
	DimensionSaleUnitPrice  = "sale_unit_price" // 销售单价
	DimensionRegion         = "region"          //地域 境内外
	DimensionCountry        = "country"         //国家
	DimensionProvince       = "province"        //省
	DimensionCity           = "city"            //市
	DimensionDistrict       = "district"        //区县
	DimensionAgeGroup       = "age"             //年龄段
	DimensionGender         = "gender"          //性别
	DimensionTargetAudience = "target_audience" //适用人群
	DimensionGroupAccount   = "group_account"   //集团账号
	DimensionGroupMember    = "group_member"    //集团成员
	DimensionMerchantId     = "merchant_id"     //商户id
)

// 报表模板指标字段定义
const (
	IndicatorPayCount     = "pay_count"      // 预订数量
	IndicatorPaySalePrice = "pay_sale_price" // 销售金额
	IndicatorPayCostPrice = "pay_cost_price" // 采购金额

	IndicatorActualSaleCount = "actual_sale_count" // 实售数量
	IndicatorActualSalePrice = "actual_sale_price" // 实售金额
	IndicatorActualCostPrice = "actual_cost_price" // 实采金额
	IndicatorActualProfit    = "actual_profit"     // 实际利润

	IndicatorActualVerifyCount     = "actual_verify_count"      // 净验证数量
	IndicatorActualVerifySalePrice = "actual_verify_sale_price" // 净验证金额
	IndicatorActualVerifyCostPrice = "actual_verify_cost_price" // 净验证采购金额
	IndicatorActualVerifyProfit    = "actual_verify_profit"     // 净验证利润

	IndicatorVerifyCount     = "verify_count"      // 核销数量
	IndicatorVerifySalePrice = "verify_sale_price" // 核销金额
	IndicatorVerifyCostPrice = "verify_cost_price" // 核销采购金额

	IndicatorCancelCount     = "cancel_count"      // 取消数量
	IndicatorCancelSalePrice = "cancel_sale_price" // 取消金额
	IndicatorCancelCostPrice = "cancel_cost_price" // 取消采购金额

	IndicatorRevokeCount     = "revoke_count"      // 撤销数量
	IndicatorRevokeSalePrice = "revoke_sale_price" // 撤销金额
	IndicatorRevokeCostPrice = "revoke_cost_price" // 撤销采购金额

	IndicatorRefundFeeProfit = "refund_fee_profit" // 退款手续费利润

	IndicatorAfterSaleCount = "after_sale_count" // 售后数量
	IndicatorAfterSalePrice = "after_sale_price" // 售后销售金额
	IndicatorAfterCostPrice = "after_cost_price" // 售后采购金额

	IndicatorTouristCount = "count" // 游客人数

	IndicatorCollectCount = "collect_count" // 取票数量
)

const (
	TimestampLayoutMake = "2006-01-02 15:04:05"
)

var TouristAreaDimensionSortChart = []string{
	DimensionRegion,
	DimensionCountry,
	DimensionProvince,
	DimensionCity,
}

var TouristAreaDimension = []string{
	DimensionRegion,
	DimensionCountry,
	DimensionProvince,
	DimensionCity,
	DimensionDistrict,
}

const (
	RoleStaff = 6 //账户类型：6 员工
)

// 明细字段
const (
	DetailFieldIdType      = "id_type"      //证件类型
	DetailFieldOperateType = "operate_type" //操作类型
)
