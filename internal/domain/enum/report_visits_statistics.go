package enum

const (
	OldReportPayPageModuleKey    = "old_report_pay_page"    // 旧报表-预订-页面
	OldReportVerifyPageModuleKey = "old_report_verify_page" // 旧报表-验证-页面
	NewReportPayPageModuleKey    = "new_report_pay_page"    // 新报表-预订-页面
	NewReportVerifyPageModuleKey = "new_report_verify_page" // 新报表-验证-页面
)

const (
	ReportVisitsStatisticsOldSortFlag = "old" //按旧的总数
	ReportVisitsStatisticsNewSortFlag = "new" //按新的总数
)

const (
	ReportVisitsStatisticsDimensionAccount     = "account"
	ReportVisitsStatisticsDimensionAccountName = "name"
)

const (
	ReportVisitsStatisticsIndicatorNewTotalCount  = "new_total_count"
	ReportVisitsStatisticsIndicatorNewPayCount    = "new_pay_count"
	ReportVisitsStatisticsIndicatorNewVerifyCount = "new_verify_count"
	ReportVisitsStatisticsIndicatorOldTotalCount  = "old_total_count"
	ReportVisitsStatisticsIndicatorOldPayCount    = "old_pay_count"
	ReportVisitsStatisticsIndicatorOldVerifyCount = "old_verify_count"
)

var DimensionFieldsMapReportVisitsStatistics = map[string]string{
	ReportVisitsStatisticsDimensionAccount:     "账号",
	ReportVisitsStatisticsDimensionAccountName: "账号名称",
}

var IndicatorFieldsMapReportVisitsStatistics = map[string]string{
	ReportVisitsStatisticsIndicatorOldTotalCount:  "旧报表查询总数",
	ReportVisitsStatisticsIndicatorOldPayCount:    "旧预订查询总数",
	ReportVisitsStatisticsIndicatorOldVerifyCount: "旧验证查询总数",
	ReportVisitsStatisticsIndicatorNewTotalCount:  "新报表查询总数",
	ReportVisitsStatisticsIndicatorNewPayCount:    "新销售查询总数",
	ReportVisitsStatisticsIndicatorNewVerifyCount: "新验证查询总数",
}

var ReportVisitsStatisticsFields = []string{
	ReportVisitsStatisticsDimensionAccount,
	ReportVisitsStatisticsDimensionAccountName,
	ReportVisitsStatisticsIndicatorNewTotalCount,
	ReportVisitsStatisticsIndicatorNewPayCount,
	ReportVisitsStatisticsIndicatorNewVerifyCount,
	ReportVisitsStatisticsIndicatorOldTotalCount,
	ReportVisitsStatisticsIndicatorOldPayCount,
	ReportVisitsStatisticsIndicatorOldVerifyCount,
}
