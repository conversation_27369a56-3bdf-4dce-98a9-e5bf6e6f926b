package enum

const (
	PayModeMoneyTestMemberId = 3265310
)

const (
	PayModeMoneyExcelSheetName = "收款明细表"
	PayModeMoneyExcelTitle     = "观山乐谷营业日报表(收款明细表)"
	PayModeMoneyExcelFileName  = "观山乐谷营业日报表"
)

const (
	PayModeMoneyProjectIdSize   = 48
	PayModeMoneyProjectIdPrefix = 8
)

const (
	PayModeMoneyDataSourcePlayPft    = 1
	PayModeMoneyDataSourceBusinessYb = 2
	PayModeMoneyDataSourceCateringYb = 3
	PayModeMoneyDataSourceRecharge   = 4
)

const (
	PayModeMoneyDataSourcePlayPftCode    = "play_pft"
	PayModeMoneyDataSourceBusinessYbCode = "business_yb"
	PayModeMoneyDataSourceCateringYbCode = "catering_yb"
	PayModeMoneyDataSourceRechargeCode   = "recharge"

	PayModeMoneyDataSourcePlayPftName    = "游乐（票付通）"
	PayModeMoneyDataSourceBusinessYbName = "商业（银豹）"
	PayModeMoneyDataSourceCateringYbName = "餐饮（银豹)"
	PayModeMoneyDataSourceRechargeName   = "充值"
)

var PayModeMoneyDataSourceNameMap = map[int]string{
	PayModeMoneyDataSourcePlayPft:    PayModeMoneyDataSourcePlayPftName,
	PayModeMoneyDataSourceBusinessYb: PayModeMoneyDataSourceBusinessYbName,
	PayModeMoneyDataSourceCateringYb: PayModeMoneyDataSourceCateringYbName,
	PayModeMoneyDataSourceRecharge:   PayModeMoneyDataSourceRechargeName,
}

var PayModeMoneyDataSourceSort = []int{
	PayModeMoneyDataSourcePlayPft,
	PayModeMoneyDataSourceBusinessYb,
	PayModeMoneyDataSourceCateringYb,
	PayModeMoneyDataSourceRecharge,
}

const (
	PayModeMoneyExternalProjectIdRechargePft        = "recharge_pft"
	PayModeMoneyExternalProjectIdRechargeYb         = "recharge_yb"
	PayModeMoneyExternalProjectIdSwimsuitYb         = "5810CDEA738B4CEA11846DABE8E1CF81"
	PayModeMoneyExternalProjectIdSalesCartYb        = "B034531F036746E6A6F3D9F18CC2E354"
	PayModeMoneyExternalProjectIdGiftShopYb         = "3F9DC14627ADD8A91AD0CA191AE56ACB"
	PayModeMoneyExternalProjectIdFarmerRestaurantYb = "F57468888323B9F1ED3D8A8B35EB6025"
	PayModeMoneyExternalProjectIdThemeRestaurantYb  = "BDF9E5E750F95B1DB874E37552A5D919"
	PayModeMoneyExternalProjectIdSnackShopYb        = "9444A8719D4418E33F7BEEA922AEE570"
	PayModeMoneyExternalProjectIdPaddyCoffeeYb      = "094DF3249EDBA7D5CFD4883597B93E3C"
)

// PayModeMoneyProjectIdSwimsuitYb         = 10000001
// PayModeMoneyProjectIdSalesCartYb        = 10000002
// PayModeMoneyProjectIdGiftShopYb         = 10000003
// PayModeMoneyProjectIdFarmerRestaurantYb = 10000004
// PayModeMoneyProjectIdThemeRestaurantYb  = 10000005
// PayModeMoneyProjectIdSnackShopYb        = 10000006
// PayModeMoneyProjectIdPaddyCoffeeYb      = 10000007
// PayModeMoneyProjectIdRechargePft        = 10000008
// PayModeMoneyProjectIdRechargeYb         = 10000009
// 通过CreateProjectId函数位运算转换后的项目id
const (
	PayModeMoneyProjectIdSwimsuitYb         = 140738128355392
	PayModeMoneyProjectIdSalesCartYb        = 140738128355456
	PayModeMoneyProjectIdGiftShopYb         = 140738128355520
	PayModeMoneyProjectIdFarmerRestaurantYb = 105553436266624
	PayModeMoneyProjectIdThemeRestaurantYb  = 105553436266656
	PayModeMoneyProjectIdSnackShopYb        = 105553436266688
	PayModeMoneyProjectIdPaddyCoffeeYb      = 105553436266720
	PayModeMoneyProjectIdRechargePft        = 140737808355584
	PayModeMoneyProjectIdRechargeYb         = 140737808355616
)

var PayModeMoneyProjectIdMap = map[string]int{
	PayModeMoneyExternalProjectIdRechargePft:        PayModeMoneyProjectIdRechargePft,
	PayModeMoneyExternalProjectIdRechargeYb:         PayModeMoneyProjectIdRechargeYb,
	PayModeMoneyExternalProjectIdSwimsuitYb:         PayModeMoneyProjectIdSwimsuitYb,
	PayModeMoneyExternalProjectIdSalesCartYb:        PayModeMoneyProjectIdSalesCartYb,
	PayModeMoneyExternalProjectIdGiftShopYb:         PayModeMoneyProjectIdGiftShopYb,
	PayModeMoneyExternalProjectIdFarmerRestaurantYb: PayModeMoneyProjectIdFarmerRestaurantYb,
	PayModeMoneyExternalProjectIdThemeRestaurantYb:  PayModeMoneyProjectIdThemeRestaurantYb,
	PayModeMoneyExternalProjectIdSnackShopYb:        PayModeMoneyProjectIdSnackShopYb,
	PayModeMoneyExternalProjectIdPaddyCoffeeYb:      PayModeMoneyProjectIdPaddyCoffeeYb,
}

var PayModeMoneyProjectIdSort = []int{
	PayModeMoneyProjectIdSwimsuitYb,
	PayModeMoneyProjectIdSalesCartYb,
	PayModeMoneyProjectIdGiftShopYb,
	PayModeMoneyProjectIdFarmerRestaurantYb,
	PayModeMoneyProjectIdThemeRestaurantYb,
	PayModeMoneyProjectIdSnackShopYb,
	PayModeMoneyProjectIdPaddyCoffeeYb,
	PayModeMoneyProjectIdRechargePft,
	PayModeMoneyProjectIdRechargeYb,
}

var PayModeMoneyProjectIdNameMap = map[int]string{
	PayModeMoneyProjectIdSwimsuitYb:         "水世界泳装店",
	PayModeMoneyProjectIdSalesCartYb:        "水世界售货车",
	PayModeMoneyProjectIdGiftShopYb:         "村趣礼品店",
	PayModeMoneyProjectIdFarmerRestaurantYb: "农夫餐厅",
	PayModeMoneyProjectIdThemeRestaurantYb:  "主题餐厅/鸡喔喔",
	PayModeMoneyProjectIdSnackShopYb:        "儿童小食店",
	PayModeMoneyProjectIdPaddyCoffeeYb:      "稻田咖啡",
	PayModeMoneyProjectIdRechargePft:        "票付通会员充值",
	PayModeMoneyProjectIdRechargeYb:         "银豹储值卡充值",
}

var PayModeMoneyProjectIdAndDataSourceMap = map[int][]int{
	PayModeMoneyDataSourceBusinessYb: {
		PayModeMoneyProjectIdSwimsuitYb,
		PayModeMoneyProjectIdSalesCartYb,
		PayModeMoneyProjectIdGiftShopYb,
	},
	PayModeMoneyDataSourceCateringYb: {
		PayModeMoneyProjectIdFarmerRestaurantYb,
		PayModeMoneyProjectIdThemeRestaurantYb,
		PayModeMoneyProjectIdSnackShopYb,
		PayModeMoneyProjectIdPaddyCoffeeYb,
	},
	PayModeMoneyDataSourceRecharge: {
		PayModeMoneyProjectIdRechargePft,
		PayModeMoneyProjectIdRechargeYb,
	},
}

const (
	PayModeMoneyTimeTypeNil     = 0
	PayModeMoneyTimeTypeMorning = 1
	PayModeMoneyTimeTypeCentre  = 2
	PayModeMoneyTimeTypeEvening = 3

	PayModeMoneyTimeTypeNilName     = ""
	PayModeMoneyTimeTypeMorningName = "早餐"
	PayModeMoneyTimeTypeCentreName  = "中餐"
	PayModeMoneyTimeTypeEveningName = "晚餐"
)

var PayModeMoneyTimeTypeNameMap = map[int]string{
	PayModeMoneyTimeTypeNil:     "",
	PayModeMoneyTimeTypeMorning: "早餐",
	PayModeMoneyTimeTypeCentre:  "中餐",
	PayModeMoneyTimeTypeEvening: "晚餐",
}

var PayModeMoneyTimeTypSort = []int{
	PayModeMoneyTimeTypeNil,
	PayModeMoneyTimeTypeMorning,
	PayModeMoneyTimeTypeCentre,
	PayModeMoneyTimeTypeEvening,
}

const (
	PayModeMoneyDimensionMerchantId                  = "merchant_id"                   // 商户ID维度字段名
	PayModeMoneyDimensionDataSource                  = "data_source"                   // 数据来源维度字段名
	PayModeMoneyDimensionProjectId                   = "project_id"                    // 项目维度字段名
	PayModeMoneyDimensionTimeType                    = "time_type"                     // 时间类型维度字段名
	PayModeMoneyDimensionEntranceCount               = "entrance_count"                // 入园人数指标字段名
	PayModeMoneyDimensionTicketRevenue               = "ticket_revenue"                // 门票收入指标字段名
	PayModeMoneyDimensionPrepaidConsumptionRevenue   = "prepaid_consumption_revenue"   // 二消收入(预付卡)指标字段名
	PayModeMoneyDimensionTotalAmount                 = "total_amount"                  // 合计金额指标字段名
	PayModeMoneyDimensionOperatingRevenue            = "operating_revenue"             // 营收金额指标字段名
	PayModeMoneyDimensionDiscountAmount              = "discount_amount"               // 折扣金额指标字段名
	PayModeMoneyDimensionAccountsReceivablePayment   = "accounts_receivable_payment"   // 挂账金额指标字段名
	PayModeMoneyDimensionEntertainmentExpensePayment = "entertainment_expense_payment" // 招待费用指标字段名
	PayModeMoneyDimensionCashPayment                 = "cash_payment"                  // 现金支付金额指标字段名
	PayModeMoneyDimensionUnionPayPayment             = "union_pay_payment"             // 银联支付金额指标字段名
	PayModeMoneyDimensionStoredValueCardPayment      = "stored_value_card_payment"     // 储值卡支付金额指标字段名
	PayModeMoneyDimensionYinbaoPayPayment            = "yinbao_pay_payment"            // 银豹付支付金额指标字段名
	PayModeMoneyDimensionRuralCommercialBankPayment  = "rural_commercial_bank_payment" // 农商行收银宝支付金额指标字段名
	PayModeMoneyDimensionCreditPayment               = "credit_payment"                // 授信支付金额指标字段名
	PayModeMoneyDimensionYibaoPayment                = "yibao_payment"                 // 易宝支付金额指标字段名
	PayModeMoneyDimensionAlipayPayment               = "alipay_payment"                // 支付宝支付金额指标字段名
	PayModeMoneyDimensionWechatPayment               = "wechat_payment"                // 微信支付金额指标字段名
	PayModeMoneyDimensionPrepaidCardPayment          = "prepaid_card_payment"          // 预付卡支付金额指标字段名
	PayModeMoneyDimensionOtherPayment                = "other_payment"                 // 其他支付金额指标字段名
	PayModeMoneyDimensionMeituanCouponPayment        = "meituan_coupon_payment"        // 美团优惠券支付金额指标字段名
	PayModeMoneyDimensionTotalIncome                 = "total_income"                  // 收入合计金额指标字段名
)

// 指标字段列表
var TemplateIndicator = []string{
	PayModeMoneyDimensionEntranceCount,
	PayModeMoneyDimensionTicketRevenue,
	PayModeMoneyDimensionPrepaidConsumptionRevenue,
	PayModeMoneyDimensionTotalAmount,
	PayModeMoneyDimensionOperatingRevenue,
	PayModeMoneyDimensionDiscountAmount,
	PayModeMoneyDimensionAccountsReceivablePayment,
	PayModeMoneyDimensionEntertainmentExpensePayment,
	PayModeMoneyDimensionCashPayment,
	PayModeMoneyDimensionUnionPayPayment,
	PayModeMoneyDimensionStoredValueCardPayment,
	PayModeMoneyDimensionYinbaoPayPayment,
	PayModeMoneyDimensionRuralCommercialBankPayment,
	PayModeMoneyDimensionCreditPayment,
	PayModeMoneyDimensionYibaoPayment,
	PayModeMoneyDimensionAlipayPayment,
	PayModeMoneyDimensionWechatPayment,
	PayModeMoneyDimensionPrepaidCardPayment,
	PayModeMoneyDimensionOtherPayment,
	PayModeMoneyDimensionMeituanCouponPayment,
	PayModeMoneyDimensionTotalIncome,
}

// 日款项合计指标
var TotalDailyPaymentsIndicator = []string{
	PayModeMoneyDimensionCashPayment,
	PayModeMoneyDimensionUnionPayPayment,
	PayModeMoneyDimensionStoredValueCardPayment,
	PayModeMoneyDimensionYinbaoPayPayment,
	PayModeMoneyDimensionRuralCommercialBankPayment,
	PayModeMoneyDimensionCreditPayment,
	PayModeMoneyDimensionYibaoPayment,
	PayModeMoneyDimensionAlipayPayment,
	PayModeMoneyDimensionWechatPayment,
	PayModeMoneyDimensionPrepaidCardPayment,
	PayModeMoneyDimensionOtherPayment,
	PayModeMoneyDimensionMeituanCouponPayment,
	PayModeMoneyDimensionTotalIncome,
}

const (
	YiBaoTicketTypeSell   = "SELL"
	YiBaoTicketTypeReturn = "SELL_RETURN"
)

var PayModeMoneyProjectAndPayModeMap = map[int]map[string][]string{
	PayModeMoneyProjectIdRechargePft: {
		PayModeMoneyDimensionCashPayment:   []string{"1101"},
		PayModeMoneyDimensionYibaoPayment:  []string{"2601"},
		PayModeMoneyDimensionAlipayPayment: []string{"1201"},
		PayModeMoneyDimensionWechatPayment: []string{"1202"},
	},
	PayModeMoneyProjectIdRechargeYb: {
		PayModeMoneyDimensionCashPayment:                []string{"现金"},
		PayModeMoneyDimensionUnionPayPayment:            []string{"银联卡"},
		PayModeMoneyDimensionStoredValueCardPayment:     []string{"会员储值卡"},
		PayModeMoneyDimensionYinbaoPayPayment:           []string{"银豹付"},
		PayModeMoneyDimensionRuralCommercialBankPayment: []string{"农商行收银宝"},
	},
	PayModeMoneyProjectIdSwimsuitYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_112"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_111"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_113"},
	},
	PayModeMoneyProjectIdSalesCartYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_110"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_109"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_111"},
	},
	PayModeMoneyProjectIdGiftShopYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_111"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_110"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_112"},
	},
	PayModeMoneyProjectIdFarmerRestaurantYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_111"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_110"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_112"},
		PayModeMoneyDimensionMeituanCouponPayment:        []string{"payCode_-5000"},
	},
	PayModeMoneyProjectIdThemeRestaurantYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_110"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_109"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_111"},
		PayModeMoneyDimensionMeituanCouponPayment:        []string{"payCode_-5000"},
	},
	PayModeMoneyProjectIdSnackShopYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_110"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_109"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_111"},
		PayModeMoneyDimensionMeituanCouponPayment:        []string{"payCode_-5000"},
	},
	PayModeMoneyProjectIdPaddyCoffeeYb: {
		PayModeMoneyDimensionAccountsReceivablePayment:   []string{"payCode_110"},
		PayModeMoneyDimensionEntertainmentExpensePayment: []string{"payCode_109"},
		PayModeMoneyDimensionCashPayment:                 []string{"payCode_1"},
		PayModeMoneyDimensionUnionPayPayment:             []string{"payCode_3"},
		PayModeMoneyDimensionStoredValueCardPayment:      []string{"payCode_2"},
		PayModeMoneyDimensionYinbaoPayPayment:            []string{"payCode_-2900", "payCode_-2901", "payCode_-2902", "payCode_-2903", "payCode_-2904", "payCode_-2906"},
		PayModeMoneyDimensionRuralCommercialBankPayment:  []string{"payCode_111"},
		PayModeMoneyDimensionMeituanCouponPayment:        []string{"payCode_-5000"},
	},
}

// PayModeMoneyProjectAndTimeTypeMap 项目对应的时间类型分类是否启用
var PayModeMoneyProjectAndTimeTypeMap = map[int]map[int][]string{
	PayModeMoneyProjectIdFarmerRestaurantYb: {
		PayModeMoneyTimeTypeCentre:  []string{"00:00:00", "15:59:59"},
		PayModeMoneyTimeTypeEvening: []string{"16:00:00", "23:59:59"},
	},
	PayModeMoneyProjectIdThemeRestaurantYb: {
		PayModeMoneyTimeTypeCentre:  []string{"00:00:00", "15:59:59"},
		PayModeMoneyTimeTypeEvening: []string{"16:00:00", "23:59:59"},
	},
}
