package enum

const (
	DimensionToTagSourceMapKeyTagCenter = 1 //来源：标签中心
	DimensionToTagSourceMapKeyGroup     = 2 //来源：集团
	DimensionToTagSourceMapKeyAge       = 3 //来源：年龄
)

// 维度映射标签来源
var DimensionToTagSourceMap = map[string]int{
	DimensionSpu:          DimensionToTagSourceMapKeyTagCenter,
	DimensionSku:          DimensionToTagSourceMapKeyTagCenter,
	DimensionSaleChannel:  DimensionToTagSourceMapKeyTagCenter,
	DimensionPayMode:      DimensionToTagSourceMapKeyTagCenter,
	DimensionGroupAccount: DimensionToTagSourceMapKeyGroup,
	DimensionAgeGroup:     DimensionToTagSourceMapKeyAge,
}

// 标签中心场景映射标签来源
var TagCenterSceneToTagSourceMap = map[string]int{
	DimensionToTagCenterSceneMapKeyProduct:     DimensionToTagSourceMapKeyTagCenter,
	DimensionToTagCenterSceneMapKeyTicket:      DimensionToTagSourceMapKeyTagCenter,
	DimensionToTagCenterSceneMapKeySaleChannel: DimensionToTagSourceMapKeyTagCenter,
	DimensionToTagCenterSceneMapKeyPayMode:     DimensionToTagSourceMapKeyTagCenter,
}
