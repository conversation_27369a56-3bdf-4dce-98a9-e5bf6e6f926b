package enum

const (
	DiscountPoint        int = 1 //积分
	DiscountCoupons      int = 2 //优惠券
	DiscountMemberRebate int = 3 //会员折扣
	DiscountDeduct       int = 4 //结算减免
	DiscountSpecial      int = 5 //结算特价
	DiscountFirstOrder   int = 6 //首单立减
	DiscountFlashSale    int = 7 //限时抢购
)

//报表标识游客优惠
const TouristDiscount string = "discount"

//报表标识分销优惠
const SettlementDiscount string = "settlement_discount"

//游客优惠类型
var TouristDiscountType = []int{
	DiscountPoint,
	DiscountCoupons,
	DiscountMemberRebate,
	DiscountFirstOrder,
	DiscountFlashSale,
}

//分销优惠类型
var SettlementDiscountType = []int{
	DiscountDeduct,
	DiscountSpecial,
}
