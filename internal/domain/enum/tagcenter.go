package enum

const (
	DimensionToTagCenterSceneMapKeyProduct     = "product"     // 产品
	DimensionToTagCenterSceneMapKeyTicket      = "ticket"      // 门票
	DimensionToTagCenterSceneMapKeySaleChannel = "saleChannel" // 销售渠道
	DimensionToTagCenterSceneMapKeyPayMode     = "payMode"     // 支付方式
)

// 维度字段与标签中心场景映射
var DimensionToTagCenterSceneMap = map[string]string{
	DimensionSpu:         DimensionToTagCenterSceneMapKeyProduct,
	DimensionSku:         DimensionToTagCenterSceneMapKeyTicket,
	DimensionSaleChannel: DimensionToTagCenterSceneMapKeySaleChannel,
	DimensionPayMode:     DimensionToTagCenterSceneMapKeyPayMode,
}

// 标签中心场景与维度字段映射
var DimensionToTagCenterSceneMapReverse = map[string]string{
	DimensionToTagCenterSceneMapKeyProduct:     DimensionSpu,
	DimensionToTagCenterSceneMapKeyTicket:      DimensionSku,
	DimensionToTagCenterSceneMapKeySaleChannel: DimensionSaleChannel,
	DimensionToTagCenterSceneMapKeyPayMode:     DimensionPayMode,
}

// 维度类型与标签中心场景映射
var DimensionTypeToTagCenterSceneMap = map[int]string{
	MerchantSchemeDimensionTypeSpu:         DimensionToTagCenterSceneMapKeyProduct,
	MerchantSchemeDimensionTypeSku:         DimensionToTagCenterSceneMapKeyTicket,
	MerchantSchemeDimensionTypePayMode:     DimensionToTagCenterSceneMapKeyPayMode,
	MerchantSchemeDimensionTypeSaleChannel: DimensionToTagCenterSceneMapKeySaleChannel,
}
