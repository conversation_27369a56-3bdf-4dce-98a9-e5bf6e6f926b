package enum

import "time"

const (
	// CacheTimeImmediate 无缓存，立即过期
	CacheTimeImmediate time.Duration = 0

	// CacheTimeSecond 1秒缓存
	CacheTimeSecond time.Duration = time.Second

	// CacheTimeMinute 1分钟缓存
	CacheTimeMinute time.Duration = time.Minute

	// CacheTimeHour 1小时缓存
	CacheTimeHour time.Duration = time.Hour

	// CacheTimeHalfDay 12小时缓存
	CacheTimeHalfDay time.Duration = 12 * time.Hour

	// CacheTimeDay 1天缓存
	CacheTimeDay time.Duration = 24 * time.Hour

	// CacheTimeWeek 1周缓存
	CacheTimeWeek time.Duration = 7 * 24 * time.Hour

	// CacheTimeMonth 1月缓存(按30天计算)
	CacheTimeMonth time.Duration = 30 * 24 * time.Hour

	// CacheTimeYear 1年缓存(按365天计算)
	CacheTimeYear time.Duration = 365 * 24 * time.Hour
)
