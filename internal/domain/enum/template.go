package enum

const (
	TemplateCategoryOperationData     = 1 // 营运数据报表
	TemplateCategoryH5Pay             = 2 // 移动端销售报表
	TemplateCategoryPay               = 3 // 销售报表
	TemplateCategoryVerify            = 4 // 验证报表
	TemplateCategoryH5Verify          = 5 // 移动端验证报表
	TemplateCategoryTouristSourceArea = 6 // 多维统计/客源地
	TemplateCategoryGroupPay          = 7 //集团报表-销售
	TemplateCategoryGroupVerify       = 8 //集团报表-验证

	TemplateExtOptionUseDimensionSchemeGroup = "use_scheme" // 使用维度方案分组

	TemplateMultipleModeSourceArea = "source_area" //多维数据年龄标识
	TemplateMultipleModeAge        = "age"         //多维数据年龄标识
	TemplateMultipleModeGender     = "gender"      //多维数据性别标识

	//模板配置key
	TemplateDefinitionTitle               = "title"                 //模板名称
	TemplateDefinitionMultipleMode        = "multiple_mode"         //多维数据模式
	TemplateDefinitionDimension           = "dimension"             //模板维度
	TemplateDefinitionIndicator           = "indicator"             //模板指标
	TemplateDefinitionSpecialDimension    = "special_dimension"     //模板特殊维度范围
	TemplateDefinitionSpecialProductRules = "special_product_rules" //模板特殊产品统计规则
	TemplateDefinitionAgeGroup            = "age_group"             //模板年龄段配置
	TemplateDefinitionOperateType         = "operate_type"          //模板操作类型
)

var TemplateDimensionFieldsMapCommon = map[string]string{
	DimensionDate:          "日期",
	DimensionSaleChannel:   "订单渠道",
	DimensionDistributor:   "分销商",
	DimensionSpu:           "产品",
	DimensionSku:           "票种",
	DimensionPayMode:       "支付方式",
	DimensionSaleUnitPrice: "销售单价",
}

var TemplateIndicatorFieldsMapCommon = map[string]string{
	IndicatorPayCount:        "预订数量",
	IndicatorPaySalePrice:    "销售金额",
	IndicatorActualSaleCount: "实售数量",
	IndicatorActualSalePrice: "实售金额",
	IndicatorVerifyCount:     "核销数量",
	IndicatorVerifySalePrice: "核销金额",
	IndicatorCancelCount:     "取消数量",
	IndicatorCancelSalePrice: "取消金额",
	IndicatorRevokeCount:     "撤销撤改数量",
	IndicatorRevokeSalePrice: "撤销撤改金额",
	IndicatorAfterSaleCount:  "售后数量",
	IndicatorAfterSalePrice:  "售后金额",
}

var TemplateDimensionFieldsMapPay = map[string]string{
	DimensionDate:           "日期",
	DimensionSpu:            "产品",
	DimensionSku:            "票类",
	DimensionPayMode:        "支付方式",
	DimensionSaleChannel:    "订单渠道",
	DimensionDistributor:    "分销商",
	DimensionSellOperator:   "售票员",
	DimensionSellSite:       "售票站点",
	DimensionCostUnitPrice:  "采购单价",
	DimensionSaleUnitPrice:  "销售单价",
	DimensionTargetAudience: "适用人群",
	DimensionGroupMember:    "集团成员",
}

var TemplateIndicatorFieldsMapPay = map[string]string{
	IndicatorPayCount:        "销售数量",
	IndicatorPaySalePrice:    "销售金额",
	IndicatorPayCostPrice:    "采购金额",
	IndicatorCancelCount:     "取消数量",
	IndicatorCancelSalePrice: "取消支出金额",
	IndicatorCancelCostPrice: "取消收入金额",
	IndicatorRevokeCount:     "撤销/撤改数量",
	IndicatorRevokeSalePrice: "撤销/撤改支出金额",
	IndicatorRevokeCostPrice: "撤销/撤改收入金额",
	IndicatorActualSaleCount: "净销售数量",
	IndicatorActualSalePrice: "净销售金额",
	IndicatorActualCostPrice: "净采购金额",
	IndicatorActualProfit:    "利润",
	IndicatorRefundFeeProfit: "退票手续费",
	IndicatorAfterSaleCount:  "售后数量",
	IndicatorAfterSalePrice:  "售后支出金额",
	IndicatorAfterCostPrice:  "售后收入金额",
}

var TemplateDimensionFieldsMapVerify = map[string]string{
	DimensionDate:           "日期",
	DimensionSpu:            "产品",
	DimensionSku:            "票类",
	DimensionPayMode:        "支付方式",
	DimensionSaleChannel:    "订单渠道",
	DimensionDistributor:    "分销商",
	DimensionSellOperator:   "售票员",
	DimensionSellSite:       "售票站点",
	DimensionOperator:       "操作员",
	DimensionCostUnitPrice:  "采购单价",
	DimensionSaleUnitPrice:  "销售单价",
	DimensionTargetAudience: "适用人群",
	DimensionGroupMember:    "集团成员",
}

var TemplateIndicatorFieldsMapVerify = map[string]string{
	IndicatorVerifyCount:           "验证数量",
	IndicatorVerifySalePrice:       "验证金额",
	IndicatorVerifyCostPrice:       "采购金额",
	IndicatorRevokeCount:           "撤销/撤改数量",
	IndicatorRevokeSalePrice:       "撤销/撤改支出金额",
	IndicatorRevokeCostPrice:       "撤销/撤改收入金额",
	IndicatorActualVerifyCount:     "净验证数量",
	IndicatorActualVerifySalePrice: "净验证金额",
	IndicatorActualVerifyCostPrice: "净采购金额",
	IndicatorActualVerifyProfit:    "利润",
	IndicatorRefundFeeProfit:       "退票手续费",
	IndicatorAfterSaleCount:        "售后数量",
	IndicatorAfterSalePrice:        "售后支出金额",
	IndicatorAfterCostPrice:        "售后收入金额",
}

var TemplateDimensionFieldsMapTouristSourceArea = map[string]string{
	DimensionRegion:   "地域",
	DimensionCountry:  "国家",
	DimensionProvince: "省",
	DimensionCity:     "市",
	DimensionDistrict: "区",
}

var TemplateDimensionFieldsMapTouristGender = map[string]string{
	DimensionGender: "性别",
}

var TemplateDimensionFieldsMapTouristAge = map[string]string{
	DimensionAgeGroup: "年龄段",
}
var TemplateIndicatorFieldsMapTourist = map[string]string{
	IndicatorTouristCount: "人数",
}

// 模板无需验证的维度字段
var TemplateDimensionFieldsMapNoValidate = map[string]string{
	DimensionGroupAccount: "集团账号",
}

// 集团报表模板分类
var TemplateCategoryIsGroupReport = []int{
	TemplateCategoryGroupPay,
	TemplateCategoryGroupVerify,
}

// 模板查询不需要商户ID
var TemplateQueryNoNeedMerchantId = []int{
	TemplateCategoryGroupPay,
	TemplateCategoryGroupVerify,
}

// 模板查询不需要商户ID查询的商户类型维度字段映射
// 如集团账号不需要商户ID查询，但是通过标签汇总配置的商户id查询，
// 这时标签汇总的维度字段对应的group_account字段，需要将其带入查询的维度字段了，特别是列表总计查询
var QueryNoNeedMerchantIdDimensionMap = map[int][]string{
	TemplateCategoryGroupPay:    {DimensionGroupAccount},
	TemplateCategoryGroupVerify: {DimensionGroupAccount},
}
