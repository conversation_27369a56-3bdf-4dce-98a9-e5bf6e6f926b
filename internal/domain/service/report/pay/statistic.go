package pay

import (
	"gitee.com/golang-module/carbon/v2"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func Statistics(
	merchantId int,
	reportType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	doDimensionRange logicdmcommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
) (statistics DoIndicator, err error) {
	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	doStatistics, err := logicdmcommon.Statistics(logicdmcommon.DoQueryParams{
		DoTemplate:     doTemplate,
		MerchantId:     merchantId,
		ReportType:     reportType,
		StartTime:      startTime,
		EndTime:        endTime,
		DimensionRange: doDimensionRange,
		DataLimit:      &dataLimit,
	})
	if err != nil {
		return
	}
	statistics.calculateIndicatorFields(doStatistics, doTemplate.Indicator)

	return
}
