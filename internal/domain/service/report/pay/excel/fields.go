package excel

import (
	"report-service/internal/domain/enum"
	commonExcel "report-service/internal/domain/service/report/common/excel"
)

var TemplateDimensionFieldsToResultMap = map[string]struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	enum.DimensionDate:           {Field: commonExcel.FieldDate, Name: "日期"},
	enum.DimensionSpu:            {Field: commonExcel.FieldSpuId, Name: "产品"},
	enum.DimensionSku:            {Field: commonExcel.FieldSkuId, Name: "票类"},
	enum.DimensionPayMode:        {Field: commonExcel.FieldSalePayMode, Name: "支付方式"},
	enum.DimensionSaleChannel:    {Field: commonExcel.FieldSaleChannel, Name: "订单渠道"},
	enum.DimensionDistributor:    {Field: commonExcel.FieldDistributorId, Name: "分销商"},
	enum.DimensionSellOperator:   {Field: commonExcel.FieldSellOperatorId, Name: "售票员"},
	enum.DimensionSellSite:       {Field: commonExcel.FieldSellSiteId, Name: "售票站点"},
	enum.DimensionCostUnitPrice:  {Field: commonExcel.FieldCostUnitPrice, Name: "采购单价"},
	enum.DimensionSaleUnitPrice:  {Field: commonExcel.FieldSaleUnitPrice, Name: "销售单价"},
	enum.DimensionTargetAudience: {Field: commonExcel.FieldTargetAudience, Name: "适用人群"},
}

var TemplateIndicatorFieldsToResultMap = map[string]struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	enum.IndicatorPayCount:        {Field: commonExcel.FieldPayCount, Name: "销售数量"},
	enum.IndicatorPayCostPrice:    {Field: commonExcel.FieldPayCostPrice, Name: "采购金额"},
	enum.IndicatorPaySalePrice:    {Field: commonExcel.FieldPaySalePrice, Name: "销售金额"},
	enum.IndicatorCancelCount:     {Field: commonExcel.FieldCancelCount, Name: "取消数量"},
	enum.IndicatorCancelCostPrice: {Field: commonExcel.FieldCancelCostPrice, Name: "取消收入金额"},
	enum.IndicatorCancelSalePrice: {Field: commonExcel.FieldCancelSalePrice, Name: "取消支出金额"},
	enum.IndicatorRevokeCount:     {Field: commonExcel.FieldRevokeCount, Name: "撤销/撤改数"},
	enum.IndicatorRevokeCostPrice: {Field: commonExcel.FieldRevokeCostPrice, Name: "撤销/撤改收入金额"},
	enum.IndicatorRevokeSalePrice: {Field: commonExcel.FieldRevokeSalePrice, Name: "撤销/撤改支出金额"},
	enum.IndicatorAfterSaleCount:  {Field: commonExcel.FieldAfterSaleCount, Name: "售后数量"},
	enum.IndicatorAfterCostPrice:  {Field: commonExcel.FieldAfterCostPrice, Name: "售后收入金额"},
	enum.IndicatorAfterSalePrice:  {Field: commonExcel.FieldAfterSalePrice, Name: "售后支出金额"},
	enum.IndicatorActualSaleCount: {Field: commonExcel.FieldActualSaleCount, Name: "净销售数量"},
	enum.IndicatorActualSalePrice: {Field: commonExcel.FieldActualSalePrice, Name: "净销售金额"},
	enum.IndicatorActualCostPrice: {Field: commonExcel.FieldActualCostPrice, Name: "净采购金额"},
	enum.IndicatorActualProfit:    {Field: commonExcel.FieldActualProfit, Name: "利润"},
	enum.IndicatorRefundFeeProfit: {Field: commonExcel.FieldRefundFeeProfit, Name: "退票手续费"},
	enum.IndicatorCollectCount:    {Field: commonExcel.FieldCollectCount, Name: "取票数量"},
}

var DimensionIdAndNameMap = map[string]string{
	commonExcel.FieldDate:               commonExcel.FieldDate,
	commonExcel.FieldCostUnitPrice:      commonExcel.FieldCostUnitPrice,
	commonExcel.FieldSaleUnitPrice:      commonExcel.FieldSaleUnitPrice,
	commonExcel.FieldDistributorId:      commonExcel.FieldDistributorName,
	commonExcel.FieldSpuId:              commonExcel.FieldSpuName,
	commonExcel.FieldSkuId:              commonExcel.FieldSkuName,
	commonExcel.FieldSaleChannel:        commonExcel.FieldSaleChannelName,
	commonExcel.FieldSalePayMode:        commonExcel.FieldSalePayModeName,
	commonExcel.FieldSellOperatorId:     commonExcel.FieldSellOperatorName,
	commonExcel.FieldSellSiteId:         commonExcel.FieldSellSiteName,
	commonExcel.FieldTargetAudience:     commonExcel.FieldTargetAudienceName,
	commonExcel.FieldSpuTagCode:         commonExcel.FieldSpuTagName,
	commonExcel.FieldSkuTagCode:         commonExcel.FieldSkuTagName,
	commonExcel.FieldPayModeTagCode:     commonExcel.FieldPayModeTagName,
	commonExcel.FieldSaleChannelTagCode: commonExcel.FieldSaleChannelTagName,
}

var PriceFields = []string{
	commonExcel.FieldCostUnitPrice,
	commonExcel.FieldSaleUnitPrice,
	commonExcel.FieldPaySalePrice,
	commonExcel.FieldPayCostPrice,
	commonExcel.FieldCancelSalePrice,
	commonExcel.FieldCancelCostPrice,
	commonExcel.FieldRevokeSalePrice,
	commonExcel.FieldRevokeCostPrice,
	commonExcel.FieldActualSalePrice,
	commonExcel.FieldActualCostPrice,
	commonExcel.FieldActualProfit,
	commonExcel.FieldRefundFeeProfit,
	commonExcel.FieldAfterSalePrice,
	commonExcel.FieldAfterCostPrice,
}

var DimensionToTagCenterSceneAndResultTagCodeMap = map[string]struct {
	SceneKey string
	TagKey   string
}{
	enum.DimensionSpu:         {enum.DimensionToTagCenterSceneMapKeyProduct, commonExcel.FieldSpuTagCode},
	enum.DimensionSku:         {enum.DimensionToTagCenterSceneMapKeyTicket, commonExcel.FieldSkuTagCode},
	enum.DimensionSaleChannel: {enum.DimensionToTagCenterSceneMapKeySaleChannel, commonExcel.FieldSaleChannelTagCode},
	enum.DimensionPayMode:     {enum.DimensionToTagCenterSceneMapKeyPayMode, commonExcel.FieldPayModeTagCode},
}
