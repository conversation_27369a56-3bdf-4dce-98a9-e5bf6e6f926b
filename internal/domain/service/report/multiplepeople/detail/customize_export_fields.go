package detail

import (
	"report-service/internal/domain/service/report/common/customizeexportfields/types"
	"report-service/internal/domain/service/report/common/multipledetail"
)

// Permission 权限配置 是否主账号和员工账号配置隔离
var Permission = types.DoExportPermission{
	AccountIsolation: true,
}

// Definition 基础配置
var Definition = []types.DoExportDefinitionItem{
	{
		Title: "基础信息",
		List: []types.DoExportDefinitionList{
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldBusinessCode, Title: "门票码"}, Required: true},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldOperatedAt, Title: "操作时间"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldOperateType, Title: "操作类型"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldOperator, Title: "操作人员"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldOperateSite, Title: "操作站点"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldCount, Title: "计数"}, Required: false},
		},
	},
	{
		Title: "订单信息",
		List: []types.DoExportDefinitionList{
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldOrderNum, Title: "订单号"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldSpu, Title: "产品"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldSku, Title: "票种"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldOrderChannel, Title: "下单渠道"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldResellerGroup, Title: "分销商分组"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldReseller, Title: "分销商"}, Required: false},
		},
	},
	{
		Title: "门票相关人员信息",
		List: []types.DoExportDefinitionList{
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldIdType, Title: "证件类型"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldIdNumber, Title: "证件号"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldRegion, Title: "地域"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldCountry, Title: "国家"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldProvince, Title: "省"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldCity, Title: "市"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldDistrict, Title: "区"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldGender, Title: "性别"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldAge, Title: "年龄"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldNickname, Title: "游客姓名"}, Required: false},
			{KeyValuePair: types.KeyValuePair{Key: multipledetail.FieldMobile, Title: "手机号"}, Required: false},
		},
	},
}

type CustomizeExportDetailTouristMultiple struct{}

func (c *CustomizeExportDetailTouristMultiple) GetPermission() types.DoExportPermission {
	return Permission
}

func (c *CustomizeExportDetailTouristMultiple) GetDefinition() types.DoExportDefinition {
	return Definition
}
