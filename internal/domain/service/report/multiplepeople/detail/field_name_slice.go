package detail

import (
	"report-service/internal/domain/service/report/common/multipledetail"
)

var FieldNameSlice = []struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	{Field: multipledetail.FieldBusinessCode, Name: "门票码"},
	{Field: multipledetail.FieldSerialNumber, Name: "序号"},
	{Field: multipledetail.FieldOperatedAt, Name: "操作时间"},
	{Field: multipledetail.FieldOperateType, Name: "操作类型"},
	{Field: multipledetail.FieldOperator, Name: "操作人员"},
	{Field: multipledetail.FieldOperateSite, Name: "操作站点"},
	{Field: multipledetail.FieldCount, Name: "计数"},
	{Field: multipledetail.FieldOrderNum, Name: "订单号"},
	{Field: multipledetail.FieldSpu, Name: "产品"},
	{Field: multipledetail.FieldSku, Name: "票种"},
	{Field: multipledetail.FieldOrderChannel, Name: "下单渠道"},
	{Field: multipledetail.FieldResellerGroup, Name: "分销商分组"},
	{Field: multipledetail.FieldReseller, Name: "分销商"},
	{Field: multipledetail.FieldIdType, Name: "证件类型"},
	{Field: multipledetail.FieldIdNumber, Name: "证件号"},
	{Field: multipledetail.FieldRegion, Name: "地域"},
	{Field: multipledetail.FieldCountry, Name: "国家"},
	{Field: multipledetail.FieldProvince, Name: "省"},
	{Field: multipledetail.FieldCity, Name: "市"},
	{Field: multipledetail.FieldDistrict, Name: "区"},
	{Field: multipledetail.FieldGender, Name: "性别"},
	{Field: multipledetail.FieldAge, Name: "年龄"},
}
