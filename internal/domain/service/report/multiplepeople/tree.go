package multiplepeople

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"sort"
)

type TreeNode struct {
	Region   string      `json:"region"`
	Name     string      `json:"name"`
	Count    int         `json:"count"`
	Children []*TreeNode `json:"children,omitempty"`
}

func buildTreeWithCount(list []DoPaginationItem, dimension []string) []*TreeNode {
	rootMap := make(map[string]*TreeNode)
	//重置字段排序
	fieldOrder := []string{}
	for _, field := range enum.TouristAreaDimensionSortChart {
		if !utils.Container(dimension, field) {
			continue
		}
		fieldOrder = append(fieldOrder, field)
	}
	findOrCreateChild := func(parentNode *TreeNode, fieldKey string, fieldVal string, fieldNum int) *TreeNode {
		for _, child := range parentNode.Children {
			if child.Region == fieldKey {
				child.Count += fieldNum
				return child
			}
		}
		newNode := nodeCreate(fieldKey, fieldVal, fieldNum)
		parentNode.Children = append(parentNode.Children, newNode)
		return newNode
	}
	for _, item := range list {
		var currentNode *TreeNode
		for i, fieldName := range fieldOrder {
			fieldNum := *item.Count
			fieldKey, fieldVal := getFieldKeyAndVal(fieldName, item)
			if fieldKey == nil || fieldVal == nil {
				return nil
			}
			if i == 0 {
				if _, ok := rootMap[*fieldKey]; !ok {
					rootMap[*fieldKey] = nodeCreate(*fieldKey, *fieldVal, fieldNum)
				} else {
					rootMap[*fieldKey].Count += fieldNum
				}
				currentNode = rootMap[*fieldKey]
			} else {
				// 在父节点下查找或创建子节点
				currentNode = findOrCreateChild(currentNode, *fieldKey, *fieldVal, fieldNum)
			}
		}
	}
	// 将根节点转换为切片并返回
	tree := make([]*TreeNode, 0, len(rootMap))
	for _, node := range rootMap {
		tree = append(tree, node)
	}
	// 对每个根节点的子树进行递归排序
	sortTreeDescending(tree)
	return tree
}

func getFieldKeyAndVal(fieldName string, item DoPaginationItem) (fieldKey *string, fieldVal *string) {
	switch fieldName {
	case enum.DimensionRegion:
		fieldKey = item.Region
		fieldVal = item.RegionName
	case enum.DimensionCountry:
		fieldKey = item.Country
		fieldVal = item.CountryName
	case enum.DimensionProvince:
		fieldKey = item.Province
		fieldVal = item.ProvinceName
	case enum.DimensionCity:
		fieldKey = item.City
		fieldVal = item.CityName
	case enum.DimensionDistrict:
		fieldKey = item.District
		fieldVal = item.DistrictName
	default:
		global.LOG.Error(fmt.Sprintf("转换树结构，不存在值%s，取值失败", fieldName))
	}
	return
}

func nodeCreate(fieldKey string, fieldVal string, fieldNum int) *TreeNode {
	newNode := &TreeNode{
		Region:   fieldKey,
		Name:     fieldVal,
		Count:    fieldNum,
		Children: []*TreeNode{},
	}
	return newNode
}

func sortTreeDescending(nodes []*TreeNode) {
	// 当前层排序
	sort.Slice(nodes, func(i, j int) bool {
		// 先按数量排序，数量相同再按区域编号排序
		if nodes[i].Count == nodes[j].Count {
			return nodes[i].Region < nodes[j].Region
		}
		return nodes[i].Count > nodes[j].Count
	})

	// 递归子节点继续排序
	for _, node := range nodes {
		if len(node.Children) > 0 {
			sortTreeDescending(node.Children)
		}
	}
}
