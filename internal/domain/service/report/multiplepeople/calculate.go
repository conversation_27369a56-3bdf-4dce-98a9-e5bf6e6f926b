package multiplepeople

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/tourist"
	"report-service/pkg/utils"
)

type DoIndicator struct {
	Count *int `json:"count"` //游客人数
}

func (i *DoIndicator) calculateIndicatorFields(
	indicator tourist.DoIndicator,
	templateIndicator []string,
) {
	count := indicator.Count
	for _, item := range templateIndicator {
		switch item {
		case enum.IndicatorTouristCount:
			i.Count = utils.Add(i.Count, &count)
		}
	}
}
