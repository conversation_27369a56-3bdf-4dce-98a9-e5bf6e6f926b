package multiplepeople

import (
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/pkg/utils"
)

type DoResponseList struct {
	List        []DoPaginationItem
	SubjectType int
}

func (d *DoResponseList) Response() []DoPaginationItem {
	switch d.SubjectType {
	case enum.TouristSubjectTypeRegion:
		d.List = d.responseRegion()
	case enum.TouristSubjectTypeGender:
		d.List = d.responseGender()
	case enum.TouristSubjectTypeAgeChart, enum.TouristSubjectTypeGenderChart, enum.TouristSubjectTypeAreaChart:
		d.List = d.responseChart()
	}
	return d.List
}

// 地域固定格式输出
func (d *DoResponseList) responseRegion() []DoPaginationItem {
	var newList []DoPaginationItem
	for _, s := range enum.RegionKeySort {
		isExist := false
		for _, item := range d.List {
			if s != cast.ToInt(*item.Region) {
				continue
			}
			newList = append(newList, item)
			isExist = true
		}
		if !isExist {
			var item DoPaginationItem
			region := cast.ToString(s)
			item.Region = &region
			regionName := enum.RegionKeyNameMap[s]
			item.RegionName = &regionName
			count := 0
			item.Count = &count
			newList = append(newList, item)
		}
	}
	return newList
}

// 性别固定格式输出
func (d *DoResponseList) responseGender() []DoPaginationItem {
	var newList []DoPaginationItem
	for _, s := range enum.GenderKeySort {
		isExist := false
		for _, item := range d.List {
			if s != *item.Gender {
				continue
			}
			newList = append(newList, item)
			isExist = true
		}
		if !isExist {
			var item DoPaginationItem
			item.Gender = &s
			genderName := enum.GenderKeyNameMap[s]
			item.GenderName = &genderName
			count := 0
			item.Count = &count
			newList = append(newList, item)
		}
	}
	return newList
}

// 图表输出格式化
func (d *DoResponseList) responseChart() []DoPaginationItem {
	list := utils.SliceToMap(d.List)
	filterOutZeroCountItems(&list)
	handleAbsCountItems(&list)
	if list == nil || len(list) == 0 {
		return []DoPaginationItem{}
	}
	return utils.MapToSlice(list)
}

// 过滤为0的数据
func filterOutZeroCountItems(list *map[int]DoPaginationItem) {
	for key, doPaginationItem := range *list {
		if doPaginationItem.Count != nil && *doPaginationItem.Count == 0 {
			delete(*list, key)
		}
	}
	return
}

// 处理指标为绝对值
func handleAbsCountItems(list *map[int]DoPaginationItem) {
	for _, doPaginationItem := range *list {
		if doPaginationItem.Count != nil && *doPaginationItem.Count < 0 {
			count := utils.Abs(doPaginationItem.Count)
			*doPaginationItem.Count = *count
		}
	}
	return
}

func ResponseHandler(list []DoPaginationItem, subjectType int) *DoResponseList {
	return &DoResponseList{
		List:        list,
		SubjectType: subjectType,
	}
}
