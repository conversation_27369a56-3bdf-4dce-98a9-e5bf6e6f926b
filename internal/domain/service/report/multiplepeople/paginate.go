package multiplepeople

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	logicdmturist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

type DoPaginationItem struct {
	logicdmturist.DoDimension
	DoIndicator
}

func Paginate(
	merchantId int,
	reportType int,
	subjectType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	doDimensionRange logicdmcommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
	page, pageSize int,
) (list []DoPaginationItem, total int, err error) {
	doList, total, err := logicdmturist.Paginate(doTemplate, merchantId, reportType, subjectType, startTime, endTime,
		doDimensionRange, dataLimit, page, pageSize)
	if err != nil {
		return
	}
	list = make([]DoPaginationItem, 0)
	for _, doItem := range doList {
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		item.calculateIndicatorFields(doItem.DoIndicator, []string{enum.IndicatorTouristCount})
		list = append(list, item)
	}
	//返回格式兼容处理
	list = ResponseHandler(list, subjectType).Response()
	return
}
