package multiplepeople

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	logicdmtourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

func Statistics(
	merchantId int,
	memberId int,
	reportType int,
	templateId int,
	startTime, endTime carbon.Carbon,
	doDimensionRange logicdmcommon.DoDimensionRange,
) (statistics DoIndicator, err error) {
	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(merchantId, memberId)
	if check, checkErr := dataLimit.AllLimit(); check {
		err = checkErr
		return
	}
	doTemplate, err := template.Detail(templateId, enum.TemplateCategoryTouristSourceArea, merchantId, memberId)
	if err != nil {
		return
	}
	doStatistics, err := logicdmtourist.Statistics(*doTemplate, merchantId, reportType, startTime, endTime, doDimensionRange, dataLimit)
	if err != nil {
		return
	}
	statistics.calculateIndicatorFields(doStatistics, doTemplate.Indicator)
	return
}
