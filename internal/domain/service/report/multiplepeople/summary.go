package multiplepeople

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	logicdmturist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

func SummaryTree(
	merchantId int,
	reportType int,
	subjectType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	doDimensionRange logicdmcommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
) (tree []*TreeNode, err error) {
	doList, err := logicdmturist.SummaryTree(doTemplate, merchantId, reportType, subjectType, startTime, endTime,
		doDimensionRange, dataLimit)
	if err != nil {
		return
	}
	list := make([]DoPaginationItem, 0)
	for _, doItem := range doList {
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		item.calculateIndicatorFields(doItem.DoIndicator, []string{enum.IndicatorTouristCount})
		list = append(list, item)
	}
	//返回格式兼容处理
	list = ResponseHandler(list, subjectType).Response()
	dimension, err := logicdmturist.GetSubjectTypeDimension(doTemplate, []int{subjectType})
	if err != nil {
		return
	}
	//转为关系树结构
	tree = buildTreeWithCount(list, dimension)
	return
}
