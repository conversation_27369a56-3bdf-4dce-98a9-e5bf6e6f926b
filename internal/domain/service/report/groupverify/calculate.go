package groupverify

import (
	"report-service/internal/domain/enum"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/pkg/utils"
)

type DoIndicator struct {
	VerifyCount           *int `json:"verify_count,omitempty"`             // 验证数量
	VerifySalePrice       *int `json:"verify_sale_price,omitempty"`        // 验证金额
	VerifyCostPrice       *int `json:"verify_cost_price,omitempty"`        // 验证采购金额
	ActualVerifyCount     *int `json:"actual_verify_count,omitempty"`      // 净验证数量
	ActualVerifySalePrice *int `json:"actual_verify_sale_price,omitempty"` // 净验证金额
	ActualVerifyCostPrice *int `json:"actual_verify_cost_price,omitempty"` // 净验证采购金额
	ActualVerifyProfit    *int `json:"actual_verify_profit,omitempty"`     // 净验证利润
	RevokeCount           *int `json:"revoke_count,omitempty"`             // 撤销数量
	RevokeSalePrice       *int `json:"revoke_sale_price,omitempty"`        // 撤销销售金额
	RevokeCostPrice       *int `json:"revoke_cost_price,omitempty"`        // 撤销采购金额
	RefundFeeProfit       *int `json:"refund_fee_profit,omitempty"`        // 退款手续费利润
	AfterSaleCount        *int `json:"after_sale_count,omitempty"`         // 售后数量
	AfterSalePrice        *int `json:"after_sale_price,omitempty"`         // 售后销售金额
	AfterCostPrice        *int `json:"after_cost_price,omitempty"`         // 售后采购金额
}

// 指标字段计算
// 先全部算出来，再根据指标模板来决定是否显示
func (i *DoIndicator) calculateIndicatorFields(
	indicator dmcommon.DoIndicator,
	templateIndicator []string,
) {
	verifyCount := indicator.VerifyCount
	verifySalePrice := indicator.VerifySalePrice - indicator.VerifySaleDiscountPrice
	verifyCostPrice := indicator.VerifyCostPrice - indicator.VerifyCostDiscountPrice

	revokeCount := indicator.RevokeCount
	revokeSalePrice := indicator.RevokeSalePrice - indicator.RevokeSaleDiscountPrice
	revokeCostPrice := indicator.RevokeCostPrice - indicator.RevokeCostDiscountPrice
	refundFeeProfit := indicator.RevokeSaleFee - indicator.RevokeCostFee

	afterSaleCount := indicator.AfterSaleCount
	afterSalePrice := indicator.AfterSalePrice
	afterCostPrice := indicator.AfterCostPrice

	actualVerifyCount := indicator.VerifyCount - indicator.RevokeCount - afterSaleCount
	actualVerifySalePrice := verifySalePrice - revokeSalePrice - afterSalePrice
	actualVerifyCostPrice := verifyCostPrice - revokeCostPrice - afterCostPrice
	actualVerifyProfit := actualVerifySalePrice - actualVerifyCostPrice

	for _, item := range templateIndicator {
		switch item {
		case enum.IndicatorVerifyCount:
			i.VerifyCount = utils.Add(i.VerifyCount, &verifyCount)
		case enum.IndicatorVerifySalePrice:
			i.VerifySalePrice = utils.Add(i.VerifySalePrice, &verifySalePrice)
		case enum.IndicatorVerifyCostPrice:
			i.VerifyCostPrice = utils.Add(i.VerifyCostPrice, &verifyCostPrice)
		case enum.IndicatorActualVerifyCount:
			i.ActualVerifyCount = utils.Add(i.ActualVerifyCount, &actualVerifyCount)
		case enum.IndicatorActualVerifySalePrice:
			i.ActualVerifySalePrice = utils.Add(i.ActualVerifySalePrice, &actualVerifySalePrice)
		case enum.IndicatorActualVerifyCostPrice:
			i.ActualVerifyCostPrice = utils.Add(i.ActualVerifyCostPrice, &actualVerifyCostPrice)
		case enum.IndicatorActualVerifyProfit:
			i.ActualVerifyProfit = utils.Add(i.ActualVerifyProfit, &actualVerifyProfit)
		case enum.IndicatorRevokeCount:
			i.RevokeCount = utils.Add(i.RevokeCount, &revokeCount)
		case enum.IndicatorRevokeSalePrice:
			i.RevokeSalePrice = utils.Add(i.RevokeSalePrice, &revokeSalePrice)
		case enum.IndicatorRevokeCostPrice:
			i.RevokeCostPrice = utils.Add(i.RevokeCostPrice, &revokeCostPrice)
		case enum.IndicatorRefundFeeProfit:
			i.RefundFeeProfit = utils.Add(i.RefundFeeProfit, &refundFeeProfit)
		case enum.IndicatorAfterSaleCount:
			i.AfterSaleCount = utils.Add(i.AfterSaleCount, &afterSaleCount)
		case enum.IndicatorAfterSalePrice:
			i.AfterSalePrice = utils.Add(i.AfterSalePrice, &afterSalePrice)
		case enum.IndicatorAfterCostPrice:
			i.AfterCostPrice = utils.Add(i.AfterCostPrice, &afterCostPrice)
		}
	}
}

// 获取指标字段的操作类型
func GetOperateTypes(indicator []string) (operateTypes []int) {
	operateTypes = make([]int, 0)
	for _, item := range indicator {
		switch item {
		case enum.IndicatorVerifyCount, enum.IndicatorVerifySalePrice, enum.IndicatorVerifyCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeVerify)
		case enum.IndicatorActualVerifyCount, enum.IndicatorActualVerifySalePrice, enum.IndicatorActualVerifyCostPrice, enum.IndicatorActualVerifyProfit:
			operateTypes = append(operateTypes, enum.DWMOperateTypeVerify, enum.DWMOperateTypeRevoke, enum.DWMOperateTypeAfterSale)
		case enum.IndicatorRevokeCount, enum.IndicatorRefundFeeProfit, enum.IndicatorRevokeSalePrice, enum.IndicatorRevokeCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeRevoke)
		case enum.IndicatorAfterSaleCount, enum.IndicatorAfterSalePrice, enum.IndicatorAfterCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeAfterSale)
		}
	}
	operateTypes = utils.RemoveDuplicate(operateTypes)
	return
}
