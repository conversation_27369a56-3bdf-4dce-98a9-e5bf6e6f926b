package h5

import (
	"report-service/internal/domain/enum"
	logicDmCommon "report-service/internal/domain/logic/dm/common"
	"report-service/pkg/utils"
)

// 指标字段计算
// 先全部算出来，再根据指标模板来决定是否显示
func (i *DoIndicator) calculateIndicatorFields(
	templateCategory int,
	indicator logicDmCommon.DoIndicator,
	templateIndicator []string,
) {
	actualSaleCount := indicator.PayCount - indicator.CancelCount - indicator.RevokeCount - indicator.AfterSaleCount
	actualSalePrice := (indicator.PaySalePrice - indicator.PaySaleDiscountPrice) - (indicator.CancelSalePrice - indicator.CancelSaleDiscountPrice) - (indicator.RevokeSalePrice - indicator.RevokeSaleDiscountPrice) - indicator.AfterSalePrice

	actualVerifyCount := indicator.VerifyCount - indicator.RevokeCount - indicator.AfterSaleCount
	actualVerifySalePrice := (indicator.VerifySalePrice - indicator.VerifySaleDiscountPrice) - (indicator.RevokeSalePrice - indicator.RevokeSaleDiscountPrice) - indicator.AfterSalePrice

	if templateCategory == enum.TemplateCategoryH5Pay {
		for _, item := range templateIndicator {
			switch item {
			case enum.IndicatorActualSaleCount:
				i.ActualSaleCount = utils.Add(i.ActualSaleCount, &actualSaleCount)
			case enum.IndicatorActualSalePrice:
				i.ActualSalePrice = utils.Add(i.ActualSalePrice, &actualSalePrice)
			}
		}
	}
	if templateCategory == enum.TemplateCategoryH5Verify {
		for _, item := range templateIndicator {
			switch item {
			case enum.IndicatorActualVerifyCount:
				i.ActualVerifyCount = utils.Add(i.ActualVerifyCount, &actualVerifyCount)
			case enum.IndicatorActualVerifySalePrice:
				i.ActualVerifySalePrice = utils.Add(i.ActualVerifySalePrice, &actualVerifySalePrice)
			}
		}
	}
}

// 获取指标字段的操作类型
func getOperateTypes(indicator []string) (operateTypes []int) {
	operateTypes = make([]int, 0)
	for _, item := range indicator {
		switch item {
		case enum.IndicatorActualSaleCount, enum.IndicatorActualSalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypePay, enum.DWMOperateTypeCancel, enum.DWMOperateTypeRevoke, enum.DWMOperateTypeAfterSale)
		case enum.IndicatorActualVerifyCount, enum.IndicatorActualVerifySalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeVerify, enum.DWMOperateTypeRevoke, enum.DWMOperateTypeAfterSale)
		}
	}
	operateTypes = utils.RemoveDuplicate(operateTypes)
	return
}
