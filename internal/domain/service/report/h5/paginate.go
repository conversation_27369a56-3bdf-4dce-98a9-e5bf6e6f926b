package h5

import (
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"

	"gitee.com/golang-module/carbon/v2"
)

type DoPaginationItem struct {
	Date            *string `json:"date,omitempty"`              // 时间，可能是小时、天、月、年
	SpuId           *int    `json:"spu_id,omitempty"`            // SpuID
	SpuName         *string `json:"spu_name,omitempty"`          // Spu名称
	SkuId           *int    `json:"sku_id,omitempty"`            // SkuID
	SkuName         *string `json:"sku_name,omitempty"`          // Sku名称
	SaleChannel     *int    `json:"sale_channel,omitempty"`      // 销售渠道
	SaleChannelName *string `json:"sale_channel_name,omitempty"` // 销售渠道名称
	SaleUnitPrice   *int    `json:"sale_unit_price,omitempty"`   // 销售单价
	// 方案分组
	SpuGroupId           *int    `json:"spu_group_id,omitempty"`            // SPU分组ID
	SpuGroupName         *string `json:"spu_group_name,omitempty"`          // SPU分组名称
	SkuGroupId           *int    `json:"sku_group_id,omitempty"`            // SKU分组ID
	SkuGroupName         *string `json:"sku_group_name,omitempty"`          // SKU分组名称
	SaleChannelGroupId   *int    `json:"sale_channel_group_id,omitempty"`   // 销售渠道分组ID
	SaleChannelGroupName *string `json:"sale_channel_group_name,omitempty"` // 销售渠道分组名称
	// 标签分组
	SpuTagCode         *string `json:"spu_tag_code,omitempty"`          // SPU标签编码
	SpuTagName         *string `json:"spu_tag_name,omitempty"`          // SPU标签名称
	SkuTagCode         *string `json:"sku_tag_code,omitempty"`          // SKU标签编码
	SkuTagName         *string `json:"sku_tag_name,omitempty"`          // SKU标签名称
	SaleChannelTagCode *string `json:"sale_channel_tag_code,omitempty"` // 销售渠道标签编码
	SaleChannelTagName *string `json:"sale_channel_tag_name,omitempty"` // 销售渠道标签名称
	DoIndicator
}

type DoIndicator struct {
	ActualSaleCount       *int `json:"actual_sale_count,omitempty"`        // 实售数量
	ActualSalePrice       *int `json:"actual_sale_price,omitempty"`        // 实售金额
	ActualVerifyCount     *int `json:"actual_verify_count,omitempty"`      // 净验证数量
	ActualVerifySalePrice *int `json:"actual_verify_sale_price,omitempty"` // 净验证销售金额
}

func Paginate(
	merchantId int,
	memberId int,
	templateCategory int,
	startTime, endTime carbon.Carbon,
	tagCodes []string,
	dimensionRange logicdmcommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
	page, pageSize int,
) (list []DoPaginationItem, total int, err error) {
	doTemplate, err := template.Detail(0, templateCategory, merchantId, memberId)
	if err != nil {
		return
	}

	operateTypes := getOperateTypes(doTemplate.Indicator)

	doList, total, err := logicdmcommon.Paginate(logicdmcommon.DoQueryParams{
		DoTemplate:     *doTemplate,
		MerchantId:     merchantId,
		ReportType:     enum.ReportTypeHour,
		StartTime:      startTime,
		EndTime:        endTime,
		TagCodes:       tagCodes,
		DimensionRange: dimensionRange,
		OperateTypes:   operateTypes,
		DataLimit:      &dataLimit,
	}, page, pageSize)
	if err != nil {
		return
	}

	list = make([]DoPaginationItem, 0)
	for _, doItem := range doList {
		item := DoPaginationItem{
			Date:               doItem.Date,
			SpuId:              doItem.SpuId,
			SpuName:            doItem.SpuName,
			SkuId:              doItem.SkuId,
			SkuName:            doItem.SkuName,
			SaleChannel:        doItem.SaleChannel,
			SaleChannelName:    doItem.SaleChannelName,
			SaleUnitPrice:      doItem.SaleUnitPrice,
			SpuTagCode:         doItem.SpuTagCode,
			SpuTagName:         doItem.SpuTagName,
			SkuTagCode:         doItem.SkuTagCode,
			SkuTagName:         doItem.SkuTagName,
			SaleChannelTagCode: doItem.SaleChannelTagCode,
			SaleChannelTagName: doItem.SaleChannelTagName,
		}
		item.calculateIndicatorFields(templateCategory, doItem.DoIndicator, doTemplate.Indicator)
		list = append(list, item)
	}
	return
}
