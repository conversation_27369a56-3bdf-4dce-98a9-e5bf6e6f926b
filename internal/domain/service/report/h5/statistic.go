package h5

import (
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
)

func Statistics(
	merchantId int,
	memberId int,
	templateCategory int,
	tagCodes []string,
	doDimensionRange logicdmcommon.DoDimensionRange,
	startTime, endTime carbon.Carbon,
	dataLimit datajobslimit.LimitSpuConfig,
) (statistics DoIndicator, err error) {
	doTemplate, err := template.Detail(0, templateCategory, merchantId, memberId)
	if err != nil {
		return
	}

	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	doStatistics, err := logicdmcommon.Statistics(logicdmcommon.DoQueryParams{
		DoTemplate:     *doTemplate,
		MerchantId:     merchantId,
		ReportType:     enum.ReportTypeHour,
		StartTime:      startTime,
		EndTime:        endTime,
		TagCodes:       tagCodes,
		DimensionRange: doDimensionRange,
		DataLimit:      &dataLimit,
	})
	if err != nil {
		return
	}
	statistics.calculateIndicatorFields(templateCategory, doStatistics, doTemplate.Indicator)

	return
}
