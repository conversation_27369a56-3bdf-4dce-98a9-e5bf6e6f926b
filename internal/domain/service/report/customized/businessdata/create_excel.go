package businessdata

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
	"os"
	"path/filepath"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/service/report/customized/businessdata/detail"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"
	"report-service/pkg/utils/excel"
	"report-service/pkg/utils/zip"
)

var fileExcelFormat = "%s_%d_%d.xlsx"
var fileZipFormat = "%s_%d_%d.zip"

type DoExcelData struct {
	excel.ExcelData
}

func CreateExcelData(
	merchantId int,
	uniqueKey int,
	startTime, endTime carbon.Carbon,
) (fileKey string, taskId int, err error) {
	//文件名生成
	var realFilePath, filePath, zipPath string
	filePath = fmt.Sprintf(fileExcelFormat, enum.PayModeMoneyExcelFileName, merchantId, uniqueKey)
	zipPath = fmt.Sprintf(fileZipFormat, enum.PayModeMoneyExcelFileName, merchantId, uniqueKey)
	realFilePath, err = getRealFilePath(filePath)
	if err != nil {
		return
	}
	realZipPath, err := getRealFilePath(zipPath)
	if err != nil {
		return
	}
	defer func() {
		// 删除Excel文件
		if rmErr := rmRealFilePath(realFilePath); rmErr != nil {
			global.LOG.Error("删除Excel临时文件失败", zap.String("file", realFilePath), zap.Any("error", rmErr))
			globalNotice.Warning(fmt.Sprintf("删除Excel临时文件失败，file：%s，err：%v", realFilePath, rmErr))

		}
		// 删除ZIP文件
		if rmErr := rmRealFilePath(realZipPath); rmErr != nil {
			global.LOG.Error("删除Zip临时文件失败", zap.String("file", realZipPath), zap.Any("error", rmErr))
			globalNotice.Warning(fmt.Sprintf("删除Zip临时文件失败，file：%s，err：%v", realZipPath, rmErr))
		}
	}()
	//数据查询
	list, err := Paginate(merchantId, startTime, endTime)
	if err != nil {
		return
	}
	// 小计计算，根据数据来源字段，对模块进行小计计算
	statistics := make([]DoStatisticsIndicatorItem, 0)
	for _, ds := range enum.PayModeMoneyDataSourceSort {
		var indicator DoIndicator
		indicator.statisticPagination(list, ds)
		statistics = append(statistics, DoStatisticsIndicatorItem{
			DataSource:  ds,
			DoIndicator: indicator,
		})
	}
	// 二次计算合计
	twoStatistics := make([]DoStatisticsIndicatorItem, 0)
	// 总合计
	var totalIndicator DoIndicator
	totalIndicator.calculateTotalIndicatorFields(statistics, true)
	twoStatistics = append(twoStatistics, DoStatisticsIndicatorItem{
		DataSource:  0,
		DoIndicator: totalIndicator,
	})
	// 当日款项合计
	var dailyPaymentsIndicator DoIndicator
	dailyPaymentsIndicator.calculateTotalIndicatorFields(statistics, false)
	twoStatistics = append(twoStatistics, DoStatisticsIndicatorItem{
		DataSource:  -1,
		DoIndicator: dailyPaymentsIndicator,
	})
	//合并小计
	statistics = append(statistics, twoStatistics...)
	//生成excel数据
	excelData := createExcelBaseData(startTime, endTime)
	for _, ds := range enum.PayModeMoneyDataSourceSort {
		var excelTitle []struct {
			Field string `json:"field"`
			Name  string `json:"name"`
		}
		isWriteTitles := true
		switch ds {
		case enum.PayModeMoneyDataSourcePlayPft:
			excelTitle = detail.PlayPftFieldNameSlice
		case enum.PayModeMoneyDataSourceBusinessYb:
			excelTitle = detail.BusinessYbFieldNameSlice
		case enum.PayModeMoneyDataSourceCateringYb:
			excelTitle = detail.CateringYbFieldNameSlice
		case enum.PayModeMoneyDataSourceRecharge:
			excelTitle = detail.RechargeFieldNameSlice
			isWriteTitles = false
		}
		if len(excelTitle) == 0 {
			continue
		}
		//写入抬头
		if isWriteTitles {
			excelData.WriteTitles(ds, excelTitle)
		}
		//写入数据
		excelData.WriteList(list, ds, excelTitle)
		//充值没有小计，只有当日款项合计
		if ds == enum.PayModeMoneyDataSourceRecharge {
			//写入当日款项合计
			excelData.WriteSubtotal(statistics, -1, excelTitle)
			continue
		}
		//写入小计
		excelData.WriteSubtotal(statistics, ds, excelTitle)
		//指定位置写入总计
		if ds == enum.PayModeMoneyDataSourceCateringYb {
			//写入总计
			excelData.WriteSubtotal(statistics, 0, excelTitle)
		}
	}
	err = excel.GenerateExcelWithMerge(realFilePath, excelData.ExcelData)
	if err != nil {
		err = fmt.Errorf("生成Excel失败: %v\n", err)
		return
	}
	err = zip.ZipFile(realFilePath, realZipPath)
	if err != nil {
		err = fmt.Errorf("压缩Excel失败: %v\n", err)
		return
	}
	//提交下载任务
	taskId, err = downloadcenter.AddFileTask(merchantId, merchantId, enum.PayModeMoneyExcelFileName, realZipPath)
	if err != nil {
		return "", 0, err
	}
	fileKey = filePath
	return
}

// GenerateRepeatedValues 生成指定数量的重复值切片
func GenerateRepeatedValues(value interface{}, count int) []interface{} {
	values := make([]interface{}, count)
	for i := 0; i < count; i++ {
		values[i] = value
	}
	return values
}

// 首列样式
func firstCellStyle() excel.CellStyle {
	return excel.CellStyle{
		FontSize:            12,
		Bold:                false,
		BgColor:             "#dde4f3",
		MergeCellVertical:   true,
		MergeCellHorizontal: true,
	}
}

// 默认列样式
func defaultCellStyle() excel.CellStyle {
	return excel.CellStyle{
		FontSize:            12,
		Bold:                false,
		BgColor:             "#FFFFFF",
		MergeCellVertical:   false,
		MergeCellHorizontal: false,
	}
}

// 标题列样式
func titleCellStyle(mergeVal bool) excel.CellStyle {
	return excel.CellStyle{
		FontSize:            12,
		Bold:                true,
		BgColor:             "#dde4f3",
		MergeCellVertical:   mergeVal,
		MergeCellHorizontal: mergeVal,
	}
}

func headerCellStyle() *excel.CellStyle {
	return &excel.CellStyle{
		FontSize:   12,
		Bold:       false,
		BgColor:    "#F5F5F5",
		Horizontal: "left",
		Vertical:   "center",
	}
}

// 无任何样式
func notCellStyle() excel.CellStyle {
	notBorder := false
	return excel.CellStyle{
		FontSize:            12,
		Bold:                false,
		BgColor:             "#FFFFFF",
		MergeCellVertical:   false,
		MergeCellHorizontal: false,
		Border:              &notBorder,
	}
}

func subtotalCellStyle() excel.CellStyle {
	return excel.CellStyle{
		FontSize:            12,
		Bold:                true,
		BgColor:             "#fff2c9",
		MergeCellVertical:   false,
		MergeCellHorizontal: false,
	}
}

func mergeSubtotalCellStyle() excel.CellStyle {
	return excel.CellStyle{
		FontSize:            12,
		Bold:                true,
		BgColor:             "#fff2c9",
		MergeCellVertical:   true,
		MergeCellHorizontal: true,
	}
}

// 合并单元格样式
func mergeCellStyle() excel.CellStyle {
	return excel.CellStyle{
		FontSize:            12,
		Bold:                false,
		BgColor:             "#FFFFFF",
		MergeCellVertical:   true,
		MergeCellHorizontal: true,
	}
}

func createExcelBaseData(startTime, endTime carbon.Carbon) DoExcelData {
	//营业日期
	currentDate := "营业日期：" + startTime.ToDateString() + " ~ " + endTime.ToDateString()
	//如果选择是单天的话，需要展示营业日期：yyyy-mm-dd
	if startTime.ToDateString() == endTime.ToDateString() {
		currentDate = "营业日期：" + startTime.ToDateString()
	}
	fieldCount := detail.GetMaxFieldNameSliceLength()
	//生成excel数据
	return DoExcelData{excel.ExcelData{
		SheetName:   enum.PayModeMoneyExcelSheetName,
		Title:       enum.PayModeMoneyExcelTitle,
		MergeTitles: true,
		Headers: []excel.HeaderRow{
			{
				Values: GenerateRepeatedValues(currentDate, fieldCount),
				Height: 20,
				Style:  headerCellStyle(),
			},
		},
		Data:       [][]interface{}{},
		DataStyles: [][]excel.CellStyle{},
	}}
}

func (e *DoExcelData) WriteTitles(
	dataSource int,
	excelTitle []struct {
		Field string `json:"field"`
		Name  string `json:"name"`
	},
) {
	dataSourceName := ""
	if name, ok := enum.PayModeMoneyDataSourceNameMap[dataSource]; ok {
		dataSourceName = name
	}
	cellData := make([]interface{}, 0, len(excelTitle))
	cellStyle := make([]excel.CellStyle, 0, len(excelTitle))
	mergeFields := []string{
		enum.PayModeMoneyDimensionDataSource,
		enum.PayModeMoneyDimensionProjectId,
		enum.PayModeMoneyDimensionTimeType,
	}
	for _, et := range excelTitle {
		//首列名称处理
		if et.Field == enum.PayModeMoneyDimensionDataSource && dataSourceName != "" && dataSource != enum.PayModeMoneyDataSourcePlayPft {
			cellData = append(cellData, dataSourceName)
		} else {
			cellData = append(cellData, et.Name)
		}
		mergeVal := false
		if utils.Container(mergeFields, et.Field) {
			mergeVal = true
		}
		cellStyle = append(cellStyle, titleCellStyle(mergeVal))
	}
	//写入抬头
	e.Data = append(e.Data, cellData)
	e.DataStyles = append(e.DataStyles, cellStyle)
	return
}

func (e *DoExcelData) WriteList(
	list []DoPaginationItem,
	dataSource int,
	excelTitle []struct {
		Field string `json:"field"`
		Name  string `json:"name"`
	},
) {
	isRecharge := enum.PayModeMoneyDataSourceRecharge == dataSource
	//写入数据
	for _, item := range list {
		if item.DataSource == nil || *item.DataSource != dataSource {
			continue
		}
		cellData := make([]interface{}, 0, len(excelTitle))
		cellStyle := make([]excel.CellStyle, 0, len(excelTitle))
		for _, et := range excelTitle {
			switch et.Field {
			case enum.PayModeMoneyDimensionDataSource:
				if isRecharge {
					cellData = append(cellData, "")
				} else {
					cellData = append(cellData, *item.DataSourceName)
				}
				cellStyle = append(cellStyle, firstCellStyle())
			case enum.PayModeMoneyDimensionProjectId:
				if isRecharge {
					cellData = append(cellData, "")
				} else {
					cellData = append(cellData, *item.ProjectIDName)
				}
				cellStyle = append(cellStyle, mergeCellStyle())
			case enum.PayModeMoneyDimensionTimeType:
				if isRecharge {
					cellData = append(cellData, "")
				} else {
					if item.TimeType == nil || *item.TimeType == enum.PayModeMoneyTimeTypeNil {
						cellData = append(cellData, *item.ProjectIDName)
					} else {
						cellData = append(cellData, *item.TimeTypeName)
					}
				}
				cellStyle = append(cellStyle, mergeCellStyle())
			case enum.PayModeMoneyDimensionEntranceCount:
				cellData = append(cellData, *item.EntranceCount)
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionTicketRevenue:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.TicketRevenue))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionPrepaidConsumptionRevenue:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.PrepaidConsumptionRevenue))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionTotalAmount:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.TotalAmount))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionOperatingRevenue:
				if isRecharge {
					cellData = append(cellData, "")
					cellStyle = append(cellStyle, notCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*item.OperatingRevenue))
					cellStyle = append(cellStyle, defaultCellStyle())
				}
			case enum.PayModeMoneyDimensionDiscountAmount:
				if isRecharge {
					cellData = append(cellData, "")
					cellStyle = append(cellStyle, notCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*item.DiscountAmount))
					cellStyle = append(cellStyle, defaultCellStyle())
				}
			case enum.PayModeMoneyDimensionAccountsReceivablePayment:
				if isRecharge {
					cellData = append(cellData, *item.ProjectIDName)
					//cellStyle = append(cellStyle, titleCellStyle(false))
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*item.AccountsReceivablePayment))
				}
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionEntertainmentExpensePayment:
				if isRecharge {
					cellData = append(cellData, utils.ConvertCentToYuan(*item.TotalIncome))
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*item.EntertainmentExpensePayment))
				}
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionCashPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.CashPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionUnionPayPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.UnionPayPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionStoredValueCardPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.StoredValueCardPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionYinbaoPayPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.YinbaoPayPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionRuralCommercialBankPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.RuralCommercialBankPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionCreditPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.CreditPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionYibaoPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.YibaoPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionAlipayPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.AlipayPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionWechatPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.WechatPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionPrepaidCardPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.PrepaidCardPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionOtherPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.OtherPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionMeituanCouponPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*item.MeituanCouponPayment))
				cellStyle = append(cellStyle, defaultCellStyle())
			case enum.PayModeMoneyDimensionTotalIncome:
				if isRecharge {
					cellData = append(cellData, "")
					cellStyle = append(cellStyle, notCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*item.TotalIncome))
					cellStyle = append(cellStyle, defaultCellStyle())
				}
			}
		}
		e.Data = append(e.Data, cellData)
		e.DataStyles = append(e.DataStyles, cellStyle)
	}
	return
}

// WriteSubtotal 写入小计
func (e *DoExcelData) WriteSubtotal(
	statistics []DoStatisticsIndicatorItem,
	dataSource int,
	excelTitle []struct {
		Field string `json:"field"`
		Name  string `json:"name"`
	},
) {
	isTotalDailyPayments := -1 == dataSource
	title := "小计"
	if dataSource == 0 {
		title = "总收入"
	}
	for _, statistic := range statistics {
		if dataSource != statistic.DataSource {
			continue
		}
		indicator := statistic.DoIndicator
		cellData := make([]interface{}, 0, len(excelTitle))
		cellStyle := make([]excel.CellStyle, 0, len(excelTitle))
		for _, et := range excelTitle {
			switch et.Field {
			case enum.PayModeMoneyDimensionDataSource:
				if isTotalDailyPayments {
					cellData = append(cellData, "")
				} else {
					cellData = append(cellData, title)
				}
				cellStyle = append(cellStyle, mergeSubtotalCellStyle())
			case enum.PayModeMoneyDimensionProjectId:
				if isTotalDailyPayments {
					cellData = append(cellData, "")
				} else {
					cellData = append(cellData, title)
				}
				cellStyle = append(cellStyle, mergeSubtotalCellStyle())
			case enum.PayModeMoneyDimensionTimeType:
				if isTotalDailyPayments {
					cellData = append(cellData, "")
				} else {
					cellData = append(cellData, title)
				}
				cellStyle = append(cellStyle, mergeSubtotalCellStyle())
			case enum.PayModeMoneyDimensionEntranceCount:
				cellData = append(cellData, *indicator.EntranceCount)
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionTicketRevenue:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.TicketRevenue))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionPrepaidConsumptionRevenue:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.PrepaidConsumptionRevenue))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionTotalAmount:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.TotalAmount))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionOperatingRevenue:
				if isTotalDailyPayments {
					cellData = append(cellData, "")
					cellStyle = append(cellStyle, notCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*indicator.OperatingRevenue))
					cellStyle = append(cellStyle, subtotalCellStyle())
				}
			case enum.PayModeMoneyDimensionDiscountAmount:
				if isTotalDailyPayments {
					cellData = append(cellData, "")
					cellStyle = append(cellStyle, notCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*indicator.DiscountAmount))
					cellStyle = append(cellStyle, subtotalCellStyle())
				}
			case enum.PayModeMoneyDimensionAccountsReceivablePayment:
				if isTotalDailyPayments {
					cellData = append(cellData, "当日款项合计")
					cellStyle = append(cellStyle, subtotalCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*indicator.AccountsReceivablePayment))
					cellStyle = append(cellStyle, subtotalCellStyle())
				}
			case enum.PayModeMoneyDimensionEntertainmentExpensePayment:
				if isTotalDailyPayments {
					cellData = append(cellData, utils.ConvertCentToYuan(*indicator.TotalIncome))
					cellStyle = append(cellStyle, subtotalCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*indicator.EntertainmentExpensePayment))
					cellStyle = append(cellStyle, subtotalCellStyle())
				}
			case enum.PayModeMoneyDimensionCashPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.CashPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionUnionPayPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.UnionPayPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionStoredValueCardPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.StoredValueCardPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionYinbaoPayPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.YinbaoPayPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionRuralCommercialBankPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.RuralCommercialBankPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionCreditPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.CreditPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionYibaoPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.YibaoPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionAlipayPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.AlipayPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionWechatPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.WechatPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionPrepaidCardPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.PrepaidCardPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionOtherPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.OtherPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionMeituanCouponPayment:
				cellData = append(cellData, utils.ConvertCentToYuan(*indicator.MeituanCouponPayment))
				cellStyle = append(cellStyle, subtotalCellStyle())
			case enum.PayModeMoneyDimensionTotalIncome:
				if isTotalDailyPayments {
					cellData = append(cellData, "")
					cellStyle = append(cellStyle, notCellStyle())
				} else {
					cellData = append(cellData, utils.ConvertCentToYuan(*indicator.TotalIncome))
					cellStyle = append(cellStyle, subtotalCellStyle())
				}
			}
		}
		e.Data = append(e.Data, cellData)
		e.DataStyles = append(e.DataStyles, cellStyle)
	}

	return
}

// getRealFilePath 获取真实文件路径
func getRealFilePath(realFilePath string) (string, error) {
	//系统临时文件目录存储
	tmpDir := filepath.Join(os.TempDir(), "report")
	if _, err := os.Stat(tmpDir); err != nil {
		if os.IsNotExist(err) {
			if err = os.MkdirAll(tmpDir, os.ModePerm); err != nil {
				return "", fmt.Errorf("创建临时目录失败: %v", err)
			}
		} else {
			return "", fmt.Errorf("检查临时目录状态失败: %v", err)
		}
	}
	realFilePath = filepath.Join(tmpDir, realFilePath)
	return realFilePath, nil
}

// rmRealFilePath 删除真实文件路径
func rmRealFilePath(realFilePath string) error {
	_, err := os.Stat(realFilePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil
		}
		return fmt.Errorf("检查文件状态失败: %v", err)
	}
	err = os.Remove(realFilePath)
	if err != nil {
		return fmt.Errorf("删除临时文件失败: %v", err)
	}
	return nil
}
