package detail

import "report-service/internal/domain/enum"

var PlayPftFieldNameSlice = []struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	{Field: enum.PayModeMoneyDimensionDataSource, Name: "模块"},
	{Field: enum.PayModeMoneyDimensionProjectId, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionTimeType, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionEntranceCount, Name: "入园人数"},
	{Field: enum.PayModeMoneyDimensionTicketRevenue, Name: "门票收入"},
	{Field: enum.PayModeMoneyDimensionPrepaidConsumptionRevenue, Name: "二消收入(预付卡)"},
	{Field: enum.PayModeMoneyDimensionTotalAmount, Name: "合计"},
	{Field: enum.PayModeMoneyDimensionCashPayment, Name: "现金支付"},
	{Field: enum.PayModeMoneyDimensionUnionPayPayment, Name: "银联支"},
	{Field: enum.PayModeMoneyDimensionStoredValueCardPayment, Name: "储值卡支付"},
	{Field: enum.PayModeMoneyDimensionYinbaoPayPayment, Name: "银豹付支付"},
	{Field: enum.PayModeMoneyDimensionRuralCommercialBankPayment, Name: "农商行收银宝"},
	{Field: enum.PayModeMoneyDimensionCreditPayment, Name: "授信支付"},
	{Field: enum.PayModeMoneyDimensionYibaoPayment, Name: "易宝"},
	{Field: enum.PayModeMoneyDimensionAlipayPayment, Name: "支付宝"},
	{Field: enum.PayModeMoneyDimensionWechatPayment, Name: "微信"},
	{Field: enum.PayModeMoneyDimensionPrepaidCardPayment, Name: "预付卡"},
	{Field: enum.PayModeMoneyDimensionOtherPayment, Name: "其他支付"},
	{Field: enum.PayModeMoneyDimensionTotalIncome, Name: "收入合计"},
}

var BusinessYbFieldNameSlice = []struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	{Field: enum.PayModeMoneyDimensionDataSource, Name: "模块"},
	{Field: enum.PayModeMoneyDimensionProjectId, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionTimeType, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionOperatingRevenue, Name: "营收"},
	{Field: enum.PayModeMoneyDimensionDiscountAmount, Name: "折扣"},
	{Field: enum.PayModeMoneyDimensionAccountsReceivablePayment, Name: "挂账"},
	{Field: enum.PayModeMoneyDimensionEntertainmentExpensePayment, Name: "招待"},
	{Field: enum.PayModeMoneyDimensionCashPayment, Name: "现金支付"},
	{Field: enum.PayModeMoneyDimensionUnionPayPayment, Name: "银联支付"},
	{Field: enum.PayModeMoneyDimensionStoredValueCardPayment, Name: "储值卡支付"},
	{Field: enum.PayModeMoneyDimensionYinbaoPayPayment, Name: "银豹付支付"},
	{Field: enum.PayModeMoneyDimensionRuralCommercialBankPayment, Name: "农商行收银宝"},
	{Field: enum.PayModeMoneyDimensionCreditPayment, Name: "授信支付"},
	{Field: enum.PayModeMoneyDimensionYibaoPayment, Name: "易宝"},
	{Field: enum.PayModeMoneyDimensionAlipayPayment, Name: "支付宝"},
	{Field: enum.PayModeMoneyDimensionWechatPayment, Name: "微信"},
	{Field: enum.PayModeMoneyDimensionPrepaidCardPayment, Name: "预付卡"},
	{Field: enum.PayModeMoneyDimensionOtherPayment, Name: "其他支付"},
	{Field: enum.PayModeMoneyDimensionTotalIncome, Name: "收入合计"},
}

var CateringYbFieldNameSlice = []struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	{Field: enum.PayModeMoneyDimensionDataSource, Name: "模块"},
	{Field: enum.PayModeMoneyDimensionProjectId, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionTimeType, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionOperatingRevenue, Name: "营收"},
	{Field: enum.PayModeMoneyDimensionDiscountAmount, Name: "折扣"},
	{Field: enum.PayModeMoneyDimensionAccountsReceivablePayment, Name: "挂账"},
	{Field: enum.PayModeMoneyDimensionEntertainmentExpensePayment, Name: "招待"},
	{Field: enum.PayModeMoneyDimensionCashPayment, Name: "现金支付"},
	{Field: enum.PayModeMoneyDimensionUnionPayPayment, Name: "银联支付"},
	{Field: enum.PayModeMoneyDimensionStoredValueCardPayment, Name: "储值卡支付"},
	{Field: enum.PayModeMoneyDimensionYinbaoPayPayment, Name: "银豹付支付"},
	{Field: enum.PayModeMoneyDimensionRuralCommercialBankPayment, Name: "农商行收银宝"},
	{Field: enum.PayModeMoneyDimensionCreditPayment, Name: "授信支付"},
	{Field: enum.PayModeMoneyDimensionYibaoPayment, Name: "易宝"},
	{Field: enum.PayModeMoneyDimensionAlipayPayment, Name: "支付宝"},
	{Field: enum.PayModeMoneyDimensionWechatPayment, Name: "微信"},
	{Field: enum.PayModeMoneyDimensionPrepaidCardPayment, Name: "预付卡"},
	{Field: enum.PayModeMoneyDimensionMeituanCouponPayment, Name: "美团优惠券"},
	{Field: enum.PayModeMoneyDimensionTotalIncome, Name: "收入合计"},
}

var RechargeFieldNameSlice = []struct {
	Field string `json:"field"`
	Name  string `json:"name"`
}{
	{Field: enum.PayModeMoneyDimensionDataSource, Name: ""},
	{Field: enum.PayModeMoneyDimensionProjectId, Name: ""},
	{Field: enum.PayModeMoneyDimensionTimeType, Name: ""},
	{Field: enum.PayModeMoneyDimensionOperatingRevenue, Name: ""},
	{Field: enum.PayModeMoneyDimensionDiscountAmount, Name: ""},
	{Field: enum.PayModeMoneyDimensionAccountsReceivablePayment, Name: "项目"},
	{Field: enum.PayModeMoneyDimensionEntertainmentExpensePayment, Name: "收入合计"},
	{Field: enum.PayModeMoneyDimensionCashPayment, Name: "现金支付"},
	{Field: enum.PayModeMoneyDimensionUnionPayPayment, Name: "银联支付"},
	{Field: enum.PayModeMoneyDimensionStoredValueCardPayment, Name: "储值卡支付"},
	{Field: enum.PayModeMoneyDimensionYinbaoPayPayment, Name: "银豹付支付"},
	{Field: enum.PayModeMoneyDimensionRuralCommercialBankPayment, Name: "农商行收银宝"},
	{Field: enum.PayModeMoneyDimensionCreditPayment, Name: "授信支付"},
	{Field: enum.PayModeMoneyDimensionYibaoPayment, Name: "易宝"},
	{Field: enum.PayModeMoneyDimensionAlipayPayment, Name: "支付宝"},
	{Field: enum.PayModeMoneyDimensionWechatPayment, Name: "微信"},
	{Field: enum.PayModeMoneyDimensionPrepaidCardPayment, Name: "预付卡"},
	{Field: enum.PayModeMoneyDimensionMeituanCouponPayment, Name: "美团优惠券"},
	{Field: enum.PayModeMoneyDimensionTotalIncome, Name: ""},
}

// GetMaxFieldNameSliceLength 获取所有字段名切片中的最大长度
func GetMaxFieldNameSliceLength() int {
	maxLength := len(PlayPftFieldNameSlice)

	if len(BusinessYbFieldNameSlice) > maxLength {
		maxLength = len(BusinessYbFieldNameSlice)
	}

	if len(CateringYbFieldNameSlice) > maxLength {
		maxLength = len(CateringYbFieldNameSlice)
	}

	if len(RechargeFieldNameSlice) > maxLength {
		maxLength = len(RechargeFieldNameSlice)
	}

	return maxLength
}
