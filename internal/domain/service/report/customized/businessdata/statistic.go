package businessdata

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	dmPaymodemoney "report-service/internal/domain/logic/dm/customized/paymodemoney"
)

type DoStatisticsIndicatorItem struct {
	DataSource int `json:"data_source"`
	DoIndicator
}

func Statistics(
	merchantId int,
	startTime, endTime carbon.Carbon,
) (statistics []DoStatisticsIndicatorItem, err error) {
	if err = dmPaymodemoney.CheckReportTypeAndTimeRange(startTime, endTime); err != nil {
		return
	}
	// 获取核销报表数据
	var doVerifyList []DoPaginationItem
	doVerifyList, err = GetVerifyPayModeData(merchantId, startTime, endTime)
	if err != nil {
		return nil, err
	}
	// 获取支付金额报表数据
	var doList []dmPaymodemoney.DoPaginationItem
	doList, err = dmPaymodemoney.AggregateData(merchantId, startTime, endTime)
	if err != nil {
		return nil, err
	}
	// 数据合并
	list := make([]DoPaginationItem, 0)
	list = append(list, doVerifyList...)
	projectIdTimeTypeMap := make(map[int][]int, 0)
	for _, doItem := range doList {
		if doItem.ProjectID == nil {
			continue
		}
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		item.calculateIndicatorFields(doItem.DoIndicator, enum.TemplateIndicator)
		projectIdTimeTypeMap[*item.ProjectID] = append(projectIdTimeTypeMap[*item.ProjectID], *item.TimeType)
		list = append(list, item)
	}
	// 固定格式展示，不存在补全兼容处理
	for _, projectId := range enum.PayModeMoneyProjectIdSort {
		// 处理项目数据
		projectItems := ProcessProjectData(projectId, projectIdTimeTypeMap)
		list = append(list, projectItems...)
	}
	// 小计计算，根据数据来源字段，对模块进行小计计算
	data := make([]DoStatisticsIndicatorItem, 0)
	for _, ds := range enum.PayModeMoneyDataSourceSort {
		var indicator DoIndicator
		indicator.statisticPagination(list, ds)
		data = append(data, DoStatisticsIndicatorItem{
			DataSource:  ds,
			DoIndicator: indicator,
		})
	}
	// 二次计算合计
	twoStatistics := make([]DoStatisticsIndicatorItem, 0)
	// 总合计
	var totalIndicator DoIndicator
	totalIndicator.calculateTotalIndicatorFields(data, true)
	twoStatistics = append(twoStatistics, DoStatisticsIndicatorItem{
		DataSource:  0,
		DoIndicator: totalIndicator,
	})
	// 当日款项合计
	var dailyPaymentsIndicator DoIndicator
	dailyPaymentsIndicator.calculateTotalIndicatorFields(data, false)
	twoStatistics = append(twoStatistics, DoStatisticsIndicatorItem{
		DataSource:  -1,
		DoIndicator: dailyPaymentsIndicator,
	})
	// 合并小计
	data = append(data, twoStatistics...)

	//合计只返回：游乐-票付通小计、商业-银豹小计、餐饮-银豹小计、总合计、当日款项合计，充值没有小计
	statistics = make([]DoStatisticsIndicatorItem, 0)
	for _, datum := range data {
		if datum.DataSource == enum.PayModeMoneyDataSourceRecharge {
			continue
		}
		statistics = append(statistics, datum)
	}

	return
}
