package businessdata

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	dmPaymodemoney "report-service/internal/domain/logic/dm/customized/paymodemoney"
)

type DoPaginationItem struct {
	dmPaymodemoney.DoDimension
	DoIndicator
}

type DoPagination []DoPaginationItem

func Paginate(
	merchantId int,
	startTime, endTime carbon.Carbon,
) (list []DoPaginationItem, err error) {
	if err = dmPaymodemoney.CheckReportTypeAndTimeRange(startTime, endTime); err != nil {
		return
	}
	// 获取核销报表数据
	var doVerifyList []DoPaginationItem
	doVerifyList, err = GetVerifyPayModeData(merchantId, startTime, endTime)
	if err != nil {
		return nil, err
	}
	// 获取支付金额报表数据
	var doList []dmPaymodemoney.DoPaginationItem
	doList, err = dmPaymodemoney.AggregateData(merchantId, startTime, endTime)
	if err != nil {
		return nil, err
	}
	// 数据合并
	list = make([]DoPaginationItem, 0)
	list = append(list, doVerifyList...)
	projectIdTimeTypeMap := make(map[int][]int, 0)
	for _, doItem := range doList {
		if doItem.ProjectID == nil {
			continue
		}
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		item.calculateIndicatorFields(doItem.DoIndicator, enum.TemplateIndicator)
		projectIdTimeTypeMap[*item.ProjectID] = append(projectIdTimeTypeMap[*item.ProjectID], *item.TimeType)
		list = append(list, item)
	}
	// 固定格式展示，不存在补全兼容处理
	for _, projectId := range enum.PayModeMoneyProjectIdSort {
		// 处理项目数据
		projectItems := ProcessProjectData(projectId, projectIdTimeTypeMap)
		if projectItems == nil {
			continue
		}
		list = append(list, projectItems...)
	}

	//排序
	list = SortResultByKey(list)

	return list, nil
}
