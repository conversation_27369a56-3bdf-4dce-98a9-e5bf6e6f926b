package businessdata

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	dwmPaymodemoney "report-service/internal/domain/logic/dwm/customized/paymodemoney/handler"
	"report-service/internal/domain/logic/report/template/defaultconfig"
	"report-service/internal/domain/service/report/verify"
	"report-service/pkg/sdk/api/tagcenter/tag"
	"report-service/pkg/utils"
)

func GetVerifyPayModeData(
	merchantId int,
	startTime, endTime carbon.Carbon,
) (
	list []DoPaginationItem,
	err error,
) {
	// 获取分组配置code
	var tagGroupCode string
	tagGroupCode, err = GetConfigTagGroupCode(merchantId)
	if err != nil {
		return nil, err
	}
	// 获取分组标签
	var tags []tag.ResponseTagInfoItem
	tags, err = merchantTagQuery(merchantId, tagGroupCode)
	if err != nil {
		return nil, err
	}
	// 通用参数
	dataSource := enum.PayModeMoneyDataSourcePlayPft
	dataSourceName := enum.PayModeMoneyDataSourcePlayPftName
	timeType := enum.PayModeMoneyTimeTypeNil
	timeTypeName := enum.PayModeMoneyTimeTypeNilName
	// 获取验证报表数据
	verifyList, err := getVerifyList(merchantId, startTime, endTime, tagGroupCode)
	if err != nil {
		return nil, err
	}
	// 验证报表数据处理
	result := make(map[int]DoPaginationItem, 0)
	for _, item := range verifyList {
		skuTagCode := item.SkuTagCode
		if skuTagCode == nil {
			continue
		}
		if *skuTagCode == "" {
			skuTagCodeEmpty := "0"
			item.SkuTagCode = &skuTagCodeEmpty
		}
		//临时生成分组标识唯一key
		projectIdKey := dwmPaymodemoney.GenerateBusinessNo([]string{cast.ToString(dataSource), cast.ToString(skuTagCode)})
		projectId := dwmPaymodemoney.CreateProjectId(dataSource, int(projectIdKey))
		//生成分组信息
		var exists bool
		var info DoPaginationItem
		if info, exists = result[projectId]; !exists {
			info = CreateBaseData(projectId, *item.SkuTagName, dataSource, dataSourceName, timeType, timeTypeName)
		}
		//指标数据计算
		actualVerifyCount, actualVerifySalePrice, ticketRevenue, prepaidConsumptionRevenue, totalAmount := CalculateVerifyIndicatorFields(item)
		//计算验证报表按支付方式的指标
		info.calculateVerifyPayModeIndicatorFields(item, actualVerifySalePrice)
		//指标数据赋值
		info.EntranceCount = utils.Add(info.EntranceCount, &actualVerifyCount)
		info.TicketRevenue = utils.Add(info.TicketRevenue, &ticketRevenue)
		info.PrepaidConsumptionRevenue = utils.Add(info.PrepaidConsumptionRevenue, &prepaidConsumptionRevenue)
		info.TotalAmount = utils.Add(info.TotalAmount, &totalAmount)
		// 覆盖到结果集
		result[projectId] = info
	}
	list = FillDataByTags(result, tags, dataSource, dataSourceName, timeType, timeTypeName)
	return
}

// 获取商户全部的分组标签
func merchantTagQuery(
	merchantId int,
	tagGroupCode string,
) (
	[]tag.ResponseTagInfoItem,
	error,
) {
	var apiParams tag.QueryTagPaginateParams
	apiParams.GroupCode = tagGroupCode
	apiParams.Type = 2
	apiParams.PageNum = 1
	apiParams.PageSize = 200
	apiParams.Sids = []int{merchantId}
	responseData, err := tag.TagPaginate(apiParams)
	if err != nil {
		return nil, err
	}
	if len(responseData) > 0 {
		return responseData, nil
	}
	if len(responseData) == 0 {
		return nil, fmt.Errorf("请配置分组标签")
	}
	return nil, nil
}

func getVerifyList(
	merchantId int,
	startTime, endTime carbon.Carbon,
	tagGroupCode string,
) (
	list []logicdmcommon.DoPaginationItem,
	err error,
) {
	//固定默认模板
	doTemplate := defaultconfig.BusinessDataTemplate(tagGroupCode)
	//先获取验证报表数据, 分组标签最大200个
	operateTypes := verify.GetOperateTypes(doTemplate.Indicator)
	timeGroupType := 1
	reportType := 1
	list, _, err = logicdmcommon.Paginate(logicdmcommon.DoQueryParams{
		DoTemplate:    *doTemplate,
		MerchantId:    merchantId,
		ReportType:    reportType,
		StartTime:     startTime,
		EndTime:       endTime,
		TimeGroupType: &timeGroupType,
		OperateTypes:  operateTypes,
	}, 1, 200)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// FillDataByTags 按全部标签填充数据，不存在的补全默认数据
func FillDataByTags(
	result map[int]DoPaginationItem,
	tags []tag.ResponseTagInfoItem,
	dataSource int,
	dataSourceName string,
	timeType int,
	timeTypeName string,
) []DoPaginationItem {
	list := make([]DoPaginationItem, 0)
	for _, tagItem := range tags {
		//临时生成分组标识唯一key
		projectIdKey := dwmPaymodemoney.GenerateBusinessNo([]string{cast.ToString(dataSource), cast.ToString(tagItem.Code)})
		projectId := dwmPaymodemoney.CreateProjectId(dataSource, int(projectIdKey))
		if info, ok := result[projectId]; ok {
			list = append(list, info)
		} else {
			info = CreateBaseData(projectId, tagItem.Name, dataSource, dataSourceName, timeType, timeTypeName)
			list = append(list, info)
		}
	}
	return list
}
