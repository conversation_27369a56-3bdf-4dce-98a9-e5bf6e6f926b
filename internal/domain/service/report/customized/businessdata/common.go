package businessdata

import (
	"fmt"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/customized/paymodemoneyreport"
	"report-service/pkg/utils"
	"sort"
	"strings"
)

// CreateBaseData 生成基础指标数据
func CreateBaseData(
	projectID int,
	projectIDName string,
	dataSource int,
	dataSourceName string,
	timeType int,
	timeTypeName string,
) (info DoPaginationItem) {
	//定义默认指标值
	defaultVal := 0
	//维度值
	info.ProjectID = &projectID
	info.ProjectIDName = &projectIDName
	info.DataSource = &dataSource
	info.DataSourceName = &dataSourceName
	info.TimeType = &timeType
	info.TimeTypeName = &timeTypeName
	//指标默认值
	info.EntranceCount = &defaultVal
	info.TicketRevenue = &defaultVal
	info.PrepaidConsumptionRevenue = &defaultVal
	info.TotalAmount = &defaultVal
	info.OperatingRevenue = &defaultVal
	info.DiscountAmount = &defaultVal
	info.AccountsReceivablePayment = &defaultVal
	info.EntertainmentExpensePayment = &defaultVal
	info.CashPayment = &defaultVal
	info.UnionPayPayment = &defaultVal
	info.StoredValueCardPayment = &defaultVal
	info.YinbaoPayPayment = &defaultVal
	info.RuralCommercialBankPayment = &defaultVal
	info.CreditPayment = &defaultVal
	info.YibaoPayment = &defaultVal
	info.AlipayPayment = &defaultVal
	info.WechatPayment = &defaultVal
	info.PrepaidCardPayment = &defaultVal
	info.OtherPayment = &defaultVal
	info.MeituanCouponPayment = &defaultVal
	info.TotalIncome = &defaultVal
	return
}

// GenerateDataKey 生成数据项的唯一标识key
func GenerateDataKey(dataSource, projectID, timeType int) string {
	return strings.Join([]string{
		cast.ToString(dataSource),
		cast.ToString(projectID),
		cast.ToString(timeType),
	}, "|")
}

// GetDataSourceInfo 根据项目ID获取数据源信息
func GetDataSourceInfo(projectId int) (dataSource int, dataSourceName string) {
	for i, item := range enum.PayModeMoneyProjectIdAndDataSourceMap {
		if !utils.Container(item, projectId) {
			continue
		}
		dataSource = i
		if name, ok := enum.PayModeMoneyDataSourceNameMap[dataSource]; ok {
			dataSourceName = name
		}
		break // 找到匹配项后退出循环
	}
	return
}

// ProcessProjectData 处理项目数据，不存在的生成基础数据项
func ProcessProjectData(projectId int, projectIdTimeTypeMap map[int][]int) (items []DoPaginationItem) {
	dataSource, dataSourceName := GetDataSourceInfo(projectId)
	var projectName string
	if name, ok := enum.PayModeMoneyProjectIdNameMap[projectId]; ok {
		projectName = name
	}
	// 获取当前项目的时间类型列表
	timeTypes, projectExists := projectIdTimeTypeMap[projectId]
	// 如果项目存在且标记为忽略，则直接返回
	if projectExists && utils.Container(timeTypes, enum.PayModeMoneyTimeTypeNil) {
		return nil
	}
	// 获取项目支持的时间类型映射
	timeTypeMap, hasTimeTypes := enum.PayModeMoneyProjectAndTimeTypeMap[projectId]
	// 如果没有定义时间类型映射
	if !hasTimeTypes {
		// 如果项目存在但没有时间类型映射，返回空
		if projectExists {
			return nil
		}
		// 如果项目不存在，创建默认数据项
		item := CreateBaseData(
			projectId,
			projectName,
			dataSource,
			dataSourceName,
			enum.PayModeMoneyTimeTypeNil,
			"",
		)
		return []DoPaginationItem{item}
	}
	// 如果项目存在，检查缺失的时间类型并创建基础数据项
	if projectExists {
		items = make([]DoPaginationItem, 0)
		for t := range timeTypeMap {
			if !utils.Container(timeTypes, t) {
				items = append(items, createItemWithTimeType(projectId, projectName, dataSource, dataSourceName, t))
			}
		}
		return items
	}
	// 如果项目不存在，为所有支持的时间类型创建基础数据项
	items = make([]DoPaginationItem, 0, len(timeTypeMap))
	for t := range timeTypeMap {
		items = append(items, createItemWithTimeType(projectId, projectName, dataSource, dataSourceName, t))
	}
	return items
}

// SortResultByKey 根据key对结果进行排序
func SortResultByKey(result []DoPaginationItem) []DoPaginationItem {
	list := make([]DoPaginationItem, 0, len(result))
	keys := make([]string, 0, len(result))

	data := make(map[string]DoPaginationItem)
	for _, item := range result {
		key := GenerateDataKey(*item.DataSource, *item.ProjectID, *item.TimeType)
		data[key] = item
	}
	// 收集所有key
	for k := range data {
		keys = append(keys, k)
	}
	// 对key进行排序
	sort.Strings(keys)
	// 按排序后的key顺序添加元素到列表
	for _, k := range keys {
		list = append(list, data[k])
	}
	return list
}

// createItemWithTimeType 创建带时间类型的基础数据项
func createItemWithTimeType(projectId int, projectName string, dataSource int, dataSourceName string, timeType int) DoPaginationItem {
	tName := cast.ToString(timeType)
	if n, ok := enum.PayModeMoneyTimeTypeNameMap[timeType]; ok {
		tName = n
	}
	return CreateBaseData(
		projectId,
		projectName,
		dataSource,
		dataSourceName,
		timeType,
		tName,
	)
}

// GetConfigTagGroupCode 获取商户的分组标签
func GetConfigTagGroupCode(merchantId int) (string, error) {
	config, err := paymodemoneyreport.GetMerchantConfig(merchantId)
	if err != nil {
		return "", err
	}
	if config == nil || config.GroupCode == "" {
		return "", fmt.Errorf("请先配置分组标签")
	}
	tagGroupCode := config.GroupCode
	return tagGroupCode, nil
}
