package businessdata

import (
	"report-service/internal/domain/enum"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/dm/customized/paymodemoney"
	"report-service/pkg/utils"
)

type DoIndicator struct {
	EntranceCount               *int `json:"entrance_count,omitempty"`                // 入园人数
	TicketRevenue               *int `json:"ticket_revenue,omitempty"`                // 门票收入
	PrepaidConsumptionRevenue   *int `json:"prepaid_consumption_revenue,omitempty"`   // 二消收入(预付卡)
	TotalAmount                 *int `json:"total_amount,omitempty"`                  // 合计
	OperatingRevenue            *int `json:"operating_revenue,omitempty"`             // 营收
	DiscountAmount              *int `json:"discount_amount,omitempty"`               // 折扣
	AccountsReceivablePayment   *int `json:"accounts_receivable_payment,omitempty"`   // 挂账
	EntertainmentExpensePayment *int `json:"entertainment_expense_payment,omitempty"` // 招待
	CashPayment                 *int `json:"cash_payment,omitempty"`                  // 现金支付
	UnionPayPayment             *int `json:"union_pay_payment,omitempty"`             // 银联支付
	StoredValueCardPayment      *int `json:"stored_value_card_payment,omitempty"`     // 储值卡支付
	YinbaoPayPayment            *int `json:"yinbao_pay_payment,omitempty"`            // 银豹付支付
	RuralCommercialBankPayment  *int `json:"rural_commercial_bank_payment,omitempty"` // 农商行收银宝
	CreditPayment               *int `json:"credit_payment,omitempty"`                // 授信支付
	YibaoPayment                *int `json:"yibao_payment,omitempty"`                 // 易宝
	AlipayPayment               *int `json:"alipay_payment,omitempty"`                // 支付宝
	WechatPayment               *int `json:"wechat_payment,omitempty"`                // 微信
	PrepaidCardPayment          *int `json:"prepaid_card_payment,omitempty"`          // 预付卡
	OtherPayment                *int `json:"other_payment,omitempty"`                 // 其他
	MeituanCouponPayment        *int `json:"meituan_coupon_payment,omitempty"`        // 美团优惠券
	TotalIncome                 *int `json:"total_income,omitempty"`                  // 收入总计
}

// 指标字段计算
// 先全部算出来，再根据指标模板来决定是否显示
func (i *DoIndicator) calculateIndicatorFields(
	indicator paymodemoney.DoIndicator,
	templateIndicator []string,
) {
	//合计：门票收入+二消收入(预付卡)
	totalAmount := indicator.TicketRevenue + indicator.PrepaidConsumptionRevenue
	for _, item := range templateIndicator {
		switch item {
		case enum.PayModeMoneyDimensionEntranceCount:
			i.EntranceCount = utils.Add(i.EntranceCount, &indicator.EntranceCount)
		case enum.PayModeMoneyDimensionTicketRevenue:
			i.TicketRevenue = utils.Add(i.TicketRevenue, &indicator.TicketRevenue)
		case enum.PayModeMoneyDimensionPrepaidConsumptionRevenue:
			i.PrepaidConsumptionRevenue = utils.Add(i.PrepaidConsumptionRevenue, &indicator.PrepaidConsumptionRevenue)
		case enum.PayModeMoneyDimensionTotalAmount:
			i.TotalAmount = utils.Add(i.TotalAmount, &totalAmount)
		case enum.PayModeMoneyDimensionOperatingRevenue:
			i.OperatingRevenue = utils.Add(i.OperatingRevenue, &indicator.OperatingRevenue)
		case enum.PayModeMoneyDimensionDiscountAmount:
			i.DiscountAmount = utils.Add(i.DiscountAmount, &indicator.DiscountAmount)
		case enum.PayModeMoneyDimensionAccountsReceivablePayment:
			i.AccountsReceivablePayment = utils.Add(i.AccountsReceivablePayment, &indicator.AccountsReceivablePayment)
		case enum.PayModeMoneyDimensionEntertainmentExpensePayment:
			i.EntertainmentExpensePayment = utils.Add(i.EntertainmentExpensePayment, &indicator.EntertainmentExpensePayment)
		case enum.PayModeMoneyDimensionCashPayment:
			i.CashPayment = utils.Add(i.CashPayment, &indicator.CashPayment)
		case enum.PayModeMoneyDimensionUnionPayPayment:
			i.UnionPayPayment = utils.Add(i.UnionPayPayment, &indicator.UnionPayPayment)
		case enum.PayModeMoneyDimensionStoredValueCardPayment:
			i.StoredValueCardPayment = utils.Add(i.StoredValueCardPayment, &indicator.StoredValueCardPayment)
		case enum.PayModeMoneyDimensionYinbaoPayPayment:
			i.YinbaoPayPayment = utils.Add(i.YinbaoPayPayment, &indicator.YinbaoPayPayment)
		case enum.PayModeMoneyDimensionRuralCommercialBankPayment:
			i.RuralCommercialBankPayment = utils.Add(i.RuralCommercialBankPayment, &indicator.RuralCommercialBankPayment)
		case enum.PayModeMoneyDimensionCreditPayment:
			i.CreditPayment = utils.Add(i.CreditPayment, &indicator.CreditPayment)
		case enum.PayModeMoneyDimensionYibaoPayment:
			i.YibaoPayment = utils.Add(i.YibaoPayment, &indicator.YibaoPayment)
		case enum.PayModeMoneyDimensionAlipayPayment:
			i.AlipayPayment = utils.Add(i.AlipayPayment, &indicator.AlipayPayment)
		case enum.PayModeMoneyDimensionWechatPayment:
			i.WechatPayment = utils.Add(i.WechatPayment, &indicator.WechatPayment)
		case enum.PayModeMoneyDimensionPrepaidCardPayment:
			i.PrepaidCardPayment = utils.Add(i.PrepaidCardPayment, &indicator.PrepaidCardPayment)
		case enum.PayModeMoneyDimensionOtherPayment:
			i.OtherPayment = utils.Add(i.OtherPayment, &indicator.OtherPayment)
		case enum.PayModeMoneyDimensionMeituanCouponPayment:
			i.MeituanCouponPayment = utils.Add(i.MeituanCouponPayment, &indicator.MeituanCouponPayment)
		case enum.PayModeMoneyDimensionTotalIncome:
			i.TotalIncome = utils.Add(i.TotalIncome, &indicator.TotalIncome)
		}
	}
}

// 总计指标字段计算
func (i *DoIndicator) calculateTotalIndicatorFields(
	statistics []DoStatisticsIndicatorItem,
	rmRecharge bool,
) {
	defaultVal := 0
	for _, item := range enum.TemplateIndicator {
		switch item {
		case enum.PayModeMoneyDimensionOperatingRevenue:
			i.OperatingRevenue = &defaultVal
			for _, val := range statistics {
				if val.DataSource == enum.PayModeMoneyDataSourcePlayPft {
					i.OperatingRevenue = utils.Add(i.OperatingRevenue, val.DoIndicator.TotalAmount)
					continue
				}
				i.OperatingRevenue = utils.Add(i.OperatingRevenue, val.DoIndicator.OperatingRevenue)
			}
		case enum.PayModeMoneyDimensionDiscountAmount:
			i.DiscountAmount = &defaultVal
			for _, val := range statistics {
				if val.DataSource == enum.PayModeMoneyDataSourcePlayPft {
					continue
				}
				i.DiscountAmount = utils.Add(i.DiscountAmount, val.DoIndicator.DiscountAmount)
			}
		case enum.PayModeMoneyDimensionAccountsReceivablePayment:
			i.AccountsReceivablePayment = &defaultVal
			for _, val := range statistics {
				if val.DataSource == enum.PayModeMoneyDataSourcePlayPft {
					continue
				}
				i.AccountsReceivablePayment = utils.Add(i.AccountsReceivablePayment, val.DoIndicator.AccountsReceivablePayment)
			}
		case enum.PayModeMoneyDimensionEntertainmentExpensePayment:
			i.EntertainmentExpensePayment = &defaultVal
			for _, val := range statistics {
				if val.DataSource == enum.PayModeMoneyDataSourcePlayPft {
					continue
				}
				i.EntertainmentExpensePayment = utils.Add(i.EntertainmentExpensePayment, val.DoIndicator.EntertainmentExpensePayment)
			}
		case enum.PayModeMoneyDimensionCashPayment:
			i.CashPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.CashPayment = utils.Add(i.CashPayment, val.DoIndicator.CashPayment)
			}
		case enum.PayModeMoneyDimensionUnionPayPayment:
			i.UnionPayPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.UnionPayPayment = utils.Add(i.UnionPayPayment, val.DoIndicator.UnionPayPayment)
			}
		case enum.PayModeMoneyDimensionStoredValueCardPayment:
			i.StoredValueCardPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.StoredValueCardPayment = utils.Add(i.StoredValueCardPayment, val.DoIndicator.StoredValueCardPayment)
			}
		case enum.PayModeMoneyDimensionYinbaoPayPayment:
			i.YinbaoPayPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.YinbaoPayPayment = utils.Add(i.YinbaoPayPayment, val.DoIndicator.YinbaoPayPayment)
			}
		case enum.PayModeMoneyDimensionRuralCommercialBankPayment:
			i.RuralCommercialBankPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.RuralCommercialBankPayment = utils.Add(i.RuralCommercialBankPayment, val.DoIndicator.RuralCommercialBankPayment)
			}
		case enum.PayModeMoneyDimensionCreditPayment:
			i.CreditPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.CreditPayment = utils.Add(i.CreditPayment, val.DoIndicator.CreditPayment)
			}
		case enum.PayModeMoneyDimensionYibaoPayment:
			i.YibaoPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.YibaoPayment = utils.Add(i.YibaoPayment, val.DoIndicator.YibaoPayment)
			}
		case enum.PayModeMoneyDimensionAlipayPayment:
			i.AlipayPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.AlipayPayment = utils.Add(i.AlipayPayment, val.DoIndicator.AlipayPayment)
			}
		case enum.PayModeMoneyDimensionWechatPayment:
			i.WechatPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.WechatPayment = utils.Add(i.WechatPayment, val.DoIndicator.WechatPayment)
			}
		case enum.PayModeMoneyDimensionPrepaidCardPayment:
			i.PrepaidCardPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.PrepaidCardPayment = utils.Add(i.PrepaidCardPayment, val.DoIndicator.PrepaidCardPayment)
			}
		case enum.PayModeMoneyDimensionOtherPayment:
			i.OtherPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.OtherPayment = utils.Add(i.OtherPayment, val.DoIndicator.OtherPayment)
			}
		case enum.PayModeMoneyDimensionMeituanCouponPayment:
			i.MeituanCouponPayment = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				i.MeituanCouponPayment = utils.Add(i.MeituanCouponPayment, val.DoIndicator.MeituanCouponPayment)
			}
		case enum.PayModeMoneyDimensionTotalIncome:
			i.TotalIncome = &defaultVal
			for _, val := range statistics {
				if enum.PayModeMoneyDataSourceRecharge == val.DataSource && rmRecharge {
					continue
				}
				//如果移除充值场景，则需要考虑挂帐和招待
				if rmRecharge {
					i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.AccountsReceivablePayment)
					i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.EntertainmentExpensePayment)
				}
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.CashPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.UnionPayPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.StoredValueCardPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.YinbaoPayPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.RuralCommercialBankPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.CreditPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.YibaoPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.AlipayPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.WechatPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.PrepaidCardPayment)
				i.TotalIncome = utils.Add(i.TotalIncome, val.DoIndicator.MeituanCouponPayment)
			}
		}
	}
}

// 计算验证报表按支付方式的指标
func (i *DoIndicator) calculateVerifyPayModeIndicatorFields(
	indicator dmcommon.DoPaginationItem,
	actualVerifySalePrice int,
) {
	addTotalIncome := true
	//支付方式数据赋值
	switch *indicator.SalePayMode {
	case enum.PayModeCash:
		i.CashPayment = utils.Add(i.CashPayment, &actualVerifySalePrice)
	case enum.PayModeCredit:
		i.CreditPayment = utils.Add(i.CreditPayment, &actualVerifySalePrice)
	case enum.PayModeYiBao:
		i.YibaoPayment = utils.Add(i.YibaoPayment, &actualVerifySalePrice)
	case enum.PayModeAlipay:
		i.AlipayPayment = utils.Add(i.AlipayPayment, &actualVerifySalePrice)
	case enum.PayModeWechat:
		i.WechatPayment = utils.Add(i.WechatPayment, &actualVerifySalePrice)
	case enum.PayModePrepaidCardOne, enum.PayModePrepaidCardTwo:
		i.PrepaidCardPayment = utils.Add(i.PrepaidCardPayment, &actualVerifySalePrice)
	default:
		addTotalIncome = false
	}
	// 计算支付方式总收入, 存在支付方式映射关系的，才需要计算总收入
	if addTotalIncome {
		i.TotalIncome = utils.Add(i.TotalIncome, &actualVerifySalePrice)
	}
}

// 列表合计计算
func (i *DoIndicator) statisticPagination(
	doPagination DoPagination,
	dataSource int,
) {
	for _, indicator := range doPagination {
		if indicator.DataSource == nil || *indicator.DataSource != dataSource {
			continue
		}
		i.statisticPaginationItem(indicator, enum.TemplateIndicator)
	}
}

func (i *DoIndicator) statisticPaginationItem(
	indicator DoPaginationItem,
	templateIndicator []string,
) {
	for _, item := range templateIndicator {
		switch item {
		case enum.PayModeMoneyDimensionEntranceCount:
			i.EntranceCount = utils.Add(i.EntranceCount, indicator.EntranceCount)
		case enum.PayModeMoneyDimensionTicketRevenue:
			i.TicketRevenue = utils.Add(i.TicketRevenue, indicator.TicketRevenue)
		case enum.PayModeMoneyDimensionPrepaidConsumptionRevenue:
			i.PrepaidConsumptionRevenue = utils.Add(i.PrepaidConsumptionRevenue, indicator.PrepaidConsumptionRevenue)
		case enum.PayModeMoneyDimensionTotalAmount:
			i.TotalAmount = utils.Add(i.TotalAmount, indicator.TotalAmount)
		case enum.PayModeMoneyDimensionOperatingRevenue:
			i.OperatingRevenue = utils.Add(i.OperatingRevenue, indicator.OperatingRevenue)
		case enum.PayModeMoneyDimensionDiscountAmount:
			i.DiscountAmount = utils.Add(i.DiscountAmount, indicator.DiscountAmount)
		case enum.PayModeMoneyDimensionAccountsReceivablePayment:
			i.AccountsReceivablePayment = utils.Add(i.AccountsReceivablePayment, indicator.AccountsReceivablePayment)
		case enum.PayModeMoneyDimensionEntertainmentExpensePayment:
			i.EntertainmentExpensePayment = utils.Add(i.EntertainmentExpensePayment, indicator.EntertainmentExpensePayment)
		case enum.PayModeMoneyDimensionCashPayment:
			i.CashPayment = utils.Add(i.CashPayment, indicator.CashPayment)
		case enum.PayModeMoneyDimensionUnionPayPayment:
			i.UnionPayPayment = utils.Add(i.UnionPayPayment, indicator.UnionPayPayment)
		case enum.PayModeMoneyDimensionStoredValueCardPayment:
			i.StoredValueCardPayment = utils.Add(i.StoredValueCardPayment, indicator.StoredValueCardPayment)
		case enum.PayModeMoneyDimensionYinbaoPayPayment:
			i.YinbaoPayPayment = utils.Add(i.YinbaoPayPayment, indicator.YinbaoPayPayment)
		case enum.PayModeMoneyDimensionRuralCommercialBankPayment:
			i.RuralCommercialBankPayment = utils.Add(i.RuralCommercialBankPayment, indicator.RuralCommercialBankPayment)
		case enum.PayModeMoneyDimensionCreditPayment:
			i.CreditPayment = utils.Add(i.CreditPayment, indicator.CreditPayment)
		case enum.PayModeMoneyDimensionYibaoPayment:
			i.YibaoPayment = utils.Add(i.YibaoPayment, indicator.YibaoPayment)
		case enum.PayModeMoneyDimensionAlipayPayment:
			i.AlipayPayment = utils.Add(i.AlipayPayment, indicator.AlipayPayment)
		case enum.PayModeMoneyDimensionWechatPayment:
			i.WechatPayment = utils.Add(i.WechatPayment, indicator.WechatPayment)
		case enum.PayModeMoneyDimensionPrepaidCardPayment:
			i.PrepaidCardPayment = utils.Add(i.PrepaidCardPayment, indicator.PrepaidCardPayment)
		case enum.PayModeMoneyDimensionOtherPayment:
			i.OtherPayment = utils.Add(i.OtherPayment, indicator.OtherPayment)
		case enum.PayModeMoneyDimensionMeituanCouponPayment:
			i.MeituanCouponPayment = utils.Add(i.MeituanCouponPayment, indicator.MeituanCouponPayment)
		case enum.PayModeMoneyDimensionTotalIncome:
			i.TotalIncome = utils.Add(i.TotalIncome, indicator.TotalIncome)
		}
	}
}

func CalculateVerifyIndicatorFields(
	indicator dmcommon.DoPaginationItem,
) (
	actualVerifyCount,
	actualVerifySalePrice,
	ticketRevenue,
	prepaidConsumptionRevenue,
	totalAmount int,
) {
	//指标数据计算
	verifySalePrice := indicator.VerifySalePrice - indicator.VerifySaleDiscountPrice
	revokeSalePrice := indicator.RevokeSalePrice - indicator.RevokeSaleDiscountPrice
	afterSaleCount := indicator.AfterSaleCount
	afterSalePrice := indicator.AfterSalePrice
	actualVerifyCount = indicator.VerifyCount - indicator.RevokeCount - afterSaleCount
	actualVerifySalePrice = verifySalePrice - revokeSalePrice - afterSalePrice
	//计算门票收入和二消收入（预付卡）
	ticketRevenue = actualVerifySalePrice
	prepaidConsumptionRevenue = 0
	//预付卡支付方式计算不一样
	if utils.Container(enum.PayModePrepaidCard, *indicator.SalePayMode) {
		ticketRevenue = 0
		prepaidConsumptionRevenue = actualVerifySalePrice
	}
	//计算门票收入和二消收入的合计收入
	totalAmount = ticketRevenue + prepaidConsumptionRevenue
	return
}
