package operationdata

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/merchantconfig"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func getMerchantConfig(items []logicdmcommon.DoPaginationItem) (merchantConfig map[int]CalculateIndicatorStrategy, err error) {
	merchantConfig = make(map[int]CalculateIndicatorStrategy)
	merchantIds := make([]int, 0)
	for _, item := range items {
		if item.DistributorId != nil {
			merchantIds = append(merchantIds, *item.DistributorId)
		}
	}
	if len(merchantIds) == 0 {
		return
	}
	configs, err := merchantconfig.ListByKeyAndMerchantIds(enum.MerchantConfigKeyOperationDataReport, merchantIds)
	if err != nil {
		return
	}
	for _, config := range configs {
		if config.Payload == nil {
			continue
		}
		var strategy CalculateIndicatorStrategy
		if err = utils.JsonConvertor(config.Payload, &strategy); err != nil {
			return
		}
		merchantConfig[config.MerchantId] = strategy
	}
	return
}

type CalculateIndicatorStrategy struct {
	EnabledVerifyIndicator bool `json:"enabled_verify_indicator"` // 是否启用核销指标
}

func getEnabledVerifyIndicatorDistributorIds() (distributorIds []int, err error) {
	configs, err := merchantconfig.ListByKey(enum.MerchantConfigKeyOperationDataReport)
	if err != nil {
		return
	}
	distributorIds = make([]int, 0)
	for _, config := range configs {
		var strategy CalculateIndicatorStrategy
		if err = utils.JsonConvertor(config.Payload, &strategy); err != nil {
			err = szerrors.NewLogicErrorWithText("商户计算指标策略解析失败")
			return
		}
		if strategy.EnabledVerifyIndicator {
			distributorIds = append(distributorIds, config.MerchantId)
		}
	}
	return
}
