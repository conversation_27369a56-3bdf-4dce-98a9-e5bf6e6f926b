package operationdata

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
)

type DoPaginationItem struct {
	logicdmcommon.DoDimension
	DoIndicator
}

type DoIndicator struct {
	PayCount        *int `json:"pay_count,omitempty"`         // 预订数量
	PaySalePrice    *int `json:"pay_sale_price,omitempty"`    // 销售金额
	ActualSaleCount *int `json:"actual_sale_count,omitempty"` // 实售数量
	ActualSalePrice *int `json:"actual_sale_price,omitempty"` // 实售金额
	VerifyCount     *int `json:"verify_count,omitempty"`      // 核销数量
	VerifySalePrice *int `json:"verify_sale_price,omitempty"` // 核销金额
	CancelCount     *int `json:"cancel_count,omitempty"`      // 取消数量
	CancelSalePrice *int `json:"cancel_sale_price,omitempty"` // 取消金额
	RevokeCount     *int `json:"revoke_count,omitempty"`      // 撤销数量
	RevokeSalePrice *int `json:"revoke_sale_price,omitempty"` // 撤销金额
	AfterSaleCount  *int `json:"after_sale_count,omitempty"`  // 售后数量
	AfterSalePrice  *int `json:"after_sale_price,omitempty"`  // 售后金额
}

func Paginate(
	merchantId int,
	reportType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	timeGroupType *int,
	doDimensionRange logicdmcommon.DoDimensionRange,
	page, pageSize int,
) (list []DoPaginationItem, total int, err error) {
	operateTypes := getOperateTypes(doTemplate.Indicator)

	doList, total, err := logicdmcommon.Paginate(logicdmcommon.DoQueryParams{
		DoTemplate:     doTemplate,
		MerchantId:     merchantId,
		ReportType:     reportType,
		StartTime:      startTime,
		EndTime:        endTime,
		TimeGroupType:  timeGroupType,
		DimensionRange: doDimensionRange,
		OperateTypes:   operateTypes,
	}, page, pageSize)
	if err != nil {
		return
	}

	calculateIndicatorStrategyMap, err := getMerchantConfig(doList)
	if err != nil {
		return
	}

	list = make([]DoPaginationItem, 0)
	for _, doItem := range doList {
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		strategy, ok := calculateIndicatorStrategyMap[cast.ToInt(doItem.DistributorId)]
		if !ok {
			strategy = CalculateIndicatorStrategy{}
		}
		item.calculateIndicatorFields(doItem.DoIndicator, doTemplate.Indicator, strategy)
		list = append(list, item)
	}
	return
}
