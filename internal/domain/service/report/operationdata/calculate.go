package operationdata

import (
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/pkg/utils"
)

// 指标字段计算
// 先全部算出来，再根据指标模板来决定是否显示
func (i *DoIndicator) calculateIndicatorFields(
	indicator logicdmcommon.DoIndicator,
	templateIndicator []string,
	strategy CalculateIndicatorStrategy,
) {
	// 预订数量：维度汇总内，支付+加票行为数量
	payCount := indicator.PayCount
	// 销售金额：维度汇总内，支付+加票行为销售金额（扣减优惠）
	paySalePrice := indicator.PaySalePrice - indicator.PaySaleDiscountPrice
	// 取消数量：维度汇总内，取消行为数量
	cancelCount := indicator.CancelCount
	// 取消金额：维度汇总内，取消行为销售金额（扣减优惠）
	cancelSalePrice := indicator.CancelSalePrice - indicator.CancelSaleDiscountPrice
	// 撤销数量：维度汇总内，撤销+撤改行为数量
	revokeCount := indicator.RevokeCount
	// 撤销金额：维度汇总内，撤销+撤改行为销售金额（扣减优惠）
	revokeSalePrice := indicator.RevokeSalePrice - indicator.RevokeSaleDiscountPrice
	// 售后数量：维度汇总内，售后行为数量
	afterSaleCount := indicator.AfterSaleCount
	// 售后金额：维度汇总内，售后行为售后金额
	afterSalePrice := indicator.AfterSalePrice
	// 核销数量：需要根据分销商区分
	var verifyCount int
	if strategy.EnabledVerifyIndicator {
		// 分销商为5916699，核销数量=维度汇总内，验证+完结-撤销行为数量
		verifyCount = indicator.VerifyCount - indicator.RevokeCount
	} else {
		// 分销商非5916699，核销数量=维度汇总内，支付+加票-取消-撤销行为的数量
		verifyCount = indicator.PayCount - indicator.CancelCount - indicator.RevokeCount
	}
	// 核销金额：需要根据分销商区分
	var verifySalePrice int
	if strategy.EnabledVerifyIndicator {
		// 分销商为5916699，核销金额=维度汇总内，验证+完结-撤销-撤改行为的销售金额（扣减优惠）
		verifySalePrice = indicator.VerifySalePrice - indicator.VerifySaleDiscountPrice - revokeSalePrice
	} else {
		// 分销商非5916699，核销数量=维度汇总内，支付+加票-取消-撤销-撤改行为的销售金额（扣减优惠）
		verifySalePrice = paySalePrice - cancelSalePrice - revokeSalePrice
	}
	// 实售数量：维度汇总内，支付+加票-取消-撤销行为的数量
	actualSaleCount := indicator.PayCount - indicator.CancelCount - indicator.RevokeCount
	// 实售金额：维度汇总内，支付+加票-取消-撤销行为的销售金额（扣减优惠）
	actualSalePrice := paySalePrice - cancelSalePrice - revokeSalePrice

	for _, item := range templateIndicator {
		switch item {
		case enum.IndicatorPayCount:
			i.PayCount = utils.Add(i.PayCount, &payCount)
		case enum.IndicatorPaySalePrice:
			i.PaySalePrice = utils.Add(i.PaySalePrice, &paySalePrice)
		case enum.IndicatorActualSaleCount:
			i.ActualSaleCount = utils.Add(i.ActualSaleCount, &actualSaleCount)
		case enum.IndicatorActualSalePrice:
			i.ActualSalePrice = utils.Add(i.ActualSalePrice, &actualSalePrice)
		case enum.IndicatorVerifyCount:
			i.VerifyCount = utils.Add(i.VerifyCount, &verifyCount)
		case enum.IndicatorVerifySalePrice:
			i.VerifySalePrice = utils.Add(i.VerifySalePrice, &verifySalePrice)
		case enum.IndicatorCancelCount:
			i.CancelCount = utils.Add(i.CancelCount, &cancelCount)
		case enum.IndicatorCancelSalePrice:
			i.CancelSalePrice = utils.Add(i.CancelSalePrice, &cancelSalePrice)
		case enum.IndicatorRevokeCount:
			i.RevokeCount = utils.Add(i.RevokeCount, &revokeCount)
		case enum.IndicatorRevokeSalePrice:
			i.RevokeSalePrice = utils.Add(i.RevokeSalePrice, &revokeSalePrice)
		case enum.IndicatorAfterSaleCount:
			i.AfterSaleCount = utils.Add(i.AfterSaleCount, &afterSaleCount)
		case enum.IndicatorAfterSalePrice:
			i.AfterSalePrice = utils.Add(i.AfterSalePrice, &afterSalePrice)
		}
	}
}

// 获取指标字段的操作类型
func getOperateTypes(indicator []string) (operateTypes []int) {
	operateTypes = make([]int, 0)
	for _, item := range indicator {
		switch item {
		case enum.IndicatorPayCount, enum.IndicatorPaySalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypePay)
		case enum.IndicatorActualSaleCount, enum.IndicatorActualSalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypePay, enum.DWMOperateTypeCancel, enum.DWMOperateTypeRevoke)
		case enum.IndicatorVerifyCount, enum.IndicatorVerifySalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypePay, enum.DWMOperateTypeVerify, enum.DWMOperateTypeCancel, enum.DWMOperateTypeRevoke)
		case enum.IndicatorCancelCount, enum.IndicatorCancelSalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeCancel)
		case enum.IndicatorRevokeCount, enum.IndicatorRevokeSalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeRevoke)
		case enum.IndicatorAfterSaleCount, enum.IndicatorAfterSalePrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeAfterSale)
		}
	}
	operateTypes = utils.RemoveDuplicate(operateTypes)
	return
}
