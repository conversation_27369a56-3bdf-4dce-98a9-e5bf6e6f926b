package operationdata

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func Statistics(
	merchantId int,
	memberId int,
	reportType int,
	templateId int,
	startTime, endTime carbon.Carbon,
	doDimensionRange logicdmcommon.DoDimensionRange,
) (statistics DoIndicator, err error) {
	doTemplate, err := template.Detail(templateId, enum.TemplateCategoryOperationData, merchantId, memberId)
	if err != nil {
		return
	}

	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	// 获取商户计算指标策略
	enabledVerifyIndicatorDistributorIds, err := getEnabledVerifyIndicatorDistributorIds()
	if err != nil {
		return
	}

	doQueryParams := logicdmcommon.DoQueryParams{
		DoTemplate:            *doTemplate,
		MerchantId:            merchantId,
		ReportType:            reportType,
		StartTime:             startTime,
		EndTime:               endTime,
		DimensionRange:        doDimensionRange,
		IncludeDistributorIds: nil,
		ExcludeDistributorIds: nil,
	}

	if len(enabledVerifyIndicatorDistributorIds) <= 0 {
		var poStatistics logicdmcommon.DoIndicator
		doQueryParams.IncludeDistributorIds = enabledVerifyIndicatorDistributorIds
		poStatistics, err = logicdmcommon.Statistics(doQueryParams)
		if err != nil {
			return
		}
		statistics.calculateIndicatorFields(poStatistics, doTemplate.Indicator, CalculateIndicatorStrategy{})
		return
	}

	// 不同分销商计算指标策略不同，需要分开统计
	var includeStatistics, excludeStatistics logicdmcommon.DoIndicator
	doQueryParams.IncludeDistributorIds = enabledVerifyIndicatorDistributorIds
	doQueryParams.ExcludeDistributorIds = nil
	includeStatistics, err = logicdmcommon.Statistics(doQueryParams)
	if err != nil {
		return
	}
	statistics.calculateIndicatorFields(includeStatistics, doTemplate.Indicator, CalculateIndicatorStrategy{EnabledVerifyIndicator: true})

	doQueryParams.IncludeDistributorIds = nil
	doQueryParams.ExcludeDistributorIds = enabledVerifyIndicatorDistributorIds
	excludeStatistics, err = logicdmcommon.Statistics(doQueryParams)
	if err != nil {
		return
	}
	statistics.calculateIndicatorFields(excludeStatistics, doTemplate.Indicator, CalculateIndicatorStrategy{})

	return
}
