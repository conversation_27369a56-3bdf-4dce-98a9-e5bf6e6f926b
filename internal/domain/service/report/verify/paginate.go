package verify

import (
	logicDmCommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"

	"gitee.com/golang-module/carbon/v2"
)

type DoPaginationItem struct {
	logicDmCommon.DoDimension
	DoIndicator
}

func Paginate(
	merchantId int,
	reportType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	timeGroupType *int,
	doDimensionRange logicDmCommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
	page, pageSize int,
) (list []DoPaginationItem, total int, err error) {
	operateTypes := GetOperateTypes(doTemplate.Indicator)

	doList, total, err := logicDmCommon.Paginate(logicDmCommon.DoQueryParams{
		DoTemplate:     doTemplate,
		MerchantId:     merchantId,
		ReportType:     reportType,
		StartTime:      startTime,
		EndTime:        endTime,
		TimeGroupType:  timeGroupType,
		DimensionRange: doDimensionRange,
		OperateTypes:   operateTypes,
		DataLimit:      &dataLimit,
	}, page, pageSize)
	if err != nil {
		return
	}

	list = make([]DoPaginationItem, 0)
	for _, doItem := range doList {
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		item.calculateIndicatorFields(doItem.DoIndicator, doTemplate.Indicator)
		list = append(list, item)
	}
	return
}
