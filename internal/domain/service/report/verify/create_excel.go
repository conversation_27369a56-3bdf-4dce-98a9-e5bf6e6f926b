package verify

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/common/excel"
	"report-service/internal/domain/service/report/common/exportexcel"
	"report-service/internal/domain/service/report/common/filehandle"
	verifyexcel "report-service/internal/domain/service/report/verify/excel"
	"report-service/pkg/utils"
)

const (
	pageSize        = 10000
	ExcelSheetName  = "验证报表"
	ExcelFileName   = "验证报表"
	FileExcelFormat = "%s_%d_%d.xlsx"
	FileZipFormat   = "%s_%d_%d.zip"
)

func CreateExcelData(
	merchantId int,
	memberId int,
	reportType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	timeGroupType *int,
	doDimensionRange logicdmcommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
	uniqueKey int,
) (fileKey string, taskId int, err error) {
	//文件名生成
	var realFilePath, realZipPath, filePath, zipPath string
	filePath = fmt.Sprintf(FileExcelFormat, ExcelFileName, merchantId, uniqueKey)
	zipPath = fmt.Sprintf(FileZipFormat, ExcelFileName, merchantId, uniqueKey)
	realFilePath, err = filehandle.GetRealFilePath(filePath)
	if err != nil {
		return
	}
	realZipPath, err = filehandle.GetRealFilePath(zipPath)
	if err != nil {
		return
	}

	//表头和维度指标字段
	var dimensionKeys, indicatorKeys, heads []string
	dimensionKeys, indicatorKeys, heads = exportexcel.GetExcelHeader(
		doTemplate,
		verifyexcel.TemplateDimensionFieldsToResultMap,
		verifyexcel.TemplateIndicatorFieldsToResultMap,
		verifyexcel.DimensionToTagCenterSceneAndResultTagCodeMap,
	)
	//excel基础配置
	defaultExcelConfig := exportexcel.ExcelConfig{
		MainTitle:    getExcelMainTitle(startTime, endTime, doTemplate.Title),
		SheetName:    ExcelSheetName,
		RealFilePath: realFilePath,
		RealZipPath:  realZipPath,
		FileName:     ExcelFileName,
	}
	//创建excel处理器
	excelProcessor := exportexcel.NewExcelProcessor(
		defaultExcelConfig,
		merchantId,
		memberId,
		dimensionKeys,
		indicatorKeys,
		verifyexcel.PriceFields,
		verifyexcel.DimensionIdAndNameMap,
	)
	//写入抬头
	excelProcessor.WriteTitles(heads)
	//查询总计
	statistics, err := Statistics(merchantId, reportType, doTemplate, startTime, endTime, doDimensionRange, dataLimit)
	if err != nil {
		return
	}
	statisticsData, err := utils.StructToMapViaJSON(statistics)
	if err != nil {
		return
	}
	//写入总计
	excelProcessor.WriteTotal(statisticsData)
	//分页循环处理
	pageNum := 1
	for {
		var total int
		var doList []DoPaginationItem
		doList, total, err = Paginate(merchantId, reportType, doTemplate, startTime, endTime, timeGroupType, doDimensionRange, dataLimit, pageNum, pageSize)
		if err != nil {
			return
		}
		if total == 0 {
			break
		}
		var data []exportexcel.DataRow
		data, err = convertListToSubtotalRowData(doList)
		if err != nil {
			return
		}
		//写入excel
		excelProcessor.WriteList(data, false)
		if total <= pageNum*pageSize {
			break
		}
		pageNum++
	}
	//生成excel文件
	taskId, err = excelProcessor.GenerateFile()
	if err != nil {
		return
	}
	fileKey = filePath
	return
}

// getExcelMainTitle 获取主标题名称
func getExcelMainTitle(startTime, endTime carbon.Carbon, title string) string {
	return "(" + startTime.ToDateTimeString() + " 至 " + endTime.ToDateTimeString() + ") " + title
}

// convertListToSubtotalRowData 转换列表数据为excel行数据
func convertListToSubtotalRowData(doList []DoPaginationItem) ([]exportexcel.DataRow, error) {
	dataRow := make([]exportexcel.DataRow, 0)
	for _, doItem := range doList {
		var item exportexcel.DataRow
		dimension := doItem.DoDimension
		indicator := doItem.DoIndicator
		itemDimensions, err := utils.StructToMapViaJSON(dimension)
		if err != nil {
			return nil, err
		}
		item.Dimensions = make(map[string]interface{}, 0)
		for k, v := range itemDimensions {
			if utils.Container([]string{excel.FieldSellOperatorName, excel.FieldSellSiteName}, k) {
				if val, ok := v.(string); ok && val == "" {
					item.Dimensions[k] = "--"
					continue
				}
			}
			if utils.Container(verifyexcel.PriceFields, k) {
				if val, ok := v.(float64); ok && val == 0 {
					item.Dimensions[k] = utils.ConvertCentToYuanByFloat64(val)
					continue
				}
			}
			item.Dimensions[k] = v
		}
		itemIndicators, err := utils.StructToMapViaJSON(indicator)
		if err != nil {
			return nil, err
		}
		item.Metrics = itemIndicators
		dataRow = append(dataRow, item)
	}
	return dataRow, nil
}
