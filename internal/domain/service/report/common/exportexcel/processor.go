package exportexcel

import (
	"fmt"
	"go.uber.org/zap"
	"report-service/internal/domain/service/report/common/filehandle"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"
	"report-service/pkg/utils/excel"
	"report-service/pkg/utils/zip"
)

// ExcelProcessor Excel处理器接口
type ExcelProcessor interface {
	WriteTitles(heads []string)
	WriteTotal(map[string]interface{})
	WriteList(data []DataRow, mergeHorizontal bool)
	GenerateFile() (int, error)
	CleanupTempFiles()
}

type excelProcessor struct {
	merchantId             int
	memberId               int
	config                 ExcelConfig
	dimensionKeys          []string
	indicatorKeys          []string
	priceFields            []string
	excelData              DoExcelData
	dimensionIdAndNameMaps map[string]string
}

func NewExcelProcessor(config ExcelConfig, merchantId, memberId int, dimensionKeys, indicatorKeys, priceFields []string, dimensionIdAndNameMaps map[string]string) ExcelProcessor {
	return &excelProcessor{
		config:                 config,
		merchantId:             merchantId,
		memberId:               memberId,
		dimensionKeys:          dimensionKeys,
		indicatorKeys:          indicatorKeys,
		priceFields:            priceFields,
		dimensionIdAndNameMaps: dimensionIdAndNameMaps,
		excelData:              createExcelBaseData(config.MainTitle, config.SheetName),
	}
}

func (p *excelProcessor) WriteTitles(heads []string) {
	cellData := make([]interface{}, 0, len(heads))
	cellStyle := make([]excel.CellStyle, 0, len(heads))
	for _, name := range heads {
		cellData = append(cellData, name)
		cellStyle = append(cellStyle, TitleCellStyle())
	}
	//写入抬头
	p.excelData.Data = append(p.excelData.Data, cellData)
	p.excelData.DataStyles = append(p.excelData.DataStyles, cellStyle)
	return
}

func (p *excelProcessor) WriteTotal(indicatorData map[string]interface{}) {
	cellData := make([]interface{}, 0, len(indicatorData))
	cellStyle := make([]excel.CellStyle, 0, len(indicatorData))
	for range p.dimensionKeys {
		cellData = append(cellData, "合计:")
		cellStyle = append(cellStyle, SubTotalCellStyle(true, false))
	}
	for _, key := range p.indicatorKeys {
		if val, ok := indicatorData[key]; ok {
			if p.priceFields != nil && utils.Container(p.priceFields, key) {
				if v, vOk := val.(float64); vOk {
					cellData = append(cellData, utils.ConvertCentToYuanByFloat64(v))
				} else {
					cellData = append(cellData, val)
				}
			} else {
				cellData = append(cellData, val)
			}
		} else {
			cellData = append(cellData, "")
		}
		cellStyle = append(cellStyle, SubTotalCellStyle(false, false))
	}
	//写入合计
	p.excelData.Data = append(p.excelData.Data, cellData)
	p.excelData.DataStyles = append(p.excelData.DataStyles, cellStyle)
	return
}

func (p *excelProcessor) WriteList(data []DataRow, mergeHorizontal bool) {
	generator := NewSubtotalGenerator(p.dimensionKeys, p.indicatorKeys, p.dimensionIdAndNameMaps, mergeHorizontal)
	// 生成小计
	list, marks := generator.GenerateSubtotals(data)
	maxCellNum := len(p.dimensionKeys) + len(p.indicatorKeys)
	//写入数据
	for k, item := range list {
		cellData := make([]interface{}, 0, maxCellNum)
		cellStyle := make([]excel.CellStyle, 0, maxCellNum)
		isSubTotal := false
		//维度数据写入
		for _, key := range p.dimensionKeys {
			mergeCellH := false
			mergeCellV := false
			if marks != nil && marks[key] != nil && (marks[key][k].Vertical || marks[key][k].Horizontal) {
				mergeCellH = marks[key][k].Horizontal
				mergeCellV = marks[key][k].Vertical
				isSubTotal = marks[key][k].Sum
			}
			if v, ok := item.Dimensions[key]; ok {
				if p.priceFields != nil && utils.Container(p.priceFields, key) {
					if vi, viOk := v.(float64); viOk {
						cellData = append(cellData, utils.ConvertCentToYuanByFloat64(vi))
					} else {
						cellData = append(cellData, v)
					}
				} else {
					cellData = append(cellData, v)
				}
				if isSubTotal {
					cellStyle = append(cellStyle, SubTotalCellStyle(mergeCellH, mergeCellV))
					continue
				}
				cellStyle = append(cellStyle, DefaultCellStyle(mergeCellH, mergeCellV))
			} else {
				cellData = append(cellData, "")
				if isSubTotal {
					cellStyle = append(cellStyle, SubTotalCellStyle(mergeCellH, mergeCellV))
					continue
				}
				cellStyle = append(cellStyle, DefaultCellStyle(mergeCellH, mergeCellV))
			}
		}
		//指标数据写入
		for _, key := range p.indicatorKeys {
			if v, ok := item.Metrics[key]; ok {
				if p.priceFields != nil && utils.Container(p.priceFields, key) {
					if vi, viOk := v.(float64); viOk {
						cellData = append(cellData, utils.ConvertCentToYuanByFloat64(vi))
					} else {
						cellData = append(cellData, v)
					}
				} else {
					cellData = append(cellData, v)
				}
				if isSubTotal {
					cellStyle = append(cellStyle, SubTotalCellStyle(false, false))
					continue
				}
				cellStyle = append(cellStyle, DefaultCellStyle(false, false))
			} else {
				cellData = append(cellData, "")
				if isSubTotal {
					cellStyle = append(cellStyle, SubTotalCellStyle(false, false))
					continue
				}
				cellStyle = append(cellStyle, DefaultCellStyle(false, false))
			}
		}
		p.excelData.Data = append(p.excelData.Data, cellData)
		p.excelData.DataStyles = append(p.excelData.DataStyles, cellStyle)
	}
	return
}

func (p *excelProcessor) GenerateFile() (taskId int, err error) {
	defer p.CleanupTempFiles() //结束清除文件
	err = excel.GenerateExcelWithMerge(p.config.RealFilePath, p.excelData.ExcelData)
	if err != nil {
		err = fmt.Errorf("生成Excel失败: %v\n", err)
		return
	}
	err = zip.ZipFile(p.config.RealFilePath, p.config.RealZipPath)
	if err != nil {
		err = fmt.Errorf("压缩Excel失败: %v\n", err)
		return
	}
	//提交下载任务
	taskId, err = downloadcenter.AddFileTask(p.merchantId, p.memberId, p.config.FileName, p.config.RealZipPath)
	if err != nil {
		return
	}
	return
}

func (p *excelProcessor) CleanupTempFiles() {
	// 删除Excel文件
	if rmErr := filehandle.RmRealFilePath(p.config.RealFilePath); rmErr != nil {
		global.LOG.Error("删除Excel临时文件失败", zap.String("file", p.config.RealFilePath), zap.Any("error", rmErr))
		globalNotice.Warning(fmt.Sprintf("删除Excel临时文件失败，file：%s，err：%v", p.config.RealFilePath, rmErr))

	}
	// 删除ZIP文件
	if rmErr := filehandle.RmRealFilePath(p.config.RealZipPath); rmErr != nil {
		global.LOG.Error("删除Zip临时文件失败", zap.String("file", p.config.RealZipPath), zap.Any("error", rmErr))
		globalNotice.Warning(fmt.Sprintf("删除Zip临时文件失败，file：%s，err：%v", p.config.RealZipPath, rmErr))
	}
}

func createExcelBaseData(title string, sheetName string) DoExcelData {
	//生成excel数据
	return DoExcelData{excel.ExcelData{
		SheetName:   sheetName,
		Title:       title,
		MergeTitles: true,
		Headers:     []excel.HeaderRow{},
		Data:        [][]interface{}{},
		DataStyles:  [][]excel.CellStyle{},
		TitleStyle:  GetTitleStyle(),
	}}
}
