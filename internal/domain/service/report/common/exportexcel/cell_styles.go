package exportexcel

import "report-service/pkg/utils/excel"

// GetTitleStyle 获取主标题默认样式
func GetTitleStyle() *excel.CellStyle {
	border := true
	return &excel.CellStyle{
		Bold:       true,
		FontSize:   14,
		Horizontal: "center",
		Vertical:   "center",
		BgColor:    "#e5f5fd",
		FontColor:  "#4c4e53",
		Border:     &border,
	}
}

// TitleCellStyle 标题列样式
func TitleCellStyle() excel.CellStyle {
	border := true
	return excel.CellStyle{
		FontSize:            10,
		Bold:                true,
		BgColor:             "#E9E9EB",
		FontColor:           "#606266",
		Horizontal:          "center",
		Vertical:            "center",
		Border:              &border,
		MergeCellVertical:   false,
		MergeCellHorizontal: false,
	}
}

// DefaultCellStyle 默认列样式
func DefaultCellStyle(mCH, mCV bool) excel.CellStyle {
	return excel.CellStyle{
		FontSize:            10,
		Bold:                false,
		BgColor:             "#FFFFFF",
		MergeCellVertical:   mCV,
		MergeCellHorizontal: mCH,
	}
}

// SubTotalCellStyle 小计列样式
func SubTotalCellStyle(mCH, mCV bool) excel.CellStyle {
	return excel.CellStyle{
		FontSize:            10,
		Bold:                false,
		BgColor:             "#f5f5f5",
		MergeCellVertical:   mCV,
		MergeCellHorizontal: mCH,
	}
}
