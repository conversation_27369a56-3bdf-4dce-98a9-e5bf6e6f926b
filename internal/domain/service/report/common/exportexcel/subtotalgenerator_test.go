package exportexcel

import (
	"fmt"
	"testing"
)

func TestNewSubtotalGenerator(t *testing.T) {
	// 定义维度和指标键
	dimensionKeys := []string{"region", "city", "store", "storeX"}
	metricKeys := []string{"sales", "profit"}
	dimensionIdAndNameMaps := map[string]string{
		"region": "region_name",
		"city":   "city_name",
		"store":  "store_name",
		"storeX": "store_nameX",
	}

	// 创建合计生成器
	generator := NewSubtotalGenerator(dimensionKeys, metricKeys, dimensionIdAndNameMaps, false)

	// 示例数据
	data := []DataRow{
		{
			Dimensions: map[string]interface{}{
				"region":      123,
				"region_name": "华东",
				"city":        12,
				"city_name":   "上海",
				"store":       11,
				"store_name":  "门店A",
				"storeX":      22,
				"store_nameX": "门店1",
			},
			Metrics: map[string]interface{}{
				"sales":  10000,
				"profit": 2000,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      123,
				"city":        12,
				"store":       22,
				"storeX":      33,
				"region_name": "华东",
				"city_name":   "上海",
				"store_name":  "门店C",
				"store_nameX": "门店1",
			},
			Metrics: map[string]interface{}{
				"sales":  10000,
				"profit": 2000,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      123,
				"city":        12,
				"store":       22,
				"storeX":      44,
				"region_name": "华东",
				"city_name":   "上海",
				"store_name":  "门店C",
				"store_nameX": "门店2",
			},
			Metrics: map[string]interface{}{
				"sales":  10000,
				"profit": 2000,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      123,
				"city":        13,
				"store":       33,
				"storeX":      33,
				"region_name": "华东",
				"city_name":   "福建",
				"store_name":  "门店B",
				"store_nameX": "门店B",
			},
			Metrics: map[string]interface{}{
				"sales":  15000,
				"profit": 3000,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      122,
				"city":        14,
				"store":       44,
				"storeX":      66,
				"region_name": "华南",
				"city_name":   "广州",
				"store_name":  "门店D",
				"store_nameX": "门店1",
			},
			Metrics: map[string]interface{}{
				"sales":  12000,
				"profit": 2400,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      122,
				"city":        14,
				"store":       44,
				"storeX":      77,
				"region_name": "华南",
				"city_name":   "广州",
				"store_name":  "门店D",
				"store_nameX": "门店2",
			},
			Metrics: map[string]interface{}{
				"sales":  12000,
				"profit": 2400,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      122,
				"city":        15,
				"store":       55,
				"storeX":      66,
				"region_name": "华南",
				"city_name":   "广州2",
				"store_name":  "门店D-1",
				"store_nameX": "门店1",
			},
			Metrics: map[string]interface{}{
				"sales":  12000,
				"profit": 2400,
			},
		},
		{
			Dimensions: map[string]interface{}{
				"region":      122,
				"city":        15,
				"store":       66,
				"storeX":      66,
				"region_name": "华南",
				"city_name":   "广州2",
				"store_name":  "门店D-2",
				"store_nameX": "门店2",
			},
			Metrics: map[string]interface{}{
				"sales":  12000,
				"profit": 2400,
			},
		},
	}

	// 生成小计
	results, marks := generator.GenerateSubtotals(data)

	// 输出结果
	for _, row := range results {
		//map结构无序，需要重新根据key排序
		for _, key := range dimensionKeys {
			fmt.Printf("%s: %v, ", key, row.Dimensions[key])
		}
		for _, key := range metricKeys {
			fmt.Printf("%s: %.2f, ", key, row.Metrics[key])
		}
		fmt.Println("")
	}
	fmt.Println(marks)
}
