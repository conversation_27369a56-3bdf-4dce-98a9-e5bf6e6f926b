package exportexcel

import (
	"fmt"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/global"
)

// GetExcelHeader 获取Excel表头和维度指标key
func GetExcelHeader(doTemplate template.DoListItem,
	dimensionFieldsToResultMap map[string]struct {
		Field string `json:"field"`
		Name  string `json:"name"`
	},
	indicatorFieldsToResultMap map[string]struct {
		Field string `json:"field"`
		Name  string `json:"name"`
	},
	TagCenterSceneAndResultMap map[string]struct {
		SceneKey string
		TagKey   string
	},
) ([]string, []string, []string) {
	fields := append(doTemplate.Dimension, doTemplate.Indicator...)
	dimensionKeys := make([]string, 0)
	indicatorKeys := make([]string, 0)
	heads := make([]string, 0)
	for _, field := range fields {
		if fieldKey, ok := dimensionFieldsToResultMap[field]; ok {
			heads = append(heads, fieldKey.Name)
			// 检查报表模板是否使用了标签汇总，需要转换成tag_code字段
			if tagInfo, exists := TagCenterSceneAndResultMap[field]; exists {
				if tagGroup, tagExists := doTemplate.DimensionTagGroup[tagInfo.SceneKey]; tagExists && tagGroup.Code != "" {
					dimensionKeys = append(dimensionKeys, tagInfo.TagKey)
					continue
				}
			}
			dimensionKeys = append(dimensionKeys, fieldKey.Field)
		} else if fieldKey, ok = indicatorFieldsToResultMap[field]; ok {
			heads = append(heads, fieldKey.Name)
			indicatorKeys = append(indicatorKeys, fieldKey.Field)
		} else {
			//记录下错误，不做错误返回
			global.LOG.Error(fmt.Sprintf("报表模板字段，不存在值%s，取值失败", field))
			continue
		}
	}
	return dimensionKeys, indicatorKeys, heads
}
