package exportexcel

import (
	"fmt"
	"sort"
	"strings"
)

// DataRow 表示列表中的一行数据
type DataRow struct {
	// Dimensions 维度数据
	Dimensions map[string]interface{}
	// Metrics 指标数据
	Metrics map[string]interface{}
}

// SubtotalGenerator 合计生成器
type SubtotalGenerator struct {
	dimensionKeys          []string          //维度keys
	metricKeys             []string          //指标keys
	dimensionIdAndNameMaps map[string]string //维度id和名称映射
	mergeHorizontal        bool              //是否横向合并
}

type MergeMarksItem struct {
	//横向
	Horizontal bool
	//纵向
	Vertical bool
	//合计标识
	Sum bool
}

// NewSubtotalGenerator 创建新的合计生成器
func NewSubtotalGenerator(dimensionKeys, metricKeys []string, dimensionIdAndNameMaps map[string]string, mergeHorizontal bool) *SubtotalGenerator {
	return &SubtotalGenerator{
		dimensionKeys:          dimensionKeys,
		metricKeys:             metricKeys,
		dimensionIdAndNameMaps: dimensionIdAndNameMaps,
		mergeHorizontal:        mergeHorizontal,
	}
}

// GenerateSubtotals 生成多级小计行
func (sg *SubtotalGenerator) GenerateSubtotals(data []DataRow) ([]DataRow, map[string]map[int]MergeMarksItem) {
	if len(data) == 0 {
		return []DataRow{}, map[string]map[int]MergeMarksItem{}
	}
	// 按维度排序
	sortedData := sg.sortByDimensions(data)

	// 为每个维度层级生成小计（除了第一个维度和最后一个维度）
	for level := 0; level < len(sg.dimensionKeys)-1; level++ {
		sortedData = sg.generateLevelSubtotal(sortedData, level)
	}
	// 生成结果 （维度需要转换成维度名称，合计不处理）
	return sg.GenerateRepeatedValues(sortedData), sg.GenerateAdjacentMarks(sortedData)
}

func (sg *SubtotalGenerator) GenerateRepeatedValues(data []DataRow) []DataRow {
	result := make([]DataRow, 0)
	for _, val := range data {
		if sg.isSubtotalRow(val) {
			result = append(result, val)
			continue
		}
		var item DataRow
		item.Metrics = val.Metrics
		item.Dimensions = make(map[string]interface{}, len(sg.dimensionKeys))
		for _, key := range sg.dimensionKeys {
			valKey := sg.dimensionIdAndNameMaps[key]
			item.Dimensions[key] = ""
			if v, ok := val.Dimensions[fmt.Sprintf("%v", valKey)]; ok {
				item.Dimensions[key] = v
			}
		}
		result = append(result, item)
	}
	return result
}

// GenerateAdjacentMarks 生成相邻行相同维度的标记, 用于单元格是否合并
func (sg *SubtotalGenerator) GenerateAdjacentMarks(data []DataRow) map[string]map[int]MergeMarksItem {
	result := make(map[string]map[int]MergeMarksItem)
	// 初始化结果map
	for _, dimKey := range sg.dimensionKeys {
		result[dimKey] = make(map[int]MergeMarksItem)
	}
	dataLen := len(data)
	// 遍历数据行，比较相邻行的维度值
	for i := 0; i < dataLen; i++ {
		for dimIndex, dimKey := range sg.dimensionKeys {
			// 获取当前行和前一行的维度值
			currentVal, currentExists := data[i].Dimensions[dimKey]
			// 如果是小计行，标记为横向true并继续
			if sg.isSubtotalRow(data[i]) {
				// 检查是否存在标记为小计的维度, 标记第一个合计
				firstSubTotalIndex := 0
				for checkDimIndex, key := range sg.dimensionKeys {
					if val, exists := data[i].Dimensions[key]; exists {
						if str, ok := val.(string); ok && strings.Contains(str, "合计") {
							firstSubTotalIndex = checkDimIndex
							break
						}
					}
				}
				if dimIndex < firstSubTotalIndex {
					result[dimKey][i] = MergeMarksItem{
						Horizontal: false,
						Vertical:   true,
					}
				} else {
					result[dimKey][i] = MergeMarksItem{
						Horizontal: true,
						Vertical:   false,
						Sum:        true,
					}
				}
				continue
			}
			//设置默认值
			resultItem := result[dimKey][i]
			resultItem = MergeMarksItem{}
			// 比较与下一行的值
			if currentExists && i+1 < dataLen {
				nextVal, nextExists := data[i+1].Dimensions[dimKey]
				if nextExists && fmt.Sprintf("%v", currentVal) == fmt.Sprintf("%v", nextVal) {
					resultItem.Vertical = true
				}
			}
			// 比较与上一行的值（如果尚未标记为true）
			if !result[dimKey][i].Vertical && currentExists && i+1 < dataLen && i >= 1 {
				previousVal, previousExists := data[i-1].Dimensions[dimKey]
				if previousExists && fmt.Sprintf("%v", currentVal) == fmt.Sprintf("%v", previousVal) {
					resultItem.Vertical = true
				}
			}
			//末行反向处理
			if !result[dimKey][i].Vertical && currentExists && i+1 == dataLen && i >= 1 {
				previousValL, previousExistsL := data[i-1].Dimensions[dimKey]
				if previousExistsL && fmt.Sprintf("%v", currentVal) == fmt.Sprintf("%v", previousValL) {
					resultItem.Vertical = true
				}
			}

			//横向合并处理
			if sg.mergeHorizontal {
				// 比较与右一列的值
				if currentExists && dimIndex+1 < len(sg.dimensionKeys) {
					nextValH, nextExistsH := data[i].Dimensions[sg.dimensionKeys[dimIndex+1]]
					if nextExistsH && fmt.Sprintf("%v", currentVal) == fmt.Sprintf("%v", nextValH) {
						resultItem.Horizontal = true
					}
				}
				// 比较与左一列的值（如果尚未标记为true）
				if !result[dimKey][i].Horizontal && currentExists && dimIndex >= 1 {
					previousValH, previousExistsH := data[i].Dimensions[sg.dimensionKeys[dimIndex-1]]
					if previousExistsH && fmt.Sprintf("%v", currentVal) == fmt.Sprintf("%v", previousValH) {
						resultItem.Horizontal = true
					}
				}
				//末列反向处理
				if !result[dimKey][i].Horizontal && currentExists && dimIndex+1 == len(sg.dimensionKeys) && dimIndex >= 1 {
					previousValHL, previousExistsHL := data[i].Dimensions[sg.dimensionKeys[dimIndex-1]]
					if previousExistsHL && fmt.Sprintf("%v", currentVal) == fmt.Sprintf("%v", previousValHL) {
						resultItem.Horizontal = true
					}
				}
			}
			result[dimKey][i] = resultItem
		}
	}
	return result
}

// sortByDimensions 按维度键排序数据，支持数字和字符串排序（降序）
func (sg *SubtotalGenerator) sortByDimensions(data []DataRow) []DataRow {
	sortedData := make([]DataRow, len(data))
	copy(sortedData, data)

	sort.Slice(sortedData, func(i, j int) bool {
		// 按照 dimensionKeys 的顺序依次比较
		for _, key := range sg.dimensionKeys {
			valI, okI := sortedData[i].Dimensions[key]
			valJ, okJ := sortedData[j].Dimensions[key]

			if !okI && !okJ {
				continue
			}
			if !okI {
				return false
			}
			if !okJ {
				return true
			}

			// 判断值的类型并进行相应排序
			switch vI := valI.(type) {
			case float64:
				if vJ, ok := valJ.(float64); ok {
					if vI > vJ {
						return true
					} else if vI < vJ {
						return false
					}
					continue // 相等，比较下一个维度
				}
			case int:
				if vJ, ok := valJ.(int); ok {
					if vI > vJ {
						return true
					} else if vI < vJ {
						return false
					}
					continue // 相等，比较下一个维度
				}
			case string:
				if vJ, ok := valJ.(string); ok {
					if vI > vJ {
						return true
					} else if vI < vJ {
						return false
					}
					continue // 相等，比较下一个维度
				}
			}

			// 默认使用字符串表示比较
			strI := fmt.Sprintf("%v", valI)
			strJ := fmt.Sprintf("%v", valJ)

			if strI > strJ {
				return true
			} else if strI < strJ {
				return false
			}
		}
		return false
	})

	return sortedData
}

// generateLevelSubtotal 为指定层级生成小计
func (sg *SubtotalGenerator) generateLevelSubtotal(data []DataRow, level int) []DataRow {
	var result []DataRow
	i := 0

	for i < len(data) {
		// 跳过已有的小计行
		if sg.isSubtotalRow(data[i]) {
			result = append(result, data[i])
			i++
			continue
		}

		// 找到具有相同层级维度值的连续行
		j := i
		for j < len(data) && sg.hasSameDimensionsUpToLevel(data[i], data[j], level) {
			j++
		}

		// 提取当前组的数据（不包括小计行）
		var groupData []DataRow
		for k := i; k < j; k++ {
			if !sg.isSubtotalRow(data[k]) {
				groupData = append(groupData, data[k])
			}
		}

		// 添加原始数据行到结果中
		for k := i; k < j; k++ {
			result = append(result, data[k])
		}

		// 只有当组内有多行数据时才生成小计
		if len(groupData) > 1 {
			subtotal := sg.calculateSubtotal(groupData, level)
			result = append(result, subtotal)
		}

		i = j
	}

	return result
}

// hasSameDimensionsUpToLevel 检查两个数据行在指定层级之前是否具有相同的维度值
func (sg *SubtotalGenerator) hasSameDimensionsUpToLevel(row1, row2 DataRow, level int) bool {
	if sg.isSubtotalRow(row1) || sg.isSubtotalRow(row2) {
		return false
	}
	// 比较从第0层到当前level层的所有维度
	for i := 0; i <= level; i++ {
		key := sg.dimensionKeys[i]
		val1, ok1 := row1.Dimensions[key]
		val2, ok2 := row2.Dimensions[key]

		if !ok1 && !ok2 {
			continue
		}
		if !ok1 || !ok2 {
			return false
		}
		if fmt.Sprintf("%v", val1) != fmt.Sprintf("%v", val2) {
			return false
		}
	}
	return true
}

// isSubtotalRow 判断是否为小计行
func (sg *SubtotalGenerator) isSubtotalRow(row DataRow) bool {
	// 检查是否存在标记为小计的维度
	for _, key := range sg.dimensionKeys {
		if val, exists := row.Dimensions[key]; exists {
			if str, ok := val.(string); ok && strings.Contains(str, "合计") {
				return true
			}
		}
	}
	return false
}

// calculateSubtotal 计算指定层级的小计行
func (sg *SubtotalGenerator) calculateSubtotal(groupData []DataRow, level int) DataRow {
	subtotal := DataRow{
		Dimensions: make(map[string]interface{}),
		Metrics:    make(map[string]interface{}),
	}

	// 复制到当前层级的所有维度值
	for i := 0; i <= level; i++ {
		key := sg.dimensionKeys[i]
		if _, exists := groupData[0].Dimensions[key]; exists {
			valKey := sg.dimensionIdAndNameMaps[key]
			subtotal.Dimensions[key] = ""
			if v, ok := groupData[0].Dimensions[fmt.Sprintf("%v", valKey)]; ok {
				subtotal.Dimensions[key] = v
			}
		}
	}

	currentLevelKey := sg.dimensionKeys[level]
	// 当前层级+1的维度设置为"小计"
	if level+1 < len(sg.dimensionKeys) {
		nextLevelKey := sg.dimensionKeys[level+1]
		subtotal.Dimensions[nextLevelKey] = fmt.Sprintf("%v--(%v)", subtotal.Dimensions[currentLevelKey], "本页合计")
	}

	// 后续维度置空或也设为小计（保持一致性）
	for i := level + 2; i < len(sg.dimensionKeys); i++ {
		subtotal.Dimensions[sg.dimensionKeys[i]] = fmt.Sprintf("%v--(%v)", subtotal.Dimensions[currentLevelKey], "本页合计")
	}

	// 计算指标合计
	for _, key := range sg.metricKeys {
		var sum float64
		var hasValidValue bool
		for _, row := range groupData {
			if val, exists := row.Metrics[key]; exists {
				// 安全地将值转换为float64
				if numericVal := sg.toNumeric(val); numericVal != nil {
					sum += *numericVal
					hasValidValue = true
				}
			}
		}
		// 只有当存在有效值时才设置合计值
		if hasValidValue {
			subtotal.Metrics[key] = sum
		} else {
			subtotal.Metrics[key] = 0.0
		}
	}
	return subtotal
}

// toNumeric 安全地将interface{}转换为float64指针
func (sg *SubtotalGenerator) toNumeric(val interface{}) *float64 {
	switch v := val.(type) {
	case float64:
		return &v
	case float32:
		result := float64(v)
		return &result
	case int:
		result := float64(v)
		return &result
	case int32:
		result := float64(v)
		return &result
	case int64:
		result := float64(v)
		return &result
	case string:
		// 尝试解析字符串为数字
		if f, err := fmt.Sscanf(v, "%f", new(float64)); err == nil && f == 1 {
			var result float64
			_, _ = fmt.Sscanf(v, "%f", &result)
			return &result
		}
		return nil
	default:
		// 尝试通过字符串形式转换
		if str := fmt.Sprintf("%v", v); str != "" {
			if f, err := fmt.Sscanf(str, "%f", new(float64)); err == nil && f == 1 {
				var result float64
				_, _ = fmt.Sscanf(str, "%f", &result)
				return &result
			}
		}
		return nil
	}
}
