package detail

const (
	FieldOrderNo                    = "order_no"                      // 订单编号
	FieldParentOrderNo              = "parent_order_no"               // 主订单号
	FieldProductTypeName            = "product_type_name"             // 产品类型
	FieldSpuName                    = "spu_name"                      // 产品名称
	FieldSkuName                    = "sku_name"                      // 票名称
	FieldPlaytime                   = "playtime"                      // 游玩日期
	FieldBeginTime                  = "begin_time"                    // 开始有效日期
	FieldEndTime                    = "end_time"                      // 截止有效日期
	FieldSaleChannelName            = "sale_channel_name"             // 订单下单渠道
	FieldStatusName                 = "status_name"                   // 订单状态
	FieldCostUnitPrice              = "cost_unit_price"               // 采购单价
	FieldSaleUnitPrice              = "sale_unit_price"               // 销售单价
	FieldCostPayModeName            = "cost_pay_mode_name"            // 买入支付方式
	FieldSalePayModeName            = "sale_pay_mode_name"            // 卖出支付方式
	FieldOrderName                  = "order_name"                    // 取票人姓名
	FieldOrderTel                   = "order_tel"                     // 取票人手机
	FieldPersonId                   = "person_id"                     // 取票人证件号
	FieldDistributorCName           = "distributor_c_name"            // 分销商企业名称
	FieldDistributorDName           = "distributor_d_name"            // 分销商账户名称
	FieldDistributorGroupName       = "distributor_group_name"        // 分销商分组
	FieldSupplierDName              = "supplier_d_name"               // 供应商账户名称
	FieldMemo                       = "memo"                          // 订单备注
	FieldSellOperatorName           = "sell_operator_name"            // 售票员
	FieldSellSiteName               = "sell_site_name"                // 售票站点
	FieldTOrderNum                  = "t_order_num"                   // 上游订单号
	FieldRemoteNum                  = "remote_num"                    // 下游订单号
	FieldCmbId                      = "cmb_id"                        // 合并支付订单号
	FieldSectionTimeStr             = "section_time_str"              // 分时时间段
	FieldOperateTypeName            = "operate_type_name"             // 操作类型
	FieldOperatedAt                 = "operated_at"                   // 操作时间
	FieldOperateCount               = "operate_count"                 // 数量
	FieldOperateIncomePrice         = "operate_income_price"          // 收入金额
	FieldOperateIncomeDiscountPrice = "operate_income_discount_price" // 收入优惠金额
	FieldOperatePayPrice            = "operate_pay_price"             // 支出金额
	FieldOperatePayDiscountPrice    = "operate_pay_discount_price"    // 支出优惠金额
	FieldOperateChannelName         = "operate_channel_name"          // 操作渠道
	FieldOperatorName               = "operator_name"                 // 操作员
	FieldOperateSiteName            = "operate_site_name"             // 操作站点
	FieldCancelCostFee              = "cancel_cost_fee"               // 退票手续费
	FieldAfterSaleNo                = "after_sale_no"                 // 售后编号
	FieldPaymentPayCenterSerialNo   = "pay_serial_no"                 // 支付中心支付订单号
	FieldPayCenterOrderNo           = "pay_center_order_no"           // 支付中心订单号
	FieldOrderTime                  = "order_time"                    // 下单时间
	FieldTargetAudience             = "target_audience"               // 适用人群
	FieldGroupMemberName            = "group_member_d_name"           // 集团成员名称
)
