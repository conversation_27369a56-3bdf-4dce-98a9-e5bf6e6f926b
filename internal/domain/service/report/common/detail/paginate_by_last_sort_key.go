package detail

import (
	"gitee.com/golang-module/carbon/v2"
	dmcommon "report-service/internal/domain/logic/dm/common"
	dwmcommon "report-service/internal/domain/logic/dwm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

func PaginateByLastSortKey(
	doTemplate template.DoListItem,
	operateTypes []int,
	merchantId int,
	startTime, endTime carbon.Carbon,
	doDimensionRange dmcommon.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
	lastSortKey *string,
	pageSize int,
) (list []map[string]interface{}, nextLastSortKey string, err error) {
	models, nextLastSortKey, err := dwmcommon.PaginateByLastSortKey(dwmcommon.DoQueryParams{
		DoTemplate:     doTemplate,
		OperateTypes:   operateTypes,
		MerchantId:     merchantId,
		StartTime:      startTime,
		EndTime:        endTime,
		DimensionRange: doDimensionRange,
		DataLimit:      &dataLimit,
	}, lastSortKey, pageSize)
	if err != nil {
		return
	}
	list, err = ConvertCommonModelsToFields(models, doTemplate)
	return
}
