package detail

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dwm/common"
	"report-service/internal/domain/logic/ods"
	reportcommon "report-service/internal/domain/logic/report/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/sdk/api/biztradeorderservice"
	"report-service/pkg/sdk/api/evolutegroupservice"
	"report-service/pkg/sdk/api/landservice"
	"report-service/pkg/sdk/api/ordercenter"
	"report-service/pkg/sdk/api/orderqueryservice"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/sdk/api/tagcenter/tag"
	"report-service/pkg/sdk/api/ticketservice"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
)

func ConvertCommonModelsToFields(models []common.Model, doTemplate template.DoListItem) (list []map[string]interface{}, err error) {
	if len(models) == 0 {
		return nil, nil
	}

	list = make([]map[string]interface{}, 0)

	spuIdKeyNameMap, err := getSpuIdKeyNameMap(models)
	if err != nil {
		return
	}

	skuIdKeyNameMap, err := getSkuIdKeyNameMap(models)
	if err != nil {
		return
	}

	memberIdKeyMemberInfoMap, err := getMemberIdKeyMemberInfoMap(models)
	if err != nil {
		return
	}

	fidKeyMemberExtMap, err := getFidKeyMemberExtMap(models)
	if err != nil {
		return
	}

	siteIdKeyNameMap, err := getSiteIdKeyNameMap(models)
	if err != nil {
		return
	}

	orderNoKeyOrderDetailMap, err := getOrderNoKeyOrderDetailMap(models)
	if err != nil {
		return
	}

	orderNoKeyCmbIdMap, err := getOrderNoKeyCmbIdMap(models)
	if err != nil {
		return
	}

	selfPayModeKeyNameMap, err := getPayModeKeyNameMap(models, doTemplate)
	if err != nil {
		return
	}

	targetAudienceKeyNameMap, err := getTargetAudienceKeyNameMap(models)
	if err != nil {
		return
	}

	distributorIdKeyDistributorGroupNameMap, err := getDistributorIdKeyDistributorGroupNameMap(models)
	if err != nil {
		return
	}

	externalOperateNoKeyPaySerialNoMap, err := getExternalOperateNoKeyPaySerialNoMap(models)
	if err != nil {
		return
	}

	for _, model := range models {
		orderDetail, ok := orderNoKeyOrderDetailMap[model.OrderNo]
		if !ok {
			return nil, szerrors.NewLogicErrorWithText(fmt.Sprintf("订单 %s 详情不存在", model.OrderNo))
		}

		operateCountAndPrice := getOperateCountAndPrice(model)

		afterSaleNo := ""
		if model.OperateType == enum.DWMOperateTypeAfterSale {
			afterSaleNo = model.ExternalOperateNo
		}
		paymentPayCenterSerialNo := externalOperateNoKeyPaySerialNoMap[model.TradeNo]
		payCenterSerialNo := externalOperateNoKeyPaySerialNoMap[model.ExternalOperateNo]

		list = append(list, map[string]interface{}{
			FieldOrderNo:                    model.OrderNo,
			FieldParentOrderNo:              model.ParentOrderNo,
			FieldProductTypeName:            ods.ProductTypeTitle(model.ProductType, model.SubType),
			FieldSpuName:                    spuIdKeyNameMap[model.SpuId],
			FieldSkuName:                    skuIdKeyNameMap[model.SkuId],
			FieldPlaytime:                   orderDetail.Playtime,
			FieldBeginTime:                  orderDetail.BeginTime,
			FieldEndTime:                    orderDetail.EndTime,
			FieldSaleChannelName:            enum.OrderModeMap[model.SaleChannel],
			FieldStatusName:                 enum.OrderStatusMap[orderDetail.Status],
			FieldCostUnitPrice:              utils.FormatPrice(model.CostUnitPrice),
			FieldSaleUnitPrice:              utils.FormatPrice(model.SaleUnitPrice),
			FieldCostPayModeName:            enum.PaymodeMap[model.CostPayMode],
			FieldSalePayModeName:            selfPayModeKeyNameMap[model.SalePayMode],
			FieldOrderName:                  orderDetail.OrderName,
			FieldOrderTel:                   orderDetail.OrderTel,
			FieldPersonId:                   orderDetail.PersonId,
			FieldDistributorCName:           fidKeyMemberExtMap[model.DistributorId].ComName,
			FieldDistributorDName:           memberIdKeyMemberInfoMap[model.DistributorId].Dname,
			FieldDistributorGroupName:       distributorIdKeyDistributorGroupNameMap[model.MerchantId][model.DistributorId],
			FieldSupplierDName:              memberIdKeyMemberInfoMap[model.ParentMerchantId].Dname,
			FieldMemo:                       orderDetail.Memo,
			FieldSellOperatorName:           memberIdKeyMemberInfoMap[model.SellOperatorId].Dname,
			FieldSellSiteName:               siteIdKeyNameMap[model.SellSiteId],
			FieldTOrderNum:                  orderDetail.TorderNum,
			FieldRemoteNum:                  orderDetail.RemoteNum,
			FieldCmbId:                      orderNoKeyCmbIdMap[model.OrderNo],
			FieldSectionTimeStr:             orderDetail.ExtContent.SectionTimeStr,
			FieldOperateTypeName:            enum.DWMOperateTypeMap[model.OperateType],
			FieldOperatedAt:                 carbon.Parse(model.OperatedAt).ToDateTimeString(),
			FieldOperateCount:               operateCountAndPrice.OperateCount,
			FieldOperateIncomePrice:         formatPriceByOperateType(model.OperateType, operateCountAndPrice.OperateIncomePrice),
			FieldOperateIncomeDiscountPrice: formatPriceByOperateType(model.OperateType, operateCountAndPrice.OperateIncomeDiscountPrice),
			FieldOperatePayPrice:            formatPriceByOperateType(model.OperateType, operateCountAndPrice.OperatePayPrice),
			FieldOperatePayDiscountPrice:    formatPriceByOperateType(model.OperateType, operateCountAndPrice.OperatePayDiscountPrice),
			FieldOperateChannelName:         enum.TrackSourceMap[model.OperateChannel],
			FieldOperatorName:               memberIdKeyMemberInfoMap[model.OperatorId].Dname,
			FieldOperateSiteName:            siteIdKeyNameMap[model.OperateSiteId],
			FieldCancelCostFee:              formatPriceByOperateType(model.OperateType, operateCountAndPrice.CancelCostFee),
			FieldAfterSaleNo:                afterSaleNo,
			FieldPaymentPayCenterSerialNo:   paymentPayCenterSerialNo,
			FieldPayCenterOrderNo:           payCenterSerialNo,
			FieldOrderTime:                  orderDetail.OrderTimeDate,
			FieldTargetAudience:             targetAudienceKeyNameMap[model.TargetAudience],
			FieldGroupMemberName:            memberIdKeyMemberInfoMap[model.MerchantId].Dname,
		})
	}
	return list, nil
}

func getFidKeyMemberExtMap(models []common.Model) (fidKeyMemberExtMap map[int]pftmember.MemberExt, err error) {
	fidKeyMemberExtMap = make(map[int]pftmember.MemberExt)
	fidList := make([]int, 0)
	for _, model := range models {
		if model.DistributorId > 0 {
			fidList = append(fidList, model.DistributorId)
		}
	}
	if len(fidList) == 0 {
		return
	}
	fidList = utils.RemoveDuplicate(fidList)
	return pftmember.GetFidKeyMemberExtMap(fidList)
}

func getSpuIdKeyNameMap(models []common.Model) (spuIdKeyNameMap map[int]string, err error) {
	spuIdKeyNameMap = make(map[int]string)
	spuIds := make([]int, 0)
	for _, model := range models {
		if model.SpuId > 0 {
			spuIds = append(spuIds, model.SpuId)
		}
	}
	if len(spuIds) == 0 {
		return
	}
	spuIds = utils.RemoveDuplicate(spuIds)
	spuIdKeyNameMap, err = landservice.QueryLandTitleByIds(spuIds)
	return
}

func getSkuIdKeyNameMap(models []common.Model) (skuIdKeyNameMap map[int]string, err error) {
	skuIdKeyNameMap = make(map[int]string)
	skuIds := make([]int, 0)
	for _, model := range models {
		if model.SkuId > 0 {
			skuIds = append(skuIds, model.SkuId)
		}
	}
	if len(skuIds) == 0 {
		return
	}
	skuIds = utils.RemoveDuplicate(skuIds)
	skuIdKeyNameMap, err = ticketservice.QueryTicketTitleByIds(skuIds)
	return
}

func getMemberIdKeyMemberInfoMap(models []common.Model) (memberIdKeyMemberInfoMap map[int]pftmember.MemberBaseInfo, err error) {
	memberIdKeyMemberInfoMap = make(map[int]pftmember.MemberBaseInfo)
	memberIds := make([]int, 0)
	for _, model := range models {
		if model.ParentMerchantId > 0 {
			memberIds = append(memberIds, model.ParentMerchantId)
		}
		if model.MerchantId > 0 {
			memberIds = append(memberIds, model.MerchantId)
		}
		if model.DistributorId > 0 {
			memberIds = append(memberIds, model.DistributorId)
		}
		if model.OperatorId > 0 {
			memberIds = append(memberIds, model.OperatorId)
		}
		if model.SellOperatorId > 0 {
			memberIds = append(memberIds, model.SellOperatorId)
		}
	}
	if len(memberIds) == 0 {
		return
	}
	memberIds = utils.RemoveDuplicate(memberIds)
	memberIdKeyMemberInfoMap, err = pftmember.GetMemberBaseInfoMapByIds(memberIds)
	return
}

func getSiteIdKeyNameMap(models []common.Model) (siteIdKeyNameMap map[int]string, err error) {
	siteIdKeyNameMap = make(map[int]string)
	siteIds := make([]int, 0)
	for _, model := range models {
		if model.OperateSiteId > 0 {
			siteIds = append(siteIds, model.OperateSiteId)
		}
		if model.SellSiteId > 0 {
			siteIds = append(siteIds, model.SellSiteId)
		}
	}
	if len(siteIds) == 0 {
		return
	}
	siteIds = utils.RemoveDuplicate(siteIds)
	siteIdKeyNameMap, err = platformrpcapi.QuerySiteNameMapByIds(siteIds)
	return
}

func getOrderNoKeyOrderDetailMap(models []common.Model) (orderNoKeyOrderDetailMap map[string]orderqueryservice.OrderDetailInfo, err error) {
	orderNoKeyOrderDetailMap = make(map[string]orderqueryservice.OrderDetailInfo)
	orderNos := make([]string, 0)
	for _, model := range models {
		if model.OrderNo != "" {
			orderNos = append(orderNos, model.OrderNo)
		}
	}
	if len(orderNos) == 0 {
		return
	}
	orderNos = utils.RemoveDuplicate(orderNos)
	orderNoKeyOrderDetailMap, err = orderqueryservice.GetOrderDetail(orderNos)
	return
}

func getOrderNoKeyCmbIdMap(models []common.Model) (orderNoKeyCmbIdMap map[string]string, err error) {
	orderNoKeyCmbIdMap = make(map[string]string)
	orderNos := make([]string, 0)
	for _, model := range models {
		if model.OrderNo != "" {
			orderNos = append(orderNos, model.OrderNo)
		}
	}
	if len(orderNos) == 0 {
		return
	}
	orderNos = utils.RemoveDuplicate(orderNos)
	orderNoKeyCmbIdMap, err = ordercenter.GetProductOrderToTradeOrderMap(orderNos)
	for _, no := range orderNos {
		if val, ok := orderNoKeyCmbIdMap[no]; ok && val != "" {
			continue
		}
		//合并支付订单号取交易单号，交易单号不存在则则取订单号
		orderNoKeyCmbIdMap[no] = no
	}

	return
}

func getPayModeKeyNameMap(models []common.Model, doTemplate template.DoListItem) (payModeKeyNameMap map[int]string, err error) {
	payModeKeyNameMap = make(map[int]string)
	//模板不需要商户id查询的，返回默认值，不需要自定义支付方式
	if utils.Container(enum.TemplateQueryNoNeedMerchantId, doTemplate.Category) {
		for i, s := range enum.PaymodeMap {
			payModeKeyNameMap[i] = s
		}
		return
	}
	payModeKeyNameMap = reportcommon.PayWayListConf(models[0].MerchantId, false)
	return
}

func getDistributorIdKeyDistributorGroupNameMap(models []common.Model) (distributorIdKeyDistributorGroupNameMap map[int]map[int]string, err error) {
	distributorIdKeyDistributorGroupNameMap = make(map[int]map[int]string)
	distributorIds := make(map[int][]int, 0)
	//集团账号下存在多商户的情况
	for _, model := range models {
		if model.DistributorId > 0 {
			distributorIds[model.MerchantId] = append(distributorIds[model.MerchantId], model.DistributorId)
		}
	}
	if len(distributorIds) == 0 {
		return
	}
	for merchantId, dIds := range distributorIds {
		dIds = utils.RemoveDuplicate(dIds)
		groupNameMap := make(map[int]string)
		groupNameMap, err = evolutegroupservice.QueryGroupNameBySidFids(merchantId, dIds)
		if groupNameMap != nil {
			// 初始化内层 map（如果不存在）
			if _, ok := distributorIdKeyDistributorGroupNameMap[merchantId]; !ok {
				distributorIdKeyDistributorGroupNameMap[merchantId] = make(map[int]string)
			}
			for dId, groupName := range groupNameMap {
				distributorIdKeyDistributorGroupNameMap[merchantId][dId] = groupName
			}
		}
	}
	return
}

func getExternalOperateNoKeyPaySerialNoMap(models []common.Model) (externalOperateNoKeyPaySerialNoMap map[string]string, err error) {
	externalOperateNoKeyPaySerialNoMap = make(map[string]string)
	externalOperateNos := make([]string, 0)
	for _, model := range models {
		// 外部操作单号查询，目前只支持支付行为查询支付流水号
		if model.ExternalOperateNo != "" && model.OperateType == enum.DWMOperateTypePay {
			externalOperateNos = append(externalOperateNos, model.ExternalOperateNo)
		}
		// 交易单号查询
		if model.TradeNo != "" {
			externalOperateNos = append(externalOperateNos, model.TradeNo)
		}
	}
	if len(externalOperateNos) == 0 {
		return
	}
	externalOperateNos = utils.RemoveDuplicate(externalOperateNos)
	externalOperateNoKeyPaySerialNoMap, err = biztradeorderservice.BatchQueryPayIdMapByOrderIds(externalOperateNos)
	return
}

type OperateCountAndPrice struct {
	OperateCount               int // 数量
	OperateIncomePrice         int // 收入金额
	OperateIncomeDiscountPrice int // 收入优惠金额
	OperatePayPrice            int // 支出金额
	OperatePayDiscountPrice    int // 支出优惠金额
	CancelCostFee              int // 退票手续费
}

func getOperateCountAndPrice(model common.Model) (operateCountAndPrice OperateCountAndPrice) {
	switch model.OperateType {
	case enum.DWMOperateTypePay:
		operateCountAndPrice.OperateCount = model.PayCount
		operateCountAndPrice.OperateIncomePrice = model.PaySalePrice - model.PaySaleDiscountPrice
		operateCountAndPrice.OperateIncomeDiscountPrice = model.PaySaleDiscountPrice
		operateCountAndPrice.OperatePayPrice = model.PayCostPrice - model.PayCostDiscountPrice
		operateCountAndPrice.OperatePayDiscountPrice = model.PayCostDiscountPrice
	case enum.DWMOperateTypeVerify:
		operateCountAndPrice.OperateCount = model.VerifyCount
		operateCountAndPrice.OperateIncomePrice = model.VerifySalePrice - model.VerifySaleDiscountPrice
		operateCountAndPrice.OperateIncomeDiscountPrice = model.VerifySaleDiscountPrice
		operateCountAndPrice.OperatePayPrice = model.VerifyCostPrice - model.VerifyCostDiscountPrice
		operateCountAndPrice.OperatePayDiscountPrice = model.VerifyCostDiscountPrice
	case enum.DWMOperateTypeCancel:
		operateCountAndPrice.OperateCount = model.CancelCount
		operateCountAndPrice.OperateIncomePrice = model.CancelCostPrice - model.CancelCostDiscountPrice
		operateCountAndPrice.OperateIncomeDiscountPrice = model.CancelCostDiscountPrice
		operateCountAndPrice.OperatePayPrice = model.CancelSalePrice - model.CancelSaleDiscountPrice
		operateCountAndPrice.OperatePayDiscountPrice = model.CancelSaleDiscountPrice
		operateCountAndPrice.CancelCostFee = model.CancelSaleFee - model.CancelCostFee
	case enum.DWMOperateTypeRevoke:
		operateCountAndPrice.OperateCount = model.RevokeCount
		operateCountAndPrice.OperateIncomePrice = model.RevokeCostPrice - model.RevokeCostDiscountPrice
		operateCountAndPrice.OperateIncomeDiscountPrice = model.RevokeCostDiscountPrice
		operateCountAndPrice.OperatePayPrice = model.RevokeSalePrice - model.RevokeSaleDiscountPrice
		operateCountAndPrice.OperatePayDiscountPrice = model.RevokeSaleDiscountPrice
		operateCountAndPrice.CancelCostFee = model.RevokeSaleFee - model.RevokeCostFee
	case enum.DWMOperateTypeAfterSale:
		operateCountAndPrice.OperateCount = model.AfterSaleCount
		operateCountAndPrice.OperateIncomePrice = model.AfterCostPrice
		operateCountAndPrice.OperatePayPrice = model.AfterSalePrice
	case enum.DWMOperateTypeCollect:
		operateCountAndPrice.OperateCount = model.CollectCount
	case enum.DWMOperateTypeReprint:
		operateCountAndPrice.OperateCount = model.ReprintCount
	}
	return
}

func getTargetAudienceKeyNameMap(models []common.Model) (TargetAudienceKeyNameMap map[string]string, err error) {
	TargetAudienceKeyNameMap = make(map[string]string)
	codes := make([]string, 0)
	for _, item := range models {
		if item.TargetAudience != "" {
			codes = append(codes, item.TargetAudience)
		}
	}
	if len(codes) == 0 {
		return
	}
	codes = utils.RemoveDuplicate(codes)
	var params tag.QueryTagPaginateAggregateParams
	params.Codes = codes
	params.PageNum = 1
	params.PageSize = len(codes)
	TargetAudienceKeyNameMap, err = tag.TagPaginateAggregateCodeAndNameMap(params)
	return
}

// formatPriceByOperateType 根据操作类型格式化价格，取票和重打印时返回"--"
func formatPriceByOperateType(operateType int, price int) string {
	if operateType == enum.DWMOperateTypeCollect || operateType == enum.DWMOperateTypeReprint {
		return "--"
	}
	return utils.FormatPrice(price)
}
