package filehandle

import (
	"fmt"
	"os"
	"path/filepath"
)

// GetRealFilePath 获取真实文件路径
func GetRealFilePath(realFilePath string) (string, error) {
	if realFilePath == "" {
		return "", fmt.Errorf("realFilePath不能为空")
	}
	//系统临时文件目录存储
	tmpDir := filepath.Join(os.TempDir(), "report")
	if _, err := os.Stat(tmpDir); err != nil {
		if os.IsNotExist(err) {
			if err = os.MkdirAll(tmpDir, os.ModePerm); err != nil {
				return "", fmt.Errorf("创建临时目录失败: %v", err)
			}
		} else {
			return "", fmt.Errorf("检查临时目录状态失败: %v", err)
		}
	}
	realFilePath = filepath.Join(tmpDir, realFilePath)
	return realFilePath, nil
}
