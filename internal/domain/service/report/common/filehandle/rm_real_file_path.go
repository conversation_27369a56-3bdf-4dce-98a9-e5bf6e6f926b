package filehandle

import (
	"fmt"
	"os"
)

// RmRealFilePath 删除真实文件路径
func RmRealFilePath(realFilePath string) error {
	if realFilePath == "" {
		return nil
	}
	_, err := os.Stat(realFilePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil
		}
		return fmt.Errorf("检查文件状态失败: %v", err)
	}
	err = os.Remove(realFilePath)
	if err != nil {
		return fmt.Errorf("删除临时文件失败: %v", err)
	}
	return nil
}
