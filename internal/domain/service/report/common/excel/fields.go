package excel

const (
	FieldDate                    = "date"                       // 日期
	FieldCostUnitPrice           = "cost_unit_price"            // 采购单价
	FieldSaleUnitPrice           = "sale_unit_price"            // 销售单价
	FieldMerchantId              = "merchant_id"                // 商户ID
	FieldMerchantName            = "merchant_name"              // 商户名称
	FieldParentMerchantId        = "parent_merchant_id"         // 上级商户ID
	FieldParentMerchantName      = "parent_merchant_name"       // 上级商户名称
	FieldDistributorId           = "distributor_id"             // 分销商户ID
	FieldDistributorName         = "distributor_name"           // 分销商名称
	FieldPoiId                   = "poi_id"                     // PoiID
	FieldPoiName                 = "poi_name"                   // Poi名称
	FieldSpuId                   = "spu_id"                     // SpuID
	FieldSpuName                 = "spu_name"                   // Spu名称
	FieldSkuId                   = "sku_id"                     // SkuID
	FieldSkuName                 = "sku_name"                   // Sku名称
	FieldSaleChannel             = "sale_channel"               // 销售渠道
	FieldSaleChannelName         = "sale_channel_name"          // 销售渠道名称
	FieldCostPayMode             = "cost_pay_mode"              // 采购支付方式
	FieldCostPayModeName         = "cost_pay_mode_name"         // 采购支付方式名称
	FieldSalePayMode             = "sale_pay_mode"              // 销售支付方式
	FieldSalePayModeName         = "sale_pay_mode_name"         // 销售支付方式名称
	FieldSellOperatorId          = "sell_operator_id"           // 售票员ID
	FieldSellOperatorName        = "sell_operator_name"         // 售票员名称
	FieldOperatorId              = "operator_id"                // 操作人ID
	FieldOperatorName            = "operator_name"              // 操作人名称
	FieldSellSiteId              = "sell_site_id"               // 售票站点ID
	FieldSellSiteName            = "sell_site_name"             // 售票站点名称
	FieldTargetAudience          = "target_audience"            // 适用人群
	FieldTargetAudienceName      = "target_audience_name"       // 适用人群名称
	FieldGroupMember             = "group_member"               // 集团成员
	FieldGroupMemberName         = "group_member_name"          // 集团成员名称
	FieldSpuTagCode              = "spu_tag_code"               // SPU标签编码
	FieldSpuTagName              = "spu_tag_name"               // SPU标签名称
	FieldSkuTagCode              = "sku_tag_code"               // SKU标签编码
	FieldSkuTagName              = "sku_tag_name"               // SKU标签名称
	FieldPayModeTagCode          = "pay_mode_tag_code"          // 支付方式标签编码
	FieldPayModeTagName          = "pay_mode_tag_name"          // 支付方式标签名称
	FieldSaleChannelTagCode      = "sale_channel_tag_code"      // 销售渠道标签编码
	FieldSaleChannelTagName      = "sale_channel_tag_name"      // 销售渠道标签名称
	FieldPayCount                = "pay_count"                  // 预订数量
	FieldPayCostPrice            = "pay_cost_price"             // 预订采购金额
	FieldPayCostDiscountPrice    = "pay_cost_discount_price"    // 预订采购优惠金额
	FieldPaySalePrice            = "pay_sale_price"             // 预订销售金额
	FieldPaySaleDiscountPrice    = "pay_sale_discount_price"    // 预订销售优惠金额
	FieldVerifyCount             = "verify_count"               // 核销数量
	FieldVerifyCostPrice         = "verify_cost_price"          // 核销采购金额
	FieldVerifyCostDiscountPrice = "verify_cost_discount_price" // 核销采购优惠金额
	FieldVerifySalePrice         = "verify_sale_price"          // 核销销售金额
	FieldVerifySaleDiscountPrice = "verify_sale_discount_price" // 核销销售优惠金额
	FieldCancelCount             = "cancel_count"               // 取消数量
	FieldCancelCostPrice         = "cancel_cost_price"          // 取消采购金额
	FieldCancelCostDiscountPrice = "cancel_cost_discount_price" // 取消采购优惠金额
	FieldCancelCostFee           = "cancel_cost_fee"            // 取消采购手续费
	FieldCancelSalePrice         = "cancel_sale_price"          // 取消销售金额
	FieldCancelSaleDiscountPrice = "cancel_sale_discount_price" // 取消销售优惠金额
	FieldCancelSaleFee           = "cancel_sale_fee"            // 取消销售手续费
	FieldRevokeCount             = "revoke_count"               // 撤销数量
	FieldRevokeCostPrice         = "revoke_cost_price"          // 撤销采购金额
	FieldRevokeCostDiscountPrice = "revoke_cost_discount_price" // 撤销采购优惠金额
	FieldRevokeCostFee           = "revoke_cost_fee"            // 撤销采购手续费
	FieldRevokeSalePrice         = "revoke_sale_price"          // 撤销销售金额
	FieldRevokeSaleDiscountPrice = "revoke_sale_discount_price" // 撤销销售优惠金额
	FieldRevokeSaleFee           = "revoke_sale_fee"            // 撤销销售手续费
	FieldAfterSaleCount          = "after_sale_count"           // 售后数量
	FieldAfterCostPrice          = "after_cost_price"           // 售后采购金额
	FieldAfterSalePrice          = "after_sale_price"           // 售后销售金额
	FieldActualSaleCount         = "actual_sale_count"          //实售数量
	FieldActualSalePrice         = "actual_sale_price"          //实售金额
	FieldActualCostPrice         = "actual_cost_price"          //采购金额
	FieldActualProfit            = "actual_profit"              //实际利润
	FieldRefundFeeProfit         = "refund_fee_profit"          //退款手续费利润
	FieldActualVerifySaleCount   = "actual_verify_count"        //净验证数量
	FieldActualVerifySalePrice   = "actual_verify_sale_price"   //净验证金额
	FieldActualVerifyCostPrice   = "actual_verify_cost_price"   //净采购金额
	FieldActualVerifyProfit      = "actual_verify_profit"       //实际利润
	FieldCollectCount            = "collect_count"              // 取票数量

)
