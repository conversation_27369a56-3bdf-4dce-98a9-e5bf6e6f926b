package customizeexportfields

import (
	"report-service/pkg/szerrors"
)

type CustomizeManager struct {
	Config  CustomizeConfig
	Storage ConfigStorage
}

func NewCustomizeManager(config CustomizeConfig, storage ConfigStorage) *CustomizeManager {
	return &CustomizeManager{
		Config:  config,
		Storage: storage,
	}
}

// GetExportFieldManager 获取导出字段管理器
func GetExportFieldManager(tagKey string) (*CustomizeManager, error) {
	if customizeConfig, ok := ConfigMaps[tagKey]; ok {
		manager := NewCustomizeManager(customizeConfig, &MerchantConfig{})
		return manager, nil
	}
	return nil, szerrors.NewLogicErrorWithText("导出明细自定义字段配置未找到，请重试！")
}
