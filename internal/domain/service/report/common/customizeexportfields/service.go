package customizeexportfields

import (
	"report-service/internal/domain/service/report/common/customizeexportfields/types"
)

func FieldsEnum(key string) ([]types.DoExportDefinitionItem, error) {
	manager, err := GetExportFieldManager(key)
	if err != nil {
		return nil, err
	}
	return manager.Config.GetDefinition(), nil
}

func FieldsInfo(merchantId int, memberId int, key string) ([]string, error) {
	manager, err := GetExportFieldManager(key)
	if err != nil {
		return nil, err
	}
	permission := manager.Config.GetPermission()
	merchantId, memberId = permission.HandleAccountIsolation(merchantId, memberId)
	merchantConfig, err := manager.Storage.GetConfig(key, merchantId, memberId)
	if err != nil {
		return nil, err
	}
	if merchantConfig != nil {
		return merchantConfig.Config, nil
	}
	definition := manager.Config.GetDefinition()
	return definition.GetDefaultKeys(), nil
}

func FieldsSave(merchantId int, memberId int, key string, value types.DoExportConfig) error {
	manager, err := GetExportFieldManager(key)
	if err != nil {
		return err
	}
	err = value.CheckConfig()
	if err != nil {
		return err
	}
	definition := manager.Config.GetDefinition()
	err = definition.CheckConfig(value.Config)
	if err != nil {
		return err
	}
	permission := manager.Config.GetPermission()
	merchantId, memberId = permission.HandleAccountIsolation(merchantId, memberId)
	return manager.Storage.SaveConfig(key, merchantId, memberId, value)
}

func FieldsKeyVal(merchantId int, memberId int, key string) ([]types.KeyValuePair, error) {
	manager, err := GetExportFieldManager(key)
	if err != nil {
		return nil, err
	}
	permission := manager.Config.GetPermission()
	merchantId, memberId = permission.HandleAccountIsolation(merchantId, memberId)
	merchantConfig, err := manager.Storage.GetConfig(key, merchantId, memberId)
	if err != nil {
		return nil, err
	}
	definition := manager.Config.GetDefinition()
	var fields = definition.GetDefaultKeys()
	if merchantConfig != nil {
		fields = merchantConfig.Config
	}
	var keyVal []types.KeyValuePair
	keyVal = definition.GetOrderedKeyValuePairs(fields)
	return keyVal, nil
}
