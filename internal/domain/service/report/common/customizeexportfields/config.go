package customizeexportfields

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/service/report/common/customizeexportfields/types"
	"report-service/internal/domain/service/report/multiplepeople/detail"
)

var ConfigMaps = map[string]CustomizeConfig{
	enum.CustomizeExportDetailTouristMultiple: &detail.CustomizeExportDetailTouristMultiple{}, //多维人数统计
}

type CustomizeConfig interface {
	GetDefinition() types.DoExportDefinition
	GetPermission() types.DoExportPermission
}

type ConfigStorage interface {
	GetConfig(key string, merchantId int, memberId int) (*types.DoExportConfig, error)
	SaveConfig(key string, merchantId int, memberId int, value types.DoExportConfig) error
}
