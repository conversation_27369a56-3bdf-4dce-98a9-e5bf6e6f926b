package customizeexportfields

import (
	"report-service/internal/domain/logic/config/merchantconfig"
	"report-service/internal/domain/service/report/common/customizeexportfields/types"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

const (
	ExportConfigKeyPrefix = "customize_export_" //导出key前缀
)

type MerchantConfig struct{}

// GetConfig 获取商户配置
func (d *MerchantConfig) GetConfig(key string, merchantId int, memberId int) (*types.DoExportConfig, error) {
	info, err := merchantconfig.InfoByKeyAndMerchantId(generateKey(key), merchantId, memberId)
	if err != nil {
		return nil, err
	}
	if info == nil {
		return nil, nil
	}
	var infoData types.DoExportConfig
	err = utils.JsonConvertor(info.Payload, &infoData)
	if err != nil {
		return nil, szerrors.NewLogicErrorWithText("配置解析失败")
	}
	return &infoData, nil
}

// SaveConfig 保存商户配置
func (d *MerchantConfig) SaveConfig(key string, merchantId int, memberId int, value types.DoExportConfig) error {
	params := merchantconfig.SetConfigParams{
		Key:        generateKey(key),
		MerchantId: merchantId,
		MemberId:   memberId,
		Value:      value,
	}
	return merchantconfig.SetConfigByKeyAndMerchantId(params)
}

// generateKey 获取key
func generateKey(key string) string {
	return ExportConfigKeyPrefix + key
}
