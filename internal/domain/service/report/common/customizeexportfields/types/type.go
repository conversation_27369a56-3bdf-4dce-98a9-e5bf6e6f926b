package types

type DoExportConfig struct {
	Config []string `json:"config"`
}

type DoExportDefinitionItem struct {
	Title string                   `json:"title"`
	List  []DoExportDefinitionList `json:"list"`
}

type DoExportDefinitionList struct {
	KeyValuePair
	Required bool `json:"required"` //字段是否必选
}

type KeyValuePair struct {
	Key   string `json:"key"`   //字段key
	Title string `json:"title"` //字段名称
}

type DoExportPermission struct {
	AccountIsolation bool `json:"account_isolation"` //是否员工和主账号隔离
}

type DoExportDefinition []DoExportDefinitionItem
