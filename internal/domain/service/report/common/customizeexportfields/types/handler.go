package types

import (
	"fmt"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

// GetDefaultKeys 从DoExportDefinition中提取所有字段的键(Key)
func (d *DoExportDefinition) GetDefaultKeys() []string {
	total := 0
	for _, def := range *d {
		total += len(def.List)
	}
	keys := make([]string, 0, total)

	for _, def := range *d {
		for _, item := range def.List {
			keys = append(keys, item.KeyValuePair.Key)
		}
	}
	return keys
}

// GetOrderedKeyValuePairs 根据给定的配置键列表，返回按顺序排列的键值对
func (d *DoExportDefinition) GetOrderedKeyValuePairs(configKeys []string) []KeyValuePair {
	// 创建一个映射来快速查找 Key 对应的 Title
	keyToTitle := make(map[string]string)
	for _, def := range *d {
		for _, item := range def.List {
			keyToTitle[item.KeyValuePair.Key] = item.KeyValuePair.Title
		}
	}
	// 按照 configKeys 的顺序构建结果
	result := make([]KeyValuePair, 0, len(configKeys))
	for _, key := range configKeys {
		if title, exists := keyToTitle[key]; exists {
			result = append(result, KeyValuePair{
				Key:   key,
				Title: title,
			})
		}
	}
	return result
}

// HandleAccountIsolation 处理员工账号与主账号的隔离逻辑
func (d *DoExportPermission) HandleAccountIsolation(merchantId int, memberId int) (int, int) {
	//处理下员工账号和主账号隔离, 不隔离即memberId=merchantId
	if !d.AccountIsolation && merchantId != memberId {
		memberId = merchantId
	}
	return merchantId, memberId
}

// CheckConfig 检查导出字段配置
func (d *DoExportConfig) CheckConfig() error {
	if len(d.Config) == 0 {
		return szerrors.NewInvalidParamErrorWithText("导出字段不能为空，至少选一个")
	}
	return nil
}

// CheckConfig 检查导出字段必选配置
func (d *DoExportDefinition) CheckConfig(config []string) error {
	var fields []string
	for _, definition := range *d {
		for _, s := range definition.List {
			if s.Required == true && !utils.Container(config, s.Key) {
				return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("%s字段为必须勾选", s.Title))
			}
			fields = append(fields, s.Key)
		}
	}
	for _, s := range config {
		if !utils.Container(fields, s) {
			return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("%s-Key未知字段，无法解析", s))
		}
	}
	return nil
}
