package multipledetail

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	dwmtourist "report-service/internal/domain/logic/dwm/tourist"
	"report-service/pkg/sdk/api/baseservice/bizaddress"
	"report-service/pkg/sdk/api/baseservice/bizregionarea"
	"report-service/pkg/sdk/api/evolutegroupservice"
	"report-service/pkg/sdk/api/landservice"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/sdk/api/ticketservice"
	"report-service/pkg/utils"
)

func ConvertCommonModelsToFields(models []dwmtourist.Model, fields []string) (list []map[string]interface{}, err error) {
	if len(models) == 0 {
		return nil, nil
	}
	spuIdKeyNameMap, err := getSpuIdKeyNameMap(models, fields)
	if err != nil {
		return
	}
	skuIdKeyNameMap, err := getSkuIdKeyNameMap(models, fields)
	if err != nil {
		return
	}
	memberIdKeyMemberInfoMap, err := getMemberIdKeyMemberInfoMap(models, fields)
	if err != nil {
		return
	}
	siteIdKeyNameMap, err := getSiteIdKeyNameMap(models, fields)
	if err != nil {
		return
	}
	areaCodeKeyNameMap, err := getAreaCodeKeyNameMap(models, fields)
	if err != nil {
		return
	}
	countryCodeKeyNameMap, err := getCountryCodeKeyNameMap(models, fields)
	if err != nil {
		return
	}
	merchantId := models[0].MerchantId // 商户ID，取第一个，该字段为必要过滤条件，不可能出现不同的情况
	distributorIdKeyDistributorGroupNameMap, err := getDistributorIdKeyDistributorGroupNameMap(merchantId, models, fields)
	if err != nil {
		return
	}
	for _, model := range models {
		items := make(map[string]interface{})
		//门票吗
		items = fillItemsVal(items, fields, FieldBusinessCode, model.BusinessCode)
		//操作时间
		items = fillItemsVal(items, fields, FieldOperatedAt, carbon.Parse(model.OperatedAt).ToDateTimeString())
		//操作类型
		items = fillItemsVal(items, fields, FieldOperateType, enum.DWMOperateTypeMap[model.OperateType])
		//操作人员
		items = fillItemsVal(items, fields, FieldOperator, memberIdKeyMemberInfoMap[model.OperatorId].Dname)
		//操作站点
		items = fillItemsVal(items, fields, FieldOperateSite, siteIdKeyNameMap[model.OperateSiteId])
		//计数
		items = fillItemsVal(items, fields, FieldCount, model.Count)
		//订单号
		items = fillItemsVal(items, fields, FieldOrderNum, model.OrderNo)
		//产品
		items = fillItemsVal(items, fields, FieldSpu, spuIdKeyNameMap[model.SpuId])
		//票种
		items = fillItemsVal(items, fields, FieldSku, skuIdKeyNameMap[model.SkuId])
		//下单渠道
		items = fillItemsVal(items, fields, FieldOrderChannel, enum.OrderModeMap[model.OrderChannel])
		//分销商分组
		items = fillItemsVal(items, fields, FieldResellerGroup, distributorIdKeyDistributorGroupNameMap[model.DistributorId])
		//分销商
		items = fillItemsVal(items, fields, FieldReseller, memberIdKeyMemberInfoMap[model.DistributorId].Dname)
		//证件类型
		items = fillItemsVal(items, fields, FieldIdType, enum.DWMTouristVoucherTypeMap[model.IdType])
		//证件号
		items = fillItemsVal(items, fields, FieldIdNumber, model.IdNumber)
		//游客名称
		items = fillItemsVal(items, fields, FieldNickname, model.Nickname)
		//手机号
		items = fillItemsVal(items, fields, FieldMobile, model.Mobile)

		//地域
		regionName := "未知"
		regionKey := cast.ToInt(model.Region)
		if _, ok := enum.RegionKeyNameMap[regionKey]; ok {
			regionName = enum.RegionKeyNameMap[regionKey]
		}
		items = fillItemsVal(items, fields, FieldRegion, regionName)

		//国家
		countryName := "未知"
		if _, ok := countryCodeKeyNameMap[model.Country]; ok {
			countryName = countryCodeKeyNameMap[model.Country]
		}
		items = fillItemsVal(items, fields, FieldCountry, countryName)

		//省
		provinceName := "未知"
		if _, ok := areaCodeKeyNameMap[model.Province]; ok {
			provinceName = areaCodeKeyNameMap[model.Province]
		}
		items = fillItemsVal(items, fields, FieldProvince, provinceName)

		//市
		cityName := "未知"
		if _, ok := areaCodeKeyNameMap[model.City]; ok {
			cityName = areaCodeKeyNameMap[model.City]
		}
		items = fillItemsVal(items, fields, FieldCity, cityName)

		//区
		districtName := "未知"
		if _, ok := areaCodeKeyNameMap[model.District]; ok {
			districtName = areaCodeKeyNameMap[model.District]
		}
		items = fillItemsVal(items, fields, FieldDistrict, districtName)

		//年龄
		ageName := cast.ToString(model.Age)
		if _, ok := enum.AgeKeyNameMap[model.Age]; ok {
			ageName = enum.AgeKeyNameMap[model.Age]
		}
		items = fillItemsVal(items, fields, FieldAge, ageName)

		//性别
		genderName := "未知"
		if _, ok := enum.GenderKeyNameMap[model.Gender]; ok {
			genderName = enum.GenderKeyNameMap[model.Gender]
		}
		items = fillItemsVal(items, fields, FieldGender, genderName)

		list = append(list, items)
	}

	return list, nil
}

func fillItemsVal(items map[string]interface{}, fields []string, field string, val interface{}) map[string]interface{} {
	if utils.Container(fields, field) {
		items[field] = val
	}
	return items
}

func getSpuIdKeyNameMap(models []dwmtourist.Model, fields []string) (spuIdKeyNameMap map[int]string, err error) {
	spuIdKeyNameMap = make(map[int]string)
	if !utils.Container(fields, FieldSpu) {
		return
	}
	spuIds := make([]int, 0)
	for _, model := range models {
		if model.SpuId > 0 {
			spuIds = append(spuIds, model.SpuId)
		}
	}
	if len(spuIds) == 0 {
		return
	}
	spuIds = utils.RemoveDuplicate(spuIds)
	spuIdKeyNameMap, err = landservice.QueryLandTitleByIds(spuIds)
	return
}

func getSkuIdKeyNameMap(models []dwmtourist.Model, fields []string) (skuIdKeyNameMap map[int]string, err error) {
	skuIdKeyNameMap = make(map[int]string)
	if !utils.Container(fields, FieldSku) {
		return
	}
	skuIds := make([]int, 0)
	for _, model := range models {
		if model.SkuId > 0 {
			skuIds = append(skuIds, model.SkuId)
		}
	}
	if len(skuIds) == 0 {
		return
	}
	skuIds = utils.RemoveDuplicate(skuIds)
	skuIdKeyNameMap, err = ticketservice.QueryTicketTitleByIds(skuIds)
	return
}

func getMemberIdKeyMemberInfoMap(models []dwmtourist.Model, fields []string) (memberIdKeyMemberInfoMap map[int]pftmember.MemberBaseInfo, err error) {
	memberIdKeyMemberInfoMap = make(map[int]pftmember.MemberBaseInfo)
	if !utils.Container(fields, FieldReseller) && !utils.Container(fields, FieldOperator) {
		return
	}
	memberIds := make([]int, 0)
	for _, model := range models {
		if model.DistributorId > 0 {
			memberIds = append(memberIds, model.DistributorId)
		}
		if model.OperatorId > 0 {
			memberIds = append(memberIds, model.OperatorId)
		}
	}
	if len(memberIds) == 0 {
		return
	}
	memberIds = utils.RemoveDuplicate(memberIds)
	memberIdKeyMemberInfoMap, err = pftmember.GetMemberBaseInfoMapByIds(memberIds)
	return
}

func getSiteIdKeyNameMap(models []dwmtourist.Model, fields []string) (siteIdKeyNameMap map[int]string, err error) {
	siteIdKeyNameMap = make(map[int]string)
	if !utils.Container(fields, FieldOperateSite) {
		return
	}
	siteIds := make([]int, 0)
	for _, model := range models {
		if model.OperateSiteId > 0 {
			siteIds = append(siteIds, model.OperateSiteId)
		}
	}
	if len(siteIds) == 0 {
		return
	}
	siteIds = utils.RemoveDuplicate(siteIds)
	siteIdKeyNameMap, err = platformrpcapi.QuerySiteNameMapByIds(siteIds)
	return
}

func getDistributorIdKeyDistributorGroupNameMap(merchantId int, models []dwmtourist.Model, fields []string) (distributorIdKeyDistributorGroupNameMap map[int]string, err error) {
	distributorIdKeyDistributorGroupNameMap = make(map[int]string)
	if !utils.Container(fields, FieldReseller) {
		return
	}
	distributorIds := make([]int, 0)
	for _, model := range models {
		if model.DistributorId > 0 {
			distributorIds = append(distributorIds, model.DistributorId)
		}
	}
	if len(distributorIds) == 0 {
		return
	}
	distributorIds = utils.RemoveDuplicate(distributorIds)
	distributorIdKeyDistributorGroupNameMap, err = evolutegroupservice.QueryGroupNameBySidFids(merchantId, distributorIds)
	return
}

func getAreaCodeKeyNameMap(models []dwmtourist.Model, fields []string) (areaCodeKeyNameMap map[string]string, err error) {
	areaCodeKeyNameMap = make(map[string]string)
	if !utils.Container(fields, FieldProvince) &&
		!utils.Container(fields, FieldCity) &&
		!utils.Container(fields, FieldDistrict) {
		return
	}
	var areaCodes []string
	for _, model := range models {
		if model.Province != "" {
			areaCodes = append(areaCodes, model.Province)
		}
		if model.City != "" {
			areaCodes = append(areaCodes, model.City)
		}
		if model.District != "" {
			areaCodes = append(areaCodes, model.District)
		}
	}
	if len(areaCodes) == 0 {
		return
	}
	areaCodeKeyNameMap, err = bizregionarea.QueryAreaNameMapByAreaCode(areaCodes)
	if err != nil {
		return
	}
	return
}

func getCountryCodeKeyNameMap(models []dwmtourist.Model, fields []string) (countryCodeKeyNameMap map[string]string, err error) {
	countryCodeKeyNameMap = make(map[string]string)
	if !utils.Container(fields, FieldCountry) {
		return
	}
	var countryCodes []string
	for _, model := range models {
		if model.Country != "" {
			countryCodes = append(countryCodes, model.Country)
		}
	}
	if len(countryCodes) == 0 {
		return
	}
	countryCodeKeyNameMap, err = bizaddress.QueryCountryNameMapByCountryCode(countryCodes)
	if err != nil {
		return
	}
	return
}
