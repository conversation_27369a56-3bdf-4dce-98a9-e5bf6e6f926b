package multipledetail

import (
	dwmtourist "report-service/internal/domain/logic/dwm/tourist"
)

func Statistics(
	params DoQueryParams,
) (
	Statistics *dwmtourist.TouristStatistics,
	err error,
) {
	Statistics, err = dwmtourist.Statistics(dwmtourist.DoQueryParams{
		DoTemplate:     params.DoTemplate,
		MerchantId:     params.MerchantId,
		StartTime:      params.StartTime,
		EndTime:        params.EndTime,
		DimensionRange: params.DimensionRange,
		FieldsFilter:   params.FieldsFilter,
		DataLimit:      params.DataLimit,
		Fields:         params.Fields,
		RequireCount:   params.RequireCount,
	})
	if err != nil {
		return
	}
	return
}
