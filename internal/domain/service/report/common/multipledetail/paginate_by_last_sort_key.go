package multipledetail

import (
	"gitee.com/golang-module/carbon/v2"
	dmcommon "report-service/internal/domain/logic/dm/common"
	dwmtourist "report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

type DoQueryParams struct {
	DoTemplate         template.DoListItem          `json:"do_template"`
	MerchantId         int                          `json:"merchant_id"`
	StartTime          carbon.Carbon                `json:"start_time"`
	EndTime            carbon.Carbon                `json:"end_time"`
	DimensionRange     dmcommon.DoDimensionRange    `json:"dimension_range"`
	FieldsFilter       dwmtourist.DoFieldsFilter    `json:"fields_range"`
	DataLimit          datajobslimit.LimitSpuConfig `json:"data_limit"`
	Fields             []string                     `json:"fields"`
	RequireCount       bool                         `json:"with_total"`
	RequirePrevSortKey bool                         `json:"require_prev_sort_key"`
}

func PaginateByLastSortKey(
	params DoQueryParams,
	lastSortKey *string,
	pageSize int,
) (
	list []map[string]interface{},
	nextLastSortKey string,
	err error,
	ext *dwmtourist.ResultExt,
) {
	var paginateResult *dwmtourist.PaginateResult
	paginateResult, err = dwmtourist.PaginateByLastSortKey(dwmtourist.DoQueryParams{
		DoTemplate:         params.DoTemplate,
		MerchantId:         params.MerchantId,
		StartTime:          params.StartTime,
		EndTime:            params.EndTime,
		DimensionRange:     params.DimensionRange,
		FieldsFilter:       params.FieldsFilter,
		DataLimit:          params.DataLimit,
		Fields:             params.Fields,
		RequireCount:       params.RequireCount,
		RequirePrevSortKey: params.RequirePrevSortKey,
	}, lastSortKey, pageSize)
	if err != nil {
		return
	}
	if paginateResult == nil {
		return
	}
	list, err = ConvertCommonModelsToFields(paginateResult.List, params.Fields)
	ext = paginateResult.Ext
	nextLastSortKey = paginateResult.NextSortKey
	return
}
