package grouppay

import (
	"report-service/internal/domain/enum"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/pkg/utils"
)

type DoIndicator struct {
	PayCount        *int `json:"pay_count,omitempty"`         // 预订数量
	PaySalePrice    *int `json:"pay_sale_price,omitempty"`    // 销售金额
	PayCostPrice    *int `json:"pay_cost_price,omitempty"`    // 采购金额
	CancelCount     *int `json:"cancel_count,omitempty"`      // 取消数量
	CancelSalePrice *int `json:"cancel_sale_price,omitempty"` // 取消销售金额
	CancelCostPrice *int `json:"cancel_cost_price,omitempty"` // 取消采购金额
	RevokeCount     *int `json:"revoke_count,omitempty"`      // 撤销数量
	RevokeSalePrice *int `json:"revoke_sale_price,omitempty"` // 撤销销售金额
	RevokeCostPrice *int `json:"revoke_cost_price,omitempty"` // 撤销采购金额
	ActualSaleCount *int `json:"actual_sale_count,omitempty"` // 实售数量
	ActualSalePrice *int `json:"actual_sale_price,omitempty"` // 实售金额
	ActualCostPrice *int `json:"actual_cost_price,omitempty"` // 实采金额
	ActualProfit    *int `json:"actual_profit,omitempty"`     // 实际利润
	RefundFeeProfit *int `json:"refund_fee_profit,omitempty"` // 退款手续费利润
	AfterSaleCount  *int `json:"after_sale_count,omitempty"`  // 售后数量
	AfterSalePrice  *int `json:"after_sale_price,omitempty"`  // 售后销售金额
	AfterCostPrice  *int `json:"after_cost_price,omitempty"`  // 售后采购金额
}

// 指标字段计算
// 先全部算出来，再根据指标模板来决定是否显示
func (i *DoIndicator) calculateIndicatorFields(
	indicator dmcommon.DoIndicator,
	templateIndicator []string,
) {
	payCount := indicator.PayCount
	paySalePrice := indicator.PaySalePrice - indicator.PaySaleDiscountPrice
	payCostPrice := indicator.PayCostPrice - indicator.PayCostDiscountPrice

	cancelCount := indicator.CancelCount
	cancelSalePrice := indicator.CancelSalePrice - indicator.CancelSaleDiscountPrice
	cancelCostPrice := indicator.CancelCostPrice - indicator.CancelCostDiscountPrice

	revokeCount := indicator.RevokeCount
	revokeSalePrice := indicator.RevokeSalePrice - indicator.RevokeSaleDiscountPrice
	revokeCostPrice := indicator.RevokeCostPrice - indicator.RevokeCostDiscountPrice

	afterSaleCount := indicator.AfterSaleCount
	afterSalePrice := indicator.AfterSalePrice
	afterCostPrice := indicator.AfterCostPrice

	actualSaleCount := payCount - cancelCount - revokeCount - afterSaleCount
	actualSalePrice := paySalePrice - cancelSalePrice - revokeSalePrice - afterSalePrice
	actualCostPrice := payCostPrice - cancelCostPrice - revokeCostPrice - afterCostPrice
	actualProfit := actualSalePrice - actualCostPrice

	refundFeeProfit := indicator.CancelSaleFee - indicator.CancelCostFee + indicator.RevokeSaleFee - indicator.RevokeCostFee

	for _, item := range templateIndicator {
		switch item {
		case enum.IndicatorPayCount:
			i.PayCount = utils.Add(i.PayCount, &payCount)
		case enum.IndicatorPaySalePrice:
			i.PaySalePrice = utils.Add(i.PaySalePrice, &paySalePrice)
		case enum.IndicatorPayCostPrice:
			i.PayCostPrice = utils.Add(i.PayCostPrice, &payCostPrice)
		case enum.IndicatorCancelCount:
			i.CancelCount = utils.Add(i.CancelCount, &cancelCount)
		case enum.IndicatorCancelSalePrice:
			i.CancelSalePrice = utils.Add(i.CancelSalePrice, &cancelSalePrice)
		case enum.IndicatorCancelCostPrice:
			i.CancelCostPrice = utils.Add(i.CancelCostPrice, &cancelCostPrice)
		case enum.IndicatorRevokeCount:
			i.RevokeCount = utils.Add(i.RevokeCount, &revokeCount)
		case enum.IndicatorRevokeSalePrice:
			i.RevokeSalePrice = utils.Add(i.RevokeSalePrice, &revokeSalePrice)
		case enum.IndicatorRevokeCostPrice:
			i.RevokeCostPrice = utils.Add(i.RevokeCostPrice, &revokeCostPrice)
		case enum.IndicatorActualSaleCount:
			i.ActualSaleCount = utils.Add(i.ActualSaleCount, &actualSaleCount)
		case enum.IndicatorActualSalePrice:
			i.ActualSalePrice = utils.Add(i.ActualSalePrice, &actualSalePrice)
		case enum.IndicatorActualCostPrice:
			i.ActualCostPrice = utils.Add(i.ActualCostPrice, &actualCostPrice)
		case enum.IndicatorActualProfit:
			i.ActualProfit = utils.Add(i.ActualProfit, &actualProfit)
		case enum.IndicatorRefundFeeProfit:
			i.RefundFeeProfit = utils.Add(i.RefundFeeProfit, &refundFeeProfit)
		case enum.IndicatorAfterSaleCount:
			i.AfterSaleCount = utils.Add(i.AfterSaleCount, &afterSaleCount)
		case enum.IndicatorAfterSalePrice:
			i.AfterSalePrice = utils.Add(i.AfterSalePrice, &afterSalePrice)
		case enum.IndicatorAfterCostPrice:
			i.AfterCostPrice = utils.Add(i.AfterCostPrice, &afterCostPrice)
		}
	}
}

// 获取指标字段的操作类型
func GetOperateTypes(indicator []string) (operateTypes []int) {
	operateTypes = make([]int, 0)
	for _, item := range indicator {
		switch item {
		case enum.IndicatorPayCount, enum.IndicatorPaySalePrice, enum.IndicatorPayCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypePay)
		case enum.IndicatorCancelCount, enum.IndicatorCancelSalePrice, enum.IndicatorCancelCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeCancel)
		case enum.IndicatorRevokeCount, enum.IndicatorRevokeSalePrice, enum.IndicatorRevokeCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeRevoke)
		case enum.IndicatorActualSaleCount, enum.IndicatorActualSalePrice, enum.IndicatorActualCostPrice, enum.IndicatorActualProfit:
			operateTypes = append(operateTypes, enum.DWMOperateTypePay, enum.DWMOperateTypeCancel, enum.DWMOperateTypeRevoke, enum.DWMOperateTypeAfterSale)
		case enum.IndicatorRefundFeeProfit:
			operateTypes = append(operateTypes, enum.DWMOperateTypeCancel, enum.DWMOperateTypeRevoke)
		case enum.IndicatorAfterSaleCount, enum.IndicatorAfterSalePrice, enum.IndicatorAfterCostPrice:
			operateTypes = append(operateTypes, enum.DWMOperateTypeAfterSale)
		}
	}
	operateTypes = utils.RemoveDuplicate(operateTypes)
	return
}
