package grouppay

import (
	logicdmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"

	"gitee.com/golang-module/carbon/v2"
)

type DoPaginationItem struct {
	logicdmcommon.DoDimension
	DoIndicator
}

func Paginate(
	merchantId int,
	reportType int,
	doTemplate template.DoListItem,
	startTime, endTime carbon.Carbon,
	timeGroupType *int,
	doDimensionRange logicdmcommon.DoDimensionRange,
	page, pageSize int,
) (list []DoPaginationItem, total int, err error) {
	operateTypes := GetOperateTypes(doTemplate.Indicator)

	doList, total, err := logicdmcommon.Paginate(logicdmcommon.DoQueryParams{
		DoTemplate:     doTemplate,
		MerchantId:     merchantId,
		ReportType:     reportType,
		StartTime:      startTime,
		EndTime:        endTime,
		TimeGroupType:  timeGroupType,
		DimensionRange: doDimensionRange,
		OperateTypes:   operateTypes,
	}, page, pageSize)
	if err != nil {
		return
	}

	list = make([]DoPaginationItem, 0)
	for _, doItem := range doList {
		item := DoPaginationItem{DoDimension: doItem.DoDimension}
		item.calculateIndicatorFields(doItem.DoIndicator, doTemplate.Indicator)
		list = append(list, item)
	}
	return
}
