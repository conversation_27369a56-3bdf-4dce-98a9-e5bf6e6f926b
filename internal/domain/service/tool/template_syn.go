package tool

import (
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/service/tool/templatesyn"
	"report-service/internal/global"
	"report-service/internal/global/notice"
)

// TemplateTypeHandlerMap 定义同步模板的对象
var TemplateTypeHandlerMap = map[int]func(params ...interface{}) error{
	//多维报表统计-人数统计
	enum.TemplateTypeMultiplePeople: templatesyn.MultiplePeople,
}

func TemplateSyn(reportType int, params ...interface{}) {
	var err error
	defer func() {
		if err != nil {
			errMsg := fmt.Sprintf("同步旧报表模板执行失败，错误信息：%s",
				err.Error())
			//日志记录
			global.LOG.Error(errMsg)
			//推送消息预警
			notice.Error(errMsg)
		} else {
			//推送消息通知
			notice.Warning(fmt.Sprintf("同步旧报表模板执行成功，旧报表模板类型：%d", reportType))
		}
	}()

	//验证模板类型
	if _, ok := TemplateTypeHandlerMap[reportType]; !ok {
		err = errors.New("报表模板类型不存在，无法处理")
	}

	//同步旧模板逻辑
	handler := TemplateTypeHandlerMap[reportType]
	err = handler(params...)

	return
}
