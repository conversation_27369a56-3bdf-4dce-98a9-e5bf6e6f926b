package tool

import (
	"encoding/json"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/merchantconfig"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
	"report-service/internal/domain/repository"
	"strings"
)

func SynSupplierConfig() {
	fids, err := getConfigFids(0)
	if err != nil {
		return
	}
	for _, fid := range fids {
		res, resErr := repository.PftReportTouristSourceAreaConfigResult.Paginate([]int{fid}, false, 1, 5)
		if resErr != nil {
			fmt.Println(fmt.Sprintf("商户配置获取失败，商户id：%d， 错误：%s", fid, resErr.Error()))
			continue
		}
		var config touristsourceareamodule.ModuleConfig
		check1 := false
		check2 := false
		check3 := false
		check4 := false
		check5 := false
		checkMore := []string{}
		for _, re := range res {
			switch re.Type {
			case 1:
				config.IsEnable = cast.ToInt(re.Content) == 1
				check1 = true
				checkMore = append(checkMore, cast.ToString(re.Type))
			case 2:
				s := carbon.Parse(re.Content)
				config.StartTime = s.ToDateTimeString()
				check2 = true
				checkMore = append(checkMore, cast.ToString(re.Type))
			case 3:
				s := carbon.Parse(re.Content)
				config.EndTime = s.ToDateTimeString()
				check3 = true
				checkMore = append(checkMore, cast.ToString(re.Type))
			case 4:
				config.OpenStatus = cast.ToInt(re.Content)
				config.IsProcessing = cast.ToInt(re.Content) != touristsourceareamodule.HandleOpenStatusFinish
				check4 = true
				checkMore = append(checkMore, cast.ToString(re.Type))
			case 5:
				var data touristsourceareamodule.ModuleConfigProgress
				err = json.Unmarshal([]byte(re.Content), &data)
				if err != nil {
					fmt.Println(fmt.Sprintf("商户进度配置解析失败，商户id：%d， 进度配置：%s", fid, re.Content))
					return
				}
				config.Progress = &data
				check5 = true
				checkMore = append(checkMore, cast.ToString(re.Type))
			default:
				fmt.Println(fmt.Sprintf("商户配置获取异常，商户id：%d， 不存在类型：%d", fid, re.Type))
				continue
			}
		}
		if !check1 || !check2 || !check3 || !check4 || !check5 {
			fmt.Println(fmt.Sprintf("商户配置存在缺失，商户id：%d， 部分存在的配置：%s", fid, strings.Join(checkMore, ",")))
			continue
		}
		//和当前时间校验是不是还在有效期返回内, 不在的话要标记数据已删除
		config.IsCleanUp = false
		if carbon.Parse(config.EndTime).Lt(carbon.Now().StartOfDay()) {
			config.IsCleanUp = true
		}
		setConfigParams := merchantconfig.SetConfigParams{
			Key:        module.GetModuleConfigKey(enum.ModuleTagTouristSourceArea),
			MerchantId: fid,
			MemberId:   fid,
			Value:      config,
		}
		err = merchantconfig.SetConfigByKeyAndMerchantId(setConfigParams)
		if err != nil {
			fmt.Println(fmt.Sprintf("商户配置同步失败，商户id：%d", fid))
			return
		}
	}
	return
}

func getConfigFids(fid int) ([]int, error) {
	fids := []int{}
	if fid > 0 {
		fids = append(fids, fid)
	}
	maxNum := 999
	for pageNum := 1; pageNum <= maxNum; pageNum++ {
		res, err := repository.PftReportTouristSourceAreaConfigResult.Paginate(fids, true, pageNum, 100)
		if err != nil {
			return nil, err
		}
		if len(res) == 0 {
			break
		}
		for _, re := range res {
			fids = append(fids, re.SupplierId)
		}
	}
	return fids, nil
}
