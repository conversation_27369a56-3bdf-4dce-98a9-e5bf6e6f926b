package tool

import (
	"errors"
	"fmt"
	"math"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	ods2 "report-service/internal/domain/logic/ods"
	"report-service/internal/domain/service/ods"
	"report-service/internal/global"
	szkafka "report-service/internal/global/kafka"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/ordertrackqueryservice"
	pkgszkafka "report-service/pkg/szkafka"
	"report-service/pkg/utils"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

const makeDate = "2006-01-02 15:04:05"

type RangeDateItem struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

var scriptId = 0

type EventOrderTrackActionHandler func(eventParams []myuuordertrack.EventMyuuOrderTrackParams, merchantIds []int) error

func BusinessWashTask(merchantIds []int, begin string, end string, pageSize int, orderNumIn []string, actionIn []int, snowflakeId int, skuIds []int) error {
	if ((orderNumIn == nil || len(orderNumIn) == 0) && (begin == "" || end == "")) || pageSize == 0 {
		return errors.New("BusinessWashTask-error 参数错误")
	}
	scriptId = snowflakeId
	if begin != "" && end != "" {
		RangeDateList := generateTimeSlices(begin, end)
		if RangeDateList == nil || len(RangeDateList) == 0 {
			return errors.New(fmt.Sprintf("%d BusinessWashTask-error 时间范围解析失败", scriptId))
		}
		if len(skuIds) > 100 {
			splitList, err := chunkSlice(skuIds, 100)
			if err != nil {
				return err
			}
			for _, skuId := range splitList {
				for _, v := range RangeDateList {
					setLog(fmt.Sprintf("时段：%s ~ %s， 处理开始······", v.Begin, v.End))
					taskErr := handleTask(merchantIds, v.Begin, v.End, pageSize, orderNumIn, actionIn, skuId)
					if taskErr != nil {
						setLog(taskErr.Error())
						return taskErr
					}
					setLog(fmt.Sprintf("时段：%s ~ %s， 处理完成·", v.Begin, v.End))
				}
			}
		} else {
			for _, v := range RangeDateList {
				setLog(fmt.Sprintf("时段：%s ~ %s， 处理开始······", v.Begin, v.End))
				taskErr := handleTask(merchantIds, v.Begin, v.End, pageSize, orderNumIn, actionIn, skuIds)
				if taskErr != nil {
					setLog(taskErr.Error())
					return taskErr
				}
				setLog(fmt.Sprintf("时段：%s ~ %s， 处理完成·", v.Begin, v.End))
			}
		}

		return nil
	}

	taskErr := handleTask(merchantIds, begin, end, pageSize, orderNumIn, actionIn, []int{})
	if taskErr != nil {
		return taskErr
	}

	return nil
}

func handleTask(merchantIds []int, begin string, end string, pageSize int, orderNumIn []string, actionIn []int, skuIds []int) error {
	if len(actionIn) <= 0 {
		actionIn = myuuordertrack.EventMyuuOrderTrackAction
	}
	for _, action := range actionIn {
		maxNum := 1000000
		size := pageSize
		javaParams := map[string]interface{}{
			"page":     1,
			"size":     size,
			"actionIn": []int{action},
			"sortBy":   []string{"insertTime", "id"},
			"desc":     true,
		}
		if orderNumIn != nil && len(orderNumIn) > 0 {
			javaParams["ordernumIn"] = orderNumIn
		}
		if begin != "" && end != "" {
			javaParams["minInsertTime"] = begin
			javaParams["maxInsertTime"] = end
		}
		if len(skuIds) > 0 {
			javaParams["tidIn"] = skuIds
		}

		result, err := ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
		if err != nil {
			setLog(fmt.Sprintf("BusinessWashTask-error 查询order_track.api异常，错误信息：%s, Action:%d", err, action))
			continue
		}
		if result.Total == 0 {
			setLog(fmt.Sprintf("BusinessWashTask-error Action:%d, 无可执行的数据.", action))
			continue
		}
		if result.List == nil || len(result.List) <= 0 {
			setLog(fmt.Sprintf("BusinessWashTask-error Action:%d, 无可执行的列表数据.", action))
			continue
		}
		maxPage := float64(result.Total) / float64(size)
		maxNum = int(math.Ceil((maxPage)))
		setLog(fmt.Sprintf("BusinessWashTask-info Action:%d, 待执行总数:%d, 总页数:%d", action, result.Total, maxNum))
		for page := 1; page <= maxNum; page++ {
			setLog(fmt.Sprintf("BusinessWashTask-info Action：%d，执行页：%d", action, page))
			javaParams["page"] = page
			result, err = ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
			if err != nil {
				setLog(fmt.Sprintf("BusinessWashTask-error 查询order_track.api异常，错误信息：%s，错误页数：%d", err, page))
				break
			}
			if result.Total == 0 {
				setLog("BusinessWashTask-info 无可执行的数据")
				break
			}
			if result.List == nil || len(result.List) <= 0 {
				setLog("BusinessWashTask-info 无可执行的列表数据")
				break
			}

			//参数转换
			var eventParams []myuuordertrack.EventMyuuOrderTrackParams
			for _, item := range result.List {
				tmp := myuuordertrack.EventMyuuOrderTrackParams{
					AfterList: myuuordertrack.EventMyuuOrderTrackParamsAfterList{
						ApplyDid:       item.ApplyDid,
						Tnum:           item.TNum,
						BranchTerminal: item.BranchTerminal,
						IdCard:         item.IDCard,
						OperMember:     item.OperMember,
						OrdreMonth:     item.OrderMonth,
						Ordernum:       item.OrderNum,
						SalerID:        item.SalerID,
						SyncState:      item.SyncState,
						Source:         item.Source,
						Terminal:       item.Terminal,
						ExtContent:     item.ExtContent,
						Tid:            item.TID,
						InsertTime:     item.InsertTime,
						UpdateTime:     item.UpdateTime,
						Action:         item.Action,
						Id:             item.ID,
						LeftNum:        item.LeftNum,
					},
					BeforeList: []interface{}{},
					EventType:  "INSERT",
					SchemaName: "myuu",
					TableName:  "pft_order_track",
				}
				eventParams = append(eventParams, tmp)
			}

			//TODO::开始时间
			startTime := time.Now()
			handlers := initActionHandlers()
			handler, exists := handlers[action]
			if !exists {
				setLog(fmt.Sprintf("BusinessWashTask-error unknown order track action: %d", action))
				break
			}
			err = handler(eventParams, merchantIds)
			if err != nil {
				setLog(fmt.Sprintf("BusinessWashTask-error 分发处理异常，错误：%s，错误页数：%d", err, page))
				break
			}
			//TODO::结束时间
			endTime := time.Now()
			// 计算执行时间
			setLog(fmt.Sprintf("BusinessWashTask-time action：%d。脚本清洗总花费时间：%v", action, endTime.Sub(startTime)))
		}
	}

	return nil
}

// 初始化处理函数映射表
func initActionHandlers() map[int]EventOrderTrackActionHandler {
	return map[int]EventOrderTrackActionHandler{
		myuuordertrack.EventMyuuOrderTrackActionPay:          ods.EventOrderTrackBatchHandleActionPay,
		myuuordertrack.EventMyuuOrderTrackActionModify:       ods.EventOrderTrackBatchHandleActionCancel,
		myuuordertrack.EventMyuuOrderTrackActionCancel:       ods.EventOrderTrackBatchHandleActionCancel,
		myuuordertrack.EventMyuuOrderTrackActionVerify:       ods.EventOrderTrackBatchHandleActionVerify,
		myuuordertrack.EventMyuuOrderTrackActionRevokeCancel: ods.EventOrderTrackBatchHandleActionRevoke,
		myuuordertrack.EventMyuuOrderTrackActionRevokeModify: ods.EventOrderTrackBatchHandleActionRevoke,
		myuuordertrack.EventMyuuOrderTrackActionAfterSale:    ods.EventOrderTrackBatchHandleActionAfterSale,
		myuuordertrack.EventMyuuOrderTrackActionAddTicket:    ods.EventOrderTrackBatchHandleActionAddTicket,
		myuuordertrack.EventMyuuOrderTrackActionFinish:       ods.EventOrderTrackBatchHandleActionFinish,
	}
}

func formatTime(t time.Time) string {
	return t.Format(makeDate)
}
func generateTimeSlices(start, end string) []RangeDateItem {
	startTime, err := time.Parse(makeDate, start)
	if err != nil {
		setLog(fmt.Sprintf("Error parsing start time:%s", err))
		return nil
	}

	endTime, err := time.Parse(makeDate, end)
	if err != nil {
		setLog(fmt.Sprintf("Error parsing end time: %s", err.Error()))
		return nil
	}

	// 计算时间差（以小时为单位）
	duration := endTime.Sub(startTime).Hours()

	// 如果时间差不超过1小时，则切片只包含一个元素（开始到结束）
	if duration <= 1 {
		return []RangeDateItem{{
			formatTime(startTime), formatTime(endTime),
		}}
	}

	// 初始化切片来存储时间段
	var slices []RangeDateItem

	// 遍历每个小时，并添加到切片中
	current := startTime
	for !current.After(endTime) {
		// 计算下一个小时的开始时间
		next := current.Add(time.Hour)
		// 如果下一个小时的开始时间超过了结束时间，我们就不用继续添加了
		if next.After(endTime) {
			slices = append(slices, RangeDateItem{
				formatTime(time.Date(endTime.Year(), endTime.Month(), endTime.Day(), endTime.Hour(), 00, 00, 0, endTime.Location())),
				formatTime(endTime),
			})
			break
		}
		// 添加当前小时的开始时间
		slices = append(slices, RangeDateItem{
			formatTime(current),
			formatTime(time.Date(current.Year(), current.Month(), current.Day(), current.Hour(), 59, 59, 0, current.Location())),
		})

		// 更新当前时间为下一个小时的开始时间
		current = next
	}

	return slices
}

func setLog(msg string) {
	global.LOG.Info(fmt.Sprintf("%d %s", scriptId, msg))
}

func chunkSlice(slice []int, chunkSize int) ([][]int, error) {
	var chunks [][]int
	var chunkItem []int
	for _, tmp := range slice {
		if len(chunkItem) < chunkSize {
			chunkItem = append(chunkItem, tmp)
		}

		if len(chunkItem) == chunkSize {
			chunks = append(chunks, chunkItem)
			chunkItem = []int{}
		}
	}
	if len(chunkItem) > 0 {
		chunks = append(chunks, chunkItem)
	}

	return chunks, nil
}

func BusinessWashTaskNew(merchantIds []int, begin string, end string, pageSize int, orderNumIn []string, actionIn []int, snowflakeId int, skuIds []int) error {
	if ((orderNumIn == nil || len(orderNumIn) == 0) && (begin == "" || end == "")) || pageSize == 0 {
		return errors.New("BusinessWashTask-error 参数错误")
	}
	scriptId = snowflakeId
	if begin != "" && end != "" {
		RangeDateList := generateTimeSlices(begin, end)
		if RangeDateList == nil || len(RangeDateList) == 0 {
			return errors.New(fmt.Sprintf("%d BusinessWashTask-error 时间范围解析失败", scriptId))
		}
		if len(skuIds) > 100 {
			splitList, err := chunkSlice(skuIds, 100)
			if err != nil {
				return err
			}
			for _, skuId := range splitList {
				for _, v := range RangeDateList {
					setLog(fmt.Sprintf("时段：%s ~ %s， 处理开始······", v.Begin, v.End))
					taskErr := handleTaskNew(merchantIds, v.Begin, v.End, pageSize, orderNumIn, actionIn, skuId)
					if taskErr != nil {
						setLog(taskErr.Error())
						return taskErr
					}
					setLog(fmt.Sprintf("时段：%s ~ %s， 处理完成·", v.Begin, v.End))
				}
			}
		} else {
			for _, v := range RangeDateList {
				setLog(fmt.Sprintf("时段：%s ~ %s， 处理开始······", v.Begin, v.End))
				taskErr := handleTaskNew(merchantIds, v.Begin, v.End, pageSize, orderNumIn, actionIn, skuIds)
				if taskErr != nil {
					setLog(taskErr.Error())
					return taskErr
				}
				setLog(fmt.Sprintf("时段：%s ~ %s， 处理完成·", v.Begin, v.End))
			}
		}

		return nil
	}

	taskErr := handleTaskNew(merchantIds, begin, end, pageSize, orderNumIn, actionIn, []int{})
	if taskErr != nil {
		return taskErr
	}

	return nil
}

func handleTaskNew(merchantIds []int, begin string, end string, pageSize int, orderNumIn []string, actionIn []int, skuIds []int) error {
	if len(actionIn) <= 0 {
		actionIn = myuuordertrack.EventMyuuOrderTrackAction
	}
	maxNum := 1000000
	size := pageSize
	javaParams := map[string]interface{}{
		"page":     1,
		"size":     size,
		"actionIn": actionIn,
		"sortBy":   []string{"insertTime", "id"},
		"desc":     true,
	}
	if orderNumIn != nil && len(orderNumIn) > 0 {
		javaParams["ordernumIn"] = orderNumIn
	}
	if begin != "" && end != "" {
		javaParams["minInsertTime"] = begin
		javaParams["maxInsertTime"] = end
	}
	if len(skuIds) > 0 {
		javaParams["tidIn"] = skuIds
	}

	result, err := ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
	if err != nil {
		setLog(fmt.Sprintf("BusinessWashTask-error 查询order_track.api异常，错误信息：%s", err))
		return nil
	}
	if result.Total == 0 {
		setLog(fmt.Sprintf("BusinessWashTask-error, 无可执行的数据."))
		return nil
	}
	if result.List == nil || len(result.List) <= 0 {
		setLog(fmt.Sprintf("BusinessWashTask-error 无可执行的列表数据."))
		return nil
	}
	maxPage := float64(result.Total) / float64(size)
	maxNum = int(math.Ceil((maxPage)))
	setLog(fmt.Sprintf("BusinessWashTask-info, 待执行总数:%d, 总页数:%d", result.Total, maxNum))
	for page := 1; page <= maxNum; page++ {
		setLog(fmt.Sprintf("BusinessWashTask-info 执行页：%d", page))
		javaParams["page"] = page
		result, err = ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
		if err != nil {
			setLog(fmt.Sprintf("BusinessWashTask-error 查询order_track.api异常，错误信息：%s，错误页数：%d", err, page))
			break
		}
		if result.Total == 0 {
			setLog("BusinessWashTask-info 无可执行的数据")
			break
		}
		if result.List == nil || len(result.List) <= 0 {
			setLog("BusinessWashTask-info 无可执行的列表数据")
			break
		}

		//特定商户过滤
		result.List, err = merchantFiltering(result.List, merchantIds)

		//参数转换
		var eventList []pkgszkafka.ProduceMessage
		for _, item := range result.List {
			tmp := myuuordertrack.EventMyuuOrderTrackParams{
				AfterList: myuuordertrack.EventMyuuOrderTrackParamsAfterList{
					ApplyDid:       item.ApplyDid,
					Tnum:           item.TNum,
					BranchTerminal: item.BranchTerminal,
					IdCard:         item.IDCard,
					OperMember:     item.OperMember,
					OrdreMonth:     item.OrderMonth,
					Ordernum:       item.OrderNum,
					SalerID:        item.SalerID,
					SyncState:      item.SyncState,
					Source:         item.Source,
					Terminal:       item.Terminal,
					ExtContent:     item.ExtContent,
					Tid:            item.TID,
					InsertTime:     item.InsertTime,
					UpdateTime:     item.UpdateTime,
					Action:         item.Action,
					Id:             item.ID,
					LeftNum:        item.LeftNum,
				},
				BeforeList: []interface{}{},
				EventType:  "INSERT",
				SchemaName: "myuu",
				TableName:  "pft_order_track",
			}

			//业务操作号
			operateNo := ods2.CreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, tmp.AfterList.Id)
			//分发
			var tmpItem pkgszkafka.ProduceMessage
			topic := ""
			switch tmp.AfterList.Action {
			case myuuordertrack.EventMyuuOrderTrackActionPay:
				topic = myuuordertrack.TopicEventBusinessDispatchPay
			case myuuordertrack.EventMyuuOrderTrackActionModify:
				topic = myuuordertrack.TopicEventBusinessDispatchCancel
			case myuuordertrack.EventMyuuOrderTrackActionCancel:
				topic = myuuordertrack.TopicEventBusinessDispatchCancel
			case myuuordertrack.EventMyuuOrderTrackActionVerify:
				topic = myuuordertrack.TopicEventBusinessDispatchVerify
			case myuuordertrack.EventMyuuOrderTrackActionRevokeCancel:
				topic = myuuordertrack.TopicEventBusinessDispatchRevoke
			case myuuordertrack.EventMyuuOrderTrackActionRevokeModify:
				topic = myuuordertrack.TopicEventBusinessDispatchRevoke
			case myuuordertrack.EventMyuuOrderTrackActionAfterSale:
				topic = myuuordertrack.TopicEventBusinessDispatchAfterSale
			case myuuordertrack.EventMyuuOrderTrackActionAddTicket:
				topic = myuuordertrack.TopicEventBusinessDispatchAddTicket
			case myuuordertrack.EventMyuuOrderTrackActionFinish:
				topic = myuuordertrack.TopicEventBusinessDispatchFinish
			default:
				err = fmt.Errorf("unknown action: %d", tmp.AfterList.Action)
			}
			if err != nil {
				globalNotice.Error(fmt.Sprintf("order track action handle event error: %s", err.Error()))
				global.LOG.Error("handle event error", zap.Error(err))
				continue
			}
			tmpItem.Topic = &topic
			tmpItem.Key = strconv.Itoa(operateNo)
			tmpItem.Value = myuuordertrack.EventBusinessDispatchItem{
				Data:      tmp,
				OrderNo:   tmp.AfterList.Ordernum,
				OperateAt: tmp.AfterList.InsertTime,
				Type:      enum.OdsBusinessEventTypeCreate,
				OperateNo: operateNo,
			}
			eventList = append(eventList, tmpItem)
		}

		if len(eventList) > 0 {
			//默认加个topic，实际还是走list里面的topic
			sendErr := szkafka.SendMulti(myuuordertrack.TopicEventBusinessDispatchPay, eventList...)
			if sendErr != nil {
				return sendErr
			}
		}
	}

	return nil
}

// 商户过滤
func merchantFiltering(list []ordertrackqueryservice.OrderTrackInfoData, allowMerchantIds []int) ([]ordertrackqueryservice.OrderTrackInfoData, error) {
	if len(allowMerchantIds) == 0 {
		return list, nil
	}
	var orderNos []string
	for _, item := range list {
		orderNos = append(orderNos, item.OrderNum)
	}
	if len(orderNos) == 0 {
		return nil, nil
	}
	//获取分销链
	distributionChainMap, distributionChainErr := ods2.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		//假设获取分销链获取异常，不直接返回，全部放行
		global.LOG.Error(fmt.Sprintf("distribution chain info error. info:%s, orderNos:%s", distributionChainErr.Error(), strings.Join(orderNos, ",")))
		return list, nil
	}
	var res []ordertrackqueryservice.OrderTrackInfoData
	for _, item := range list {
		orderNo := item.OrderNum
		chain, chainExist := distributionChainMap[orderNo]
		if !chainExist {
			global.LOG.Error(fmt.Sprintf("order is not chain. orderNo:%s", orderNo))
			continue
		}
		var splitIds []int
		for _, info := range chain {
			splitIds = append(splitIds, info.SellerId)
			splitIds = append(splitIds, info.BuyerId)
		}
		//分销链上存在需要被刷的商户，则放行
		for _, splitId := range splitIds {
			if utils.Container(allowMerchantIds, splitId) {
				res = append(res, item)
				break
			}
		}
	}

	return res, nil
}
