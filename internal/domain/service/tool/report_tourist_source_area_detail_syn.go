package tool

import (
	"encoding/json"
	"errors"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/merchantconfig"
	tourist2 "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/oldreport"
	"report-service/internal/global"
	"time"
)

type ReportTouristSourceAreaDetailSynParams struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Fid       int    `json:"fid"`
}

// 支付-1
// 取消-2
// 验证-3
// 撤销-4
// 售后-5
var OpTypeToOperateTypeMap = map[int]int{
	1: enum.DWMOperateTypePay,
	2: enum.DWMOperateTypeCancel,
	3: enum.DWMOperateTypeVerify,
	4: enum.DWMOperateTypeRevoke,
	5: enum.DWMOperateTypeAfterSale,
}

func TouristSourceAreaDetailSyn(startTime, endTime string, fid int, pageSize int) {
	//获取执行参数集
	synParams, err := getSynParams(startTime, endTime, fid)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("syn params is error. err:%s", err.Error()))
		return
	}
	for _, param := range synParams {
		startDate := carbon.Parse(param.StartTime)
		endDate := carbon.Parse(param.EndTime)
		for date := startDate; date.Lte(endDate); date = date.AddDays(1) {
			//按小时处理数据
			for dt := date; dt.Lte(date.EndOfDay()); dt = dt.AddHours(1) {
				err = handleSyn(fid, dt, pageSize)
				if err != nil {
					global.LOG.Error(fmt.Sprintf("syn hour is error. err:%s", err.Error()))
					continue
				}
				//小时重算
				err = tourist2.RestatHour(dt.StartOfHour(), dt.EndOfHour(), fid)
				if err != nil {
					return
				}
			}

			//天重算,非当天重算
			if date.Lt(carbon.Now().StartOfDay()) {
				err = tourist2.RestatDay(date.StartOfDay(), date.EndOfDay(), fid)
				if err != nil {
					global.LOG.Error(fmt.Sprintf("syn hour is error. err:%s", err.Error()))
					continue
				}
			}
		}
	}

	fmt.Println("syn success.")

	return
}

func handleSyn(fid int, startTime carbon.Carbon, pageSize int) error {
	pageNum := 1
	if pageSize < 0 {
		pageSize = 100
	}
	var insetData []tourist.Model
	for {
		list, err := repository.PftReportTouristSourceAreaDetailResult.Paginate(startTime, fid, pageNum, pageSize)
		if err != nil {
			return err
		}
		if len(list) == 0 {
			break
		}
		//获取订单集合
		var orderNoList []string
		var ids []int
		for _, item := range list {
			orderNoList = append(orderNoList, item.Ordernum)
			ids = append(ids, item.Id)
		}
		//批量获取业务操作编号
		businessNoMap := ods.BatchCreateBusinessOperationNumber(enum.BusinessOperationNumberTypeTouristAreaHistory, ids)
		//获取订单数据
		orderInfoMap, orderInfoDataErr := ods.OrderInfoByOrderNos(orderNoList)
		if orderInfoDataErr != nil {
			return orderInfoDataErr
		}

		for _, item := range list {
			orderNo := item.Ordernum
			//订单信息
			orderInfo, orderInfoExist := orderInfoMap[orderNo]
			if !orderInfoExist {
				global.LOG.Error(fmt.Sprintf("syn order info is error. order_no:%s", orderNo))
				continue
			}
			//业务标识
			businessNo, businessNoExist := businessNoMap[item.Id]
			if !businessNoExist {
				global.LOG.Error(fmt.Sprintf("syn business no is error. track_id:%d", item.Id))
				continue
			}
			//一级分销商
			distributorId := tourist.GetFirstDistributorId(orderInfo)
			//操作时间转换
			opTime := getOpTime(item)
			//特殊产品规则
			packType, showBindType, annualCardType, exchangeCouponType := getSpecialProductRules(item)
			//操作信息
			opInfo, opInfoErr := getOpInfo(item)
			if opInfoErr != nil {
				continue
			}
			//数据组装
			insetItem := tourist.Model{
				BusinessNo:         businessNo,
				SpuId:              item.ProductTid,
				Count:              item.TouristNum,
				DistributorId:      distributorId,
				OperatedAt:         opTime.ToDateTimeString(),
				MerchantId:         item.SupplierId,
				SortKey:            opTime.ToTimestampStruct().String() + cast.ToString(businessNo),
				OrderNo:            orderNo,
				SkuId:              item.ProductTid,
				OperateType:        OpTypeToOperateTypeMap[item.OpType],
				PackType:           packType,
				ShowBindType:       showBindType,
				AnnualCardType:     annualCardType,
				ExchangeCouponType: exchangeCouponType,
				BusinessIdx:        item.Idx,
				BusinessCode:       item.ChkCode,
				IdNumber:           item.IDNumber,
				IdType:             item.IdType,
				OperatorId:         opInfo.OpMember,
				OperateSiteId:      opInfo.OpSite,
				OperateChannel:     opInfo.OpChannel,
				OrderChannel:       orderInfo.OrderMode,
				Age:                enum.AgeUnknownKey,
				Gender:             enum.GenderUnknownKey,
				Region:             cast.ToString(item.LevelTop),
				Province:           cast.ToString(item.Level1),
				City:               cast.ToString(item.Level2),
				District:           cast.ToString(item.Level3),
			}
			//境内==中国
			if item.LevelTop == 1 {
				insetItem.Country = cast.ToString(enum.CountryCnKey)
			} else {
				insetItem.Country = cast.ToString(enum.CommonUnknownKey)
			}
			//合并数据
			insetData = append(insetData, insetItem)

			//写入数据
			insetData, err = tourist.ResetInsertDataIfFull(insetData, false)
			if err != nil {
				continue
			}
		}
		pageNum++
	}

	//剩余数据写入
	_, err := tourist.ResetInsertDataIfFull(insetData, true)
	if err != nil {
		return err
	}

	return nil
}

func getSynParams(startTime, endTime string, fid int) ([]ReportTouristSourceAreaDetailSynParams, error) {
	if startTime != "" && endTime != "" && (carbon.Parse(startTime).IsValid() == false || carbon.Parse(endTime).IsValid() == false) {
		return nil, errors.New("时间格式错误")
	}
	var err error
	key := module.GetModuleConfigKey(enum.ModuleTagTouristSourceArea)
	pageNum := 1
	pageSize := 10
	var synParams []ReportTouristSourceAreaDetailSynParams
	for {
		//取系统全部配置
		var list []merchantconfig.MerchantConfig
		list, err = merchantconfig.PaginateByKey(key, pageNum, pageSize)
		if err != nil {
			return nil, errors.New(fmt.Sprintf("获取商户配置失败, %s", err.Error()))
		}
		if len(list) == 0 {
			break
		}
		for _, item := range list {
			value := touristsourceareamodule.GetTouristSourceAreaConfigItem(item.Payload)
			if value == nil {
				continue
			}
			if fid > 0 && item.MerchantId != fid {
				continue
			}
			var tmp ReportTouristSourceAreaDetailSynParams
			tmp.Fid = item.MerchantId
			if startTime != "" && endTime != "" {
				tmp.StartTime = startTime
				tmp.EndTime = endTime
			} else {
				tmp.StartTime = value.StartTime
				tmp.EndTime = value.EndTime
			}
			synParams = append(synParams, tmp)
		}
		pageNum += 1
	}

	return synParams, nil
}

func getSpecialProductRules(item oldreport.PftReportTouristSourceAreaDetail) (packType int, showBindType int, annualCardType int, exchangeCouponType int) {
	packType = 0
	showBindType = 0
	annualCardType = 0
	exchangeCouponType = 0
	if item.ProductRuleType == 1 {
		packType = 1
	}
	if item.ProductRuleType == 2 {
		showBindType = 1
	}
	if item.ProductRuleType == 3 {
		annualCardType = 1
	}
	if item.ProductRuleType == 4 {
		exchangeCouponType = 1
	}
	if item.ProductRuleType == 5 {
		packType = 2
	}
	if item.ProductRuleType == 6 {
		showBindType = 2
	}
	if item.ProductRuleType == 7 {
		annualCardType = 2
	}
	if item.ProductRuleType == 8 {
		exchangeCouponType = 2
	}

	return
}

func getOpTime(item oldreport.PftReportTouristSourceAreaDetail) carbon.Carbon {
	nanoseconds := cast.ToInt64(item.OpTime) * int64(time.Second)
	t := time.Unix(0, nanoseconds)
	date := t.Format(enum.TimestampLayoutMake)

	return carbon.Parse(date)
}

func getOpInfo(item oldreport.PftReportTouristSourceAreaDetail) (*oldreport.TouristOpInfo, error) {
	var conf oldreport.TouristOpInfo
	if item.TouristOpInfo != "" {
		err := json.Unmarshal([]byte(item.TouristOpInfo), &conf)
		if err != nil {
			err = errors.New(fmt.Sprintf("解析同步数据操作人信息失败，数据id：%d， 错误信息：%s", item.Id, err.Error()))
			return nil, err
		}
	}
	return &conf, nil
}
