package tool

import (
	"context"
	"errors"
	"fmt"
	"math"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	logicods "report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/permission/accesswhitelist"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	szkafka "report-service/internal/global/kafka"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/ordertrackqueryservice"
	pkgszkafka "report-service/pkg/szkafka"
	"report-service/pkg/utils"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
)

type ScriptParams struct {
	MerchantId  int
	Begin       string
	End         string
	PageSize    int
	OrderNumIn  []string
	ActionIn    []int
	SnowflakeId int
	SkuIds      []int
	ChainSize   int
}

type TimeRange struct {
	Begin, End string
}

const (
	FormatTimeMakeDate = "2006-01-02 15:04:05"
)

func (s *ScriptParams) BusinessWashScript() error {
	var err error
	defer func() {
		if r := recover(); r != nil {
			fmt.Println(fmt.Sprint("BusinessScriptTask-error, panic", r))
			return
		}
		if err != nil {
			fmt.Println(err)
			return
		}
		//执行成功
		fmt.Println("success")
	}()
	if ((s.OrderNumIn == nil || len(s.OrderNumIn) == 0) && (s.Begin == "" || s.End == "")) || s.PageSize == 0 {
		return errors.New("BusinessScriptTask-error 参数错误")
	}
	if s.Begin != "" && s.End != "" {
		err = s.HandleScriptRangeDate()
		if err != nil {
			return err
		}
	} else {
		err = s.HandleScript(TimeRange{})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *ScriptParams) HandleScriptRangeDate() error {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	RangeDateList := s.ScriptGenerateTimeSlices(s.Begin, s.End)
	if RangeDateList == nil || len(RangeDateList) == 0 {
		return errors.New(fmt.Sprintf("%d BusinessScriptTask-error 时间范围解析失败", s.SnowflakeId))
	}
	//创建一个有容量的channel作为信号量
	sem := make(chan struct{}, s.ChainSize)
	var wg sync.WaitGroup
	errChan := make(chan error, len(RangeDateList))
	for _, timeRange := range RangeDateList {
		select {
		case <-ctx.Done():
			break
		default:
			wg.Add(1)
			// 获取一个信号量
			sem <- struct{}{}
			go func(tr TimeRange) {
				defer wg.Done()
				//释放信号量
				defer func() {
					<-sem
					if r := recover(); r != nil {
						s.ScriptSetLog(fmt.Sprint("时段：", timeRange.Begin, "~", timeRange.End, "， 处理异常· panic", r))
						return
					}
				}()
				if err := s.HandleScriptRunning(ctx, tr); err != nil {
					select {
					case errChan <- err:
						// 发送错误到errChan，如果errChan已满，则忽略
					default:
					}
					cancel() // 取消所有goroutine的执行
					return
				}
			}(timeRange)
		}
	}
	go func() {
		wg.Wait()
		close(errChan)
	}()
	for err := range errChan {
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *ScriptParams) HandleScriptRunning(ctx context.Context, tr TimeRange) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		return s.HandleScript(tr)
	}
}

func (s *ScriptParams) HandleScript(tr TimeRange) error {
	if tr.Begin != "" || tr.End != "" {
		s.ScriptSetLog(fmt.Sprintf("时段：%s ~ %s， 处理开始······", tr.Begin, tr.End))
	}
	defer func() {
		if tr.Begin != "" || tr.End != "" {
			s.ScriptSetLog(fmt.Sprintf("时段：%s ~ %s， 处理完成·", tr.Begin, tr.End))
		}
	}()
	if len(s.ActionIn) <= 0 {
		s.ActionIn = myuuordertrack.EventMyuuOrderTrackAction
	}
	size := s.PageSize
	var err error
	javaParams := map[string]interface{}{
		"page":     1,
		"size":     size,
		"actionIn": s.ActionIn,
		"sortBy":   []string{"insertTime", "id"},
		"desc":     true,
	}
	if s.OrderNumIn != nil && len(s.OrderNumIn) > 0 {
		javaParams["ordernumIn"] = s.OrderNumIn
	}
	if tr.Begin != "" && tr.End != "" {
		javaParams["minInsertTime"] = tr.Begin
		javaParams["maxInsertTime"] = tr.End
	}
	if len(s.SkuIds) > 0 {
		javaParams["tidIn"] = s.SkuIds
	} else {
		if s.MerchantId != 0 && tr.Begin != "" && tr.End != "" {
			//查询旧小时报表的tid，用来反查order_track
			begin := carbon.Parse(tr.Begin)
			end := carbon.Parse(tr.End)
			var skuIds []int
			if utils.Container([]string{enum.EnvGray, enum.EnvProd}, global.CONFIG.System.Env) {
				skuIds, err = getStatisticTicketIdsByHour(begin, end, []int{s.MerchantId})
			} else {
				skuIds, err = getStatisticTicketIdsByDay(begin, end, []int{s.MerchantId})
			}
			if err != nil {
				return err
			}
			if len(skuIds) == 0 {
				return nil
			}
			javaParams["tidIn"] = skuIds
		}
	}
	var result *ordertrackqueryservice.OrderTrackInfoDataPage
	result, err = ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
	if err != nil {
		return errors.New(fmt.Sprintf("BusinessScriptTask-error 查询order_track.api异常，错误信息：%s", err))
	}
	if result.Total == 0 {
		s.ScriptSetLog(fmt.Sprintf("BusinessScriptTask-error , 无可执行的数据."))
		return nil
	}
	if result.List == nil || len(result.List) <= 0 {
		s.ScriptSetLog(fmt.Sprintf("BusinessScriptTask-error 无可执行的列表数据."))
		return nil
	}
	merchantIds := GetAllowMerchantIds()
	maxPage := float64(result.Total) / float64(size)
	maxNum := int(math.Ceil((maxPage)))
	s.ScriptSetLog(fmt.Sprintf("BusinessScriptTask-info, 待执行总数:%d, 总页数:%d", result.Total, maxNum))
	for page := 1; page <= maxNum; page++ {
		s.ScriptSetLog(fmt.Sprintf("BusinessScriptTask-info 执行页：%d", page))
		javaParams["page"] = page
		result, err = ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
		if err != nil {
			return errors.New(fmt.Sprintf("BusinessScriptTask-error 查询order_track.api异常，错误信息：%s，错误页数：%d", err, page))
		}
		if result.Total == 0 {
			s.ScriptSetLog("BusinessScriptTask-info 无可执行的数据")
			break
		}
		if result.List == nil || len(result.List) <= 0 {
			s.ScriptSetLog("BusinessScriptTask-info 无可执行的列表数据")
			break
		}
		//特定商户过滤
		if s.MerchantId == 0 {
			result.List, err = s.MerchantFiltering(result.List, merchantIds)
		}
		//参数转换
		var eventList []pkgszkafka.ProduceMessage
		for _, item := range result.List {
			tmp := myuuordertrack.EventMyuuOrderTrackParams{
				AfterList: myuuordertrack.EventMyuuOrderTrackParamsAfterList{
					ApplyDid:       item.ApplyDid,
					Tnum:           item.TNum,
					BranchTerminal: item.BranchTerminal,
					IdCard:         item.IDCard,
					OperMember:     item.OperMember,
					OrdreMonth:     item.OrderMonth,
					Ordernum:       item.OrderNum,
					SalerID:        item.SalerID,
					SyncState:      item.SyncState,
					Source:         item.Source,
					Terminal:       item.Terminal,
					ExtContent:     item.ExtContent,
					Tid:            item.TID,
					InsertTime:     item.InsertTime,
					UpdateTime:     item.UpdateTime,
					Action:         item.Action,
					Id:             item.ID,
					LeftNum:        item.LeftNum,
				},
				BeforeList: []interface{}{},
				EventType:  "INSERT",
				SchemaName: "myuu",
				TableName:  "pft_order_track",
			}

			//业务操作号
			operateNo := logicods.CreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, tmp.AfterList.Id)
			//分发
			var tmpItem pkgszkafka.ProduceMessage
			topic := ""
			switch tmp.AfterList.Action {
			case myuuordertrack.EventMyuuOrderTrackActionPay:
				topic = myuuordertrack.TopicEventBusinessDispatchPay
			case myuuordertrack.EventMyuuOrderTrackActionModify:
				topic = myuuordertrack.TopicEventBusinessDispatchCancel
			case myuuordertrack.EventMyuuOrderTrackActionCancel:
				topic = myuuordertrack.TopicEventBusinessDispatchCancel
			case myuuordertrack.EventMyuuOrderTrackActionVerify:
				topic = myuuordertrack.TopicEventBusinessDispatchVerify
			case myuuordertrack.EventMyuuOrderTrackActionRevokeCancel:
				topic = myuuordertrack.TopicEventBusinessDispatchRevoke
			case myuuordertrack.EventMyuuOrderTrackActionRevokeModify:
				topic = myuuordertrack.TopicEventBusinessDispatchRevoke
			case myuuordertrack.EventMyuuOrderTrackActionAfterSale:
				topic = myuuordertrack.TopicEventBusinessDispatchAfterSale
			case myuuordertrack.EventMyuuOrderTrackActionAddTicket:
				topic = myuuordertrack.TopicEventBusinessDispatchAddTicket
			case myuuordertrack.EventMyuuOrderTrackActionFinish:
				topic = myuuordertrack.TopicEventBusinessDispatchFinish
			default:
				err = fmt.Errorf("unknown action: %d", tmp.AfterList.Action)
			}
			if err != nil {
				globalNotice.Error(fmt.Sprintf("order track action handle event error: %s", err.Error()))
				global.LOG.Error("handle event error", zap.Error(err))
				continue
			}
			tmpItem.Topic = &topic
			tmpItem.Key = strconv.Itoa(operateNo)
			tmpItem.Value = myuuordertrack.EventBusinessDispatchItem{
				Data:      tmp,
				OrderNo:   tmp.AfterList.Ordernum,
				OperateAt: tmp.AfterList.InsertTime,
				Type:      enum.OdsBusinessEventTypeCreate,
				OperateNo: operateNo,
			}
			eventList = append(eventList, tmpItem)
		}

		if len(eventList) > 0 {
			//默认加个topic，实际还是走list里面的topic
			sendErr := szkafka.SendMulti(myuuordertrack.TopicEventBusinessDispatchPay, eventList...)
			if sendErr != nil {
				return sendErr
			}
		}
	}

	return nil
}

func (s *ScriptParams) ScriptFormatTime(t time.Time) string {
	return t.Format(FormatTimeMakeDate)
}

// 开始结束时间拆分时段 时段单位：每小时
func (s *ScriptParams) ScriptGenerateTimeSlices(start, end string) []TimeRange {
	var startTime, endTime time.Time
	var err error
	startTime, err = time.Parse(FormatTimeMakeDate, start)
	if err != nil {
		s.ScriptSetLog(fmt.Sprintf("Error parsing start time:%s", err))
		return nil
	}
	endTime, err = time.Parse(FormatTimeMakeDate, end)
	if err != nil {
		s.ScriptSetLog(fmt.Sprintf("Error parsing end time:%s", err.Error()))
		return nil
	}
	// 计算时间差（以小时为单位）
	duration := endTime.Sub(startTime).Hours()
	// 如果时间差不超过1小时，则切片只包含一个元素（开始到结束）
	if duration <= 1 {
		return []TimeRange{{
			s.ScriptFormatTime(startTime), s.ScriptFormatTime(endTime),
		}}
	}
	// 初始化切片来存储时间段
	var slices []TimeRange
	// 遍历每个小时，并添加到切片中
	current := startTime
	for !current.After(endTime) {
		// 计算下一个小时的开始时间
		next := current.Add(time.Hour)
		// 如果下一个小时的开始时间超过了结束时间，我们就不用继续添加了
		if next.After(endTime) {
			slices = append(slices, TimeRange{
				s.ScriptFormatTime(time.Date(endTime.Year(), endTime.Month(), endTime.Day(), endTime.Hour(), 00, 00, 0, endTime.Location())),
				s.ScriptFormatTime(endTime),
			})
			break
		}
		// 添加当前小时的开始时间
		slices = append(slices, TimeRange{
			s.ScriptFormatTime(current),
			s.ScriptFormatTime(time.Date(current.Year(), current.Month(), current.Day(), current.Hour(), 59, 59, 0, current.Location())),
		})
		// 更新当前时间为下一个小时的开始时间
		current = next
	}
	return slices
}

// 添加带有脚本Id的日志
func (s *ScriptParams) ScriptSetLog(msg string) {
	global.LOG.Info(fmt.Sprintf("%d %s", s.SnowflakeId, msg))
}

// 商户过滤
func (s *ScriptParams) MerchantFiltering(list []ordertrackqueryservice.OrderTrackInfoData, allowMerchantIds []int) ([]ordertrackqueryservice.OrderTrackInfoData, error) {
	if len(allowMerchantIds) == 0 {
		return list, nil
	}
	var orderNos []string
	for _, item := range list {
		orderNos = append(orderNos, item.OrderNum)
	}
	if len(orderNos) == 0 {
		return nil, nil
	}
	//获取分销链
	distributionChainMap, distributionChainErr := logicods.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		//假设获取分销链获取异常，不直接返回，全部放行
		global.LOG.Error(fmt.Sprintf("distribution chain info error. info:%s, orderNos:%s", distributionChainErr.Error(), strings.Join(orderNos, ",")))
		return list, nil
	}
	var res []ordertrackqueryservice.OrderTrackInfoData
	for _, item := range list {
		orderNo := item.OrderNum
		chain, chainExist := distributionChainMap[orderNo]
		if !chainExist {
			global.LOG.Error(fmt.Sprintf("order is not chain. orderNo:%s", orderNo))
			continue
		}
		var splitIds []int
		for _, info := range chain {
			splitIds = append(splitIds, info.SellerId)
			splitIds = append(splitIds, info.BuyerId)
		}
		//分销链上存在需要被刷的商户，则放行
		for _, splitId := range splitIds {
			if utils.Container(allowMerchantIds, splitId) {
				res = append(res, item)
				break
			}
		}
	}

	return res, nil
}

func getStatisticTicketIdsByDay(startTime, endTime carbon.Carbon, fids []int) (skuIds []int, err error) {
	var orderSkuIds, checkSkuIds []int
	orderSkuIds, err = repository.OldReportOrderTwoRepo.GetStatisticTicketIds(startTime, endTime, fids)
	if err != nil {
		return nil, err
	}
	checkSkuIds, err = repository.OldReportCheckTwoRepo.GetStatisticTicketIds(startTime, endTime, fids)
	if err != nil {
		return nil, err
	}
	skuIds = utils.MergeSlice(orderSkuIds, checkSkuIds)
	skuIds = utils.RemoveDuplicate(skuIds)
	return skuIds, nil
}

func getStatisticTicketIdsByHour(startTime, endTime carbon.Carbon, fids []int) (skuIds []int, err error) {
	var orderSkuIds, checkSkuIds []int
	orderSkuIds, err = repository.OldReportOrderTwoHourRepo.GetStatisticTicketIds(startTime, endTime, fids)
	if err != nil {
		return nil, err
	}
	checkSkuIds, err = repository.OldReportCheckTwoHourRepo.GetStatisticTicketIds(startTime, endTime, fids)
	if err != nil {
		return nil, err
	}
	skuIds = utils.MergeSlice(orderSkuIds, checkSkuIds)
	skuIds = utils.RemoveDuplicate(skuIds)
	return skuIds, nil
}

// GetAllowMerchantIds 针对不同环境简单做下区分
func GetAllowMerchantIds() []int {
	merchantIds, _ := accesswhitelist.GetAllWhiteListMerchants()
	if len(merchantIds) > 0 {
		return merchantIds
	}
	return global.CONFIG.System.MerchantWhiteList
}
