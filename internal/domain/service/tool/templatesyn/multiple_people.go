package templatesyn

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/merchantconfig"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/oldreport"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

func MultiplePeople(params ...interface{}) error {
	fid := 0
	//[旧id]新id
	idsMap := make(map[int]int)
	if len(params) > 0 {
		fid = cast.ToInt(params[0])
	}
	if len(params) > 1 {
		_ = utils.JsonConvertor(params[1], &idsMap)
	}
	var err error
	var merchantIds []int
	if fid == 0 {
		key := module.GetModuleConfigKey(enum.ModuleTagTouristSourceArea)
		pageNum := 1
		pageSize := 10
		for {
			//取系统全部配置
			var list []merchantconfig.MerchantConfig
			list, err = merchantconfig.PaginateByKey(key, pageNum, pageSize)
			if err != nil {
				return errors.New(fmt.Sprintf("获取商户配置失败, %s", err.Error()))
			}
			if len(list) == 0 {
				break
			}
			for _, item := range list {
				value := touristsourceareamodule.GetTouristSourceAreaConfigItem(item.Payload)
				if value == nil {
					continue
				}
				merchantIds = append(merchantIds, item.MerchantId)
			}

			pageNum += 1
		}
	} else {
		//处理指定商户
		merchantIds = append(merchantIds, fid)
	}
	if len(merchantIds) == 0 {
		return nil
	}

	return MultiplePeopleSynHandle(merchantIds, idsMap)
}

func MultiplePeopleSynHandle(merchantIds []int, idsMap map[int]int) error {
	var err error
	//获取模板数据
	pageNum := 1
	pageSize := 10
	oldNewIdMap := make(map[int]int)
	for {
		//取系统全部配置
		var list []oldreport.PftReportSearchConfig
		list, err = repository.PftReportSearchConfigResult.Paginate(merchantIds, enum.TemplateTypeMultiplePeople, pageNum, pageSize)
		if err != nil {
			return errors.New(fmt.Sprintf("获取商户配置失败, %s", err.Error()))
		}
		if len(list) == 0 {
			break
		}
		for _, item := range list {
			//存在id，则编辑
			if id, ok := idsMap[item.Id]; ok {
				oldNewIdMap[item.Id] = id
				err = multiplePeopleSynModify(item, id)
				if err != nil {
					global.LOG.Error(err.Error())
				}
				continue
			}
			//不存在，则新增
			var newId int
			newId, err = multiplePeopleSynCreate(item)
			if err != nil {
				global.LOG.Error(err.Error())
			}
			if newId != 0 {
				oldNewIdMap[item.Id] = newId
			}
		}

		pageNum += 1
	}
	//记录下关联关系
	marshal, err := json.Marshal(oldNewIdMap)
	if err != nil {
		return err
	}
	global.LOG.Info(fmt.Sprintf("模板同步完成, %s", string(marshal)))

	return nil
}
func multiplePeopleSynCreate(item oldreport.PftReportSearchConfig) (int, error) {
	var err error
	var tmpParams template.DoCreate
	tmpParams.Category = enum.TemplateCategoryTouristSourceArea
	tmpParams.DoCommonParams = fillCommonFields(item)
	var dimension []string
	dimension, err = getOldTemplateDimension(item)
	if err != nil {
		return 0, err
	}
	var operateType []int
	operateType, err = getOperateType(item)
	if err != nil {
		return 0, err
	}
	var doSpecialDimension common.DoSpecialDimension
	doSpecialDimension, err = getItemValue(item)
	if err != nil {
		return 0, err
	}
	var specialProductRules common.SpecialProductRules
	specialProductRules, err = getSpecialProductRules(item)
	if err != nil {
		return 0, err
	}
	tmpParams.Dimension = dimension
	tmpParams.OperateType = operateType
	tmpParams.SpecialDimension = doSpecialDimension
	tmpParams.SpecialProductRules = specialProductRules

	//保存
	id := 0
	id, err = template.Create(tmpParams)
	if err != nil {
		return 0, errors.New(fmt.Sprintf("模板编辑更新失败，旧模板：%d，新模板id：%d， 错误信息：%s", item.Id, id, err.Error()))
	}
	return id, nil
}

func multiplePeopleSynModify(item oldreport.PftReportSearchConfig, id int) error {
	var err error
	var tmpParams template.DoModify
	tmpParams.DoCommonParams = fillCommonFields(item)
	tmpParams.TemplateId = id //新模板id
	var dimension []string
	dimension, err = getOldTemplateDimension(item)
	if err != nil {
		return err
	}
	var operateType []int
	operateType, err = getOperateType(item)
	if err != nil {
		return err
	}
	var doSpecialDimension common.DoSpecialDimension
	doSpecialDimension, err = getItemValue(item)
	if err != nil {
		return err
	}
	var specialProductRules common.SpecialProductRules
	specialProductRules, err = getSpecialProductRules(item)
	if err != nil {
		return err
	}
	tmpParams.Dimension = dimension
	tmpParams.OperateType = operateType
	tmpParams.SpecialDimension = doSpecialDimension
	tmpParams.SpecialProductRules = specialProductRules

	//保存
	err = template.Modify(tmpParams)
	if err != nil {
		return errors.New(fmt.Sprintf("模板编辑更新失败，旧模板：%d，新模板id：%d， 错误信息：%s", item.Id, id, err.Error()))
	}
	return nil
}

func getOldTemplateDimension(item oldreport.PftReportSearchConfig) (dimension []string, err error) {
	err = json.Unmarshal([]byte(item.Item), &dimension)
	if err != nil {
		err = errors.New(fmt.Sprintf("模板编辑解析维度失败，旧模板：%d， 错误信息：%s", item.Id, err.Error()))
		return
	}

	return
}

func getOperateType(item oldreport.PftReportSearchConfig) (operateType []int, err error) {
	var otList []string
	err = json.Unmarshal([]byte(item.Target), &otList)
	if err != nil {
		err = errors.New(fmt.Sprintf("模板编辑解析操作类型失败，旧模板：%d， 错误信息：%s", item.Id, err.Error()))
		return
	}
	for _, ot := range otList {
		switch ot {
		case "pay":
			operateType = append(operateType, enum.DWMOperateTypePay)
		case "cancel":
			operateType = append(operateType, enum.DWMOperateTypeCancel)
		case "revoke":
			operateType = append(operateType, enum.DWMOperateTypeRevoke)
		case "after_sales":
			operateType = append(operateType, enum.DWMOperateTypeAfterSale)
		case "check":
			operateType = append(operateType, enum.DWMOperateTypeVerify)
		}
	}
	if len(operateType) == 0 {
		err = errors.New(fmt.Sprintf("模板编辑解析操作类型失败，旧模板：%d， 错误信息：不存在操作类型", item.Id))
		return
	}
	return
}

func getItemValue(item oldreport.PftReportSearchConfig) (doSpecialDimension common.DoSpecialDimension, err error) {
	var itemValue ItemValue
	if item.ItemValue != "" && item.ItemValue != "[]" {
		err = json.Unmarshal([]byte(item.ItemValue), &itemValue)
		if err != nil {
			err = errors.New(fmt.Sprintf("模板编辑解析特殊维度失败，旧模板：%d， 错误信息：%s", item.Id, err.Error()))
			return
		}
	}
	if itemValue.Lid != nil && len(itemValue.Lid) > 0 {
		for _, lidItem := range itemValue.Lid {
			doSpecialDimension.Spu = append(doSpecialDimension.Spu, lidItem.Id)
		}
	}
	if itemValue.Tid != nil && len(itemValue.Tid) > 0 {
		for _, tidItem := range itemValue.Tid {
			doSpecialDimension.Sku = append(doSpecialDimension.Sku, tidItem.Id)
		}
	}
	return
}

func getSpecialProductRules(item oldreport.PftReportSearchConfig) (specialProductRules common.SpecialProductRules, err error) {
	var conf ExtConfig
	if item.ExtConfig != "" {
		err = json.Unmarshal([]byte(item.ExtConfig), &conf)
		if err != nil {
			err = errors.New(fmt.Sprintf("模板编辑解析扩展字段失败，旧模板：%d， 错误信息：%s", item.Id, err.Error()))
			return
		}
	}
	if conf.CustomProduct != nil && len(conf.CustomProduct) > 0 {
		for _, customProductKey := range conf.CustomProduct {
			if customProductKey == 1 {
				specialProductRules.PackType = append(specialProductRules.PackType, 1)
			}
			if customProductKey == 2 {
				specialProductRules.ShowBindType = append(specialProductRules.ShowBindType, 1)
			}
			if customProductKey == 3 {
				specialProductRules.AnnualCardType = append(specialProductRules.AnnualCardType, 1)
			}
			if customProductKey == 4 {
				specialProductRules.ExchangeCouponType = append(specialProductRules.ExchangeCouponType, 1)
			}
			if customProductKey == 5 {
				specialProductRules.PackType = append(specialProductRules.PackType, 2)
			}
			if customProductKey == 6 {
				specialProductRules.ShowBindType = append(specialProductRules.ShowBindType, 2)
			}
			if customProductKey == 7 {
				specialProductRules.AnnualCardType = append(specialProductRules.AnnualCardType, 2)
			}
			if customProductKey == 8 {
				specialProductRules.ExchangeCouponType = append(specialProductRules.ExchangeCouponType, 2)
			}
		}
	}
	return
}

func fillCommonFields(item oldreport.PftReportSearchConfig) template.DoCommonParams {
	return template.DoCommonParams{
		MerchantId:   item.Fid,
		MemberId:     item.MemberId,
		Title:        item.Name,
		Indicator:    []string{enum.IndicatorTouristCount},
		MultipleMode: []string{enum.TemplateMultipleModeSourceArea},
	}
}
