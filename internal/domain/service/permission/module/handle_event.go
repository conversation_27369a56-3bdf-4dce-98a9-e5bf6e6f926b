package module

import (
	"encoding/json"
	"fmt"
	"github.com/segmentio/kafka-go"
	"report-service/internal/domain/event/modulechange"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/global"
	"report-service/internal/global/notice"
	"report-service/pkg/szerrors"
)

func HandleModuleChange(messages ...kafka.Message) error {
	for _, message := range messages {
		var data modulechange.ChangeInfo
		err := json.Unmarshal(message.Value, &data)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("module change format params error, err:%s", err.Error()))
			continue
		}
		if !validateMessage(data) {
			continue
		}
		changeTime := data.GetChangeTime()
		o := module.OperateData{
			MerchantId: data.MemberId,
			ModuleTag:  data.ModuleTag,
			ModuleId:   data.ModuleId,
			Action:     data.Action,
			ChangeTime: changeTime,
		}
		if handler, ok := ModuleHandlerMap[data.ModuleTag]; ok {
			switch data.Action {
			case modulechange.ModuleActionOpen:
				err = handler.HandleOpen(o)
			case modulechange.ModuleActionClose:
				err = handler.HandleClose(o)
			default:
				err = szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("未知应用操作类型: %d", data.Action))
			}
			if err != nil {
				errMsg := fmt.Sprintf("应用变更事件处理失败，商户：%d，标识：%s，错误信息：%s", data.MemberId, data.ModuleTag, err.Error())
				//日志记录
				global.LOG.Error(errMsg)
				//推送消息预警
				notice.Error(errMsg)
			}
		}
	}
	return nil
}

func validateMessage(params modulechange.ChangeInfo) bool {
	//参数校验
	if params.MemberId == 0 || params.Action == 0 || params.ChangeTime == "" || params.ModuleTag == "" {
		msg := "module change invalid params" + fmt.Sprintf("%+v", params)
		global.LOG.Error(msg)
		return false
	}
	if !params.GetChangeTime().IsValid() {
		return false
	}
	return true
}
