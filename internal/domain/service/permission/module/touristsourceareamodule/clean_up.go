package touristsourceareamodule

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/merchantconfig"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
)

func CleanUpDataYesterday() {
	_ = CleanUpData(carbon.Now().Yesterday().EndOfDay())
}

func CleanUpData(date carbon.Carbon, optional ...interface{}) error {
	key := module.GetModuleConfigKey(enum.ModuleTagTouristSourceArea)
	pageNum := 1
	pageSize := 10
	var cleanMerchantMap = make(map[int]touristsourceareamodule.ModuleConfig, 0)
	for {
		list, err := merchantconfig.PaginateByKey(key, pageNum, pageSize)
		if err != nil {
			return err
		}
		if len(list) == 0 {
			break
		}
		for _, item := range list {
			value := touristsourceareamodule.GetTouristSourceAreaConfigItem(item.Payload)
			if value == nil {
				continue
			}
			endTime := carbon.Parse(value.EndTime)
			if endTime.BetweenIncludedBoth(date.StartOfDay(), date.EndOfDay()) && !value.IsCleanUp {
				cleanMerchantMap[item.MerchantId] = *value
			}
		}
		pageNum += 1
	}
	for merchantId, config := range cleanMerchantMap {
		startDate := carbon.Parse(config.StartTime)
		endDate := carbon.Parse(config.EndTime)
		for d := startDate; d.Lte(endDate); d = d.AddDays(1) {
			//清除明细数据
			_ = repository.ReportDwmTouristRepository.DeleteWhereTimeRange(d.StartOfDay(), d.EndOfDay(), merchantId)
			//清除小时指标数据
			_ = repository.ReportDmTouristHourRepository.DeleteWhereTimeRange(d.StartOfDay(), d.EndOfDay(), merchantId)
			//清除日指标数据
			_ = repository.ReportDmTouristDayRepository.DeleteWhereTimeRange(d.StartOfDay(), d.EndOfDay(), merchantId)

			global.LOG.Info(fmt.Sprintf("清除商户%d【%s】的数据完成", merchantId, d.ToDateTimeString()))
		}

		//更新商户配置已删除
		_ = saveTouristSourceAreaConfigIsClean(merchantId, config)
	}

	global.LOG.Info(fmt.Sprintf("清除【%s】的数据完成", date.ToDateTimeString()))

	return nil
}

func saveTouristSourceAreaConfigIsClean(merchantId int, config touristsourceareamodule.ModuleConfig) error {
	//更新商户配置
	o := module.OperateData{
		ModuleTag:  enum.ModuleTagTouristSourceArea,
		MerchantId: merchantId,
	}
	config.IsCleanUp = true
	err := o.SaveModuleConfig(config)
	if err != nil {
		//更新失败记录
		global.LOG.Error(fmt.Sprintf("清除商户%d的数据失败【%s】, 错误：%s", merchantId, carbon.Parse(config.EndTime).ToDateTimeString(), err.Error()))
		return err
	}
	return nil
}
