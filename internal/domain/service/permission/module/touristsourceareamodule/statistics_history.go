package touristsourceareamodule

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/merchantconfig"
	dmtourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
	"report-service/internal/global"
	"report-service/internal/global/notice"
)

func StatisticsHistory() {
	var err error
	defer func() {
		if err != nil {
			errMsg := fmt.Sprintf("客源地报表汇总历史数据失败，错误信息：%s",
				err.Error())
			//日志记录
			global.LOG.Error(errMsg)
			//推送消息预警
			notice.Error(errMsg)
		}
	}()
	key := module.GetModuleConfigKey(enum.ModuleTagTouristSourceArea)
	pageNum := 1
	pageSize := 10
	//每次只处理一个商户
	var MerchantMap = make(map[int]touristsourceareamodule.ModuleConfig, 1)
	for {
		var list []merchantconfig.MerchantConfig
		list, err = merchantconfig.PaginateByKey(key, pageNum, pageSize)
		if err != nil {
			return
		}
		if len(list) == 0 {
			break
		}
		outerLoop := false
		for _, item := range list {
			value := touristsourceareamodule.GetTouristSourceAreaConfigItem(item.Payload)
			if value == nil {
				continue
			}
			if value.OpenStatus == touristsourceareamodule.HandleOpenStatusWait && value.IsProcessing {
				//存在需要处理的商户则返回
				MerchantMap[item.MerchantId] = *value
				outerLoop = true
				break
			}
		}
		if outerLoop {
			break
		}
		pageNum += 1
	}
	//无数据，直接返回
	if len(MerchantMap) == 0 {
		return
	}
	for merchantId, config := range MerchantMap {
		//更新商户配置
		o := module.OperateData{
			ModuleTag:  enum.ModuleTagTouristSourceArea,
			MerchantId: merchantId,
		}
		//更新为处理中
		err = saveTouristSourceAreaConfig(o, config, touristsourceareamodule.HandleOpenStatusProcess, config.IsProcessing, 0, config.Progress.CurrentDate)
		if err != nil {
			return
		}
		startDate := carbon.Parse(config.Progress.BeginTime)
		endDate := carbon.Parse(config.Progress.EndTime)
		currentDate := carbon.Now()
		for date := startDate; date.Lte(endDate); date = date.AddDays(1) {
			endDt := date.EndOfDay()
			//最后一天的话，取结束时间
			if endDt.Gte(endDate.EndOfDay()) {
				endDt = endDate.EndOfHour()
			}
			_ = tourist.OperateTask(date.StartOfDay(), endDt, merchantId, true)
			if date.Lt(currentDate.StartOfDay()) {
				//日指标汇总
				_ = dmtourist.RestatDay(date.StartOfDay(), date.EndOfDay(), merchantId)
				duration := endDate.DiffAbsInDuration(startDate)
				elapsed := date.DiffAbsInDuration(startDate)
				progress := float64(elapsed) / float64(duration) * 100
				//更新进度
				err = saveTouristSourceAreaConfig(o,
					config,
					touristsourceareamodule.HandleOpenStatusProcess,
					config.IsProcessing,
					cast.ToInt(progress),
					date.ToDateTimeString())
				if err != nil {
					return
				}
			}
		}
		//更新为处理完成
		err = saveTouristSourceAreaConfig(o, config, touristsourceareamodule.HandleOpenStatusFinish, false, 100, config.Progress.EndTime)
		if err != nil {
			return
		}
	}
	return
}

func saveTouristSourceAreaConfig(o module.OperateData,
	config touristsourceareamodule.ModuleConfig,
	openStatus int,
	isProcessing bool,
	percent int,
	currentDate string,
) error {
	err := touristsourceareamodule.SaveTouristSourceAreaConfig(o,
		config.IsEnable,
		config.StartTime,
		config.EndTime,
		openStatus,
		isProcessing,
		config.IsCleanUp,
		percent,
		currentDate,
		config.Progress.EndTime,
	)
	if err != nil {
		return err
	}
	return nil
}
