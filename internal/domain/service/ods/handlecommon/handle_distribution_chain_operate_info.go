package handlecommon

import (
	"errors"
	"fmt"
	"report-service/internal/domain/logic/ods/business"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/utils"
)

type DistributionChainOperateInfo struct {
	OperateSiteIds   []int       `json:"operate_site_ids"`
	OperateIds       []int       `json:"operate_ids"`
	SiteSidMap       map[int]int `json:"site_sid_map"`
	OperatorIdSidMap map[int]int `json:"operator_id_sid_map"`
}

func (d *DistributionChainOperateInfo) GetOperateInfo() (*DistributionChainOperateInfo, error) {
	siteIds := d.OperateSiteIds
	if len(siteIds) > 0 {
		//站点
		siteIds = utils.RemoveDuplicate(siteIds)
		siteSidMap, siteErr := platformrpcapi.QuerySiteSidMapByIds(siteIds)
		if siteErr != nil {
			return nil, siteErr
		}
		d.SiteSidMap = siteSidMap
	}
	operatorIds := d.OperateIds
	if len(operatorIds) > 0 {
		operatorIds = utils.RemoveDuplicate(operatorIds)
		memberRelationMapNew := make(map[int]int)
		memberRelationMap, memberErr := pftmember.QueryMemberStaffRelationMapByIds(operatorIds)
		if memberErr != nil {
			return nil, memberErr
		}
		//存在主账号，则是员工，不存在则默认为就是主账号本身
		for _, id := range operatorIds {
			if parentId, exists := memberRelationMap[id]; exists {
				memberRelationMapNew[id] = parentId
				continue
			}
			memberRelationMapNew[id] = id
		}
		d.OperatorIdSidMap = memberRelationMapNew
	}
	return d, nil
}

func (d *DistributionChainOperateInfo) HandleDistributionChainOperateInfo(chain business.DataPayloadDistributionChain, operateSiteId int, sellSiteId int, operatorId int, sellOperatorId int) business.DataPayloadDistributionChain {
	if chain.SellerId == 0 || (operateSiteId == 0 && sellSiteId == 0 && operatorId == 0 && sellOperatorId == 0) {
		return chain
	}
	//操作站点
	if operateSiteId > 0 {
		if operateSiteSid, operateSiteExists := d.SiteSidMap[operateSiteId]; operateSiteExists && operateSiteSid == chain.SellerId {
			chain.OperateSiteId = operateSiteId
		}
	}
	//销售站点归属
	if sellSiteId > 0 {
		if SellSiteSid, SellSiteExists := d.SiteSidMap[sellSiteId]; SellSiteExists && SellSiteSid == chain.SellerId {
			chain.SellSiteId = sellSiteId
		}
	}
	//操作人
	if operatorId > 0 {
		if operatorParentId, operatorParentExists := d.OperatorIdSidMap[operatorId]; operatorParentExists && operatorParentId == chain.SellerId {
			chain.OperatorId = operatorId
		}
	}
	//售票员
	if sellOperatorId > 0 {
		if sellOperatorParentId, sellOperatorParentExists := d.OperatorIdSidMap[sellOperatorId]; sellOperatorParentExists && sellOperatorParentId == chain.SellerId {
			chain.SellOperatorId = sellOperatorId
		}
	}
	return chain
}

func HandlePayDistributionChainOperateInfo(insertDataList []business.OdsProcessor) ([]business.OdsProcessor, error) {
	var operateSiteIds, operateIds []int
	for _, data := range insertDataList {
		operateSiteIds = append(operateSiteIds, data.GetOperateInfo().OperateSiteId, data.GetOperateInfo().SellSiteId)
		operateIds = append(operateIds, data.GetOperateInfo().OperatorId, data.GetOperateInfo().SellOperatorId)
	}
	if len(operateSiteIds) <= 0 && len(operateIds) <= 0 {
		return insertDataList, nil
	}
	handle := &DistributionChainOperateInfo{
		OperateSiteIds: operateSiteIds,
		OperateIds:     operateIds,
	}
	var handleErr error
	handle, handleErr = handle.GetOperateInfo()
	if handleErr != nil {
		return insertDataList, errors.New(fmt.Sprintf("distribution chain operate error. info:%s", handleErr))
	}
	for _, tmp := range insertDataList {
		//处理链路信息
		for chainIndex, chain := range tmp.GetChain() {
			chainResult := handle.HandleDistributionChainOperateInfo(chain, tmp.GetOperateInfo().OperateSiteId, tmp.GetOperateInfo().SellSiteId, tmp.GetOperateInfo().OperatorId, tmp.GetOperateInfo().SellOperatorId)
			tmp.SetChain(chainIndex, chainResult)
		}
	}
	return insertDataList, nil
}
