package handlecommon

import (
	"report-service/internal/domain/logic/ods/business"
)

const (
	DefaultReseller = 112   //默认散客
	SalePayModeKey  = 10001 //末级分销补全
)

type DistributionChainNeedAddSplitInfo struct {
	Level    int  `json:"level"`
	IsSanKe  bool `json:"is_sanke"`
	BuyerId  int  `json:"buyer_id"`
	SellerId int  `json:"seller_id"`
}

func (d *DistributionChainNeedAddSplitInfo) NeedAddSplit(level int, buyerId int) bool {
	if buyerId == DefaultReseller || buyerId == d.SellerId {
		return false
	}
	//处于最末级
	if level == -1 || level == 0 {
		return true
	}
	return false
}

// SnakeSorted 散客归整 在业务事实清洗的时候，先批量判断是不是散客，这边只要处理散客归整统一散客ID
func (d *DistributionChainNeedAddSplitInfo) SnakeSorted() (int, int) {
	chainLevel := d.Level
	if d.Level == -1 || d.Level == 0 {
		sanKeTmpId := d.GetSanKeId(d.Is<PERSON>an<PERSON>e)
		if sanKeTmpId > 0 {
			if chainLevel == 0 {
				chainLevel = 1
			} else {
				chainLevel = -3
			}
			//散客归整
			return chainLevel, sanKeTmpId
		}
	}
	return d.Level, 0
}

func (d *DistributionChainNeedAddSplitInfo) GetSanKeId(isSanKe bool) int {
	if isSanKe {
		return DefaultReseller
	}
	return 0
}

func HandleDistributionChainNeedAddSplitInfo(insertData business.OdsProcessor) {
	for _, chain := range insertData.GetChain() {
		handle := &DistributionChainNeedAddSplitInfo{
			Level:    chain.Level,
			IsSanKe:  chain.IsSanKe,
			BuyerId:  chain.BuyerId,
			SellerId: chain.SellerId,
		}
		//散客归整
		chainLevel, sanKeTmpId := handle.SnakeSorted()
		distributorId := chain.BuyerId
		if sanKeTmpId > 0 {
			//散客归整
			distributorId = sanKeTmpId
		}
		//末级补全
		if handle.NeedAddSplit(chainLevel, distributorId) {
			var distributionChainNeedItem business.DataPayloadDistributionChain
			distributionChainNeedItem.IsAddSplit = true
			distributionChainNeedItem.SellerId = chain.BuyerId
			distributionChainNeedItem.BuyerId = chain.BuyerId  //自采买卖都是自己
			distributionChainNeedItem.Level = -3               //非0/-1
			distributionChainNeedItem.PayMode = SalePayModeKey //等于自采
			distributionChainNeedItem.CostPrice = chain.SalePrice
			distributionChainNeedItem.SalePrice = 0
			insertData.AddChain(distributionChainNeedItem)
		}
	}
	return
}
