package handlecommon

import (
	"errors"
	"report-service/internal/domain/enum"
	eventOds "report-service/internal/domain/event/ods"
	szkafka "report-service/internal/global/kafka"
	pkgszkafka "report-service/pkg/szkafka"
	"strconv"
)

func SendCreateEventMessage(eventRecords eventOds.EventProcessor) error {
	topic := eventRecords.GetOdsTopic()
	if topic == "" {
		return errors.New("topic is empty")
	}
	var eventList []pkgszkafka.ProduceMessage
	for i := range eventRecords.GetEventData() {
		//设置消息信息头
		eventRecords.SetEventInfo(i)
		//设置消息类型为新增
		eventRecords.SetEventType(i, enum.OdsBusinessEventTypeCreate)
		//生成消息
		eventList = append(eventList, createProduceMessage(eventRecords, i))
	}
	if len(eventList) > 0 {
		//推送消息事件
		if sendErr := szkafka.SendMulti(topic, eventList...); sendErr != nil {
			return sendErr
		}
	}
	return nil
}

func createProduceMessage(eventRecords eventOds.EventProcessor, index int) pkgszkafka.ProduceMessage {
	operateNo := eventRecords.GetOperateNo(index)
	value := eventRecords.GetEventData()[index]
	return pkgszkafka.ProduceMessage{
		Key:   strconv.Itoa(operateNo),
		Value: value,
	}
}
