package ods

import (
	"context"
	"encoding/json"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	"report-service/internal/domain/logic/ods"
	"report-service/internal/global"
	szkafka "report-service/internal/global/kafka"
	globalNotice "report-service/internal/global/notice"
	pkgszkafka "report-service/pkg/szkafka"
	"report-service/pkg/utils"
	"strconv"
	"strings"

	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"
)

func HandleEventOrderTrack(messages ...kafka.Message) error {
	var messageList []myuuordertrack.EventMyuuOrderTrackParams
	var err error
	for _, message := range messages {
		var messageItem myuuordertrack.EventMyuuOrderTrackParams
		err = json.Unmarshal(message.Value, &messageItem)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("handle event order track is error. err:%s, msg:%+v", err.Error(), messages))
			continue
		}
		messageList = append(messageList, messageItem)
	}
	//特定商户过滤
	messageList, err = merchantFiltering(messageList)
	if err != nil {
		return err
	}
	if messageList == nil || len(messageList) == 0 {
		return nil
	}
	var eventList []pkgszkafka.ProduceMessage
	for _, eventParams := range messageList {
		//前置过滤
		if preEventFilter(eventParams) {
			continue
		}
		//业务操作号
		operateNo := ods.CreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, eventParams.AfterList.Id)
		//分发
		var tmpItem pkgszkafka.ProduceMessage
		topic := ""
		switch eventParams.AfterList.Action {
		case myuuordertrack.EventMyuuOrderTrackActionPay:
			topic = myuuordertrack.TopicEventBusinessDispatchPay
		case myuuordertrack.EventMyuuOrderTrackActionModify:
			topic = myuuordertrack.TopicEventBusinessDispatchCancel
		case myuuordertrack.EventMyuuOrderTrackActionCancel:
			topic = myuuordertrack.TopicEventBusinessDispatchCancel
		case myuuordertrack.EventMyuuOrderTrackActionVerify:
			topic = myuuordertrack.TopicEventBusinessDispatchVerify
		case myuuordertrack.EventMyuuOrderTrackActionRevokeCancel:
			topic = myuuordertrack.TopicEventBusinessDispatchRevoke
		case myuuordertrack.EventMyuuOrderTrackActionRevokeModify:
			topic = myuuordertrack.TopicEventBusinessDispatchRevoke
		case myuuordertrack.EventMyuuOrderTrackActionAfterSale:
			topic = myuuordertrack.TopicEventBusinessDispatchAfterSale
		case myuuordertrack.EventMyuuOrderTrackActionAddTicket:
			topic = myuuordertrack.TopicEventBusinessDispatchAddTicket
		case myuuordertrack.EventMyuuOrderTrackActionFinish:
			topic = myuuordertrack.TopicEventBusinessDispatchFinish
		case myuuordertrack.EventMyuuOrderTrackActionCollect:
			topic = myuuordertrack.TopicEventBusinessDispatchCollect
		case myuuordertrack.EventMyuuOrderTrackActionReprint:
			topic = myuuordertrack.TopicEventBusinessDispatchReprint
		default:
			err = fmt.Errorf("unknown action: %d", eventParams.AfterList.Action)
		}
		if err != nil {
			globalNotice.Error(fmt.Sprintf("order track action handle event error: %s", err.Error()))
			global.LOG.Error("handle event error", zap.Error(err))
			continue
		}
		tmpItem.Topic = &topic
		tmpItem.Key = strconv.Itoa(operateNo)
		tmpItem.Value = myuuordertrack.EventBusinessDispatchItem{
			Data:      eventParams,
			OrderNo:   eventParams.AfterList.Ordernum,
			OperateAt: eventParams.AfterList.InsertTime,
			Type:      enum.OdsBusinessEventTypeCreate,
			OperateNo: operateNo,
		}
		eventList = append(eventList, tmpItem)
	}
	if len(eventList) > 0 {
		//默认加个topic，实际还是走list里面的topic
		sendErr := szkafka.SendMulti(myuuordertrack.TopicEventBusinessDispatchPay, eventList...)
		if sendErr != nil {
			return sendErr
		}
		// 指标上报
		global.OpenTelemetry.OrderTrackCounter.Add(context.Background(), int64(len(eventList)))
	}

	return nil
}

// 前置过滤
func preEventFilter(eventParams myuuordertrack.EventMyuuOrderTrackParams) bool {
	//action是否在允许范围内
	if !actionAllow(eventParams.AfterList.Action) {
		return true
	}
	//event_type过滤不需要处理的时间类型
	if !eventTypeAllow(eventParams.EventType) {
		return true
	}

	return false
}

// 限制操作类型
func actionAllow(action int) bool {
	for _, v := range myuuordertrack.EventMyuuOrderTrackAction {
		if v == action {
			return true
		}
	}
	return false
}

// 限制消息事件
func eventTypeAllow(eventType string) bool {
	if eventType == myuuordertrack.EventAllowEventType {
		return true
	}

	return false
}

// 商户过滤
func merchantFiltering(list []myuuordertrack.EventMyuuOrderTrackParams) ([]myuuordertrack.EventMyuuOrderTrackParams, error) {
	allowIds := GetAllowMerchantIds()
	if len(allowIds) == 0 {
		return list, nil
	}
	var orderNos []string
	for _, item := range list {
		//前置过滤
		if preEventFilter(item) {
			continue
		}
		orderNos = append(orderNos, item.AfterList.Ordernum)
	}
	if len(orderNos) == 0 {
		return nil, nil
	}
	//获取分销链
	distributionChainMap, distributionChainErr := ods.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		//假设获取分销链获取异常，不直接返回，全部放行
		global.LOG.Error(fmt.Sprintf("distribution chain info error. info:%s, orderNos:%s", distributionChainErr.Error(), strings.Join(orderNos, ",")))
		return list, nil
	}
	var res []myuuordertrack.EventMyuuOrderTrackParams
	for _, item := range list {
		//前置过滤
		if preEventFilter(item) {
			continue
		}
		orderNo := item.AfterList.Ordernum
		chain, chainExist := distributionChainMap[orderNo]
		if !chainExist {
			global.LOG.Error(fmt.Sprintf("order is not chain. orderNo:%s", orderNo))
			continue
		}
		var splitIds []int
		for _, info := range chain {
			splitIds = append(splitIds, info.SellerId)
			splitIds = append(splitIds, info.BuyerId)
		}
		//分销链上存在需要被刷的商户，则放行
		for _, splitId := range splitIds {
			if utils.Container(allowIds, splitId) {
				res = append(res, item)
				break
			}
		}
	}

	return res, nil
}
