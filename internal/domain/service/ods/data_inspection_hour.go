package ods

import (
	"errors"
	"fmt"
	"math"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	"report-service/internal/domain/logic/dwm/common"
	logicOds "report-service/internal/domain/logic/ods"
	"report-service/internal/global"
	szkafka "report-service/internal/global/kafka"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/ordertrackqueryservice"
	"report-service/pkg/sdk/api/uussorderservice"
	pkgszkafka "report-service/pkg/szkafka"
	"report-service/pkg/utils"
	"strconv"
	"strings"

	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
)

const (
	LimitNumberRetries = 1000
	NotPayStatus       = 2
)

func CronDataInspectionHour() {
	_ = InspectionHour(
		carbon.Now().SubHours(2).StartOfHour(),
		carbon.Now().SubHours(2).EndOfHour(),
	)
}

func InspectionHour(startTime, endTime carbon.Carbon) error {
	statStartTime := carbon.Now()
	var err error
	numberRetries := 0
	var records []string
	defer func() {
		fmt.Println(startTime, endTime, fmt.Sprintf("巡检结果，总计：%d", numberRetries))
		if r := recover(); r != nil {
			globalNotice.Error(fmt.Sprint("data inspection hour error, panic", r))
			return
		}
		if err != nil {
			globalNotice.Error(fmt.Sprint("data inspection hour error error, ", err.Error()))
			return
		}

		var recordsMsg string
		if len(records) >= 50 {
			recordsMsg = strings.Join(records[:50], ",") + "..."
		} else {
			recordsMsg = strings.Join(records, ",")
		}

		//异常报错
		if numberRetries > LimitNumberRetries {
			globalNotice.Error(fmt.Sprintf("巡检需要修复的数据过多，需要人工介入，异常总计：%d，已修复：%d, 问题操作号：%s", numberRetries, LimitNumberRetries, recordsMsg))
			global.LOG.Info(
				"data inspection hour success",
				zap.String("start_time", startTime.ToDateTimeString()),
				zap.String("end_time", endTime.ToDateTimeString()),
				zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
				zap.Int("all", numberRetries),
				zap.Int("xiufu", LimitNumberRetries),
				zap.String("msg", strings.Join(records, ",")),
			)
			return
		}
		//存在异常通知
		if numberRetries > 0 {
			globalNotice.Warning(fmt.Sprintf("巡检修复数据，已修复：%d, 问题操作号：%s", numberRetries, recordsMsg))
			global.LOG.Info(
				"data inspection hour success",
				zap.String("start_time", startTime.ToDateTimeString()),
				zap.String("end_time", endTime.ToDateTimeString()),
				zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
				zap.Int("xiufu", numberRetries),
				zap.String("msg", strings.Join(records, ",")),
			)
			return
		}

		global.LOG.Info(
			"data inspection hour success",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	actionIn := myuuordertrack.EventMyuuOrderTrackAction
	size := 200
	javaParams := map[string]interface{}{
		"page":          1,
		"size":          size,
		"actionIn":      actionIn,
		"minInsertTime": startTime,
		"maxInsertTime": endTime,
		"sortBy":        []string{"insertTime", "id"},
		"desc":          true,
	}
	var result *ordertrackqueryservice.OrderTrackInfoDataPage
	result, err = ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
	if err != nil {
		return errors.New(fmt.Sprintf("数据巡检，查询order_track.api异常，错误信息：%s", err.Error()))
	}
	if result.Total == 0 {
		return nil
	}
	if result.List == nil || len(result.List) <= 0 {
		return nil
	}
	maxPage := float64(result.Total) / float64(size)
	maxNum := int(math.Ceil(maxPage))
	for page := 1; page <= maxNum; page++ {
		javaParams["page"] = page
		result, err = ordertrackqueryservice.QueryOrderTrackByParamsPage(javaParams)
		if err != nil {
			return errors.New(fmt.Sprintf("数据巡检，查询order_track.api异常，错误信息：%s，错误页数：%d", err.Error(), page))
		}
		if result.Total == 0 {
			break
		}
		if result.List == nil || len(result.List) <= 0 {
			break
		}

		var filterErr error
		//特定商户过滤 上层已经通过商户开始
		result.List, filterErr = merchantFilteringData(result.List)
		if filterErr != nil {
			return errors.New(fmt.Sprintf("数据巡检，特定商户过滤异常，错误信息：%s", filterErr.Error()))
		}

		var trackIds []int
		var cancelOrderNos []string
		for _, item := range result.List {
			trackIds = append(trackIds, item.ID)
			//获取取消的订单
			if item.Action == myuuordertrack.EventMyuuOrderTrackActionCancel {
				cancelOrderNos = append(cancelOrderNos, item.OrderNum)
			}
		}
		if len(trackIds) <= 0 {
			global.LOG.Info(fmt.Sprintf("数据巡检，无可执行的列表数据."))
			continue
		}
		orderNosPayStatusMap := make(map[string]int)
		if len(cancelOrderNos) > 0 {
			orderInfoData, orderInfoErr := uussorderservice.QueryOrderByOrderNos(cancelOrderNos)
			if orderInfoErr != nil {
				return errors.New(fmt.Sprintf("数据巡检，获取订单信息失败.错误：%s", orderInfoErr.Error()))
			}
			for _, orderInfo := range orderInfoData {
				orderNosPayStatusMap[orderInfo.OrderNum] = orderInfo.PayStatus
			}
		}
		//批量获取业务操作编号
		businessNoMap := logicOds.BatchCreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, trackIds)
		businessNoList := make([]int, 0)
		for _, item := range businessNoMap {
			businessNoList = append(businessNoList, item)
		}
		//查询上层是否记录
		diffBusinessNos, diffErr := common.DiffBusinessNosWithExisting(businessNoList)
		if diffErr != nil {
			return errors.New(fmt.Sprintf("数据巡检，查询上层是否记录异常，错误信息：%s", diffErr.Error()))
		}
		//参数转换
		var eventList []pkgszkafka.ProduceMessage
		for _, item := range result.List {
			//业务操作号
			businessNo := logicOds.CreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, item.ID)
			if !utils.Container(diffBusinessNos, businessNo) {
				continue
			}
			//是否是未支付订单，这边订单号不支持历史库查询，历史订单查询不到，默认放行不校验
			if payStatus, orderExist := orderNosPayStatusMap[item.OrderNum]; orderExist && payStatus == NotPayStatus {
				continue
			}
			if numberRetries < LimitNumberRetries {
				records = append(records, fmt.Sprintf("%d_%d", businessNo, item.ID))
				tmp := myuuordertrack.EventMyuuOrderTrackParams{
					AfterList: myuuordertrack.EventMyuuOrderTrackParamsAfterList{
						ApplyDid:       item.ApplyDid,
						Tnum:           item.TNum,
						BranchTerminal: item.BranchTerminal,
						IdCard:         item.IDCard,
						OperMember:     item.OperMember,
						OrdreMonth:     item.OrderMonth,
						Ordernum:       item.OrderNum,
						SalerID:        item.SalerID,
						SyncState:      item.SyncState,
						Source:         item.Source,
						Terminal:       item.Terminal,
						ExtContent:     item.ExtContent,
						Tid:            item.TID,
						InsertTime:     item.InsertTime,
						UpdateTime:     item.UpdateTime,
						Action:         item.Action,
						Id:             item.ID,
						LeftNum:        item.LeftNum,
					},
					BeforeList: []interface{}{},
					EventType:  "INSERT",
					SchemaName: "myuu",
					TableName:  "pft_order_track",
				}

				//分发
				var tmpItem pkgszkafka.ProduceMessage
				topic := ""
				switch tmp.AfterList.Action {
				case myuuordertrack.EventMyuuOrderTrackActionPay:
					topic = myuuordertrack.TopicEventBusinessDispatchPay
				case myuuordertrack.EventMyuuOrderTrackActionModify:
					topic = myuuordertrack.TopicEventBusinessDispatchCancel
				case myuuordertrack.EventMyuuOrderTrackActionCancel:
					topic = myuuordertrack.TopicEventBusinessDispatchCancel
				case myuuordertrack.EventMyuuOrderTrackActionVerify:
					topic = myuuordertrack.TopicEventBusinessDispatchVerify
				case myuuordertrack.EventMyuuOrderTrackActionRevokeCancel:
					topic = myuuordertrack.TopicEventBusinessDispatchRevoke
				case myuuordertrack.EventMyuuOrderTrackActionRevokeModify:
					topic = myuuordertrack.TopicEventBusinessDispatchRevoke
				case myuuordertrack.EventMyuuOrderTrackActionAfterSale:
					topic = myuuordertrack.TopicEventBusinessDispatchAfterSale
				case myuuordertrack.EventMyuuOrderTrackActionAddTicket:
					topic = myuuordertrack.TopicEventBusinessDispatchAddTicket
				case myuuordertrack.EventMyuuOrderTrackActionFinish:
					topic = myuuordertrack.TopicEventBusinessDispatchFinish
				case myuuordertrack.EventMyuuOrderTrackActionCollect:
					topic = myuuordertrack.TopicEventBusinessDispatchCollect
				case myuuordertrack.EventMyuuOrderTrackActionReprint:
					topic = myuuordertrack.TopicEventBusinessDispatchReprint
				default:
					err = fmt.Errorf("unknown action: %d", tmp.AfterList.Action)
				}
				if err != nil {
					globalNotice.Error(fmt.Sprintf("order track action handle event error: %s", err.Error()))
					global.LOG.Error("handle event error", zap.Error(err))
					continue
				}
				tmpItem.Topic = &topic
				tmpItem.Key = strconv.Itoa(businessNo)
				tmpItem.Value = myuuordertrack.EventBusinessDispatchItem{
					Data:      tmp,
					OrderNo:   tmp.AfterList.Ordernum,
					OperateAt: tmp.AfterList.InsertTime,
					Type:      enum.OdsBusinessEventTypeCreate,
					OperateNo: businessNo,
				}
				eventList = append(eventList, tmpItem)
			}
			numberRetries++
		}

		if len(eventList) > 0 {
			//默认加个topic，实际还是走list里面的topic
			sendErr := szkafka.SendMulti(myuuordertrack.TopicEventBusinessDispatchPay, eventList...)
			if sendErr != nil {
				return sendErr
			}
		}
	}

	return nil
}

// 商户过滤
func merchantFilteringData(list []ordertrackqueryservice.OrderTrackInfoData) ([]ordertrackqueryservice.OrderTrackInfoData, error) {
	allowMerchantIds := GetAllowMerchantIds()
	if len(allowMerchantIds) == 0 {
		return list, nil
	}
	var orderNos []string
	for _, item := range list {
		orderNos = append(orderNos, item.OrderNum)
	}
	if len(orderNos) == 0 {
		return nil, nil
	}
	//获取分销链
	distributionChainMap, distributionChainErr := logicOds.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		//假设获取分销链获取异常，不直接返回，全部放行
		disErrMsg := fmt.Sprintf("cron data inspection hour distribution chain info error. info:%s, orderNos:%s", distributionChainErr.Error(), strings.Join(orderNos, ","))
		global.LOG.Error(disErrMsg)
		return nil, errors.New(disErrMsg)
	}
	var res []ordertrackqueryservice.OrderTrackInfoData
	for _, item := range list {
		orderNo := item.OrderNum
		chain, chainExist := distributionChainMap[orderNo]
		if !chainExist {
			global.LOG.Error(fmt.Sprintf("cron data inspection hour order is not chain. orderNo:%s", orderNo))
			continue
		}
		var splitIds []int
		for _, info := range chain {
			splitIds = append(splitIds, info.SellerId)
			splitIds = append(splitIds, info.BuyerId)
		}
		//分销链上存在需要被刷的商户，则放行
		for _, splitId := range splitIds {
			if utils.Container(allowMerchantIds, splitId) {
				res = append(res, item)
				break
			}
		}
	}

	return res, nil
}
