package ods

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	eventOds "report-service/internal/domain/event/ods"
	logicOds "report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/ods/business"
	"report-service/internal/domain/repository"
	repositoryOds "report-service/internal/domain/repository/ods"
	"report-service/internal/domain/service/ods/handlecommon"
	"report-service/internal/global"
	"report-service/pkg/utils"

	"github.com/segmentio/kafka-go"
)

func EventBatchHandleActionAddTicket(messages ...kafka.Message) error {
	messageList := HandleKafkaMessage(messages...)
	var eventParams []myuuordertrack.EventMyuuOrderTrackParams
	for _, item := range messageList {
		//目前item.type只有新增，这里不做特殊处理，默认都是新增
		eventParams = append(eventParams, item.Data)
	}
	return EventOrderTrackBatchHandleActionAddTicket(eventParams, []int{})
}

func EventOrderTrackBatchHandleActionAddTicket(eventParams []myuuordertrack.EventMyuuOrderTrackParams, merchantIds []int) error {
	if len(eventParams) == 0 {
		return nil
	}
	orderNos, trackIds, skuIds := GetEventParamsOfIds(eventParams)
	//批量获取业务操作编号
	businessNoMap := logicOds.BatchCreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, trackIds)
	//批量获取订单号
	orderInfoMap, orderInfoDataErr := logicOds.OrderInfoByOrderNos(orderNos)
	if orderInfoDataErr != nil {
		return orderInfoDataErr
	}
	//存在指定商户业务清洗
	orderInfoMap, orderNos = HandleMerchantFiltering(orderInfoMap, orderNos, merchantIds)
	//不存在订单数据直接返回成功
	if len(orderInfoMap) == 0 {
		return nil
	}
	//分销链信息
	distributionChainMap, distributionChainErr := logicOds.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		return errors.New(fmt.Sprintf("distribution chain info error. info:%s", distributionChainErr))
	}
	//散客判断
	sanKeList, sanKeListErr := HandleLastStageUser(orderInfoMap)
	if sanKeListErr != nil {
		return errors.New(fmt.Sprintf("sanKe list info error. info:%s", sanKeListErr))
	}
	//数据预处理
	var mainOrderNos []string
	for _, params := range eventParams {
		orderInfo, orderInfoExist := orderInfoMap[params.AfterList.Ordernum]
		if !orderInfoExist {
			continue
		}
		if orderInfo.OrderMode == 23 {
			if orderInfo.Addon.PackOrder != "" && orderInfo.Addon.PackOrder != "1" {
				mainOrderNos = append(mainOrderNos, orderInfo.Addon.PackOrder)
			}
		}
	}
	var mainSkuIds []int
	var mainOrderInfoMap = make(map[string]logicOds.OrderInfoData)
	if len(mainOrderNos) > 0 {
		//批量获取订单号
		var mainOrderInfoDataErr error
		mainOrderInfoMap, mainOrderInfoDataErr = logicOds.OrderInfoByOrderNos(mainOrderNos)
		if mainOrderInfoDataErr != nil {
			return errors.New(fmt.Sprintf("main order info error. info:%s", mainOrderInfoDataErr))
		}
		for _, mainOrderInfo := range mainOrderInfoMap {
			mainSkuIds = append(mainSkuIds, mainOrderInfo.SkuId)
		}
	}

	skuIds = utils.MergeSlice(skuIds, mainSkuIds)
	spuDateMap, spuDateErr := logicOds.SpuInfoBySkuIds(skuIds)
	if spuDateErr != nil {
		return errors.New(fmt.Sprintf("spu info error. info:%s", spuDateErr))
	}
	//数据组装
	var odsProcessor []business.OdsProcessor
	for _, params := range eventParams {
		//上层会对orderNos做过滤，这边包含的订单号，才允许走业务清洗逻辑
		if !utils.Container(orderNos, params.AfterList.Ordernum) {
			continue
		}
		trackId, skuId, applyDid, operatorId, count, operateChannel, insertTime, orderNo := GetCommonParams(params)
		businessNo, businessNoExist := businessNoMap[trackId]
		if !businessNoExist {
			global.LOG.Error(fmt.Sprintf("business no is error. track_id:%d", trackId))
			continue
		}
		var insertData business.AddTicketData
		//核心字段
		insertData.PoiId = 0
		insertData.SkuId = skuId
		insertData.ApplyDid = applyDid
		insertData.OperatorId = operatorId
		insertData.Count = count
		insertData.OperateChannel = operateChannel
		insertData.AddTicketNo = businessNo
		insertData.OrderNo = orderNo
		if insertTime.IsZero() {
			global.LOG.Error(fmt.Sprintf("insert time is error. track_id:%d", trackId))
			continue
		}
		insertData.AddedTicketAt = insertTime
		orderInfo, orderInfoExist := orderInfoMap[orderNo]
		if !orderInfoExist {
			global.LOG.Error(fmt.Sprintf("order info is error. order_no:%s", orderNo))
			continue
		}
		//订单未支付
		if !CheckOrderPay(orderInfo.PayStatus, orderNo) {
			continue
		}
		//支付前操作校验,支付前操作不记录
		if beforePay := IsOperationBeforePay(trackId, orderNo); beforePay {
			continue
		}
		//套票信息
		mainSpuId, mainOrderNo := HandlePackMainOrderInfo(orderInfo, mainOrderInfoMap)
		insertData.SaleChannel = orderInfo.OrderMode
		spuInfo, spuExist := spuDateMap[orderInfo.SpuId]
		if !spuExist {
			global.LOG.Error(fmt.Sprintf("sku search spu error. order_no:%s, spu:%d", orderNo, orderInfo.SpuId))
			continue
		}
		tradeNo := GetPayExternalOperateNo(orderInfo.OrderNum, orderInfo.TradeOrderId)
		//交易单号
		insertData.TradeNo = tradeNo
		//操作站点
		insertData.OperateSiteId = params.AfterList.SalerID
		//景区信息字段
		insertData.ProductType = spuInfo.PType
		insertData.SubType = spuInfo.SubType
		insertData.SpuId = spuInfo.Id
		//获取支付售票员id
		sellOperatorInfo, getSellOperatorIdErr := GetSellOperatorInfo(orderNo)
		if getSellOperatorIdErr != nil {
			global.LOG.Error(fmt.Sprintf("order not sell_operator_id is error. order_no:%s", orderNo))
			continue
		}
		insertData.SellOperatorId = sellOperatorInfo.OperMember
		insertData.SellSiteId = sellOperatorInfo.SalerID
		//子票订单标记主票
		mainSpuInfo, mainSpuExist := spuDateMap[mainSpuId]
		if mainSpuExist {
			insertData.Payload.ParentOrderApplyDid = mainSpuInfo.ApplyDid
			insertData.Payload.ParentOrderProductType = mainSpuInfo.PType
		}
		insertData.Payload.IfPack = orderInfo.Addon.IfPack
		insertData.Payload.ParentOrderNo = mainOrderNo
		//订单扩展信息
		var orderExtContent logicOds.OrderFxDetailExtContentFields
		extContentErr := json.Unmarshal([]byte(orderInfo.FxDetails.ExtContent), &orderExtContent)
		if extContentErr != nil {
			global.LOG.Error(fmt.Sprintf("order ext content decode error. order_no:%s", orderNo))
			continue
		}
		//分组标签
		insertData.Payload.GroupsTag = orderExtContent.GetGroupsTagList()
		//分销链信息
		distributionChainData, distributionChainDataExist := distributionChainMap[orderNo]
		if !distributionChainDataExist {
			global.LOG.Error(fmt.Sprintf("distribution chain info not exist error. order_no:%s", orderNo))
			continue
		}
		var payloadDistributionChain []business.DataPayloadDistributionChain
		for _, distributionChainInfo := range distributionChainData {
			var distributionChainItem business.DataPayloadDistributionChain
			distributionChainItem.SellerId = distributionChainInfo.SellerId
			distributionChainItem.BuyerId = distributionChainInfo.BuyerId
			distributionChainItem.Level = distributionChainInfo.Level
			distributionChainItem.PayMode = distributionChainInfo.PMode
			distributionChainItem.SalePrice = distributionChainInfo.SaleMoney
			distributionChainItem.CostPrice = distributionChainInfo.CostMoney
			distributionChainItem.IsSanKe = LastStageUserIsSanKe(sanKeList, distributionChainInfo.Level, distributionChainInfo.BuyerId)
			payloadDistributionChain = append(payloadDistributionChain, distributionChainItem)
		}
		insertData.Payload.DistributionChain = payloadDistributionChain
		//散客末级补全
		var odsProcessorChain business.OdsProcessor = &insertData
		handlecommon.HandleDistributionChainNeedAddSplitInfo(odsProcessorChain)
		//添加数据
		odsProcessor = append(odsProcessor, &insertData)
	}
	if len(odsProcessor) <= 0 {
		return nil
	}
	//链路操作信息处理
	var handleInsertDataListErr error
	odsProcessor, handleInsertDataListErr = handlecommon.HandlePayDistributionChainOperateInfo(odsProcessor)
	if handleInsertDataListErr != nil {
		//记录异常的编号
		HandleInsertDataListErrToLog(trackIds, handleInsertDataListErr.Error())
	}
	//数据写入
	var records []repositoryOds.BusinessAddTicketModelData
	err := utils.JsonConvertor(odsProcessor, &records)
	if err != nil {
		return errors.New(fmt.Sprintf("ods processor error. info:%s", err))
	}
	insertErr := repository.OdsBusinessAddTicketRepository.InsertMore(records)
	if insertErr != nil {
		//记录异常的编号
		InsertErrToLog(trackIds, insertErr.Error())
		return nil
	}
	//推送消息
	var eventBusinessList eventOds.EventBusinessAddTicketList
	for _, record := range records {
		var eventBusinessItem eventOds.EventBusinessAddTicketData
		eventRecordsSwitchErr := utils.JsonConvertor(record, &eventBusinessItem)
		if eventRecordsSwitchErr != nil {
			EventRecordsSwitchErr([]int{logicOds.GetBusinessOperationNumberId(record.AddTicketNo)})
			return errors.New(fmt.Sprintf("event records switch error. info:%s", eventRecordsSwitchErr))
		}
		//时间格式转换下
		eventBusinessItem.AddedTicketAt = record.AddedTicketAt.Format(enum.TimestampLayoutMake)
		eventBusinessList.EventData = append(eventBusinessList.EventData, eventOds.EventBusinessAddTicket{
			Data: eventBusinessItem,
		})
	}
	//统一推送处理
	var odsEventProcessor eventOds.EventProcessor = &eventBusinessList
	//推送消息
	sendErr := handlecommon.SendCreateEventMessage(odsEventProcessor)
	if sendErr != nil {
		return sendErr
	}
	return nil
}
