package ods

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	"report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/permission/accesswhitelist"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/utils"
	"strconv"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
)

const (
	DefaultReseller = 112 //默认散客
)

const CacheKeyPrefix = "business_pay_operator:"

// 优惠序号解析
func SerialNumberParseRange(serialNumber string) []int {
	parts := strings.Split(serialNumber, ",")
	var serialNumberList []int
	for _, item := range parts {
		partsItem := strings.Split(item, "-")
		start, err := strconv.Atoi(partsItem[0])
		if err != nil {
			continue
		}
		if len(partsItem) != 2 {
			serialNumberList = append(serialNumberList, start)
			continue
		}
		end, err := strconv.Atoi(partsItem[1])
		if err != nil {
			continue
		}
		if start > end {
			continue
		}
		for i := start; i <= end; i++ {
			serialNumberList = append(serialNumberList, i)
		}
	}

	return serialNumberList
}

func CheckOrderExist(orderInfo interface{}, orderNo string) bool {
	if orderInfo == nil {
		global.LOG.Error(fmt.Sprintf("没找到订单信息，单号：%s", orderNo))

		return false
	}

	return true
}

func CheckOrderPay(payStatus int, orderNo string) bool {
	if payStatus == 2 {
		global.LOG.Error(fmt.Sprintf("订单未支付，单号：%s", orderNo))
		return false
	}

	return true
}

// 验证有效性是过滤掉一些无用的数据，不需要返回错误或者异常，可以记录日志
func CheckOrderValid(orderInfo *ods.OrderInfoData, orderNo string) bool {
	//订单是否存在
	if !CheckOrderExist(orderInfo, orderNo) {
		return false
	}
	//订单是否支付
	if !CheckOrderPay(orderInfo.PayStatus, orderNo) {
		return false
	}

	return true
}

// 获取下单人id
func GetSellOperatorInfo(orderNo string) (*ods.OperMemberInfo, error) {
	trackInfoCache, sellOperatorErr := global.REDIS.Get(context.Background(), CacheKeyPrefix+orderNo).Result()
	if sellOperatorErr == nil {
		var trackInfo ods.OperMemberInfo
		err := json.Unmarshal([]byte(trackInfoCache), &trackInfo)
		if err == nil {
			return &trackInfo, nil
		}
	}
	trackInfo, err := ods.QueryTrackInfoByOrderNoAndAction(orderNo, myuuordertrack.EventMyuuOrderTrackActionPay)
	if err != nil {
		return nil, err
	}
	info := ods.OperMemberInfo{
		OperMember: trackInfo.OperMember,
		InsertTime: trackInfo.InsertTime,
		SalerID:    trackInfo.SalerID,
		Id:         trackInfo.ID,
	}

	jsonData, jsonErr := json.Marshal(info)
	if jsonErr != nil {
		return nil, err
	}
	_ = global.REDIS.Set(context.Background(), CacheKeyPrefix+orderNo, jsonData, 1800*time.Second).Err()

	return &info, nil
}

// 判断支付前操作
func IsOperationBeforePay(trackId int, orderNo string) bool {
	SellOperatorInfo, err := GetSellOperatorInfo(orderNo)
	if err != nil {
		//异常不处理，直接返回true
		global.LOG.Error(fmt.Sprintf("获取订单支付记录信息异常，单号：%s", orderNo))
		return true
	}
	if SellOperatorInfo.IsOperationBeforePay(trackId) {
		global.LOG.Error(fmt.Sprintf("操作在订单支付前不记录，单号：%s", orderNo))
		return true
	}
	return false
}

// 获取手续费
func GetOrderCancelInfo(trackIds []string) (map[string][]platformrpcapi.ServiceInfoItem, error) {
	data, err := platformrpcapi.InBatchesQueryOrderCancelInfoList(trackIds)
	if err != nil {
		return nil, err
	}

	var OrderCancelInfoMap = make(map[string][]platformrpcapi.ServiceInfoItem)
	for _, info := range data {
		var itemList []platformrpcapi.ServiceInfoItem
		err = json.Unmarshal([]byte(info.ServiceInfo), &itemList)
		if err != nil {
			return nil, errors.New("order cancel info decode error")
		}

		OrderCancelInfoMap[info.TrackId] = itemList
	}

	return OrderCancelInfoMap, nil
}

// 获取批量参数信息
func GetEventParamsOfIds(eventParams []myuuordertrack.EventMyuuOrderTrackParams) (orderNos []string, trackIds []int, skuIds []int) {
	for _, itemParams := range eventParams {
		orderNos = append(orderNos, itemParams.AfterList.Ordernum)
		trackIds = append(trackIds, itemParams.AfterList.Id)
		skuIds = append(skuIds, itemParams.AfterList.Tid)
	}

	orderNos = utils.RemoveDuplicate(orderNos)
	skuIds = utils.RemoveDuplicate(skuIds)
	return
}

func GetCommonParams(params myuuordertrack.EventMyuuOrderTrackParams) (trackId int, skuId int, applyDid int, operatorId int, count int, operateChannel int, insertTime time.Time, orderNo string) {
	trackId = params.AfterList.Id
	skuId = params.AfterList.Tid
	applyDid = params.AfterList.ApplyDid
	operatorId = params.AfterList.OperMember
	count = params.AfterList.Tnum
	operateChannel = params.AfterList.Source
	orderNo = params.AfterList.Ordernum
	operateTime, _ := time.Parse(enum.TimestampLayoutMake, params.AfterList.InsertTime)
	insertTime = operateTime
	return
}

func HandlePackMainOrderInfo(orderInfo ods.OrderInfoData, mainOrderInfoMap map[string]ods.OrderInfoData) (int, string) {
	//套票信息
	var mainSpuId = 0    //主票ID
	var mainOrderNo = "" //主票订单号
	if orderInfo.OrderMode == 23 {
		if orderInfo.Addon.PackOrder != "" && orderInfo.Addon.PackOrder != "1" {
			mainOrderNo = orderInfo.Addon.PackOrder
			mainOrderInfo, mainOrderInfoExist := mainOrderInfoMap[mainOrderNo]
			if !mainOrderInfoExist {
				return mainSpuId, mainOrderNo
			}

			mainSpuId = mainOrderInfo.SpuId
		}
	}
	return mainSpuId, mainOrderNo
}

func HandleMerchantFiltering(orderInfoMap map[string]ods.OrderInfoData, orderNos []string, merchantIds []int) (map[string]ods.OrderInfoData, []string) {
	if len(orderInfoMap) == 0 || len(merchantIds) == 0 {
		return orderInfoMap, orderNos
	}
	var orderInfoMapNew = make(map[string]ods.OrderInfoData)
	var orderNosNew []string
	for orderNo, orderInfo := range orderInfoMap {
		//分销链路解析
		var splitAids = ""
		if orderInfo.FxDetails.Aids != "" {
			splitAids = orderInfo.FxDetails.Aids + "," + strconv.Itoa(orderInfo.Member)
		} else if orderInfo.Aid != orderInfo.Member {
			splitAids = strconv.Itoa(orderInfo.Aid) + "," + strconv.Itoa(orderInfo.Member)
		} else {
			splitAids = strconv.Itoa(orderInfo.Aid) + "," + strconv.Itoa(orderInfo.Aid)
		}
		strSlice := strings.Split(splitAids, ",")
		var intSlice []int
		for _, s := range strSlice {
			// 将字符串转换为整数
			num, err := strconv.Atoi(s)
			if err != nil {
				global.LOG.Error(fmt.Sprintf("HandleMerchantFiltering Error converting string to int:%s", err))
				continue
			}
			// 将转换后的整数存储到整数切片中
			intSlice = append(intSlice, num)
		}
		if len(intSlice) == 0 {
			continue
		}
		//分销链上存在需要被刷的商户，则放行
		for _, splitId := range intSlice {
			if utils.Container(merchantIds, splitId) {
				orderInfoMapNew[orderNo] = orderInfo
				orderNosNew = append(orderNosNew, orderNo)
			}
		}
	}

	return orderInfoMapNew, orderNosNew
}

// 优惠劵优惠金额计算
func CalculateOrderCouponPrice(couponPrice int, originCount int, count int, lastOne bool) (int, error) {
	if originCount == 0 || count == 0 {
		return 0, errors.New("优惠计算参数错误")
	}

	//计算本次数量的优惠金额
	if couponPrice == 0 {
		return 0, nil
	}

	var cancelCouponPrice = 0
	surplusEMoney := couponPrice % originCount
	baseDiscountPerTicket := (couponPrice - surplusEMoney) / originCount
	//非最后一张，无需优惠金额余数修正
	if !lastOne {
		surplusEMoney = 0
	}
	cancelCouponPrice = baseDiscountPerTicket*count + surplusEMoney

	return cancelCouponPrice, nil
}

// 营销优惠处理
func OrderDiscountInfoByOrderNoAndSerialNums(orderDiscountInfo []ods.OrderDiscountInfoData, serialNums []int, status int) []ods.OrderDiscountInfoData {
	var orderDiscountInfoData []ods.OrderDiscountInfoData
	for _, discountItem := range orderDiscountInfo {
		if len(serialNums) > 0 {
			if utils.Container(serialNums, discountItem.SerialNum) {
				if status == 0 || status == discountItem.Status {
					orderDiscountInfoData = append(orderDiscountInfoData, discountItem)
				}
			}
		} else {
			if status == 0 || status == discountItem.Status {
				orderDiscountInfoData = append(orderDiscountInfoData, discountItem)
			}
		}
	}
	return orderDiscountInfoData
}

// 末级散客判断
func HandleLastStageUser(orderInfoMap map[string]ods.OrderInfoData) ([]int, error) {
	var userIds []int
	for _, orderInfo := range orderInfoMap {
		if utils.Container([]int{11, 15, 18, 43}, orderInfo.OrderMode) {
			userIds = append(userIds, orderInfo.Member)
		}
	}
	userIds = utils.RemoveDuplicate(userIds)
	memberList, err := pftmember.InBatchesSearchMember(userIds)
	if err != nil {
		return nil, err
	}
	var sanKeList []int
	for _, orderInfo := range orderInfoMap {
		saleChannel := orderInfo.OrderMode
		memberId := orderInfo.Member
		for _, member := range memberList {
			if member.Id != memberId {
				continue
			}
			if member.Account == member.Mobile {
				sanKeList = append(sanKeList, memberId)
				break
			}
			if strings.Index(member.Account, "YKT") != -1 {
				sanKeList = append(sanKeList, memberId)
				break
			}
			if saleChannel != 15 && member.Dtype == 5 {
				sanKeList = append(sanKeList, memberId)
				break
			}
		}
	}

	//组合判断
	var sanKeListReal []int
	for _, orderInfo := range orderInfoMap {
		saleChannel := orderInfo.OrderMode
		memberId := orderInfo.Member
		isSanKe := false
		if utils.Container([]int{13, 56, 64}, saleChannel) {
			isSanKe = true
		}
		if memberId == DefaultReseller {
			isSanKe = true
		}
		if saleChannel == 15 && utils.Container(sanKeList, memberId) && memberId != DefaultReseller {
			isSanKe = true
		}
		if utils.Container([]int{11, 43, 18}, saleChannel) && utils.Container(sanKeList, memberId) {
			isSanKe = true
		}

		if isSanKe {
			sanKeListReal = append(sanKeListReal, memberId)
		}
	}

	return sanKeListReal, nil
}

func LastStageUserIsSanKe(sanKeList []int, level int, buyId int) bool {
	if (level == -1 || level == 0) && utils.Container(sanKeList, buyId) {
		return true
	}
	return false
}

func HandleKafkaMessage(messages ...kafka.Message) []myuuordertrack.EventBusinessDispatchItem {
	var messageList []myuuordertrack.EventBusinessDispatchItem
	var err error
	for _, message := range messages {
		var messageItem myuuordertrack.EventBusinessDispatchItem
		err = json.Unmarshal(message.Value, &messageItem)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("handle event action is error. err:%s, msg:%+v", err.Error(), messages))
			continue
		}
		messageList = append(messageList, messageItem)
	}
	return messageList
}

// GetAllowMerchantIds 针对不同环境简单做下区分
func GetAllowMerchantIds() []int {
	merchantIds, _ := accesswhitelist.GetAllWhiteListMerchants()
	if len(merchantIds) > 0 {
		return merchantIds
	}
	return global.CONFIG.System.MerchantWhiteList
}

// InsertErrToLog 写入失败记录失败，记录下异常编号
func InsertErrToLog(trackIds []int, msg string) {
	//记录异常的编号
	var trackIdsStr []string
	for _, id := range trackIds {
		trackIdsStr = append(trackIdsStr, strconv.Itoa(id))
	}
	global.LOG.Error(fmt.Sprintf("业务数据写入失败 记录ids：%s, 错误：%s", strings.Join(trackIdsStr, ","), msg))
	return
}

// HandleInsertDataListErrToLog 链路处理操作信息失败，记录下异常编号
func HandleInsertDataListErrToLog(trackIds []int, msg string) {
	//记录异常的编号
	var trackIdsStr []string
	for _, id := range trackIds {
		trackIdsStr = append(trackIdsStr, strconv.Itoa(id))
	}
	global.LOG.Error(fmt.Sprintf("链路操作信息处理 记录ids：%s， 错误：%s", strings.Join(trackIdsStr, ","), msg))
}

func EventRecordsSwitchErr(trackIds []int) {
	//记录异常的编号
	var trackIdsStr []string
	for _, id := range trackIds {
		trackIdsStr = append(trackIdsStr, strconv.Itoa(id))
	}
	global.LOG.Error(fmt.Sprintf("事件推送数据转换错误 记录ids：%s", strings.Join(trackIdsStr, ",")))
}

func GetPayExternalOperateNo(orderNo string, tradeOrderId string) string {
	var externalOperateNo string
	externalOperateNo = orderNo
	if tradeOrderId != "" {
		externalOperateNo = tradeOrderId
	}
	return externalOperateNo
}
