package ods

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	eventOds "report-service/internal/domain/event/ods"
	logicOds "report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/ods/business"
	"report-service/internal/domain/repository"
	repositoryOds "report-service/internal/domain/repository/ods"
	"report-service/internal/domain/service/ods/handlecommon"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/utils"
	"strconv"
	"strings"

	"github.com/segmentio/kafka-go"
)

func EventBatchHandleActionCancel(messages ...kafka.Message) error {
	messageList := HandleKafkaMessage(messages...)
	var eventParams []myuuordertrack.EventMyuuOrderTrackParams
	for _, item := range messageList {
		//目前item.type只有新增，这里不做特殊处理，默认都是新增
		eventParams = append(eventParams, item.Data)
	}
	return EventOrderTrackBatchHandleActionCancel(eventParams, []int{})
}

func EventOrderTrackBatchHandleActionCancel(eventParams []myuuordertrack.EventMyuuOrderTrackParams, merchantIds []int) error {
	if len(eventParams) == 0 {
		return nil
	}
	orderNos, trackIds, skuIds := GetEventParamsOfIds(eventParams)
	//批量获取业务操作编号
	businessNoMap := logicOds.BatchCreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, trackIds)
	//批量获取订单号
	orderInfoMap, orderInfoDataErr := logicOds.OrderInfoByOrderNos(orderNos)
	if orderInfoDataErr != nil {
		return orderInfoDataErr
	}
	//存在指定商户业务清洗
	orderInfoMap, orderNos = HandleMerchantFiltering(orderInfoMap, orderNos, merchantIds)
	//不存在订单数据直接返回成功
	if len(orderInfoMap) == 0 {
		return nil
	}
	//分销链信息
	distributionChainMap, distributionChainErr := logicOds.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		return errors.New(fmt.Sprintf("distribution chain info error. info:%s", distributionChainErr))
	}
	//散客判断
	sanKeList, sanKeListErr := HandleLastStageUser(orderInfoMap)
	if sanKeListErr != nil {
		return errors.New(fmt.Sprintf("sanKe list info error. info:%s", sanKeListErr))
	}
	//优惠信息 新版+旧版优惠券
	orderCouponRes, orderCouponErr := logicOds.OrderCouponPriceByOrderNos(orderNos)
	if orderCouponErr != nil {
		return errors.New(fmt.Sprintf("order coupon is error. info:%s", orderCouponErr))
	}
	//取消手续费
	trackStrIds := make([]string, len(trackIds))
	for _, trackId := range trackIds {
		trackStrIds = append(trackStrIds, strconv.Itoa(trackId))
	}
	orderCancelInfoMap, orderCancelInfoErr := GetOrderCancelInfo(trackStrIds)
	if orderCancelInfoErr != nil {
		global.LOG.Debug(fmt.Sprintf("手续费获取失败 操作ID：%s", strings.Join(trackStrIds, ",")))
	}
	//数据预处理
	var mainOrderNos []string
	var discountOrderNos []string
	for _, params := range eventParams {
		orderInfo, orderInfoExist := orderInfoMap[params.AfterList.Ordernum]
		if !orderInfoExist {
			continue
		}
		if orderInfo.OrderMode == 23 {
			if orderInfo.Addon.PackOrder != "" && orderInfo.Addon.PackOrder != "1" {
				mainOrderNos = append(mainOrderNos, orderInfo.Addon.PackOrder)
			}
		}
		var orderExtContent logicOds.OrderFxDetailExtContentFields
		extContentErr := json.Unmarshal([]byte(orderInfo.FxDetails.ExtContent), &orderExtContent)
		if extContentErr != nil {
			return errors.New(fmt.Sprintf("order ext content decode error. order_no:%s", params.AfterList.Ordernum))
		}
		//是否使用了营销优惠
		if logicOds.CheckOrderUseDiscount(orderExtContent) {
			discountOrderNos = append(discountOrderNos, params.AfterList.Ordernum)
		}
	}
	var mainSkuIds []int
	var mainOrderInfoMap = make(map[string]logicOds.OrderInfoData)
	if len(mainOrderNos) > 0 {
		//批量获取订单号
		var mainOrderInfoDataErr error
		mainOrderInfoMap, mainOrderInfoDataErr = logicOds.OrderInfoByOrderNos(mainOrderNos)
		if mainOrderInfoDataErr != nil {
			return errors.New(fmt.Sprintf("main order info error. info:%s", mainOrderInfoDataErr))
		}
		for _, mainOrderInfo := range mainOrderInfoMap {
			mainSkuIds = append(mainSkuIds, mainOrderInfo.SkuId)
		}
	}
	var orderDiscountInfoMap = make(map[string][]logicOds.OrderDiscountInfoData)
	if len(discountOrderNos) > 0 {
		var orderDiscountInfoErr error
		orderDiscountInfoMap, orderDiscountInfoErr = logicOds.OrderDiscountInfoByOrderNos(discountOrderNos)
		if orderDiscountInfoErr != nil {
			return errors.New(fmt.Sprintf("order discount info query error. info:%s", orderDiscountInfoErr))
		}
	}

	skuIds = utils.MergeSlice(skuIds, mainSkuIds)
	spuDateMap, spuDateErr := logicOds.SpuInfoBySkuIds(skuIds)
	if spuDateErr != nil {
		return errors.New(fmt.Sprintf("spu info error. info:%s", spuDateErr))
	}
	//数据组装
	var odsProcessor []business.OdsProcessor
	for _, params := range eventParams {
		//上层会对orderNos做过滤，这边包含的订单号，才允许走业务清洗逻辑
		if !utils.Container(orderNos, params.AfterList.Ordernum) {
			continue
		}
		trackId, skuId, applyDid, operatorId, count, operateChannel, insertTime, orderNo := GetCommonParams(params)
		businessNo, businessNoExist := businessNoMap[trackId]
		if !businessNoExist {
			global.LOG.Error(fmt.Sprintf("business no is error. track_id:%d", trackId))
			continue
		}
		var insertData business.CancelData
		//核心字段
		insertData.PoiId = 0
		insertData.SkuId = skuId
		insertData.ApplyDid = applyDid
		insertData.OperatorId = operatorId
		insertData.Count = count
		insertData.OperateChannel = operateChannel
		insertData.CancelNo = businessNo
		insertData.OrderNo = orderNo
		if insertTime.IsZero() {
			global.LOG.Error(fmt.Sprintf("insert time is error. track_id:%d", trackId))
			continue
		}
		insertData.CancelledAt = insertTime
		orderInfo, orderInfoExist := orderInfoMap[orderNo]
		if !orderInfoExist {
			global.LOG.Error(fmt.Sprintf("order info is error. order_no:%s", orderNo))
			continue
		}
		//订单未支付
		if !CheckOrderPay(orderInfo.PayStatus, orderNo) {
			continue
		}
		//支付前操作校验,支付前操作不记录
		if beforePay := IsOperationBeforePay(trackId, orderNo); beforePay {
			continue
		}
		//套票信息
		mainSpuId, mainOrderNo := HandlePackMainOrderInfo(orderInfo, mainOrderInfoMap)
		insertData.SaleChannel = orderInfo.OrderMode
		spuInfo, spuExist := spuDateMap[orderInfo.SpuId]
		if !spuExist {
			global.LOG.Error(fmt.Sprintf("sku search spu error. order_no:%s, spu:%d", orderNo, orderInfo.SpuId))
			continue
		}
		tradeNo := GetPayExternalOperateNo(orderInfo.OrderNum, orderInfo.TradeOrderId)
		//交易单号
		insertData.TradeNo = tradeNo
		//操作站点
		insertData.OperateSiteId = params.AfterList.SalerID
		//景区信息字段
		insertData.ProductType = spuInfo.PType
		insertData.SubType = spuInfo.SubType
		insertData.SpuId = spuInfo.Id
		//获取支付售票员id
		sellOperatorInfo, getSellOperatorIdErr := GetSellOperatorInfo(orderNo)
		if getSellOperatorIdErr != nil {
			global.LOG.Error(fmt.Sprintf("order not sell_operator_id is error. order_no:%s", orderNo))
			continue
		}
		insertData.SellOperatorId = sellOperatorInfo.OperMember
		insertData.SellSiteId = sellOperatorInfo.SalerID
		//子票订单标记主票
		mainSpuInfo, mainSpuExist := spuDateMap[mainSpuId]
		if mainSpuExist {
			insertData.Payload.ParentOrderApplyDid = mainSpuInfo.ApplyDid
			insertData.Payload.ParentOrderProductType = mainSpuInfo.PType
		}
		insertData.Payload.IfPack = orderInfo.Addon.IfPack
		insertData.Payload.ParentOrderNo = mainOrderNo

		var orderTrackExtContent myuuordertrack.EventMyuuOrderTrackExtContent
		orderTrackExtContentErr := json.Unmarshal([]byte(params.AfterList.ExtContent), &orderTrackExtContent)
		if orderTrackExtContentErr != nil {
			global.LOG.Debug(fmt.Sprintf("order track extContent is error orderNo: %s", orderNo))
		}

		var orderExtContent logicOds.OrderFxDetailExtContentFields
		extContentErr := json.Unmarshal([]byte(orderInfo.FxDetails.ExtContent), &orderExtContent)
		if extContentErr != nil {
			global.LOG.Error(fmt.Sprintf("order ext content decode error. order_no:%s", orderNo))
			continue
		}
		//是否使用了营销优惠
		orderDiscountInfo := make(map[int]map[int][]logicOds.OrderDiscountInfoData)
		if logicOds.CheckOrderUseDiscount(orderExtContent) && orderTrackExtContent.SerialNumber != "" {
			//营销优惠序号
			var serialNumberStr = ""
			serialNumberStr = orderTrackExtContent.SerialNumber
			serialNumberList := SerialNumberParseRange(serialNumberStr)
			orderDiscountInfoData, orderDiscountInfoDataExist := orderDiscountInfoMap[orderNo]
			if !orderDiscountInfoDataExist {
				global.LOG.Error(fmt.Sprintf("order discount info not exist error. order_no:%s", orderNo))
				continue
			}
			orderDiscountInfoData = OrderDiscountInfoByOrderNoAndSerialNums(orderDiscountInfoData, serialNumberList, logicOds.OrderDiscountStatusAll)
			orderDiscountInfo[orderInfo.Aid] = make(map[int][]logicOds.OrderDiscountInfoData)
			orderDiscountInfo[orderInfo.Aid][orderInfo.Member] = orderDiscountInfoData
		}

		var orderCancelInfoList []platformrpcapi.ServiceInfoItem
		orderCancelInfo, orderCancelInfoExist := orderCancelInfoMap[strconv.Itoa(trackId)]
		if orderCancelInfoExist {
			orderCancelInfoList = orderCancelInfo
		}
		//分组标签
		insertData.Payload.GroupsTag = orderExtContent.GetGroupsTagList()
		//分销链信息
		distributionChainData, distributionChainDataExist := distributionChainMap[orderNo]
		if !distributionChainDataExist {
			global.LOG.Error(fmt.Sprintf("distribution chain info not exist error. order_no:%s", orderNo))
			continue
		}
		var payloadDistributionChain []business.CancelDataPayloadDistributionChain
		shouldBreak := false
		for _, distributionChainInfo := range distributionChainData {
			var distributionChainItem business.CancelDataPayloadDistributionChain
			distributionChainItem.SellerId = distributionChainInfo.SellerId
			distributionChainItem.BuyerId = distributionChainInfo.BuyerId
			distributionChainItem.Level = distributionChainInfo.Level
			distributionChainItem.PayMode = distributionChainInfo.PMode
			distributionChainItem.SalePrice = distributionChainInfo.SaleMoney
			distributionChainItem.CostPrice = distributionChainInfo.CostMoney
			distributionChainItem.IsSanKe = LastStageUserIsSanKe(sanKeList, distributionChainInfo.Level, distributionChainInfo.BuyerId)
			distributionChainItem.Fee = 0
			for _, feeItem := range orderCancelInfoList {
				if feeItem.Aid == distributionChainInfo.SellerId && feeItem.Fid == distributionChainInfo.BuyerId {
					distributionChainItem.Fee = feeItem.Money
				}
			}
			orderCouponPrice, orderCouponExist := orderCouponRes[orderNo][distributionChainInfo.SellerId][distributionChainInfo.BuyerId]
			if orderCouponExist {
				//修改和取消都会进来，由于修改在某些场景下也有最后一张的操作，这边直接判断剩余数量为0时就是最后一张
				lastOne := params.AfterList.LeftNum == 0
				orderCouponPriceReal, orderCouponPriceErr := CalculateOrderCouponPrice(orderCouponPrice, orderInfo.ApplyInfo.OriginNum, count, lastOne)
				if orderCouponPriceErr != nil {
					global.LOG.Error(fmt.Sprintf("order coupon is error. info:%s order_no:%s sellerId:%d", orderCouponPriceErr, orderNo, distributionChainInfo.SellerId))
					shouldBreak = true
					break
				}
				distributionChainItem.CouponPrice = orderCouponPriceReal
			}
			orderDiscountData, orderDiscountExit := orderDiscountInfo[distributionChainInfo.SellerId][distributionChainInfo.BuyerId]
			var discountPrice = 0
			var discountDetail []business.DataPayloadDistributionChainDiscountDetail
			if orderDiscountExit {
				for _, discountItem := range orderDiscountData {
					discountPrice += discountItem.CouponPrice
					discountDetail = append(discountDetail, business.DataPayloadDistributionChainDiscountDetail{
						Key:   discountItem.SerialNum,
						Type:  discountItem.CouponType,
						Price: discountItem.CouponPrice,
					})
				}
			}
			distributionChainItem.DiscountPrice = discountPrice
			distributionChainItem.DiscountDetail = discountDetail
			payloadDistributionChain = append(payloadDistributionChain, distributionChainItem)
		}
		if shouldBreak {
			continue
		}
		insertData.Payload.DistributionChain = payloadDistributionChain
		//散客末级补全
		var odsProcessorChain business.OdsProcessor = &insertData
		handlecommon.HandleDistributionChainNeedAddSplitInfo(odsProcessorChain)
		//添加数据
		odsProcessor = append(odsProcessor, &insertData)
	}
	if len(odsProcessor) <= 0 {
		return nil
	}
	//链路操作信息处理
	var handleInsertDataListErr error
	odsProcessor, handleInsertDataListErr = handlecommon.HandlePayDistributionChainOperateInfo(odsProcessor)
	if handleInsertDataListErr != nil {
		//记录异常的编号
		HandleInsertDataListErrToLog(trackIds, handleInsertDataListErr.Error())
	}
	//数据写入
	var records []repositoryOds.BusinessCancelModelData
	err := utils.JsonConvertor(odsProcessor, &records)
	if err != nil {
		return errors.New(fmt.Sprintf("ods processor error. info:%s", err))
	}
	insertErr := repository.OdsBusinessCancelRepository.InsertMore(records)
	if insertErr != nil {
		//记录异常的编号
		InsertErrToLog(trackIds, insertErr.Error())
		return nil
	}
	//推送消息
	var eventBusinessList eventOds.EventBusinessCancelList
	for _, record := range records {
		var eventBusinessItem eventOds.EventBusinessCancelData
		eventRecordsSwitchErr := utils.JsonConvertor(record, &eventBusinessItem)
		if eventRecordsSwitchErr != nil {
			EventRecordsSwitchErr([]int{logicOds.GetBusinessOperationNumberId(record.CancelNo)})
			return errors.New(fmt.Sprintf("event records switch error. info:%s", eventRecordsSwitchErr))
		}
		//时间格式转换下
		eventBusinessItem.CancelledAt = record.CancelledAt.Format(enum.TimestampLayoutMake)
		eventBusinessList.EventData = append(eventBusinessList.EventData, eventOds.EventBusinessCancel{
			Data: eventBusinessItem,
		})
	}
	//统一推送处理
	var odsEventProcessor eventOds.EventProcessor = &eventBusinessList
	//推送消息
	sendErr := handlecommon.SendCreateEventMessage(odsEventProcessor)
	if sendErr != nil {
		return sendErr
	}
	return nil
}
