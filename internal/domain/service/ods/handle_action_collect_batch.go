package ods

import (
	"encoding/json"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/myuuordertrack"
	eventOds "report-service/internal/domain/event/ods"
	logicOds "report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/ods/business"
	"report-service/internal/domain/repository"
	repositoryOds "report-service/internal/domain/repository/ods"
	"report-service/internal/domain/service/ods/handlecommon"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"strconv"

	"github.com/segmentio/kafka-go"
)

func EventBatchHandleActionCollect(messages ...kafka.Message) error {
	messageList := HandleKafkaMessage(messages...)
	var eventParams []myuuordertrack.EventMyuuOrderTrackParams
	for _, item := range messageList {
		//目前item.type只有新增，这里不做特殊处理，默认都是新增
		eventParams = append(eventParams, item.Data)
	}
	return EventOrderTrackBatchHandleActionCollect(eventParams, []int{})
}

func EventOrderTrackBatchHandleActionCollect(eventParams []myuuordertrack.EventMyuuOrderTrackParams, merchantIds []int) error {
	if len(eventParams) == 0 {
		return nil
	}
	orderNos, trackIds, skuIds := GetEventParamsOfIds(eventParams)
	//批量获取业务操作编号
	businessNoMap := logicOds.BatchCreateBusinessOperationNumber(enum.BusinessOperationNumberTypeOrderTrack, trackIds)
	//批量获取订单号
	orderInfoMap, orderInfoDataErr := logicOds.OrderInfoByOrderNos(orderNos)
	if orderInfoDataErr != nil {
		return orderInfoDataErr
	}
	//存在指定商户业务清洗
	orderInfoMap, orderNos = HandleMerchantFiltering(orderInfoMap, orderNos, merchantIds)
	//不存在订单数据直接返回成功
	if len(orderInfoMap) == 0 {
		return nil
	}
	//分销链信息
	distributionChainMap, distributionChainErr := logicOds.OrderDistributionChainByOrderNos(orderNos)
	if distributionChainErr != nil {
		return fmt.Errorf("distribution chain info error. info:%s", distributionChainErr)
	}
	//散客判断
	sanKeList, sanKeListErr := HandleLastStageUser(orderInfoMap)
	if sanKeListErr != nil {
		return fmt.Errorf("sanKe list info error. info:%s", sanKeListErr)
	}
	//数据预处理
	var mainOrderNos []string
	for _, params := range eventParams {
		orderInfo, orderInfoExist := orderInfoMap[params.AfterList.Ordernum]
		if !orderInfoExist {
			continue
		}
		if orderInfo.OrderMode == 23 {
			if orderInfo.Addon.PackOrder != "" && orderInfo.Addon.PackOrder != "1" {
				mainOrderNos = append(mainOrderNos, orderInfo.Addon.PackOrder)
			}
		}
	}
	var mainSkuIds []int
	var mainOrderInfoMap = make(map[string]logicOds.OrderInfoData)
	if len(mainOrderNos) > 0 {
		//批量获取订单号
		var mainOrderInfoDataErr error
		mainOrderInfoMap, mainOrderInfoDataErr = logicOds.OrderInfoByOrderNos(mainOrderNos)
		if mainOrderInfoDataErr != nil {
			return fmt.Errorf("main order info error. info:%s", mainOrderInfoDataErr)
		}
		for _, mainOrderInfo := range mainOrderInfoMap {
			mainSkuIds = append(mainSkuIds, mainOrderInfo.SkuId)
		}
	}

	skuIds = utils.MergeSlice(skuIds, mainSkuIds)
	spuDateMap, spuDateErr := logicOds.SpuInfoBySkuIds(skuIds)
	if spuDateErr != nil {
		return fmt.Errorf("spu info error. info:%s", spuDateErr)
	}
	//数据组装
	var odsProcessor []business.OdsProcessor
	for _, params := range eventParams {
		//上层会对orderNos做过滤，这边包含的订单号，才允许走业务清洗逻辑
		if !utils.Container(orderNos, params.AfterList.Ordernum) {
			continue
		}
		trackId, skuId, applyDid, operatorId, count, operateChannel, insertTime, orderNo := GetCommonParams(params)
		businessNo, businessNoExist := businessNoMap[trackId]
		if !businessNoExist {
			global.LOG.Error(fmt.Sprintf("business no is error. track_id:%d", trackId))
			continue
		}
		var insertData business.CollectData
		//核心字段
		insertData.PoiId = 0
		insertData.SkuId = skuId
		insertData.ApplyDid = applyDid
		insertData.OperatorId = operatorId
		insertData.Count = count
		insertData.OperateChannel = operateChannel
		insertData.CollectNo = businessNo
		insertData.OrderNo = orderNo
		if insertTime.IsZero() {
			global.LOG.Error(fmt.Sprintf("insert time is error. track_id:%d", trackId))
			continue
		}
		insertData.CollectedAt = insertTime
		orderInfo, orderInfoExist := orderInfoMap[orderNo]
		if !orderInfoExist {
			global.LOG.Error(fmt.Sprintf("order info is error. order_no:%s", orderNo))
			continue
		}
		//订单未支付
		if !CheckOrderPay(orderInfo.PayStatus, orderNo) {
			global.LOG.Info(fmt.Sprintf("order not paid, skip collect. order_no:%s", orderNo))
			continue
		}
		//支付前操作校验,支付前操作不记录
		if beforePay := IsOperationBeforePay(trackId, orderNo); beforePay {
			global.LOG.Info(fmt.Sprintf("operation before pay, skip collect. order_no:%s", orderNo))
			continue
		}

		// 取票业务逻辑校验
		if params.AfterList.Tnum <= 0 {
			global.LOG.Error(fmt.Sprintf("invalid collect count. order_no:%s, tnum:%d", orderNo, params.AfterList.Tnum))
			continue
		}

		// 检查剩余票数是否足够
		if params.AfterList.LeftNum < 0 {
			global.LOG.Error(fmt.Sprintf("invalid left num. order_no:%s, left_num:%d", orderNo, params.AfterList.LeftNum))
			continue
		}
		//套票信息
		mainSpuId, mainOrderNo := HandlePackMainOrderInfo(orderInfo, mainOrderInfoMap)
		insertData.SaleChannel = orderInfo.OrderMode
		spuInfo, spuExist := spuDateMap[orderInfo.SpuId]
		if !spuExist {
			global.LOG.Error(fmt.Sprintf("sku search spu error. order_no:%s, spu:%d", orderNo, orderInfo.SpuId))
			continue
		}
		tradeNo := GetPayExternalOperateNo(orderInfo.OrderNum, orderInfo.TradeOrderId)
		//交易单号
		insertData.TradeNo = tradeNo
		//操作站点
		insertData.OperateSiteId = params.AfterList.SalerID
		//景区信息字段
		insertData.ProductType = spuInfo.PType
		insertData.SubType = spuInfo.SubType
		insertData.SpuId = spuInfo.Id
		//获取支付售票员id
		sellOperatorInfo, getSellOperatorIdErr := GetSellOperatorInfo(orderNo)
		if getSellOperatorIdErr != nil {
			global.LOG.Error(fmt.Sprintf("order not sell_operator_id is error. order_no:%s", orderNo))
			continue
		}
		insertData.SellOperatorId = sellOperatorInfo.OperMember
		insertData.SellSiteId = sellOperatorInfo.SalerID
		//子票订单标记主票
		mainSpuInfo, mainSpuExist := spuDateMap[mainSpuId]
		if mainSpuExist {
			insertData.Payload.ParentOrderApplyDid = mainSpuInfo.ApplyDid
			insertData.Payload.ParentOrderProductType = mainSpuInfo.PType
		}
		insertData.Payload.IfPack = orderInfo.Addon.IfPack
		insertData.Payload.ParentOrderNo = mainOrderNo
		//订单扩展信息
		var orderExtContent logicOds.OrderFxDetailExtContentFields
		extContentErr := json.Unmarshal([]byte(orderInfo.FxDetails.ExtContent), &orderExtContent)
		if extContentErr != nil {
			global.LOG.Error(fmt.Sprintf("order ext content decode error. order_no:%s", orderNo))
			continue
		}
		//分组标签
		insertData.Payload.GroupsTag = orderExtContent.GetGroupsTagList()

		// 处理取票特有的扩展信息
		var trackExtContent myuuordertrack.EventMyuuOrderTrackExtContent
		if params.AfterList.ExtContent != "" {
			extContentErr := json.Unmarshal([]byte(params.AfterList.ExtContent), &trackExtContent)
			if extContentErr != nil {
				global.LOG.Error(fmt.Sprintf("track ext content decode error. order_no:%s", orderNo))
			}
		}
		//分销链信息
		distributionChainData, distributionChainDataExist := distributionChainMap[orderNo]
		if !distributionChainDataExist {
			global.LOG.Error(fmt.Sprintf("distribution chain info not exist error. order_no:%s", orderNo))
			continue
		}
		var payloadDistributionChain []business.CollectDataPayloadDistributionChain
		for _, distributionChainInfo := range distributionChainData {
			var distributionChainItem business.CollectDataPayloadDistributionChain
			distributionChainItem.SellerId = distributionChainInfo.SellerId
			distributionChainItem.BuyerId = distributionChainInfo.BuyerId
			distributionChainItem.Level = distributionChainInfo.Level
			distributionChainItem.PayMode = distributionChainInfo.PMode
			distributionChainItem.SalePrice = distributionChainInfo.SaleMoney
			distributionChainItem.CostPrice = distributionChainInfo.CostMoney
			distributionChainItem.IsSanKe = LastStageUserIsSanKe(sanKeList, distributionChainInfo.Level, distributionChainInfo.BuyerId)
			payloadDistributionChain = append(payloadDistributionChain, distributionChainItem)
		}
		insertData.Payload.DistributionChain = payloadDistributionChain
		//散客末级补全
		var odsProcessorChain business.OdsProcessor = &insertData
		handlecommon.HandleDistributionChainNeedAddSplitInfo(odsProcessorChain)
		//外部操作单号
		insertData.ExternalOperateNo = strconv.Itoa(trackId)

		// 记录取票处理日志
		global.LOG.Info(fmt.Sprintf("collect ticket processed. order_no:%s, collect_no:%d, count:%d, left_num:%d",
			orderNo, insertData.CollectNo, insertData.Count, params.AfterList.LeftNum))

		//添加数据
		odsProcessor = append(odsProcessor, &insertData)
	}
	if len(odsProcessor) <= 0 {
		return nil
	}
	//链路操作信息处理
	var handleInsertDataListErr error
	odsProcessor, handleInsertDataListErr = handlecommon.HandlePayDistributionChainOperateInfo(odsProcessor)
	if handleInsertDataListErr != nil {
		//记录异常的编号
		HandleInsertDataListErrToLog(trackIds, handleInsertDataListErr.Error())
	}
	//数据写入 - 使用取票专用的数据结构
	var records []repositoryOds.BusinessCollectModelData
	err := utils.JsonConvertor(odsProcessor, &records)
	if err != nil {
		return fmt.Errorf("ods processor error. info:%s", err)
	}
	insertErr := repository.OdsBusinessCollectRepository.InsertMore(records)
	if insertErr != nil {
		//记录异常的编号
		InsertErrToLog(trackIds, insertErr.Error())
		return nil
	}
	//推送消息 - 使用取票专用的事件结构
	var eventBusinessList eventOds.EventBusinessCollectList
	for _, record := range records {
		var eventBusinessItem eventOds.EventBusinessCollectData
		eventRecordsSwitchErr := utils.JsonConvertor(record, &eventBusinessItem)
		if eventRecordsSwitchErr != nil {
			EventRecordsSwitchErr([]int{logicOds.GetBusinessOperationNumberId(record.CollectNo)})
			return fmt.Errorf("event records switch error. info:%s", eventRecordsSwitchErr)
		}
		//时间格式转换下
		eventBusinessItem.CollectedAt = record.CollectedAt.Format(enum.TimestampLayoutMake)
		eventBusinessList.EventData = append(eventBusinessList.EventData, eventOds.EventBusinessCollect{
			Data: eventBusinessItem,
		})
	}
	//统一推送处理
	var odsEventProcessor eventOds.EventProcessor = &eventBusinessList
	//推送消息
	sendErr := handlecommon.SendCreateEventMessage(odsEventProcessor)
	if sendErr != nil {
		return sendErr
	}
	return nil
}
