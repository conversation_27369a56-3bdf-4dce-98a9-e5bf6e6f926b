package dimensionscheme

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/repository"
	repoDimensionscheme "report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/tag"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/tagging"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggroup"
	"report-service/pkg/szerrors"

	"github.com/spf13/cast"
	"go.uber.org/zap"
)

// 将 MySQL 的方案、分组、维度值迁移到 SelectDB 的维度标签关系表
func MigrateScheme() error {
	// 1. 获取所有方案
	var page int = 1
	var pageSize int = 100
	allSchemes := make([]repoDimensionscheme.PoScheme, 0)

	schemeIdToTagGroupCode := make(map[int]string)

	for {
		// 获取所有方案，不区分商户和维度类型
		schemes, err := repository.SchemeRepository.Pagination(0, 0, page, pageSize)
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}

		// 暂不迁移年龄类型的维度
		for _, scheme := range schemes {
			if scheme.DimensionType == enum.MerchantSchemeDimensionTypeAgeGroup {
				continue
			}
			allSchemes = append(allSchemes, scheme)
		}

		if len(schemes) < pageSize {
			break
		}
		page++
	}

	// 2. 遍历方案，为每个方案创建标签组并同步数据
	for _, scheme := range allSchemes {
		// 2.1 创建标签组
		scene := enum.DimensionTypeToTagCenterSceneMap[scheme.DimensionType]
		if scene == "" {
			global.LOG.Error("不支持的维度类型", zap.Int("dimension_type", scheme.DimensionType))
			continue
		}

		// 幂等逻辑：先检查是否存在同名标签组
		var tagGroupCode string
		existingTagGroups, err := taggroup.TagGroupPage(&taggroup.MemberRequest{
			Name:     &scheme.Name,
			MemberID: cast.ToInt64(scheme.MerchantId),
			PageNum:  1,
			PageSize: 10,
		})

		if err != nil {
			global.LOG.Error("查询标签组失败", zap.Error(err))
			continue
		}

		// 检查是否存在同名标签组
		var existingTagGroup *taggroup.TagGroupPageItemVO
		if existingTagGroups != nil && len(existingTagGroups.Rows) > 0 {
			for _, tg := range existingTagGroups.Rows {
				if tg.Name != nil && *tg.Name == scheme.Name && tg.Code != nil {
					existingTagGroup = &tg
					break
				}
			}
		}

		if existingTagGroup != nil && existingTagGroup.Code != nil {
			// 使用已存在的标签组
			tagGroupCode = *existingTagGroup.Code
			global.LOG.Info("使用已存在的标签组", zap.String("name", scheme.Name), zap.String("tagGroupCode", tagGroupCode))
		} else {
			// 创建新标签组
			tagGroup, err := taggroup.MemberTagGroupCreate(&taggroup.MemberTagGroupCreateRequest{
				Name:           scheme.Name,
				Scene:          scene,
				TaggedMultiple: false,
				MemberId:       cast.ToInt64(scheme.MerchantId),
			})
			if err != nil {
				global.LOG.Error("创建标签组失败", zap.Error(err))
				continue
			}
			tagGroupCode = *tagGroup.Rows[0].Code
		}

		// 保存 schemeId 和 tagGroupCode 的映射关系
		schemeIdToTagGroupCode[scheme.Id] = tagGroupCode

		// 2.2 获取方案下的所有分组（上限不会超过10个，所以这里不需要翻页）
		groups, err := repository.GroupRepository.Pagination(scheme.Id, 1, 200)
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}

		// 2.3 获取方案下的所有维度值与分组关系
		groupRelations, err := repository.GroupRelationRepository.ListBySchemeId(scheme.Id)
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}

		// 创建分组ID到分组名称的映射
		groupIdToName := make(map[int]string)
		for _, group := range groups {
			groupIdToName[group.Id] = group.Name
		}

		// 2.4 构建维度值到分组ID的映射
		dimensionValueToGroupId := make(map[string]int)
		dimensionValues := make([]int, 0)
		for _, relation := range groupRelations {
			dimensionValueToGroupId[cast.ToString(relation.DimensionValue)] = relation.GroupId
			dimensionValues = append(dimensionValues, relation.DimensionValue)
		}

		// 2.5 为每个分组创建标签
		groupIdToTagCode := make(map[int]string)
		for groupId, groupName := range groupIdToName {
			// 幂等逻辑：先检查是否存在同名标签
			existingTags, err := tag.TagPage(&tag.TagPageRequestVO{
				GroupCode: tagGroupCode,
				EqName:    &groupName,
				MemberId:  cast.ToInt64(scheme.MerchantId),
				PageNum:   1,
				PageSize:  10,
			})

			if err != nil {
				global.LOG.Error("查询标签失败", zap.Error(err))
				continue
			}

			var tagCode string
			if existingTags != nil && len(existingTags.Rows) > 0 {
				// 使用已存在的标签
				tagCode = *existingTags.Rows[0].Code
				global.LOG.Info("使用已存在的标签", zap.String("name", groupName), zap.String("tagCode", tagCode))
			} else {
				// 创建新标签
				tagResp, err := tag.TagCreate(&tag.SceneTagCreateRequestVO{
					GroupCode: tagGroupCode,
					Name:      groupName,
					MemberID:  cast.ToInt64(scheme.MerchantId),
				})
				if err != nil {
					global.LOG.Error("创建标签失败", zap.Error(err))
					continue
				}
				tagCode = *tagResp.Rows[0].Code
			}

			groupIdToTagCode[groupId] = tagCode
		}

		// 2.6 构建打标请求

		// 如果维度类型为产品、票，则需要根据维度值获取subjectId，产品：subjectId=merchantId|spuId，票：subjectId=merchantId|spuId|skuId
		switch scheme.DimensionType {
		case enum.MerchantSchemeDimensionTypeSpu:
			for dimensionValue, groupId := range dimensionValueToGroupId {
				subjectId := fmt.Sprintf("%d|%s", scheme.MerchantId, dimensionValue)
				dimensionValueToGroupId[subjectId] = groupId
				delete(dimensionValueToGroupId, dimensionValue)
			}
		case enum.MerchantSchemeDimensionTypeSku:
			spuInfo, err := ods.SpuInfoBySkuIds(dimensionValues)
			if err != nil {
				global.LOG.Error("获取产品信息失败", zap.Error(err))
				continue
			}
			for dimensionValue, groupId := range dimensionValueToGroupId {
				subjectId := fmt.Sprintf("%d|%d|%s", scheme.MerchantId, spuInfo[cast.ToInt(dimensionValue)].Id, dimensionValue)
				dimensionValueToGroupId[subjectId] = groupId
				delete(dimensionValueToGroupId, dimensionValue)
			}
		}

		taggingRequests := make([]tagging.TaggingApplyTagsRequestVO, 0)
		for dimensionValue, groupId := range dimensionValueToGroupId {
			if tagCode, ok := groupIdToTagCode[groupId]; ok {
				// 对每个维度值进行打标
				taggingRequest := tagging.TaggingApplyTagsRequestVO{
					Subject:     scene,
					SubjectID:   cast.ToString(dimensionValue),
					SubjectName: cast.ToString(dimensionValue),
					Tags: []tagging.ApplyTag{
						{
							Code:      tagCode,
							GroupCode: tagGroupCode,
							Version:   "1",
						},
					},
				}

				taggingRequests = append(taggingRequests, taggingRequest)
			}
		}

		// 2.7 批量打标
		if len(taggingRequests) > 0 {
			// 每50个请求一组进行批量打标
			batchSize := 50
			for i := 0; i < len(taggingRequests); i += batchSize {
				end := i + batchSize
				if end > len(taggingRequests) {
					end = len(taggingRequests)
				}
				batch := taggingRequests[i:end]
				_, err = tagging.TaggingApplyTagsBatch(batch)
				if err != nil {
					return err
				}
			}
		}

		// 2.8 刷新标签关系到SelectDB
		err = dimensionscheme.RefreshTagRelationByTagGroupCode(scene, tagGroupCode, scheme.MerchantId)
		if err != nil {
			return err
		}
	}

	// 3. 遍历更新模板，将模板的方案分组迁移为标签组
	err := updateTemplates(schemeIdToTagGroupCode)
	if err != nil {
		return err
	}

	return nil
}

// updateTemplates 遍历更新模板，将模板的方案分组迁移为标签组
func updateTemplates(schemeIdToTagGroupCode map[int]string) error {
	// 1. 获取所有商户
	merchantIds, err := template.GetAllMerchantWithTemplate()
	if err != nil {
		return err
	}

	// 2. 遍历商户
	for _, merchantId := range merchantIds {
		// 3. 通过template的logic层获取所有模板
		templates, err := template.GetAllTemplatesByMerchantId(merchantId)
		if err != nil {
			global.LOG.Error("获取模板失败", zap.Error(err), zap.Int("merchantId", merchantId))
			continue
		}

		// 4. 遍历模板
		for _, tpl := range templates {
			// 5. 处理模板数据，这里不需要再解析了，因为GetAllTemplatesByMerchantId已经做了转换

			// 6. 替换方案分组ID为标签组Code
			// 判断DimensionScheme是否有值
			hasDimensionScheme := tpl.Payload.DimensionScheme.Spu > 0 ||
				tpl.Payload.DimensionScheme.Sku > 0 ||
				tpl.Payload.DimensionScheme.PayMode > 0 ||
				tpl.Payload.DimensionScheme.SaleChannel > 0
			if !hasDimensionScheme {
				continue
			}

			// 创建dimension_tag_group字段并初始化
			dimensionTagGroup := make(map[string]string)

			// 处理每个维度
			for dimension, scene := range enum.DimensionToTagCenterSceneMap {
				// 从DimensionScheme结构体中获取对应字段的值
				var schemeId int
				switch dimension {
				case enum.DimensionSpu:
					schemeId = tpl.Payload.DimensionScheme.Spu
				case enum.DimensionSku:
					schemeId = tpl.Payload.DimensionScheme.Sku
				case enum.DimensionPayMode:
					schemeId = tpl.Payload.DimensionScheme.PayMode
				case enum.DimensionSaleChannel:
					schemeId = tpl.Payload.DimensionScheme.SaleChannel
				}

				if schemeId > 0 {
					if tagGroupCode, ok := schemeIdToTagGroupCode[schemeId]; ok {
						dimensionTagGroup[scene] = tagGroupCode
					}
				}

				// 设置更新后的标签组信息
				tpl.Payload.DimensionTagGroup = dimensionTagGroup
			}

			if len(dimensionTagGroup) == 0 {
				continue
			}

			// 7. 通过template的logic层更新模板
			err = template.UpdateTemplate(tpl)
			if err != nil {
				global.LOG.Error("保存模板失败", zap.Error(err), zap.Int("templateId", tpl.Id))
				continue
			}
		}
	}

	return nil
}
