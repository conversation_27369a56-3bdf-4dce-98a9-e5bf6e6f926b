package dimensionscheme

import (
	dimensionSchemeLogic "report-service/internal/domain/logic/dimensionscheme"
	templateLogic "report-service/internal/domain/logic/report/template"
)

func DeleteWithCleanTemplateSchemeId(merchantId int, id int) error {
	err := dimensionSchemeLogic.Delete(merchantId, id)
	if err != nil {
		return err
	}

	err = templateLogic.BatchRmDimensionSchemeIdsByMerchantIdAndMemberId(merchantId, []int{id})
	if err != nil {
		return err
	}
	return nil
}
