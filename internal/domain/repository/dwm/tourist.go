package dwm

import (
	"gitee.com/golang-module/carbon/v2"
)

type TouristModel struct {
	// 操作唯一信息
	OperatedAt         string `json:"operated_at"`
	MerchantId         int    `json:"merchant_id"`
	BusinessNo         int    `json:"business_no"`
	SortKey            string `json:"sort_key"`
	Region             string `json:"region"`   //地域
	Country            string `json:"country"`  //国家
	Province           string `json:"province"` //省
	City               string `json:"city"`     //市
	District           string `json:"district"` //区
	Age                int    `json:"age"`
	Gender             int    `json:"gender"`
	PoiId              int    `json:"poi_id"`
	SpuId              int    `json:"spu_id"`
	SkuId              int    `json:"sku_id"`
	DistributorId      int    `json:"distributor_id"`
	OrderNo            string `json:"order_no"`
	OperateType        int    `json:"operate_type"`
	PackType           int    `json:"pack_type"`
	ShowBindType       int    `json:"show_bind_type"`
	AnnualCardType     int    `json:"annual_card_type"`
	ExchangeCouponType int    `json:"exchange_coupon_type"`
	BusinessIdx        int    `json:"business_idx"`
	BusinessCode       string `json:"business_code"`
	IdNumber           string `json:"id_number"`
	IdType             int    `json:"id_type"`
	OperatorId         int    `json:"operator_id"`
	OperateSiteId      int    `json:"operate_site_id"`
	OperateChannel     int    `json:"operate_channel"`
	OrderChannel       int    `json:"order_channel"`
	Nickname           string `json:"nickname"`
	Mobile             string `json:"mobile"`
	// 指标
	Count int `json:"count"`
}

type TouristStatistics struct {
	// 指标
	Count int `json:"count"`
}

type TouristFilterFields struct {
	OperateTypes        []int          `json:"operate_types"`
	OperatedAtStart     *carbon.Carbon `json:"operated_at_start"`
	OperatedAtEnd       *carbon.Carbon `json:"operated_at_end"`
	MerchantId          *int           `json:"merchant_id"`
	RegionIds           []string       `json:"region_ids"`   //地域
	CountryIds          []string       `json:"country_ids"`  //国家
	ProvinceIds         []string       `json:"province_ids"` //省
	CityIds             []string       `json:"city_ids"`     //市
	DistrictIds         []string       `json:"district_ids"` //区
	Ages                []int          `json:"ages"`
	Genders             []int          `json:"genders"`
	PoiIds              []int          `json:"poi_ids"`
	SpuIds              []int          `json:"spu_ids"`
	SkuIds              []int          `json:"sku_ids"`
	OperateChannels     []int          `json:"operate_channels"`
	OperatorIds         []int          `json:"operator_ids"`
	OperateSiteIds      []int          `json:"operate_site_ids"`
	PackTypes           []int          `json:"pack_types"`
	ShowBindTypes       []int          `json:"show_bind_types"`
	AnnualCardTypes     []int          `json:"annual_card_types"`
	ExchangeCouponTypes []int          `json:"exchange_coupon_types"`
	NotSpuIds           []int          `json:"not_spu_ids"`
	Nicknames           []string       `json:"nickname"`
	Mobiles             []string       `json:"mobile"`
	OrderNos            []string       `json:"order_no"`
	BusinessCodes       []string       `json:"business_code"`
	SaleChannels        []int          `json:"sale_channels"`
	IdTypes             []int          `json:"id_types"`
	IdNumbers           []string       `json:"id_number"`
}

type TouristRepository interface {
	DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error
	Insert(records []TouristModel) error
	PaginateByLastSortKey(filter TouristFilterFields, lastSortKey *string, pageSize int) (list []TouristModel, nextSortKey string, err error)
	Statistics(filter TouristFilterFields) (statistic TouristStatistics, err error)
	PaginateCount(filter TouristFilterFields) (Total int, err error)
	GetPrevSortKey(filter TouristFilterFields, lastSortKey *string, pageSize int) (prevSortKey string, err error)
}
