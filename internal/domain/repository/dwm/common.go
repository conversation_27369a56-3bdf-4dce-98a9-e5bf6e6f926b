package dwm

import (
	"gitee.com/golang-module/carbon/v2"
)

type CommonModel struct {
	OperatedAt string `json:"operated_at"` // 操作时间
	CommonModelBase
}

type CommonModelBase struct {
	// 操作唯一信息
	MerchantId        int    `json:"merchant_id"`         // 商户ID
	BusinessNo        int    `json:"business_no"`         // 业务单号
	OrderNo           string `json:"order_no"`            // 订单号
	TradeNo           string `json:"trade_no"`            // 交易单号
	SortKey           string `json:"sort_key"`            // 排序字段，实际上是 128 位的 int 类型
	ExternalOperateNo string `json:"external_operate_no"` // 外部操作单号
	ParentOrderNo     string `json:"parent_order_no"`     //主订单号
	// 维度
	TargetAudience     string `json:"target_audience"`      // 适用人群标签代码
	ProductType        string `json:"product_type"`         // 产品类型
	SubType            int    `json:"sub_type"`             // 子类型
	ParentMerchantId   int    `json:"parent_merchant_id"`   // 上级商户ID
	DistributorId      int    `json:"distributor_id"`       // 下级商户ID
	OperateType        int    `json:"operate_type"`         // 操作类型：1支付&加票 2核销&完结 3取消 4撤销 5售后
	PoiId              int    `json:"poi_id"`               // PoiID
	SpuId              int    `json:"spu_id"`               // SpuID
	SkuId              int    `json:"sku_id"`               // SkuID
	SaleChannel        int    `json:"sale_channel"`         // 销售渠道
	OperateChannel     int    `json:"operate_channel"`      // 操作渠道
	CostPayMode        int    `json:"cost_pay_mode"`        // 采购支付方式
	SalePayMode        int    `json:"sale_pay_mode"`        // 销售支付方式
	SellOperatorId     int    `json:"sell_operator_id"`     // 售票员ID
	OperatorId         int    `json:"operator_id"`          // 操作人ID
	SellSiteId         int    `json:"sell_site_id"`         // 售票站点ID
	OperateSiteId      int    `json:"operate_site_id"`      // 操作站点ID
	CostUnitPrice      int    `json:"cost_unit_price"`      // 采购单价
	SaleUnitPrice      int    `json:"sale_unit_price"`      // 销售单价
	PackType           int    `json:"pack_type"`            // 套票规则，0默认  1是主票 2是子票
	ShowBindType       int    `json:"show_bind_type"`       // 捆绑票规则，0默认  1是主票 2是子票
	AnnualCardType     int    `json:"annual_card_type"`     // 年卡规则，0默认  1是年卡 2是特权
	ExchangeCouponType int    `json:"exchange_coupon_type"` // 预售券规则，0默认  1是预售券 2是权益兑换订单
	// 指标
	PayCount                int `json:"pay_count"`                  // 预订数量
	PayCostPrice            int `json:"pay_cost_price"`             // 预订采购金额
	PayCostDiscountPrice    int `json:"pay_cost_discount_price"`    // 预订采购优惠金额
	PaySalePrice            int `json:"pay_sale_price"`             // 预订销售金额
	PaySaleDiscountPrice    int `json:"pay_sale_discount_price"`    // 预订销售优惠金额
	VerifyCount             int `json:"verify_count"`               // 核销数量
	VerifyCostPrice         int `json:"verify_cost_price"`          // 核销采购金额
	VerifyCostDiscountPrice int `json:"verify_cost_discount_price"` // 核销采购优惠金额
	VerifySalePrice         int `json:"verify_sale_price"`          // 核销销售金额
	VerifySaleDiscountPrice int `json:"verify_sale_discount_price"` // 核销销售优惠金额
	CancelCount             int `json:"cancel_count"`               // 取消数量
	CancelCostPrice         int `json:"cancel_cost_price"`          // 取消采购金额
	CancelCostDiscountPrice int `json:"cancel_cost_discount_price"` // 取消采购优惠金额
	CancelCostFee           int `json:"cancel_cost_fee"`            // 取消采购手续费
	CancelSalePrice         int `json:"cancel_sale_price"`          // 取消销售金额
	CancelSaleDiscountPrice int `json:"cancel_sale_discount_price"` // 取消销售优惠金额
	CancelSaleFee           int `json:"cancel_sale_fee"`            // 取消销售手续费
	RevokeCount             int `json:"revoke_count"`               // 撤销数量
	RevokeCostPrice         int `json:"revoke_cost_price"`          // 撤销采购金额
	RevokeCostDiscountPrice int `json:"revoke_cost_discount_price"` // 撤销采购优惠金额
	RevokeCostFee           int `json:"revoke_cost_fee"`            // 撤销采购手续费
	RevokeSalePrice         int `json:"revoke_sale_price"`          // 撤销销售金额
	RevokeSaleDiscountPrice int `json:"revoke_sale_discount_price"` // 撤销销售优惠金额
	RevokeSaleFee           int `json:"revoke_sale_fee"`            // 撤销销售手续费
	AfterSaleCount          int `json:"after_sale_count"`           // 售后数量
	AfterCostPrice          int `json:"after_cost_price"`           // 售后采购金额
	AfterSalePrice          int `json:"after_sale_price"`           // 售后销售金额
}

type CommonFilterFields struct {
	OperateTypes        []int          `json:"operate_types"`
	OperatedAtStart     *carbon.Carbon `json:"operated_at_start"`
	OperatedAtEnd       *carbon.Carbon `json:"operated_at_end"`
	MerchantId          *int           `json:"merchant_id"`
	DistributorIds      []int          `json:"distributor_ids"`
	PoiIds              []int          `json:"poi_ids"`
	SpuIds              []int          `json:"spu_ids"`
	SkuIds              []int          `json:"sku_ids"`
	SaleChannels        []int          `json:"sale_channels"`
	OperateChannels     []int          `json:"operate_channels"`
	CostPayModes        []int          `json:"cost_pay_modes"`
	SalePayModes        []int          `json:"sale_pay_modes"`
	SellOperatorIds     []int          `json:"sell_operator_ids"`
	OperatorIds         []int          `json:"operator_ids"`
	SellSiteIds         []int          `json:"sell_site_ids"`
	OperateSiteIds      []int          `json:"operate_site_ids"`
	PackTypes           []int          `json:"pack_types"`
	ShowBindTypes       []int          `json:"show_bind_types"`
	AnnualCardTypes     []int          `json:"annual_card_types"`
	ExchangeCouponTypes []int          `json:"exchange_coupon_types"`
	NotSpuIds           []int          `json:"not_spu_ids"`
	TargetAudience      []string       `json:"target_audience"`  // 适用人群标签代码
	GroupMemberIds      []int          `json:"group_member_ids"` //集团成员id
}

type CommonModeStatHour struct {
	Hour string `json:"hour"` // 小时
	CommonModelBase
}

type CommonModeStatDate struct {
	Date string `json:"date"` // 日期
	CommonModelBase
}

type CommonRepository interface {
	GetDatabaseName() string
	Insert(records []CommonModel) error
	GetByBusinessNos(businessNos []int) (result []CommonModel, err error)
	PaginateByLastSortKey(filter CommonFilterFields, lastSortKey *string, pageSize int) (list []CommonModel, nextSortKey string, err error)
}
