package repository

import (
	"report-service/internal/domain/repository/business"
	"report-service/internal/domain/repository/config"
	"report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/domain/repository/dm/common"
	dmCustomized "report-service/internal/domain/repository/dm/customized"
	"report-service/internal/domain/repository/dm/oldreport"
	"report-service/internal/domain/repository/dm/tourist"
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/domain/repository/dwm"
	dwmCustomized "report-service/internal/domain/repository/dwm/customized"
	"report-service/internal/domain/repository/ods"
	oldreport2 "report-service/internal/domain/repository/oldreport"
	"report-service/internal/domain/repository/report"
)

var (
	ConfigRepository         config.ConfigRepository
	MerchantConfigRepository config.MerchantConfigRepository

	ReportDwmCommonRepository dwm.CommonRepository

	ReportDmCommonDateRepository     common.DayRepository
	ReportDmCommonHourRepository     common.HourRepository
	ReportDmCommonRealtimeRepository common.RealtimeRepository

	OldReportOrderTwoRepo     oldreport.OrderTwoRepo
	OldReportOrderTwoHourRepo oldreport.OrderTwoHourRepo
	OldReportCheckTwoRepo     oldreport.CheckTwoRepo
	OldReportCheckTwoHourRepo oldreport.CheckTwoHourRepo

	SchemeRepository        dimensionscheme.SchemeRepository
	GroupRepository         dimensionscheme.GroupRepository
	GroupRelationRepository dimensionscheme.GroupRelationRepository
	TagRelationRepository   dimensionscheme.TagRelationRepository

	ReportTemplateConfigRepository report.TemplateConfigRepository

	OdsBusinessPayRepository       ods.BusinessPayRepository
	OdsBusinessCancelRepository    ods.BusinessCancelRepository
	OdsBusinessVerifyRepository    ods.BusinessVerifyRepository
	OdsBusinessRevokeRepository    ods.BusinessRevokeRepository
	OdsBusinessAfterSaleRepository ods.BusinessAfterSaleRepository
	OdsBusinessAddTicketRepository ods.BusinessAddTicketRepository
	OdsBusinessFinishRepository    ods.BusinessFinishRepository
	OdsBusinessCollectRepository   ods.BusinessCollectRepository
	OdsBusinessReprintRepository   ods.BusinessReprintRepository

	DwdPayRepository       dwd.PayRepository
	DwdRevokeRepository    dwd.RevokeRepository
	DwdVerifyRepository    dwd.VerifyRepository
	DwdFinishRepository    dwd.FinishRepository
	DwdAddTicketRepository dwd.AddTicketRepository
	DwdCancelRepository    dwd.CancelRepository
	DwdAfterSaleRepository dwd.AfterSaleRepository
	DwdCollectRepository   dwd.CollectRepository
	DwdReprintRepository   dwd.ReprintRepository

	//游客小时指标表
	ReportDmTouristHourRepository tourist.HourRepository
	ReportDmTouristDayRepository  tourist.DayRepository
	//游客明细表
	ReportDwmTouristRepository dwm.TouristRepository

	//旧报表模板
	PftReportSearchConfigResult            oldreport2.PftReportSearchConfigRepository
	PftReportTouristSourceAreaDetailResult oldreport2.PftReportTouristSourceAreaDetailRepository
	PftReportTouristSourceAreaConfigResult oldreport2.PftReportTouristSourceAreaConfigRepository

	//定制报表-支付方式汇总金额报表明细表
	ReportDwmPayModeMoneyRepository dwmCustomized.PayModeMoneyRepository
	//定制报表-支付方式汇总金额指标表
	ReportDmPayModeMoneyRepository dmCustomized.PayModeMoneyHourRepository

	//json-rpc应用配置
	BusinessAppRepository business.AppRepository

	// 访问白名单
	BusinessAccessWhiteListRepository business.AccessWhiteListRepository

	//报表访问统计指标表
	ReportDmVisitsStatisticsDayRepository dmCustomized.VisitsStatisticsDayRepository
)
