package common

import (
	"report-service/internal/domain/repository/dm"

	"gitee.com/golang-module/carbon/v2"
)

type DayModel struct {
	Year  int    `json:"year"`
	Month int    `json:"month"`
	Date  string `json:"date"`
	dm.CommonModel
}

type PaginationItem struct {
	Date string `json:"date"`
	dm.CommonModelDimension
	dm.CommonSearchModelRelationDimension
	dm.CommonModelIndicator
}

type DayRepository interface {
	GetDatabaseName() string
	InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int) error
	DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error
	Paginate(commonSearch dm.CommonSearch, page, pageSize int) (list []PaginationItem, total int, err error)
	Statistics(commonSearch dm.CommonSearch) (statistic dm.CommonStatistic, err error)
}
