package common

import (
	"report-service/internal/domain/repository/dm"

	"gitee.com/golang-module/carbon/v2"
)

type HourModel struct {
	DateHour string `json:"date_hour"` // 日期-小时
	dm.CommonModel
}

type HourRepository interface {
	GetDatabaseName() string
	InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int) error
	DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error
	Paginate(commonSearch dm.CommonSearch, page, pageSize int) (list []PaginationItem, total int, err error)
	Statistics(commonSearch dm.CommonSearch) (statistic dm.CommonStatistic, err error)
}
