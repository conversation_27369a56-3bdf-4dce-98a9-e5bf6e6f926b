package dm

import "gitee.com/golang-module/carbon/v2"

type CommonModel struct {
	CommonModelDimension
	CommonModelSpecialProductStatRule
	CommonModelIndicator
}

type CommonModelDimension struct {
	MerchantId       *int    `json:"merchant_id"`        // 商户ID
	ParentMerchantId *int    `json:"parent_merchant_id"` // 上级商户ID
	DistributorId    *int    `json:"distributor_id"`     // 下级商户ID
	PoiId            *int    `json:"poi_id"`             // PoiID
	SpuId            *int    `json:"spu_id"`             // SpuID
	SkuId            *int    `json:"sku_id"`             // SkuID
	SaleChannel      *int    `json:"sale_channel"`       // 销售渠道
	CostPayMode      *int    `json:"cost_pay_mode"`      // 采购支付方式
	SalePayMode      *int    `json:"sale_pay_mode"`      // 销售支付方式
	SellOperatorId   *int    `json:"sell_operator_id"`   // 售票员ID
	OperatorId       *int    `json:"operator_id"`        // 操作人ID
	SellSiteId       *int    `json:"sell_site_id"`       // 售票站点ID
	CostUnitPrice    int     `json:"cost_unit_price"`    // 采购单价
	SaleUnitPrice    int     `json:"sale_unit_price"`    // 销售单价
	TargetAudience   *string `json:"target_audience"`    //适用人群
}

type CommonModelSpecialProductStatRule struct {
	PackType           int `json:"pack_type"`            // 套票规则，0默认  1是主票 2是子票
	ShowBindType       int `json:"show_bind_type"`       // 捆绑票规则，0默认  1是主票 2是子票
	AnnualCardType     int `json:"annual_card_type"`     // 年卡规则，0默认  1是年卡 2是特权
	ExchangeCouponType int `json:"exchange_coupon_type"` // 预售券规则，0默认  1是预售券 2是权益兑换订单
}

type CommonModelIndicator struct {
	PayCount                int `json:"pay_count"`                  // 预订数量
	PayCostPrice            int `json:"pay_cost_price"`             // 预订采购金额
	PayCostDiscountPrice    int `json:"pay_cost_discount_price"`    // 预订采购优惠金额
	PaySalePrice            int `json:"pay_sale_price"`             // 预订销售金额
	PaySaleDiscountPrice    int `json:"pay_sale_discount_price"`    // 预订销售优惠金额
	VerifyCount             int `json:"verify_count"`               // 核销数量
	VerifyCostPrice         int `json:"verify_cost_price"`          // 核销采购金额
	VerifyCostDiscountPrice int `json:"verify_cost_discount_price"` // 核销采购优惠金额
	VerifySalePrice         int `json:"verify_sale_price"`          // 核销销售金额
	VerifySaleDiscountPrice int `json:"verify_sale_discount_price"` // 核销销售优惠金额
	CancelCount             int `json:"cancel_count"`               // 取消数量
	CancelCostPrice         int `json:"cancel_cost_price"`          // 取消采购金额
	CancelCostDiscountPrice int `json:"cancel_cost_discount_price"` // 取消采购优惠金额
	CancelCostFee           int `json:"cancel_cost_fee"`            // 取消采购手续费
	CancelSalePrice         int `json:"cancel_sale_price"`          // 取消销售金额
	CancelSaleDiscountPrice int `json:"cancel_sale_discount_price"` // 取消销售优惠金额
	CancelSaleFee           int `json:"cancel_sale_fee"`            // 取消销售手续费
	RevokeCount             int `json:"revoke_count"`               // 撤销数量
	RevokeCostPrice         int `json:"revoke_cost_price"`          // 撤销采购金额
	RevokeCostDiscountPrice int `json:"revoke_cost_discount_price"` // 撤销采购优惠金额
	RevokeCostFee           int `json:"revoke_cost_fee"`            // 撤销采购手续费
	RevokeSalePrice         int `json:"revoke_sale_price"`          // 撤销销售金额
	RevokeSaleDiscountPrice int `json:"revoke_sale_discount_price"` // 撤销销售优惠金额
	RevokeSaleFee           int `json:"revoke_sale_fee"`            // 撤销销售手续费
	AfterSaleCount          int `json:"after_sale_count"`           // 售后数量
	AfterCostPrice          int `json:"after_cost_price"`           // 售后采购金额
	AfterSalePrice          int `json:"after_sale_price"`           // 售后销售金额
}

// CommonSearchModelRelationDimension 通用关联维度
type CommonSearchModelRelationDimension struct {
	SpuGroupId         int    `json:"spu_group_id"`          // SPU分组ID
	SpuTagCode         string `json:"spu_tag_code"`          // SPU标签编码
	SkuGroupId         int    `json:"sku_group_id"`          // SKU分组ID
	SkuTagCode         string `json:"sku_tag_code"`          // SKU标签编码
	PayModeGroupId     int    `json:"pay_mode_group_id"`     // 支付方式分组ID
	PayModeTagCode     string `json:"pay_mode_tag_code"`     // 支付方式标签编码
	SaleChannelGroupId int    `json:"sale_channel_group_id"` // 销售渠道分组ID
	SaleChannelTagCode string `json:"sale_channel_tag_code"` // 销售渠道标签编码
	AgeGroupId         int    `json:"age_group_id"`          // 年龄段分组ID
}

type CommonSearchModelSpecialProductRule struct {
	PackType           []int `json:"pack_type"`
	ShowBindType       []int `json:"show_bind_type"`
	AnnualCardType     []int `json:"annual_card_type"`
	ExchangeCouponType []int `json:"exchange_coupon_type"`
}

type CommonSearchModelFilter struct {
	OperateType    []int    `json:"operate_type"`
	NotSpuIds      []int    `json:"not_spu_ids"`
	RegionIds      []string `json:"region_ids"`      //地域
	CountryIds     []string `json:"country_ids"`     //国家
	ProvinceIds    []string `json:"province_ids"`    //省
	CityIds        []string `json:"city_ids"`        //市
	DistrictIds    []string `json:"district_ids"`    //区
	TargetAudience []string `json:"target_audience"` //适用人群
}

type CommonStatistic struct {
	CommonModelIndicator
}

type CommonSearch struct {
	MerchantId              *int                                `json:"merchant_id"`                // 商户ID
	StartTime               carbon.Carbon                       `json:"start_time"`                 // 开始时间
	EndTime                 carbon.Carbon                       `json:"end_time"`                   // 结束时间
	TimeGroupType           *int                                `json:"time_group_type"`            // 时间分组类型
	Dimensions              []string                            `json:"dimensions"`                 // 维度列表
	DimensionScheme         map[string]int                      `json:"dimension_scheme"`           // 维度方案
	DimensionSchemeGroupIds map[string][]int                    `json:"dimension_scheme_group_ids"` // 维度方案组ID
	DimensionTagGroup       map[string]string                   `json:"dimension_tag_group"`        // 维度标签组
	DimensionTagCodes       map[string][]string                 `json:"dimension_tag_codes"`        // 维度标签编码
	DimensionRange          map[string][]int                    `json:"dimension_range"`            // 维度范围
	SpecialProductRule      CommonSearchModelSpecialProductRule `json:"special_product_rule"`       // 特殊产品规则
	OperateTypes            []int                               `json:"operate_types"`              // 操作类型
	DistributorRange        DistributorRange                    `json:"distributor_range"`          // 分销商范围
	CommonSearchModelFilter CommonSearchModelFilter             `json:"common_search_model_filter"` // 通用搜索模型过滤条件
	DimensionTagMerchantId  *int                                `json:"dimension_tag_merchant_id"`  //维度标签商户ID
}

type DistributorRange struct {
	IncludeIds []int `json:"include_ids"`
	ExcludeIds []int `json:"exclude_ids"`
}
