package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/repository/dm"
)

type HourModel struct {
	DateHour string `json:"date_hour"`
	CommonModel
}

type HourRepository interface {
	InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int) error
	DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int) error
	Paginate(
		merchantId int,
		startTime, endTime carbon.Carbon,
		dimensions []string,
		dimensionScheme map[string]int,
		dimensionRange map[string][]int,
		commonSearchModelCommonRule dm.CommonSearchModelSpecialProductRule,
		commonSearchModelFilter dm.CommonSearchModelFilter,
		page, pageSize int,
	) (list []PaginationItem, total int, err error)
	Summary(
		merchantId int,
		startTime, endTime carbon.Carbon,
		dimensions []string,
		dimensionScheme map[string]int,
		dimensionRange map[string][]int,
		commonSearchModelCommonRule dm.CommonSearchModelSpecialProductRule,
		commonSearchModelFilter dm.CommonSearchModelFilter,
	) (list []PaginationItem, err error)
	Statistics(
		merchantId int,
		startTime, endTime carbon.Carbon,
		dimensionRange map[string][]int,
		commonSearchModelCommonRule dm.CommonSearchModelSpecialProductRule,
		commonSearchModelFilter dm.CommonSearchModelFilter,
	) (statistic CommonModelIndicator, err error)
}
