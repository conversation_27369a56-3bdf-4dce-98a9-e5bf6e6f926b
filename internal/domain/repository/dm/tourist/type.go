package tourist

import "report-service/internal/domain/repository/dm"

type CommonModel struct {
	CommonModelDimension
	dm.CommonModelSpecialProductStatRule
	CommonModelIndicator
}

type CommonModelDimension struct {
	MerchantId  *int    `json:"merchant_id"`
	Region      *string `json:"region"`   //地域
	Country     *string `json:"country"`  //国家
	Province    *string `json:"province"` //省
	City        *string `json:"city"`     //市
	District    *string `json:"district"` //区
	Age         *int    `json:"age"`
	Gender      *int    `json:"gender"`
	PoiId       *int    `json:"poi_id"`
	SpuId       *int    `json:"spu_id"`
	SkuId       *int    `json:"sku_id"`
	OperateType *int    `json:"operate_type"`
}

type CommonModelIndicator struct {
	Count int `json:"count"`
}

type PaginationItem struct {
	CommonModel
	dm.CommonSearchModelRelationDimension
}
