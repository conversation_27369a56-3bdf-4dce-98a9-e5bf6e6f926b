package customized

import (
	"gitee.com/golang-module/carbon/v2"
	repoDm "report-service/internal/domain/repository/dm"
)

// PayModeMoney 支付方式金额指标表日表
type PayModeMoney struct {
	DateHour                    string `json:"date_hour"`                     // 日期小时
	MerchantID                  int    `json:"merchant_id"`                   // 商户ID
	DataSource                  int    `json:"data_source"`                   // 数据来源
	TimeType                    int    `json:"time_type"`                     // 时间类型
	ProjectID                   int    `json:"project_id"`                    // 项目ID
	OperatingRevenue            int    `json:"operating_revenue"`             // 营收金额 单位：元
	DiscountAmount              int    `json:"discount_amount"`               // 折扣金额 单位：元
	AccountsReceivablePayment   int    `json:"accounts_receivable_payment"`   // 挂帐金额 单位：元
	EntertainmentExpensePayment int    `json:"entertainment_expense_payment"` // 招待金额 单位：元
	CashPayment                 int    `json:"cash_payment"`                  // 现金支付金额 单位：元
	UnionPayPayment             int    `json:"union_pay_payment"`             // 银联支付金额 单位：元
	StoredValueCardPayment      int    `json:"stored_value_card_payment"`     // 储蓄卡支付金额 单位：元
	YinbaoPayPayment            int    `json:"yinbao_pay_payment"`            // 银豹付支付金额 单位：元
	RuralCommercialBankPayment  int    `json:"rural_commercial_bank_payment"` // 农商行收银宝金额 单位：元
	CreditPayment               int    `json:"credit_payment"`                // 授信支付金额 单位：元
	YibaoPayment                int    `json:"yibao_payment"`                 // 易宝金额 单位：元
	AlipayPayment               int    `json:"alipay_payment"`                // 支付宝金额 单位：元
	WechatPayment               int    `json:"wechat_payment"`                // 微信金额 单位：元
	PrepaidCardPayment          int    `json:"prepaid_card_payment"`          // 预付卡金额 单位：元
	MeituanCouponPayment        int    `json:"meituan_coupon_payment"`        // 美团优惠券金额 单位：元
	OtherPayment                int    `json:"other_payment"`                 // 其他金额 单位：元
	TotalIncome                 int    `json:"total_income"`                  // 收入合计金额（不含营收、折扣） 单位：元
	CreatedAt                   string `json:"created_at"`                    // 记录创建时间
}

type PayModeMoneyHourRepository interface {
	InsertIntoSelectDmCommon(startTime, endTime carbon.Carbon, merchantId int, projectId int) error
	DeleteWhereTimeRange(startTime, endTime carbon.Carbon, merchantId int, projectId int) error
	AggregateData(commonSearch repoDm.CommonSearch) (list []PayModeMoney, err error)
}
