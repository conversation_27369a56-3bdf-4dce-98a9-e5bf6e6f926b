package customized

import "gitee.com/golang-module/carbon/v2"

type VisitsStatisticsDayModel struct {
	Year           int    `json:"year"`             // 年
	Month          int    `json:"month"`            // 月
	Date           string `json:"date"`             // 日期
	MerchantId     int    `json:"merchant_id"`      // 商户ID
	OldPayCount    int    `json:"old_pay_count"`    // 旧报表-预订-访问数
	OldVerifyCount int    `json:"old_verify_count"` // 旧报表-验证-访问数
	NewPayCount    int    `json:"new_pay_count"`    // 新报表-预订-访问数
	NewVerifyCount int    `json:"new_verify_count"` // 新报表-验证-访问数
	OldTotalCount  int    `json:"old_total_count"`  // 旧报表-预订验证-总数
	NewTotalCount  int    `json:"new_total_count"`  // 新报表-预订验证-总数
}

type CommonSearch struct {
	StartTime  carbon.Carbon `json:"start_time"`  // 开始时间
	EndTime    carbon.Carbon `json:"end_time"`    // 结束时间
	MerchantId *int          `json:"merchant_id"` // 商户ID
	OrderBy    *string       `json:"order_by"`
	PageNum    *int          `json:"page_num"`  // 页码
	PageSize   *int          `json:"page_size"` // 页大小
}

type VisitsStatisticsDayRepository interface {
	Save(records []VisitsStatisticsDayModel) error
	PageQuery(params CommonSearch) (list []VisitsStatisticsDayModel, total int, err error)
}
