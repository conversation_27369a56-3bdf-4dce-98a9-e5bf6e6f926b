package oldreport

import "gitee.com/golang-module/carbon/v2"

type OrderTwo struct {
	Date                 int `json:"date"`                    // 日期 20171010
	Fid                  int `json:"fid"`                     // 用户ID
	ResellerId           int `json:"reseller_id"`             // 分销商ID
	Lid                  int `json:"lid"`                     // 景区ID
	Tid                  int `json:"tid"`                     // 门票ID
	PayWay               int `json:"pay_way"`                 // 支付方式
	Channel              int `json:"channel"`                 // 销售渠道
	OrderTicket          int `json:"order_ticket"`            // 预订门票数
	CancelTicket         int `json:"cancel_ticket"`           // 取消门票数
	RevokeTicket         int `json:"revoke_ticket"`           // 撤销门票数
	CostMoney            int `json:"cost_money"`              // 预订花费金额
	SaleMoney            int `json:"sale_money"`              // 预定金额
	CancelCostMoney      int `json:"cancel_cost_money"`       // 取消花费金额
	CancelSaleMoney      int `json:"cancel_sale_money"`       // 取消金额
	RevokeCostMoney      int `json:"revoke_cost_money"`       // 撤销花费金额
	RevokeSaleMoney      int `json:"revoke_sale_money"`       // 撤销金额
	ServiceMoney         int `json:"service_money"`           // 退款服务费
	AfterSaleTicketNum   int `json:"after_sale_ticket_num"`   // 售后数量
	AfterSaleRefundMoney int `json:"after_sale_refund_money"` // 售后退回金额
	AfterSaleIncomeMoney int `json:"after_sale_income_money"` // 售后收入金额
}

type OrderTwoRepo interface {
	Paginate(startTime, endTime carbon.Carbon, fid, tid int, groupBy []string, sumBy []string, page, pageSize int) ([]OrderTwo, error)
	Statistic(startTime, endTime carbon.Carbon, fid, tid int, resellerIds []int, isNotPackChild bool) (OrderTwo, error)
	GetStatisticTicketIds(startTime, endTime carbon.Carbon, fids []int) ([]int, error)
}
