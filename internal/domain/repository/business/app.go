package business

type AppModel struct {
	Id        int    `json:"id"`         // 主键ID
	AppId     string `json:"app_id"`     // 应用唯一标识
	AppSecret string `json:"app_secret"` // 应用密钥
	AppName   string `json:"app_name"`   // 应用名称
	CreatedAt string `json:"created_at"` // 创建时间
	UpdatedAt string `json:"updated_at"` // 更新时间
}

type AppRepository interface {
	GetAppByAppId(appId string) (*AppModel, error)
	Set(appId string, appSecret string, appName string) error
}
