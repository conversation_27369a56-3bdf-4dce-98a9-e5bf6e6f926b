package business

type AccessWhiteListModel struct {
	Id         int    `json:"id"`          // 主键ID
	MerchantId int    `json:"merchant_id"` // 商户ID
	AccessType int    `json:"access_type"` // 访问类型: 1-正常, 2-强制跳转
	Status     int    `json:"status"`      // 状态: 1-启用, 0-禁用
	CreatedAt  string `json:"created_at"`  // 创建时间
	UpdatedAt  string `json:"updated_at"`  // 更新时间
}
type CommonSearch struct {
	MerchantIds []int `json:"merchant_ids"` // 商户ID
	AccessType  *int  `json:"access_type"`  // 访问类型: 1-正常, 2-强制跳转
	Status      *int  `json:"status"`       // 状态: 1-启用, 0-禁用
	PageNum     *int  `json:"page_num"`     // 页码
	PageSize    *int  `json:"page_size"`    // 每页数量
}

type AccessWhiteListRepository interface {
	Set(merchantId int, accessType int, status int) error
	Get(merchantId int) (*AccessWhiteListModel, error)
	GetByPage(pageNum, pageSize int) ([]AccessWhiteListModel, error)
	GetList(params CommonSearch) (list []AccessWhiteListModel, total int, err error)
}
