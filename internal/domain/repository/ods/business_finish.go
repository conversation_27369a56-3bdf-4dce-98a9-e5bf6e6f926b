package ods

import "time"

type BusinessFinishModelData struct {
	BusinessModelDataBase
	FinishNo   int                            `json:"finish_no"`   //完结单号
	Payload    BusinessFinishModelDataPayload `json:"payload"`     //扩展数据
	FinishedAt time.Time                      `json:"finished_at"` //完结时间
}
type BusinessFinishModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessFinishModelDataPayloadDistributionChain `json:"distribution_chain"`
}
type BusinessFinishModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息
	BusinessModelDataPayloadDistributionChainDiscount
}
type BusinessFinishRepository interface {
	//插入数据
	InsertMore(records []BusinessFinishModelData) error
}
