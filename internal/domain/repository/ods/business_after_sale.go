package ods

import "time"

type BusinessAfterSaleModelData struct {
	BusinessModelDataBase
	AfterSaleNo  int                               `json:"after_sale_no"`  //售后单号
	Payload      BusinessAfterSaleModelDataPayload `json:"payload"`        //扩展数据
	AfterSaledAt time.Time                         `json:"after_saled_at"` //售后时间
}

type BusinessAfterSaleModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessAfterSaleModelDataPayloadDistributionChain `json:"distribution_chain"`
}

type BusinessAfterSaleModelDataPayloadDistributionChain struct {
	BusinessModelDataPayloadDistributionChain
	BusinessModelDataPayloadDistributionChainAfterSale
}
type BusinessAfterSaleRepository interface {
	//插入数据
	InsertMore(records []BusinessAfterSaleModelData) error
}
