package ods

type BusinessModelDataBase struct {
	OrderNo           string `json:"order_no"`            //订单号
	PoiId             int    `json:"poi_id"`              //PoiID
	SpuId             int    `json:"spu_id"`              //SpuID
	SkuId             int    `json:"sku_id"`              //SkuID
	SaleChannel       int    `json:"sale_channel"`        //销售渠道
	ApplyDid          int    `json:"apply_did"`           //订单供应商id
	ProductType       string `json:"product_type"`        //产品线类型
	SubType           int    `json:"sub_type"`            //产品线子类型
	Count             int    `json:"count"`               //数量
	OperateChannel    int    `json:"operate_channel"`     //操作渠道
	ExternalOperateNo string `json:"external_operate_no"` //外部操作单号
	OperatorId        int    `json:"operator_id"`         //操作人ID
	SellOperatorId    int    `json:"sell_operator_id"`    //售票员
	SellSiteId        int    `json:"sell_site_id"`        //售票站点
	OperateSiteId     int    `json:"operate_site_id"`     //操作站点
	TradeNo           string `json:"trade_no"`            // 交易单号
}

type BusinessModelDataOperateInfo struct {
	OperatorId     int `json:"operator_id"`      //操作人ID
	SellOperatorId int `json:"sell_operator_id"` //售票员
	SellSiteId     int `json:"sell_site_id"`     //售票站点
	OperateSiteId  int `json:"operate_site_id"`  //操作站点
}

// 扩展字段
type BusinessModelDataPayload struct {
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessModelDataPayloadDistributionChain `json:"distribution_chain"`
}

// 套票信息
type BusinessModelDataPayloadPackTicket struct {
	IfPack                 int    `json:"if_pack"`             //主票和子票标记， 0默认 1是主票 2子票
	ParentOrderNo          string `json:"parent_order_no"`     //主订单号
	ParentOrderProductType string `json:"parent_product_type"` //主订单产品类型
	ParentOrderApplyDid    int    `json:"parent_apply_did"`    //主订单供应商id
}

// 套票信息
type BusinessModelDataPayloadGroupsTag struct {
	//标签分组信息
	GroupsTag []GroupTag `json:"groups_tag"`
}

// 分组标签信息
type GroupTag struct {
	//标签标识code
	Code string `json:"code"`
	//标签分组标识code
	Group string `json:"group"`
}

// 分销链
type BusinessModelDataPayloadDistributionChain struct {
	SellerId       int  `json:"seller_id"`        //卖家ID
	BuyerId        int  `json:"buyer_id"`         //买家ID
	Level          int  `json:"level"`            //分销链的层级
	PayMode        int  `json:"pay_mode"`         //卖出支付方式
	CostPrice      int  `json:"cost_price"`       //买入支付金额
	SalePrice      int  `json:"sale_price"`       //卖出支付金额
	IsSanKe        bool `json:"is_san_ke"`        //是否是末级散客
	OperatorId     int  `json:"operator_id"`      //操作人ID
	SellOperatorId int  `json:"sell_operator_id"` //售票员
	SellSiteId     int  `json:"sell_site_id"`     //售票站点
	OperateSiteId  int  `json:"operate_site_id"`  //操作站点
	IsAddSplit     bool `json:"is_add_split"`     //是否是补全的链路
}

// 分销链优惠信息
type BusinessModelDataPayloadDistributionChainDiscount struct {
	CouponPrice    int                                                       `json:"coupon_price"`    //旧版优惠券优惠金额
	DiscountPrice  int                                                       `json:"discount_price"`  //优惠金额
	DiscountDetail []BusinessModelDataPayloadDistributionChainDiscountDetail `json:"discount_detail"` //优惠明细
}

// 优惠明细
type BusinessModelDataPayloadDistributionChainDiscountDetail struct {
	Key   int `json:"key"`   //优惠序号
	Type  int `json:"type"`  //优惠类型
	Price int `json:"price"` //优惠金额
}

// 售后信息
type BusinessModelDataPayloadDistributionChainAfterSale struct {
	AfterSaleCode        string `json:"after_sale_code"`         //售后编号
	AfterSaleNum         int    `json:"after_sale_num"`          //售后票数
	AfterSaleIncomePrice int    `json:"after_sale_income_price"` //售后收入金额
	AfterSaleRefundPrice int    `json:"after_sale_refund_price"` //售后退回金额
}

type BusinessModelDataPayloadDistributionChainFee struct {
	Fee int `json:"fee"` // 卖出手续费
}

type TmpListItem struct {
	Total int    `json:"total"`
	Str   string `json:"str"`
}
