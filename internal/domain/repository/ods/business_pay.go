package ods

import (
	"time"
)

type BusinessPayModelData struct {
	BusinessModelDataBase
	PayNo   int                         `json:"pay_no"`  //支付单号
	Payload BusinessPayModelDataPayload `json:"payload"` //扩展数据
	PaidAt  time.Time                   `json:"paid_at"` //支付时间
}
type BusinessPayModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessPayModelDataPayloadDistributionChain `json:"distribution_chain"`
}
type BusinessPayModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息
	BusinessModelDataPayloadDistributionChainDiscount
}

type BusinessPayRepository interface {
	InsertMore(records []BusinessPayModelData) error
}
