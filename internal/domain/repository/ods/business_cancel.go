package ods

import "time"

type BusinessCancelModelData struct {
	BusinessModelDataBase
	CancelNo    int                            `json:"cancel_no"`    //取消单号
	Payload     BusinessCancelModelDataPayload `json:"payload"`      //扩展数据
	CancelledAt time.Time                      `json:"cancelled_at"` //取消时间
}
type BusinessCancelModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessCancelModelDataPayloadDistributionChain `json:"distribution_chain"`
}
type BusinessCancelModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息
	BusinessModelDataPayloadDistributionChainDiscount
	//手续费
	BusinessModelDataPayloadDistributionChainFee
}

type BusinessCancelRepository interface {
	//插入数据
	InsertMore(records []BusinessCancelModelData) error
}
