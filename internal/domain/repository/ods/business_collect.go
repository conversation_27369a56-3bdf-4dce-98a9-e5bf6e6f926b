package ods

import "time"

type BusinessCollectModelData struct {
	BusinessModelDataBase
	CollectNo   int                             `json:"collect_no"`   //取票单号
	Payload     BusinessCollectModelDataPayload `json:"payload"`      //扩展数据
	CollectedAt time.Time                       `json:"collected_at"` //取票时间
}
type BusinessCollectModelDataPayload struct {
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessCollectModelDataPayloadDistributionChain `json:"distribution_chain"`
}
type BusinessCollectModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息，只有旧版优惠券优惠信息
	CouponPrice int `json:"coupon_price"` //旧版优惠券优惠金额
}

type BusinessCollectRepository interface {
	//插入数据
	InsertMore(records []BusinessCollectModelData) error
}
