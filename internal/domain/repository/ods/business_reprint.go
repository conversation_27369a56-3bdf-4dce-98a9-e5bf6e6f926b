package ods

import "time"

type BusinessReprintModelData struct {
	BusinessModelDataBase
	ReprintNo   int                             `json:"reprint_no"`   //重打印单号
	Payload     BusinessReprintModelDataPayload `json:"payload"`      //扩展数据
	ReprintedAt time.Time                       `json:"reprinted_at"` //重打印时间
}

type BusinessReprintModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessReprintModelDataPayloadDistributionChain `json:"distribution_chain"`
}

type BusinessReprintModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息
	BusinessModelDataPayloadDistributionChainDiscount
}

type BusinessReprintRepository interface {
	//插入数据
	InsertMore(records []BusinessReprintModelData) error
}
