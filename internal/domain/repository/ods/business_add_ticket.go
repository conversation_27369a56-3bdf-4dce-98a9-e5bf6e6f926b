package ods

import "time"

type BusinessAddTicketModelData struct {
	BusinessModelDataBase
	AddTicketNo   int                               `json:"add_ticket_no"`   //加票单号
	Payload       BusinessAddTicketModelDataPayload `json:"payload"`         //扩展数据
	AddedTicketAt time.Time                         `json:"added_ticket_at"` //加票时间
}

type BusinessAddTicketModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessModelDataPayloadDistributionChain `json:"distribution_chain"`
}

type BusinessAddTicketRepository interface {
	//插入数据
	InsertMore(records []BusinessAddTicketModelData) error
}
