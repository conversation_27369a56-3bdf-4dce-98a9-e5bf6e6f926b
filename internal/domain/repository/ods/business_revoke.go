package ods

import "time"

type BusinessRevokeModelData struct {
	BusinessModelDataBase
	RevokeNo  int                            `json:"revoke_no"`  //撤销撤改单号
	Payload   BusinessRevokeModelDataPayload `json:"payload"`    //扩展数据
	RevokedAt time.Time                      `json:"revoked_at"` //撤销撤改时间
}

type BusinessRevokeModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessRevokeModelDataPayloadDistributionChain `json:"distribution_chain"`
}

type BusinessRevokeModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息
	BusinessModelDataPayloadDistributionChainDiscount
	//手续费
	BusinessModelDataPayloadDistributionChainFee
}

type BusinessRevokeRepository interface {
	//插入数据
	InsertMore(records []BusinessRevokeModelData) error
}
