package ods

import "time"

type BusinessVerifyModelData struct {
	BusinessModelDataBase
	VerifyNo   int                            `json:"verify_no"`   //核销单号
	Payload    BusinessVerifyModelDataPayload `json:"payload"`     //扩展数据
	VerifiedAt time.Time                      `json:"verified_at"` //核销时间
}

type BusinessVerifyModelDataPayload struct {
	//分组标签信息
	BusinessModelDataPayloadGroupsTag
	//套票信息
	BusinessModelDataPayloadPackTicket
	DistributionChain []BusinessVerifyModelDataPayloadDistributionChain `json:"distribution_chain"`
}

type BusinessVerifyModelDataPayloadDistributionChain struct {
	//分销链信息
	BusinessModelDataPayloadDistributionChain
	//分销链优惠信息
	BusinessModelDataPayloadDistributionChainDiscount
}

type BusinessVerifyRepository interface {
	//插入数据
	InsertMore(records []BusinessVerifyModelData) error
}
