package dimensionscheme

type PoGroupRelation struct {
	SchemeId       int `json:"scheme_id"`
	GroupId        int `json:"group_id"`
	DimensionValue int `json:"dimension_value"`
}

type GroupRelationRepository interface {
	ListBySchemeId(schemeId int) ([]PoGroupRelation, error)
	ListBySchemeIdAndDimensionValues(schemeId int, dimensionValues []int) ([]PoGroupRelation, error)
	BatchSaveWithCleanBySchemeId(schemeId int, dimensionGroupRelations []PoGroupRelation) error
	DeleteBySchemeId(schemeId int) error
}
