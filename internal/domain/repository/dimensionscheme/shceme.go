package dimensionscheme

type PoScheme struct {
	Id            int    `json:"id"`
	MerchantId    int    `json:"merchant_id"`
	DimensionType int    `json:"dimension_type"` // 1: spu, 2: sku, 3: 支付方式, 4: 订单渠道
	Name          string `json:"name"`
}

type SchemeRepository interface {
	Insert(merchantId int, dimensionType int, name string) (int, error)
	Modify(id int, name string) error
	Delete(id int) error
	Detail(id int) (*PoScheme, error)
	Pagination(merchantId int, dimensionType int, page int, pageSize int) ([]PoScheme, error)
	Count(merchantId int, dimensionType int) (int, error)
	ListByIds(ids []int) ([]PoScheme, error)
}
