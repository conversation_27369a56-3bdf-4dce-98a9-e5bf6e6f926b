package dimensionscheme

type PoGroup struct {
	Id       int    `json:"id"`
	SchemeId int    `json:"scheme_id"`
	Name     string `json:"name"`
}

type GroupRepository interface {
	Insert(schemeId int, name string) (int, error)
	Modify(id int, name string) error
	Delete(id int) error
	DeleteBySchemeId(schemeId int) error
	Detail(id int) (*PoGroup, error)
	BatchSave(groups []*PoGroup) error
	Pagination(schemeId int, page int, pageSize int) ([]PoGroup, error)
	Count(schemeId int) (int, error)
	ListByIds(ids []int) ([]PoGroup, error)
}
