package dimensionscheme

type PoTagRelation struct {
	MerchantId   int    `json:"merchant_id" gorm:"column:merchant_id;comment:商户ID"`
	Source       int    `json:"source" gorm:"column:source;comment:来源"`
	TagGroupCode string `json:"tag_group_code" gorm:"column:tag_group_code;comment:标签组ID"`
	TagCode      string `json:"tag_code" gorm:"column:tag_code;comment:标签ID"`
	SubjectId    int    `json:"subject_id" gorm:"column:subject_id;comment:维度值"`
}

type TagRelationRepository interface {
	TableName() string
	GetDatabaseName() string

	InsertAll(tx interface{}, tagRelations []PoTagRelation) error
	DeleteByTagGroupCode(tx interface{}, merchantId int, tagGroupCode string, source int) error
	DeleteByTagCode(merchantId int, tagCode string, source int) error

	// FindTagGroupsByMerchantId 根据商户ID查询所有标签组
	FindTagGroupsByMerchantId(merchantId int, source int) ([]string, error)

	// DeleteByMerchantIdAndTagGroupCodes 根据商户ID和标签组编码列表删除标签关联
	DeleteByMerchantIdAndTagGroupCodes(merchantId int, tagGroupCodes []string, source int) error

	// GetSubjectIdListByMerchantId 根据商户ID、来源、标签组编码和标签编码查询关联的维度值列表
	GetSubjectIdListByMerchantId(merchantId int, source int, tagGroupCode string, tagCode string) ([]PoTagRelation, error)
}
