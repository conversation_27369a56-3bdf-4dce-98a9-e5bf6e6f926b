package report

type TemplateDelete struct {
	MerchantId int `json:"merchant_id"`
	MemberId   int `json:"member_id"`
	Id         int `json:"id"`
}

type TemplateCreate struct {
	MerchantId int         `json:"merchant_id"`
	MemberId   int         `json:"member_id"`
	Name       string      `json:"name"`
	Category   int         `json:"category"`
	Payload    interface{} `json:"payload"`
	OperatorId int         `json:"operator_id"`
}

type TemplateMerchantAndIdFind struct {
	MerchantId int `json:"merchant_id"`
	MemberId   int `json:"member_id"`
	Id         int `json:"id"`
}

type TemplateMerchantFind struct {
	MerchantId int `json:"merchant_id"`
	MemberId   int `json:"member_id"`
	Category   int `json:"category"`
}

type TemplateUpdate struct {
	MerchantId int         `json:"merchant_id"`
	MemberId   int         `json:"member_id"`
	Id         int         `json:"id"`
	Name       string      `json:"name"`
	Payload    interface{} `json:"payload"`
	OperatorId int         `json:"operator_id"`
}

type TemplateListItem struct {
	MerchantId int         `json:"merchant_id"`
	MemberId   int         `json:"member_id"`
	Id         int         `json:"id"`
	Name       string      `json:"name"`
	Payload    interface{} `json:"payload"`
	OperatorId int         `json:"operator_id"`
	Category   int         `json:"category"`
}

type TemplateConfigRepository interface {
	//创建模板
	Create(create TemplateCreate) (int, error)
	//删除模板
	Delete(deleteInfo TemplateDelete) error
	//更新模板
	Update(updateInfo TemplateUpdate) error
	//商户单条模板查询
	MerchantFirst(findInfo TemplateMerchantAndIdFind) (*TemplateListItem, error)
	//商户单条模板查询(主账号查询)
	MainMerchantFirst(findInfo TemplateMerchantAndIdFind) (*TemplateListItem, error)
	//商户多条模板查询
	MerchantFind(findInfo TemplateMerchantFind) ([]TemplateListItem, error)
	//商户全部模板查询
	MerchantFindAll(merchantId int) ([]TemplateListItem, error)
	//根据ID批量更新
	BatchMerchantUpdate(data map[int]interface{}) error
	//获取所有配置了模板的商户ID
	GetAllMerchantIds() ([]int, error)
}
