package config

type MerchantConfig struct {
	Id         int         `json:"id"`
	Key        string      `json:"key"`
	MerchantId int         `json:"merchant_id"`
	MemberId   int         `json:"member_id"`
	Payload    interface{} `json:"payload"`
}

type MerchantConfigRepository interface {
	FindManyByKey(key string) ([]MerchantConfig, error)
	FindManyByKeyAndMerchantIds(key string, merchantIds []int) ([]MerchantConfig, error)
	Set(key string, merchantId int, memberId int, value interface{}) error
	FindManyByKeyAndMerchantIdsAndMemberIds(key string, merchantIds []int, memberIds []int) ([]MerchantConfig, error)
	PaginateByKey(key string, pageNum int, pageSize int) ([]MerchantConfig, error)
}
