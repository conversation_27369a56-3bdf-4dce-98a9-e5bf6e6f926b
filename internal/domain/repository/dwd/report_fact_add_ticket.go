package dwd

import "time"

type ReportFactAddTicketModel struct {
	CommonModelBase
	AddTicketNo   int                             `json:"add_ticket_no"`   // 业务单号
	Payload       ReportFactAddTicketModelPayload `json:"payload"`         // 扩展数据
	AddedTicketAt time.Time                       `json:"added_ticket_at"` // 操作时间
}

type ReportFactAddTicketModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	//加票没有优惠信息，这边先注释
	//CommonModelPayloadDiscountDetail
	//CommonModelPayloadOrderCoupon
}

type AddTicketRepository interface {
	Insert(records []ReportFactAddTicketModel) error
}
