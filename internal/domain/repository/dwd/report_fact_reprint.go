package dwd

import "time"

type ReportFactReprintModel struct {
	CommonModelBase
	CommonModelDiscount
	ReprintNo   int                           `json:"reprint_no"`   // 重打印单号
	Payload     ReportFactReprintModelPayload `json:"payload"`      // 扩展数据
	ReprintedAt time.Time                     `json:"reprinted_at"` // 重打印时间
}

type ReportFactReprintModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type ReprintRepository interface {
	Insert(records []ReportFactReprintModel) error
}
