package dwd

import "time"

type ReportFactVerifyModel struct {
	CommonModelBase
	CommonModelDiscount
	VerifyNo   int                          `json:"verify_no"`   // 业务单号
	Payload    ReportFactVerifyModelPayload `json:"payload"`     // 扩展数据
	VerifiedAt time.Time                    `json:"verified_at"` // 操作时间
}

type ReportFactVerifyModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type VerifyRepository interface {
	Insert(records []ReportFactVerifyModel) error
}
