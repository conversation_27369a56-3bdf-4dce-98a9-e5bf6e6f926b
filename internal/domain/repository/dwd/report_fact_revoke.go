package dwd

import "time"

type ReportFactRevokeModel struct {
	CommonModelBase
	CommonModelDiscount
	CommonModelFee
	RevokeNo  int                          `json:"revoke_no"`  // 业务单号
	Payload   ReportFactRevokeModelPayload `json:"payload"`    // 扩展数据
	RevokedAt time.Time                    `json:"revoked_at"` // 操作时间
}

type ReportFactRevokeModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type RevokeRepository interface {
	Insert(records []ReportFactRevokeModel) error
}
