package dwd

import "time"

type ReportFactCollectModel struct {
	CommonModelBase
	CommonModelDiscount
	CollectNo   int                           `json:"collect_no"`   // 取票单号
	Payload     ReportFactCollectModelPayload `json:"payload"`      // 扩展数据
	CollectedAt time.Time                     `json:"collected_at"` // 取票时间
}

type ReportFactCollectModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type CollectRepository interface {
	Insert(records []ReportFactCollectModel) error
}
