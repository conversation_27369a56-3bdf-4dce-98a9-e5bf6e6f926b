package dwd

import "time"

type ReportFactAfterSaleModel struct {
	CommonModelBase
	CommonModelDiscount
	AfterSaleNo  int                             `json:"after_sale_no"`  // 业务单号
	Payload      ReportFactAfterSaleModelPayload `json:"payload"`        // 扩展数据
	AfterSaledAt time.Time                       `json:"after_saled_at"` // 操作时间
}

type ReportFactAfterSaleModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelDataPayloadDistributionChainAfterSale
	//加票没有优惠信息，这边先注释
	//CommonModelPayloadDiscountDetail
	//CommonModelPayloadOrderCoupon
}

type AfterSaleRepository interface {
	Insert(records []ReportFactAfterSaleModel) error
}
