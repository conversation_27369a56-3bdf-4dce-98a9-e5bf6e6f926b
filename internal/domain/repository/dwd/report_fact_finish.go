package dwd

import "time"

type ReportFactFinishModel struct {
	CommonModelBase
	CommonModelDiscount
	FinishNo   int                          `json:"finish_no"`   // 业务单号
	Payload    ReportFactFinishModelPayload `json:"payload"`     // 扩展数据
	FinishedAt time.Time                    `json:"finished_at"` // 操作时间
}

type ReportFactFinishModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type FinishRepository interface {
	Insert(records []ReportFactFinishModel) error
}
