package dwd

import "time"

type ReportFactCancelModel struct {
	CommonModelBase
	CommonModelDiscount
	CommonModelFee
	CancelNo    int                          `json:"cancel_no"`    // 业务单号
	Payload     ReportFactCancelModelPayload `json:"payload"`      // 扩展数据
	CancelledAt time.Time                    `json:"cancelled_at"` // 操作时间
}

type ReportFactCancelModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type CancelRepository interface {
	Insert(records []ReportFactCancelModel) error
}
