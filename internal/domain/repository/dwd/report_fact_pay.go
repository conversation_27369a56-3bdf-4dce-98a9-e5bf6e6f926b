package dwd

import "time"

type ReportFactPayModel struct {
	CommonModelBase
	CommonModelDiscount
	PayNo   int                       `json:"pay_no"`  // 支付单号
	Payload ReportFactPayModelPayload `json:"payload"` // 扩展数据
	PaidAt  time.Time                 `json:"paid_at"` // 支付时间
}

type ReportFactPayModelPayload struct {
	CommonModelPayloadGroupsTag
	CommonModelPayloadProductRules
	CommonModelPayloadDiscountDetail
	CommonModelPayloadOrderCoupon
}

type PayRepository interface {
	Insert(records []ReportFactPayModel) error
}
