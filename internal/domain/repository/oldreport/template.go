package oldreport

type PftReportSearchConfig struct {
	Id           int    `json:"id"`
	Fid          int    `json:"fid"`
	MemberId     int    `json:"member_id"`
	Item         string `json:"item"`
	Target       string `json:"target"`
	Name         string `json:"name"`
	Type         int    `json:"type"`
	Status       int    `json:"status"`
	DateType     int    `json:"date_type"`
	ItemValue    string `json:"item_value"`
	IsShare      int    `json:"is_share"`
	TempType     int    `json:"temp_type"`
	ItemNotValue string `json:"item_not_value"`
	ApplyToStaff string `json:"apply_to_staff"`
	ExtConfig    string `json:"ext_config"`
}

type PftReportSearchConfigRepository interface {
	Paginate(fid []int, reportType int, page, pageSize int) ([]PftReportSearchConfig, error)
}
