package oldreport

import (
	"gitee.com/golang-module/carbon/v2"
)

type PftReportTouristSourceAreaDetail struct {
	Id              int    `json:"id"`
	DateHour        int    `json:"date_hour"`
	DateDay         int    `json:"date_day"`
	DateMonth       int    `json:"date_month"`
	DateYear        int    `json:"date_year"`
	SupplierId      int    `json:"supplier_id"`
	ProductLid      int    `json:"product_lid"`
	ProductTid      int    `json:"product_tid"`
	OpType          int    `json:"op_type"`
	OpTime          int    `json:"op_time"`
	ProductRuleType int    `json:"product_rule_type"`
	LevelTop        int    `json:"level_top"`
	Level1          int    `json:"level_1"`
	Level2          int    `json:"level_2"`
	Level3          int    `json:"level_3"`
	TouristNum      int    `json:"tourist_num"`
	Ordernum        string `json:"ordernum"`
	ChkCode         string `json:"chk_code"`
	IdType          int    `json:"id_type"`
	IDNumber        string `json:"id_number"`
	Idx             int    `json:"idx"`
	TouristOpInfo   string `json:"tourist_op_info"`
	CreateTime      int    `json:"create_time"`
}

type TouristOpInfo struct {
	OpSite    int `json:"op_site"`
	OpMember  int `json:"op_member"`
	OpChannel int `json:"op_channel"`
}

type PftReportTouristSourceAreaDetailRepository interface {
	Paginate(startTime carbon.Carbon, fid int, page, pageSize int) ([]PftReportTouristSourceAreaDetail, error)
}
