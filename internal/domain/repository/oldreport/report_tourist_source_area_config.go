package oldreport

type PftReportTouristSourceAreaConfig struct {
	Id         int    `json:"id"`
	SupplierId int    `json:"supplier_id"`
	Type       int    `json:"type"`
	Content    string `json:"content"`
	CreateTime int    `json:"create_time"`
	UpdateTime int    `json:"update_time"`
}

type PromotionPeriod struct {
	Percent     int    `json:"percent"`
	BeginTime   string `json:"begin_time"`
	EndTime     string `json:"end_time"`
	CurrentDate string `json:"current_date"`
}

type PftReportTouristSourceAreaConfigRepository interface {
	Paginate(fid []int, isGroupFid bool, page, pageSize int) ([]PftReportTouristSourceAreaConfig, error)
}
