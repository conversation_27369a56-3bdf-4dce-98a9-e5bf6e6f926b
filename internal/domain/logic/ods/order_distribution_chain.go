package ods

import (
	"report-service/pkg/sdk/api/orderaidssplitqueryservice"
	"report-service/pkg/utils"
)

type OrderDistributionChainInfoData struct {
	ApplyDid           int    `json:"apply_did"`
	BuyerId            int    `json:"buyerid"`
	CostMoney          int    `json:"costMoney"`
	Id                 int    `json:"id"`
	Level              int    `json:"level"`
	MemberRelationship int    `json:"memberRelationship"`
	OrderMonth         int    `json:"orderMonth"`
	OrderTime          int    `json:"orderTime"`
	OrderId            string `json:"orderid"`
	PMode              int    `json:"pmode"`
	SaleMoney          int    `json:"saleMoney"`
	SellerId           int    `json:"sellerid"`
}

func OrderDistributionChainByOrderNos(orderNos []string) (map[string][]OrderDistributionChainInfoData, error) {
	distributionChainData, err := orderaidssplitqueryservice.QueryOrderDistributionChainByOrderNos(orderNos)
	if err != nil {
		return nil, err
	}
	var result []OrderDistributionChainInfoData
	err = utils.JsonConvertor(distributionChainData, &result)
	if err != nil {
		return nil, err
	}

	var data = make(map[string][]OrderDistributionChainInfoData)
	for _, item := range result {
		data[item.OrderId] = append(data[item.OrderId], item)
	}

	return data, nil
}
