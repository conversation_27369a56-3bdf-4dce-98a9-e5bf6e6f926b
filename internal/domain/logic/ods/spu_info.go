package ods

import (
	"report-service/pkg/sdk/api/ticketservice"
	"report-service/pkg/utils"
)

type SpuInfoItem struct {
	Id       int    `json:"id"`
	Title    string `json:"title"`
	PType    string `json:"pType"`
	SubType  int    `json:"subType"`
	SalerId  int    `json:"salerid"`
	ApplyDid int    `json:"applyDid"`
}

func SpuInfoBySkuIds(skuIds []int) (map[int]SpuInfoItem, error) {
	var result []SpuInfoItem
	spuData, err := ticketservice.QueryLandInfoByTicketIds(skuIds)
	if err != nil {
		return nil, err
	}

	err = utils.JsonConvertor(spuData, &result)
	if err != nil {
		return nil, err
	}

	var data = make(map[int]SpuInfoItem)
	for _, item := range result {
		data[item.Id] = item
	}

	return data, nil
}
