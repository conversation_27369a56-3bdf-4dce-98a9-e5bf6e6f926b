package ods

import (
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"strconv"
	"strings"
)

func ProductTypeTitle(productType string, subtype int) string {
	typeStr := productType
	if subtype != 0 {
		typeStr = fmt.Sprintf("%s-%d", typeStr, subtype)
	}
	//返回名称
	typeMap := enum.ProductType
	if title, ok := typeMap[typeStr]; ok {
		return title
	}

	return ""
}

func StrToProductTypeAndSubType(typeStr string) (string, int, error) {
	typeMap := enum.ProductType
	if _, ok := typeMap[typeStr]; !ok {
		return "", 0, errors.New("type str is not exists")
	}
	//存在则解析类型和子类型
	parts := strings.SplitN(typeStr, "-", 2)
	if len(parts) == 1 {
		return parts[0], 0, nil
	}
	if len(parts) >= 2 {
		subType, err := strconv.Atoi(parts[1])
		if err != nil {
			return "", 0, err
		}
		return parts[0], subType, nil
	}
	return "", 0, nil
}
