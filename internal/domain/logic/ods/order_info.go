package ods

import (
	"report-service/internal/domain/logic/ods/business"
	"report-service/pkg/sdk/api/orderqueryservice"
	"report-service/pkg/utils"
)

type OrderInfoData struct {
	Addon         OrderInfoAddonData     `json:"addon"`
	Aid           int                    `json:"aid"`
	ApplyDid      int                    `json:"applyDid"`
	ApplyInfo     OrderInfoApplyInfoData `json:"applyInfo"`
	BeginTime     string                 `json:"begintime"`
	EndTime       string                 `json:"endtime"`
	Code          int                    `json:"code"`
	ContactTel    string                 `json:"contacttel"`
	FxDetails     OrderInfoFxDetailsData `json:"fxDetails"`
	Id            int                    `json:"id"`
	SpuId         int                    `json:"lid"`
	Member        int                    `json:"member"`
	OrderTimeDate string                 `json:"ordertime"`
	OrderTime     int                    `json:"orderTime"`
	OrderMode     int                    `json:"ordermode"`
	OrderName     string                 `json:"ordername"`
	OrderNum      string                 `json:"ordernum"`
	OrderTel      string                 `json:"ordertel"`
	PayStatus     int                    `json:"payStatus"`
	PayTime       int                    `json:"payTime"`
	PayMode       int                    `json:"paymode"`
	PersonId      string                 `json:"personid"`
	Pid           int                    `json:"pid"`
	Playtime      string                 `json:"playtime"`
	ProductType   string                 `json:"productType"`
	RemoteNum     string                 `json:"remotenum"`
	ReMsg         int                    `json:"remsg"`
	SalerId       int                    `json:"salerid"`
	Status        int                    `json:"status"`
	SkuId         int                    `json:"tid"`
	TNum          int                    `json:"tnum"`
	TotalMoney    int                    `json:"totalmoney"`
	TPrice        int                    `json:"tprice"`
	TradeOrderId  string                 `json:"tradeOrderId"`
}

type OrderInfoAddonData struct {
	ApplyDid     int    `json:"applyDid"`
	Id           int    `json:"id"`
	IfPack       int    `json:"ifpack"`
	IfPrint      int    `json:"ifprint"`
	LinkOrderNum string `json:"linkOrdernum"`
	OrderId      string `json:"orderid"`
	PackOrder    string `json:"packOrder"`
}

type OrderInfoApplyInfoData struct {
	AddTime      string `json:"addtime"`
	ApplyId      int    `json:"applyId"`
	APrice       int    `json:"aprice"`
	CanRefund    int    `json:"canRefund"`
	CanTake      int    `json:"canTake"`
	CounterPrice int    `json:"counterPrice"`
	CurrentNum   int    `json:"currentNum"`
	Flag         int    `json:"flag"`
	Id           int    `json:"id"`
	LPrice       int    `json:"lprice"`
	OrderTime    int    `json:"orderTime"`
	OrderId      string `json:"orderid"`
	OriginNum    int    `json:"originNum"`
	Pid          int    `json:"pid"`
	Playtime     string `json:"playtime"`
	RefundNum    int    `json:"refundNum"`
	SalePrice    int    `json:"salePrice"`
	VerifiedNum  int    `json:"verifiedNum"`
}

type OrderInfoFxDetailsData struct {
	Aids       string `json:"aids"`
	AidsMoney  string `json:"aidsMoney"`
	AidsPrice  string `json:"aidsPrice"`
	ApplyDid   int    `json:"applyDid"`
	ConcatId   string `json:"concatId"`
	ExtContent string `json:"extContent"`
	Id         int    `json:"id"`
	Memo       string `json:"memo"`
	NoticeExt  string `json:"noticeExt"`
	OfflineExt string `json:"offlineExt"`
	OrderId    string `json:"orderid"`
	Origin     string `json:"origin"`
	PartnerExt string `json:"partnerExt"`
	ProductExt string `json:"productExt"`
	Series     string `json:"series"`
}

type OrderFxDetailExtContentFields struct {
	OrderExtContentDiscountFields
	//标签分组
	Tags []OrderFxDetailExtContentTags `json:"tags"`
}

type OrderFxDetailExtContentTags struct {
	//标签标识code
	Code string `json:"code"`
	//标签分组标识code
	Group string `json:"group"`
	//标签关联游客数量
	LinkNum int `json:"linkNum"`
	//标签名称
	Name string `json:"name"`
}

func OrderInfoByOrderNos(orderNos []string) (map[string]OrderInfoData, error) {
	var result []OrderInfoData
	orderData, err := orderqueryservice.CompleteOrderByOrderNos(orderNos)
	if err != nil {
		return nil, err
	}

	err = utils.JsonConvertor(orderData, &result)
	if err != nil {
		return nil, err
	}

	var data = make(map[string]OrderInfoData)
	for _, item := range result {
		data[item.OrderNum] = item
	}

	return data, nil
}

func OrderInfoByOrderNo(orderNo string) (*OrderInfoData, error) {
	var result []OrderInfoData
	orderData, err := orderqueryservice.CompleteOrderByOrderNos([]string{orderNo})
	if err != nil {
		return nil, err
	}
	err = utils.JsonConvertor(orderData, &result)
	if err != nil {
		return nil, err
	}
	for _, item := range result {
		if item.OrderNum == orderNo {
			return &item, nil
		}
	}

	return nil, nil
}

// getTagInfo 返回订单扩展详情内容中的标签信息。
// 如果没有标签信息，则返回空字符串。
// 该方法主要用于获取每个分组的标签信息。
func (o OrderFxDetailExtContentFields) GetGroupsTagList() (tags []business.GroupTag) {
	tags = []business.GroupTag{}
	if len(o.Tags) == 0 {
		return
	}
	list := make(map[string]bool)
	for _, tag := range o.Tags {
		//按分组标签的标识去重，每个分组标签只保留一个
		if _, ok := list[tag.Group]; ok {
			continue
		}
		list[tag.Group] = true
		tags = append(tags, business.GroupTag{
			Group: tag.Group,
			Code:  tag.Code,
		})
	}

	return
}
