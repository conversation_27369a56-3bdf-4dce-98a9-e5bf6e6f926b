package business

import "time"

type CollectData struct {
	DataBase
	CollectNo   int                `json:"collect_no"`   //取票单号
	Payload     CollectDataPayload `json:"payload"`      //扩展数据
	CollectedAt time.Time          `json:"collected_at"` //取票时间
}

type CollectDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []CollectDataPayloadDistributionChain `json:"distribution_chain"`
}

type CollectDataPayloadDistributionChain struct {
	//分销链信息
	DataPayloadDistributionChain
	//分销链优惠信息
	DataPayloadDistributionChainDiscount
}

func (b *CollectData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *CollectData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *CollectData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, CollectDataPayloadDistributionChain{
		DataPayloadDistributionChain: chain,
	})
	return
}

func (b *CollectData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
