package business

import "time"

type RevokeData struct {
	DataBase
	RevokeNo  int               `json:"revoke_no"`  //撤销撤改单号
	Payload   RevokeDataPayload `json:"payload"`    //扩展数据
	RevokedAt time.Time         `json:"revoked_at"` //撤销撤改时间
}

type RevokeDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []RevokeDataPayloadDistributionChain `json:"distribution_chain"`
}

type RevokeDataPayloadDistributionChain struct {
	//分销链信息
	DataPayloadDistributionChain
	//分销链优惠信息
	DataPayloadDistributionChainDiscount
	//手续费
	DataPayloadDistributionChainFee
}

func (b *RevokeData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *RevokeData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *RevokeData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, RevokeDataPayloadDistributionChain{
		DataPayloadDistributionChain: chain,
	})
	return
}

func (b *RevokeData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
