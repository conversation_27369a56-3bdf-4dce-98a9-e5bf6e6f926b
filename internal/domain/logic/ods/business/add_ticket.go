package business

import "time"

type AddTicketData struct {
	DataBase
	AddTicketNo   int                  `json:"add_ticket_no"`   //加票单号
	Payload       AddTicketDataPayload `json:"payload"`         //扩展数据
	AddedTicketAt time.Time            `json:"added_ticket_at"` //加票时间
}

type AddTicketDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []DataPayloadDistributionChain `json:"distribution_chain"`
}

func (b *AddTicketData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain
	}
	return chain
}

func (b *AddTicketData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index] = chain
	return
}

func (b *AddTicketData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, chain)
	return
}

func (b *AddTicketData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
