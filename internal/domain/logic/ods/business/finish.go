package business

import "time"

type FinishData struct {
	DataBase
	FinishNo   int               `json:"finish_no"`   //完结单号
	Payload    FinishDataPayload `json:"payload"`     //扩展数据
	FinishedAt time.Time         `json:"finished_at"` //完结时间
}
type FinishDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []FinishDataPayloadDistributionChain `json:"distribution_chain"`
}
type FinishDataPayloadDistributionChain struct {
	//分销链信息
	DataPayloadDistributionChain
	//分销链优惠信息
	DataPayloadDistributionChainDiscount
}

func (b *FinishData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *FinishData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *FinishData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, FinishDataPayloadDistributionChain{
		DataPayloadDistributionChain: chain,
	})
	return
}

func (b *FinishData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
