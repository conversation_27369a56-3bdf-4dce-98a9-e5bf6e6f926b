package business

import "time"

type CancelData struct {
	DataBase
	CancelNo    int               `json:"cancel_no"`    //取消单号
	Payload     CancelDataPayload `json:"payload"`      //扩展数据
	CancelledAt time.Time         `json:"cancelled_at"` //取消时间
}
type CancelDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []CancelDataPayloadDistributionChain `json:"distribution_chain"`
}
type CancelDataPayloadDistributionChain struct {
	//分销链信息
	DataPayloadDistributionChain
	//分销链优惠信息
	DataPayloadDistributionChainDiscount
	//手续费
	DataPayloadDistributionChainFee
}

func (b *CancelData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *CancelData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *CancelData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, CancelDataPayloadDistributionChain{
		DataPayloadDistributionChain: chain,
	})
	return
}

func (b *CancelData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
