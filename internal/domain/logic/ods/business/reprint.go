package business

import "time"

type ReprintData struct {
	DataBase
	ReprintNo   int                `json:"reprint_no"`   //重打印单号
	Payload     ReprintDataPayload `json:"payload"`      //扩展数据
	ReprintedAt time.Time          `json:"reprinted_at"` //重打印时间
}

type ReprintDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []ReprintDataPayloadDistributionChain `json:"distribution_chain"`
}

type ReprintDataPayloadDistributionChain struct {
	//分销链信息
	DataPayloadDistributionChain
	//分销链优惠信息
	DataPayloadDistributionChainDiscount
}

func (b *ReprintData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *ReprintData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *ReprintData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, ReprintDataPayloadDistributionChain{
		DataPayloadDistributionChain: chain,
	})
	return
}

func (b *ReprintData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
