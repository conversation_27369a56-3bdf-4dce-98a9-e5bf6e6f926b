package business

import "time"

type VerifyData struct {
	DataBase
	VerifyNo   int               `json:"verify_no"`   //核销单号
	Payload    VerifyDataPayload `json:"payload"`     //扩展数据
	VerifiedAt time.Time         `json:"verified_at"` //核销时间
}

type VerifyDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []VerifyDataPayloadDistributionChain `json:"distribution_chain"`
}

type VerifyDataPayloadDistributionChain struct {
	//分销链信息
	DataPayloadDistributionChain
	//分销链优惠信息
	DataPayloadDistributionChainDiscount
}

func (b *VerifyData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *VerifyData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *VerifyData) AddChain(chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, VerifyDataPayloadDistributionChain{
		DataPayloadDistributionChain: chain,
	})
	return
}

func (b *VerifyData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
