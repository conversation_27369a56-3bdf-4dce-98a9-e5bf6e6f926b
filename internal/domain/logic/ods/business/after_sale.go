package business

import "time"

type AfterSaleData struct {
	DataBase
	AfterSaleNo  int                  `json:"after_sale_no"`  //售后单号
	Payload      AfterSaleDataPayload `json:"payload"`        //扩展数据
	AfterSaledAt time.Time            `json:"after_saled_at"` //售后时间
}

type AfterSaleDataPayload struct {
	//分组标签信息
	DataPayloadGroupsTag
	//套票信息
	DataPayloadPackTicket
	DistributionChain []AfterSaleDataPayloadDistributionChain `json:"distribution_chain"`
}

type AfterSaleDataPayloadDistributionChain struct {
	DataPayloadDistributionChain
	DataPayloadDistributionChainAfterSale
}

func (b *AfterSaleData) GetChain() []DataPayloadDistributionChain {
	chain := make([]DataPayloadDistributionChain, len(b.Payload.DistributionChain))
	for index, distributionChain := range b.Payload.DistributionChain {
		chain[index] = distributionChain.DataPayloadDistributionChain
	}
	return chain
}

func (b *AfterSaleData) SetChain(index int, chain DataPayloadDistributionChain) {
	b.Payload.DistributionChain[index].DataPayloadDistributionChain = chain
	return
}

func (b *AfterSaleData) AddChain(chain DataPayloadDistributionChain) {
	//补全下售后信息在链路的完整性
	var afterSaleInfo DataPayloadDistributionChainAfterSale
	for _, tmp := range b.Payload.DistributionChain {
		if tmp.BuyerId == chain.SellerId && tmp.AfterSaleRefundPrice != 0 {
			afterSaleInfo.AfterSaleIncomePrice = tmp.AfterSaleRefundPrice
			afterSaleInfo.AfterSaleRefundPrice = 0
			afterSaleInfo.AfterSaleNum = tmp.AfterSaleNum
			afterSaleInfo.AfterSaleCode = tmp.AfterSaleCode
		}
	}
	b.Payload.DistributionChain = append(b.Payload.DistributionChain, AfterSaleDataPayloadDistributionChain{
		DataPayloadDistributionChain:          chain,
		DataPayloadDistributionChainAfterSale: afterSaleInfo,
	})
	return
}

func (b *AfterSaleData) GetOperateInfo() DataOperateInfo {
	return DataOperateInfo{
		OperatorId:     b.OperatorId,
		OperateSiteId:  b.OperateSiteId,
		SellOperatorId: b.SellOperatorId,
		SellSiteId:     b.SellSiteId,
	}
}
