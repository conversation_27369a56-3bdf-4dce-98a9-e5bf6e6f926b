package ods

import (
	"report-service/internal/domain/enum"
)

//生成业务操作号
func CreateBusinessOperationNumber(prefixType int, id int) int {
	var bitSize = enum.BusinessOperationNumberSize - enum.BusinessOperationNumberPrefixSize
	var operationNumber int
	operationNumber = prefixType<<bitSize + id

	return operationNumber
}

//获取前缀来源
func GetBusinessOperationNumberPrefixType(operationNumber int) int {
	var bitSize = enum.BusinessOperationNumberSize - enum.BusinessOperationNumberPrefixSize
	return operationNumber >> bitSize
}

//解析原始编号
func GetBusinessOperationNumberId(operationNumber int) int {
	var bitSize = enum.BusinessOperationNumberSize - enum.BusinessOperationNumberPrefixSize
	prefixType := GetBusinessOperationNumberPrefixType(operationNumber)
	return operationNumber - prefixType<<bitSize
}

//批量生产业务操作号
func BatchCreateBusinessOperationNumber(prefixType int, ids []int) map[int]int {
	var dataMap = make(map[int]int)
	for _, id := range ids {
		dataMap[id] = CreateBusinessOperationNumber(prefixType, id)
	}
	return dataMap
}
