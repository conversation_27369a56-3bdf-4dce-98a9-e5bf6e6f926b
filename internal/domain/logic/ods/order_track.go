package ods

import (
	"report-service/pkg/sdk/api/ordertrackqueryservice"
	"report-service/pkg/utils"
)

type OrderTrackInfoData struct {
	Action         int    `json:"action"`
	ApplyDid       int    `json:"applyDid"`
	BranchTerminal int    `json:"branchterminal"`
	ExtContent     string `json:"extContent"`
	ID             int    `json:"id"`
	IDCard         string `json:"idCard"`
	InsertTime     string `json:"inserttime"`
	LeftNum        int    `json:"leftNum"`
	Msg            string `json:"msg"`
	OperMember     int    `json:"operMember"`
	OrderMonth     int    `json:"orderMonth"`
	OrderTime      int    `json:"orderTime"`
	OrderNum       string `json:"ordernum"`
	SalerID        int    `json:"salerid"`
	Source         int    `json:"source"`
	SyncState      int    `json:"syncState"`
	Terminal       int    `json:"terminal"`
	TID            int    `json:"tid"`
	TNum           int    `json:"tnum"`
	UpdateTime     int    `json:"updateTime"`
}

// OperMemberInfo 操作人信息
type OperMemberInfo struct {
	InsertTime string `json:"inserttime"` //操作时间
	OperMember int    `json:"operMember"` //操作人
	SalerID    int    `json:"salerid"`    //操作站点id
	Id         int    `json:"id"`         //操作记录id
}

// 判断支付前操作
func (o *OperMemberInfo) IsOperationBeforePay(trackId int) bool {
	if trackId < o.Id {
		return true
	}
	return false
}

func QueryTrackInfoByOrderNoAndAction(orderNo string, action int) (*OrderTrackInfoData, error) {
	var result OrderTrackInfoData
	orderData, err := ordertrackqueryservice.QueryOrderTrackFirstInfo(orderNo, action)
	if err != nil {
		return nil, err
	}

	err = utils.JsonConvertor(orderData, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
