package ods

import (
	"errors"
	"report-service/pkg/sdk/api/ordercouponservice"
	"report-service/pkg/sdk/api/orderticketservice"
	"report-service/pkg/utils"
)

type OrderCouponData struct {
	Aid      int    `json:"aid"`
	Fid      int    `json:"fid"`
	Spu      int    `json:"lid"`
	Sku      int    `json:"tid"`
	EMoney   int    `json:"emoney"`
	OrderNum string `json:"ordernum"`
}

type OrderExtContentDiscountFields struct {
	//特价快照ID
	BargainPricePolicyId int `json:"bargainPricePolicyId"`
	//减免快照ID
	SpecifyReducedPolicyId int `json:"specifyReducedPolicyId"`
	//积分
	UsePoint bool `json:"usePoint"`
	//优惠券
	UseCoupon bool `json:"useCoupon"`
	//优惠
	UseDiscount bool `json:"useDiscount"`
	//统一营销优惠
	UseMarketingDiscount bool `json:"useMarketingDiscount"`
}

type OrderDiscountInfoData struct {
	CouponPrice int    `json:"couponPrice"`
	CouponType  int    `json:"couponType"`
	OrderNum    string `json:"orderNum"`
	SerialNum   int    `json:"serialNum"`
	Status      int    `json:"status"`
}

const (
	OrderDiscountStatusAll     = 0
	OrderDiscountStatusNotUsed = 1
	OrderDiscountStatusUsed    = 2
)

// 获取订单优惠券金额（优惠券，三亚会员卡优惠、微商城优惠券）
func OrderCouponPriceByOrderNos(orderNos []string) (map[string]map[int]map[int]int, error) {
	couponData, err := ordercouponservice.QueryOrderCouponByOrderNos(orderNos)
	if err != nil {
		return nil, err
	}
	var result []OrderCouponData
	err = utils.JsonConvertor(couponData, &result)
	if err != nil {
		return nil, err
	}

	//数据组合 data[订单号][供应商][分销商]=优惠信息
	var data = make(map[string]map[int]map[int]int)
	for _, item := range result {
		data[item.OrderNum] = make(map[int]map[int]int)
		data[item.OrderNum][item.Aid] = make(map[int]int)
		data[item.OrderNum][item.Aid][item.Fid] = item.EMoney
	}

	return data, nil
}

// 单笔订单获取优惠券优惠金额
func OrderCouponPriceByOrderNo(orderNo string) (OrderCouponData, error) {
	couponData, err := ordercouponservice.QueryOrderCouponByOrderNo(orderNo)
	if err != nil {
		return OrderCouponData{}, err
	}
	var result OrderCouponData
	err = utils.JsonConvertor(couponData, &result)
	if err != nil {
		return OrderCouponData{}, err
	}

	return result, nil
}

// 优惠劵优惠金额计算
func CalculateOrderCouponPrice(orderNo string, originCount int, count int, lastOne bool) (map[int]map[int]int, error) {
	if originCount == 0 || count == 0 {
		return nil, errors.New("优惠计算参数错误")
	}
	orderCouponInfo, err := OrderCouponPriceByOrderNo(orderNo)
	if err != nil {
		return nil, errors.New("获取优惠异常")
	}

	//计算本次数量的优惠金额
	var couponPrice = orderCouponInfo.EMoney
	if couponPrice == 0 {
		return nil, nil
	}

	var cancelCouponPrice = 0
	surplusEMoney := couponPrice % originCount
	baseDiscountPerTicket := (couponPrice - surplusEMoney) / originCount
	//非最后一张，无需优惠金额余数修正
	if !lastOne {
		surplusEMoney = 0
	}
	cancelCouponPrice = baseDiscountPerTicket*count + surplusEMoney
	//数据组合 data[订单号][供应商][分销商]=优惠信息
	var data = make(map[int]map[int]int)
	data[orderCouponInfo.Aid] = make(map[int]int)
	data[orderCouponInfo.Aid][orderCouponInfo.Fid] = cancelCouponPrice

	return data, nil
}

func OrderDiscountInfoByOrderNos(orderNos []string) (map[string][]OrderDiscountInfoData, error) {
	discountInfoData, err := orderticketservice.QueryOrderDiscountInfoByOrderNos(orderNos)
	if err != nil {
		return nil, err
	}
	var result []OrderDiscountInfoData
	err = utils.JsonConvertor(discountInfoData, &result)
	if err != nil {
		return nil, err
	}

	var data = make(map[string][]OrderDiscountInfoData)
	for _, item := range result {
		data[item.OrderNum] = append(data[item.OrderNum], item)
	}

	return data, nil
}

func OrderDiscountInfoByOrderNoAndSerialNums(orderNo string, serialNums []int, status int) ([]OrderDiscountInfoData, error) {
	discountInfoData, err := orderticketservice.QueryOrderDiscountList(orderNo, status, serialNums)
	if err != nil {
		return nil, err
	}
	var result []OrderDiscountInfoData
	err = utils.JsonConvertor(discountInfoData, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func CheckOrderUseDiscount(extContent OrderFxDetailExtContentFields) bool {
	if checkUseDiscount(extContent) || checkUseSettlementDiscount(extContent) {
		return true
	}

	return false
}

func checkUseDiscount(extContent OrderFxDetailExtContentFields) bool {
	if extContent.UseCoupon || extContent.UsePoint || extContent.UseDiscount || extContent.UseMarketingDiscount {
		return true
	}

	return false
}

func checkUseSettlementDiscount(extContent OrderFxDetailExtContentFields) bool {
	if extContent.BargainPricePolicyId != 0 || extContent.SpecifyReducedPolicyId != 0 {
		return true
	}

	return false
}
