package dwd

import (
	"encoding/json"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	eventOds "report-service/internal/domain/event/ods"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/logic/dwd/business"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

const (
	CostPayModeKey  = 10000 //顶级补全
	SalePayModeKey  = 10001 //末级分销补全
	DefaultReseller = 112   //默认散客
	SelfSellPayMode = 3     //自供自销
)

// 获取层级基础关联关系
func GetLevelBaseInfoMap(distributionChain interface{}) (map[int]int, map[int]int) {
	//上下级关系解析
	var distributionChainBase []eventOds.EventBusinessDataPayloadDistributionChain
	err := utils.JsonConvertor(distributionChain, &distributionChainBase)
	if err != nil {
		return nil, nil
	}
	var parentMerchantMap = make(map[int]int)
	var costPayModeMap = make(map[int]int)
	for _, chain := range distributionChainBase {
		sellerId := chain.SellerId
		buyerId := chain.BuyerId
		payMode := chain.PayMode
		//自供自销处理下
		if !chain.IsSelfSale() {
			parentMerchantMap[buyerId] = sellerId
			costPayModeMap[buyerId] = payMode
		}
	}

	return parentMerchantMap, costPayModeMap
}

func GetSanKeId(isSanKe bool) (int, error) {
	if isSanKe {
		return DefaultReseller, nil
	}

	return 0, nil
}

func NeedAddSplit(level int, buyerId int, sellerId int) bool {
	if buyerId == DefaultReseller || buyerId == sellerId {
		return false
	}

	//处于最末级
	if level == -1 || level == 0 {
		return true
	}

	return false
}

// 获取结算优惠
func GetSettlementDiscountPrice(discountData []business.CommonPayloadDiscountDetailItem) (int, []business.CommonPayloadDiscountDetailItem) {
	settlementDiscountPrice := 0
	var settlementDiscountDetail []business.CommonPayloadDiscountDetailItem
	for _, item := range discountData {
		settlementDiscountPrice += item.Price
		settlementDiscountDetail = append(settlementDiscountDetail, item)
	}

	return settlementDiscountPrice, settlementDiscountDetail
}

// 散客归整 在业务事实清洗的时候，先批量判断是不是散客，这边只要处理散客归整统一散客ID
func SnakeSorted(level int, isSanKe bool) (int, int, error) {
	chainLevel := level
	if level == -1 || level == 0 {
		sanKeTmpId, err := GetSanKeId(isSanKe)
		if err != nil {
			return 0, 0, err
		}

		if sanKeTmpId > 0 {
			if chainLevel == 0 {
				chainLevel = 1
			} else {
				chainLevel = -3
			}

			//散客归整
			return chainLevel, sanKeTmpId, nil
		}
	}

	return level, 0, nil
}

func CheckMerchantNonReport(distributionChain interface{}) (map[int]bool, error) {
	var distributionChainBase []eventOds.EventBusinessDataPayloadDistributionChain
	err := utils.JsonConvertor(distributionChain, &distributionChainBase)
	if err != nil {
		return nil, nil
	}
	var allIds []int
	for _, chain := range distributionChainBase {
		sellerId := chain.SellerId
		buyerId := chain.BuyerId
		allIds = append(allIds, sellerId)
		//末级补全的卖家也要考虑下，是否需要记录报表
		distributorId := chain.BuyerId
		chainLevel, sanKeTmpId, sanKeTmpErr := SnakeSorted(chain.Level, chain.IsSanKe)
		if sanKeTmpErr != nil {
			return nil, sanKeTmpErr
		}
		if sanKeTmpId > 0 {
			//散客归整
			distributorId = sanKeTmpId
		}
		//末级补全
		if NeedAddSplit(chainLevel, distributorId, sellerId) {
			allIds = append(allIds, buyerId)
		}
	}
	if len(allIds) == 0 {
		return nil, nil
	}
	//获取不记录报表的配置
	res := config.GetJson(enum.ConfigKeyNonReport, enum.CacheTimeDay)
	var nonReportIds []int
	if res != "" {
		err = json.Unmarshal([]byte(res), &nonReportIds)
		if err != nil {
			global.LOG.Error("获取不记入报表的配置失败 ", zap.Error(err))
			return nil, nil
		}
	}
	configExistMap := make(map[int]bool, len(allIds))
	for _, id := range allIds {
		configExistMap[id] = false
		if utils.Container(nonReportIds, id) {
			configExistMap[id] = true
		}
	}
	return configExistMap, nil
}
