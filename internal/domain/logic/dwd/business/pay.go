package business

import "time"

type ReportFactPay struct {
	CommonBase
	CommonDiscount
	PayNo   int                  `json:"pay_no"`  // 支付单号
	Payload ReportFactPayPayload `json:"payload"` // 扩展数据
	PaidAt  time.Time            `json:"paid_at"` // 支付时间
}

type ReportFactPayPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *ReportFactPay) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactPay) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactPay) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
