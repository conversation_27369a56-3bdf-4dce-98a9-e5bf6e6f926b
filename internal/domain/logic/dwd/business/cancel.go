package business

import "time"

type ReportFactCancel struct {
	CommonBase
	CommonDiscount
	CommonFee
	CancelNo    int                     `json:"cancel_no"`    // 业务单号
	Payload     ReportFactCancelPayload `json:"payload"`      // 扩展数据
	CancelledAt time.Time               `json:"cancelled_at"` // 操作时间
}

type ReportFactCancelPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *ReportFactCancel) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactCancel) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactCancel) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
