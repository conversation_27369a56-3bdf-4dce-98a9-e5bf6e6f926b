package business

import "time"

type CollectData struct {
	CommonBase
	CommonDiscount
	CollectNo   int                      `json:"collect_no"`   // 取票单号
	Payload     ReportFactCollectPayload `json:"payload"`      // 扩展数据
	CollectedAt time.Time                `json:"collected_at"` // 取票时间
}

type ReportFactCollectPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *CollectData) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *CollectData) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *CollectData) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
