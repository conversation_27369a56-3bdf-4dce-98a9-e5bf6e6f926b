package business

import "time"

type ReportFactAfterSale struct {
	CommonBase
	CommonDiscount
	AfterSaleNo  int                        `json:"after_sale_no"`  // 业务单号
	Payload      ReportFactAfterSalePayload `json:"payload"`        // 扩展数据
	AfterSaledAt time.Time                  `json:"after_saled_at"` // 操作时间
}

type ReportFactAfterSalePayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonDataPayloadDistributionChainAfterSale
}

func (r *ReportFactAfterSale) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactAfterSale) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactAfterSale) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
