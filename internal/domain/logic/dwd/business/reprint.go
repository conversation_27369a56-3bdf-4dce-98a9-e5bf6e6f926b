package business

import "time"

type ReprintData struct {
	CommonBase
	CommonDiscount
	ReprintNo   int                      `json:"reprint_no"`   // 重打印单号
	Payload     ReportFactReprintPayload `json:"payload"`      // 扩展数据
	ReprintedAt time.Time                `json:"reprinted_at"` // 重打印时间
}

type ReportFactReprintPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *ReprintData) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReprintData) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReprintData) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
