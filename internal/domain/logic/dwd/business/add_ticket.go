package business

import "time"

type ReportFactAddTicket struct {
	CommonBase
	AddTicketNo   int                        `json:"add_ticket_no"`   // 业务单号
	Payload       ReportFactAddTicketPayload `json:"payload"`         // 扩展数据
	AddedTicketAt time.Time                  `json:"added_ticket_at"` // 操作时间
}

type ReportFactAddTicketPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
}

func (r *ReportFactAddTicket) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactAddTicket) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactAddTicket) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
