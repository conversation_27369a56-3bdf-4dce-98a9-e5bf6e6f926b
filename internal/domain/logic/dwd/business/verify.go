package business

import "time"

type ReportFactVerify struct {
	CommonBase
	CommonDiscount
	VerifyNo   int                     `json:"verify_no"`   // 业务单号
	Payload    ReportFactVerifyPayload `json:"payload"`     // 扩展数据
	VerifiedAt time.Time               `json:"verified_at"` // 操作时间
}

type ReportFactVerifyPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *ReportFactVerify) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactVerify) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactVerify) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
