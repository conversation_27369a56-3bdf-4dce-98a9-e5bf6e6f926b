package business

import "time"

type ReportFactRevoke struct {
	CommonBase
	CommonDiscount
	CommonFee
	RevokeNo  int                     `json:"revoke_no"`  // 业务单号
	Payload   ReportFactRevokePayload `json:"payload"`    // 扩展数据
	RevokedAt time.Time               `json:"revoked_at"` // 操作时间
}

type ReportFactRevokePayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *ReportFactRevoke) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactRevoke) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactRevoke) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
