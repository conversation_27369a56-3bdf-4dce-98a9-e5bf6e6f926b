package business

import "time"

type ReportFactFinish struct {
	CommonBase
	CommonDiscount
	FinishNo   int                     `json:"finish_no"`   // 业务单号
	Payload    ReportFactFinishPayload `json:"payload"`     // 扩展数据
	FinishedAt time.Time               `json:"finished_at"` // 操作时间
}

type ReportFactFinishPayload struct {
	CommonPayloadGroupsTag
	CommonPayloadProductRules
	CommonPayloadDiscountDetail
	CommonPayloadOrderCoupon
}

func (r *ReportFactFinish) SetDistributorId(did int) {
	r.DistributorID = did
	return
}

func (r *ReportFactFinish) SetSalePrice(price int) {
	r.SalePrice = price
	return
}

func (r *ReportFactFinish) SetGroupsTag(groupsTag GroupsTag) {
	r.Payload.GroupsTag = groupsTag
	return
}
