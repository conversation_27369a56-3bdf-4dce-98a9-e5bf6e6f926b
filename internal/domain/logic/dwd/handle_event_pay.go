package dwd

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/segmentio/kafka-go"
	"report-service/internal/domain/enum"
	eventDwd "report-service/internal/domain/event/dwd"
	eventOds "report-service/internal/domain/event/ods"
	"report-service/internal/domain/logic/dwd/business"
	"report-service/internal/domain/logic/dwd/handlecommon"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"time"
)

func BatchHandleEventPay(messages ...kafka.Message) error {
	var messageList []eventOds.EventBusinessPay
	var err error
	for _, message := range messages {
		var messageItem eventOds.EventBusinessPay
		err = json.Unmarshal(message.Value, &messageItem)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("handle event ods is error. err:%s, msg:%+v", err.Error(), messages))
			continue
		}
		messageList = append(messageList, messageItem)
	}
	for _, item := range messageList {
		//目前item.type只有新增，这里不做特殊处理，默认都是新增
		err = HandleEventPayCreate(item)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("dwd handle event is error. err:%s, msg:%+v", err.Error(), item))
		}
	}
	return nil
}

func HandleEventPayCreate(event interface{}) error {
	var odsEvent eventOds.EventBusinessPay
	err := utils.JsonConvertor(event, &odsEvent)
	if err != nil {
		return err
	}
	//过滤非新增的数据
	if odsEvent.Type != enum.OdsBusinessEventTypeCreate {
		return nil
	}
	//新增数据
	data := odsEvent.Data
	//分销链数据
	distributionChain := data.Payload.DistributionChain
	//上下级关系解析
	parentMerchantMap, costPayModeMap := GetLevelBaseInfoMap(distributionChain)
	//获取不记录报表的配置
	merchantNonReportMap, checkErr := CheckMerchantNonReport(distributionChain)
	if checkErr != nil {
		return checkErr
	}
	operateTime, operateTimeErr := time.Parse(enum.TimestampLayoutMake, data.PaidAt)
	if operateTimeErr != nil || operateTime.IsZero() {
		operateTimeErrMsg := fmt.Sprintf("operate time is error. business no:%d", data.PayNo)
		global.LOG.Error(operateTimeErrMsg)
		return errors.New(operateTimeErrMsg)
	}
	var dwdProcessor []business.DwdProcessor
	for _, chain := range distributionChain {
		var insertItem business.ReportFactPay
		merchantId := chain.SellerId
		//配置存在，不记录报表
		if nonReportTrue, nonReportExist := merchantNonReportMap[merchantId]; nonReportExist && nonReportTrue {
			continue
		}
		insertItem.MerchantID = merchantId
		insertItem.OrderNo = data.OrderNo
		insertItem.PayNo = data.PayNo
		insertItem.PoiID = data.PoiId
		insertItem.SpuID = data.SpuId
		insertItem.SkuID = data.SkuId
		insertItem.SaleChannel = data.SaleChannel
		insertItem.OperateChannel = data.OperateChannel
		insertItem.OperatorID = chain.OperatorId
		insertItem.OperateSiteId = chain.OperateSiteId
		insertItem.Count = data.Count
		insertItem.CostUnitPrice = chain.CostPrice
		insertItem.SaleUnitPrice = chain.SalePrice
		insertItem.PaidAt = operateTime
		insertItem.CostPrice = data.Count * chain.CostPrice
		insertItem.SalePrice = data.Count * chain.SalePrice
		insertItem.ExternalOperateNo = data.ExternalOperateNo
		insertItem.TradeNo = data.TradeNo
		insertItem.SellOperatorID = chain.SellOperatorId
		insertItem.SellSiteId = chain.SellSiteId

		//如果是自供自销的话，卖出金额和单价要处理为0元
		if chain.PayMode == SelfSellPayMode {
			insertItem.SaleUnitPrice = 0
			insertItem.SalePrice = 0
		}

		//上下级商户处理
		parentMerchantId := 0
		parentId, parentExist := parentMerchantMap[merchantId]
		if parentExist {
			parentMerchantId = parentId
		}
		insertItem.ParentMerchantID = parentMerchantId

		distributorId := chain.BuyerId
		insertItem.DistributorID = distributorId

		//上下级支付方式
		insertItem.SalePayMode = chain.PayMode
		costPayMode := CostPayModeKey
		costPM, costPMExist := costPayModeMap[merchantId]
		if costPMExist {
			costPayMode = costPM
		}
		insertItem.CostPayMode = costPayMode
		//产品特殊规则
		productRules := handlecommon.ProductRulesParams{
			SaleChannel:         data.SaleChannel,
			ProductType:         data.ProductType,
			SubType:             data.SubType,
			IfPack:              data.Payload.IfPack,
			ParentOrderNo:       data.Payload.ParentOrderNo,
			ParentProductType:   data.Payload.ParentOrderProductType,
			ParentOrderApplyDid: data.Payload.ParentOrderApplyDid,
			MerchantId:          merchantId,
		}
		insertItem.Payload.CommonPayloadProductRules = productRules.HandleProductRules()
		//取出上一级优惠信息
		costDiscountPrice := 0
		for _, tmp := range distributionChain {
			if tmp.BuyerId != chain.SellerId || (tmp.BuyerId == chain.SellerId && tmp.IsSelfSale()) {
				continue
			}
			//优惠券计算
			insertItem.Payload.CostCouponPrice = tmp.CouponPrice
			costDiscountPrice += tmp.CouponPrice
			var preDetail []business.CommonPayloadDiscountDetailItem
			_ = utils.JsonConvertor(tmp.DiscountDetail, &preDetail)

			//上一级优惠只能取分销结算优惠金额
			if len(preDetail) > 0 {
				price, detail := GetSettlementDiscountPrice(preDetail)
				costDiscountPrice += price
				insertItem.Payload.CostDiscountDetail = detail
			}
		}
		insertItem.CostDiscountPrice = costDiscountPrice
		//取出当前级优惠
		saleDiscountPrice := 0
		insertItem.Payload.SaleCouponPrice = chain.CouponPrice
		saleDiscountPrice += chain.CouponPrice
		var detail []business.CommonPayloadDiscountDetailItem
		_ = utils.JsonConvertor(chain.DiscountDetail, &detail)
		saleDiscountPrice += chain.DiscountPrice
		insertItem.Payload.SaleDiscountDetail = detail
		insertItem.SaleDiscountPrice = saleDiscountPrice
		//散客归整
		snakeSorted := handlecommon.SnakeSortedParams{
			Level:       chain.Level,
			IsSanKe:     chain.IsSanKe,
			SaleChannel: data.SaleChannel,
			PayMode:     chain.PayMode,
		}
		var dwdProcessorItem business.DwdProcessor = &insertItem
		snakeSorted.SnakeSorted(dwdProcessorItem)
		//分组标签处理
		handlecommon.HandleCommonFillGroupsTag(dwdProcessorItem, data.Payload.EventBusinessDataGroupsTag)
		dwdProcessor = append(dwdProcessor, &insertItem)
	}
	if len(dwdProcessor) <= 0 {
		return nil
	}
	var records []dwd.ReportFactPayModel
	err = utils.JsonConvertor(dwdProcessor, &records)
	if err != nil {
		return err
	}
	insertErr := repository.DwdPayRepository.Insert(records)
	if insertErr != nil {
		return nil
	}
	//推送消息
	var eventReportList eventDwd.EventReportPayList
	for _, record := range records {
		var eventReportItem eventDwd.EventReportPayData
		eventRecordsSwitchErr := utils.JsonConvertor(record, &eventReportItem)
		if eventRecordsSwitchErr != nil {
			return errors.New(fmt.Sprintf("dwd event records switch error. info:%s", eventRecordsSwitchErr))
		}
		//时间格式转换下
		eventReportItem.PaidAt = record.PaidAt.Format(enum.TimestampLayoutMake)
		eventReportList.EventData = append(eventReportList.EventData, eventDwd.EventReportPay{
			Data: eventReportItem,
		})
	}
	//统一推送处理
	var dwdEventProcessor eventDwd.EventProcessor = &eventReportList
	//推送消息
	sendErr := handlecommon.SendCreateEventMessage(dwdEventProcessor)
	if sendErr != nil {
		return sendErr
	}
	return nil
}
