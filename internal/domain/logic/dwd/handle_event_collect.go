package dwd

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	eventDwd "report-service/internal/domain/event/dwd"
	eventOds "report-service/internal/domain/event/ods"
	"report-service/internal/domain/logic/dwd/business"
	"report-service/internal/domain/logic/dwd/handlecommon"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwd"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"time"

	"github.com/segmentio/kafka-go"
)

func BatchHandleEventCollect(messages ...kafka.Message) error {
	var messageList []eventOds.EventBusinessCollect
	var err error
	for _, message := range messages {
		var messageItem eventOds.EventBusinessCollect
		err = json.Unmarshal(message.Value, &messageItem)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("handle event ods is error. err:%s, msg:%+v", err.Error(), messages))
			continue
		}
		messageList = append(messageList, messageItem)
	}
	for _, item := range messageList {
		//目前item.type只有新增，这里不做特殊处理，默认都是新增
		err = HandleEventCollectCreate(item)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("dwd handle event is error. err:%s, msg:%+v", err.Error(), item))
		}
	}
	return nil
}

func HandleEventCollectCreate(event interface{}) error {
	var odsEvent eventOds.EventBusinessCollect
	err := utils.JsonConvertor(event, &odsEvent)
	if err != nil {
		return err
	}
	//过滤非新增的数据
	if odsEvent.Type != enum.OdsBusinessEventTypeCreate {
		return nil
	}
	//新增数据
	data := odsEvent.Data
	//分销链数据
	distributionChain := data.Payload.DistributionChain
	//上下级关系解析
	parentMerchantMap, costPayModeMap := GetLevelBaseInfoMap(distributionChain)
	//获取不记录报表的配置
	merchantNonReportMap, checkErr := CheckMerchantNonReport(distributionChain)
	if checkErr != nil {
		return checkErr
	}
	operateTime, operateTimeErr := time.Parse(enum.TimestampLayoutMake, data.CollectedAt)
	if operateTimeErr != nil || operateTime.IsZero() {
		operateTimeErrMsg := fmt.Sprintf("operate time is error. business no:%d", data.CollectNo)
		global.LOG.Error(operateTimeErrMsg)
		return errors.New(operateTimeErrMsg)
	}
	var dwdProcessor []business.DwdProcessor
	for _, chain := range distributionChain {
		//过滤不记录报表的商户
		if merchantNonReportMap[chain.BuyerId] {
			continue
		}
		//获取商户ID
		merchantId := chain.BuyerId
		//获取上级商户ID
		parentMerchantId, parentMerchantIdExist := parentMerchantMap[merchantId]
		if !parentMerchantIdExist {
			parentMerchantId = 0
		}
		//获取采购支付方式
		costPayMode, costPayModeExist := costPayModeMap[merchantId]
		if !costPayModeExist {
			costPayMode = 0
		}
		//获取下级商户ID
		distributorId := chain.SellerId
		//构建数据
		var insertItem business.CollectData
		insertItem.MerchantID = merchantId
		insertItem.ParentMerchantID = parentMerchantId
		insertItem.DistributorID = distributorId
		insertItem.OrderNo = data.OrderNo
		insertItem.PoiID = data.PoiId
		insertItem.SpuID = data.SpuId
		insertItem.SkuID = data.SkuId
		insertItem.SaleChannel = data.SaleChannel
		insertItem.OperateChannel = data.OperateChannel
		insertItem.Count = data.Count
		insertItem.CostUnitPrice = chain.CostPrice
		insertItem.SaleUnitPrice = chain.SalePrice
		insertItem.ExternalOperateNo = data.ExternalOperateNo
		insertItem.SellOperatorID = data.SellOperatorId
		insertItem.OperatorID = data.OperatorId
		insertItem.SellSiteId = data.SellSiteId
		insertItem.OperateSiteId = data.OperateSiteId
		insertItem.TradeNo = data.TradeNo
		insertItem.CollectNo = data.CollectNo
		insertItem.CollectedAt = operateTime
		insertItem.SalePayMode = chain.PayMode
		insertItem.CostPayMode = costPayMode
		//产品特殊规则
		productRules := handlecommon.ProductRulesParams{
			SaleChannel:         data.SaleChannel,
			ProductType:         data.ProductType,
			SubType:             data.SubType,
			IfPack:              data.Payload.IfPack,
			ParentOrderNo:       data.Payload.ParentOrderNo,
			ParentProductType:   data.Payload.ParentOrderProductType,
			ParentOrderApplyDid: data.Payload.ParentOrderApplyDid,
			MerchantId:          merchantId,
		}
		insertItem.Payload.CommonPayloadProductRules = productRules.HandleProductRules()
		//散客归整
		snakeSorted := handlecommon.SnakeSortedParams{
			Level:       chain.Level,
			IsSanKe:     chain.IsSanKe,
			SaleChannel: data.SaleChannel,
			PayMode:     chain.PayMode,
		}
		var dwdProcessorItem business.DwdProcessor = &insertItem
		snakeSorted.SnakeSorted(dwdProcessorItem)
		//分组标签处理
		handlecommon.HandleCommonFillGroupsTag(dwdProcessorItem, data.Payload.EventBusinessDataGroupsTag)
		dwdProcessor = append(dwdProcessor, &insertItem)
	}
	if len(dwdProcessor) <= 0 {
		return nil
	}
	var records []dwd.ReportFactCollectModel
	err = utils.JsonConvertor(dwdProcessor, &records)
	if err != nil {
		return err
	}
	insertErr := repository.DwdCollectRepository.Insert(records)
	if insertErr != nil {
		return nil
	}
	//推送消息
	var eventReportList eventDwd.EventReportCollectList
	for _, record := range records {
		var eventReportItem eventDwd.EventReportCollectData
		eventRecordsSwitchErr := utils.JsonConvertor(record, &eventReportItem)
		if eventRecordsSwitchErr != nil {
			return fmt.Errorf("dwd event records switch error. info:%s", eventRecordsSwitchErr)
		}
		//时间格式转换下
		eventReportItem.CollectedAt = record.CollectedAt.Format(enum.TimestampLayoutMake)
		eventReportList.EventData = append(eventReportList.EventData, eventDwd.EventReportCollect{
			Data: eventReportItem,
		})
	}
	//统一推送处理
	var dwdEventProcessor eventDwd.EventProcessor = &eventReportList
	//推送消息
	sendErr := handlecommon.SendCreateEventMessage(dwdEventProcessor)
	if sendErr != nil {
		return sendErr
	}
	return nil
}
