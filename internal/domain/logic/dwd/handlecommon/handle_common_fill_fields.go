package handlecommon

import (
	"report-service/internal/domain/enum"
	eventOds "report-service/internal/domain/event/ods"
	"report-service/internal/domain/logic/dwd/business"
)

func HandleCommonFillGroupsTag(dwdProcessor business.DwdProcessor, groupsTag eventOds.EventBusinessDataGroupsTag) {
	tag := business.GroupsTag{}
	for s, s2 := range enum.ReportToGroupApplicableCrowdManageMap {
		val := groupsTag.GetGroupTagCode(s)
		if val != "" {
			switch s2 {
			case enum.GroupApplicableCrowdManage:
				tag.TargetAudience = val
			}
		}
	}
	dwdProcessor.SetGroupsTag(tag)
	return
}
