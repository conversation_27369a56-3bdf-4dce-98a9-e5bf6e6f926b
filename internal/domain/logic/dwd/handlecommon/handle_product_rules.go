package handlecommon

import (
	"report-service/internal/domain/logic/dwd/business"
)

type ProductRulesParams struct {
	SaleChannel         int    `json:"sale_channel"`
	ProductType         string `json:"product_type"`
	SubType             int    `json:"sub_type"`
	IfPack              int    `json:"if_pack"`
	ParentOrderNo       string `json:"parent_order_no"`
	ParentProductType   string `json:"parent_product_type"`
	ParentOrderApplyDid int    `json:"parent_order_apply_did"`
	MerchantId          int    `json:"merchant_id"`
}

func (p *ProductRulesParams) HandleProductRules() business.CommonPayloadProductRules {
	var productRule business.CommonPayloadProductRules
	//产品类型
	productRule.ProductType = p.ProductType
	productRule.SubType = p.SubType
	//产品特殊规则
	if p.SaleChannel == 18 {
		//年卡特权
		productRule.AnnualCardType = 2
	}
	if p.SaleChannel == 64 {
		//预售券权益订单
		productRule.ExchangeCouponType = 2
	}
	if p.ProductType == "I" {
		//年卡
		productRule.AnnualCardType = 1
	}
	if p.ProductType == "A" && p.SubType == 1 {
		//预售券
		productRule.ExchangeCouponType = 1
	}
	if p.IfPack == 1 && p.ProductType == "F" {
		//套票
		productRule.PackType = 1
	}
	if p.IfPack == 1 && p.ProductType == "H" {
		//捆绑票
		productRule.ShowBindType = 1
	}
	if p.IfPack == 2 && p.ParentOrderNo != "" && p.ParentProductType == "F" && p.ParentOrderApplyDid == p.MerchantId {
		//套票子票
		productRule.PackType = 2
		productRule.ParentOrderNo = p.ParentOrderNo
	}
	if p.IfPack == 2 && p.ParentOrderNo != "" && p.ParentProductType == "H" && p.ParentOrderApplyDid == p.MerchantId {
		//捆绑票子票
		productRule.ShowBindType = 2
		productRule.ParentOrderNo = p.ParentOrderNo
	}

	return productRule
}
