package handlecommon

import "report-service/internal/domain/logic/dwd/business"

const (
	DefaultReseller = 112 //默认散客
)

type SnakeSortedParams struct {
	Level                     int  `json:"level"`
	IsSanKe                   bool `json:"is_sanke"`
	SaleChannel               int  `json:"sale_channel"`
	PayMode                   int  `json:"pay_mode"`
	NotNeedCheckSaleMoneyZero bool `json:"need_check_sale_money_zero"`
}

func (s *SnakeSortedParams) SnakeSorted(dwdProcessor business.DwdProcessor) {
	if s.Level == -1 || s.Level == 0 {
		sanKeTmpId := 0
		if s.<PERSON><PERSON>an<PERSON>e {
			sanKeTmpId = DefaultReseller
		}
		if sanKeTmpId > 0 {
			//散客归整
			dwdProcessor.SetDistributorId(sanKeTmpId)
			//散客归整需要判断末级是否销售金额为0
			salePriceIsZero := s.CheckSaleMoneyZero()
			if salePriceIsZero {
				dwdProcessor.SetSalePrice(0)
			}
		}
	}
	return
}

func (s *SnakeSortedParams) CheckSaleMoneyZero() bool {
	if s.NotNeedCheckSaleMoneyZero {
		return false
	}
	isZero := false
	//预售券权益支付判断
	if s.SaleChannel == 64 || s.PayMode == 65 {
		isZero = true
	}
	//预存码权益支付判断
	if s.PayMode == 67 {
		isZero = true
	}

	return isZero
}
