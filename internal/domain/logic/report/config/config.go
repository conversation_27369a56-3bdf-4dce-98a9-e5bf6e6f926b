package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/logic/config/merchantconfig"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

const (
	GlobalSettingKey              = "mobile_report_config"
	ParkPeopleSettingKey          = "mobile_report_park_people_setting"
	GlobalSettingDataShowKey      = "data_show"
	GlobalSettingReportShowKey    = "report_show"
	ParkPeopleSettingDataRangeKey = "data_range"
	DataRangeMaxNum               = 20
)

type GlobalSettingData struct {
	DataShow   []string `json:"data_show"`
	ReportShow []string `json:"report_show"`
}

type ParkPeopleSettingData struct {
	DataRange []int `json:"data_range"`
}

func BaseDefinition(key string) ([]DefinitionItem, error) {
	baseConfig, err := BaseConfig(key)
	if err != nil {
		return nil, err
	}
	return baseConfig.Definition, nil
}

func GlobalSetting(merchantId int) (*GlobalSettingData, error) {
	info, err := merchantconfig.InfoByKeyAndMerchantId(GlobalSettingKey, merchantId, merchantId)
	if err != nil {
		return nil, err
	}
	//不存在获取默认配置
	if info == nil {
		var configInfo *CommonConfig
		configInfo, err = BaseConfig(GlobalSettingKey)
		if err != nil {
			return nil, err
		}
		defaultData := configInfo.Default
		if len(defaultData) == 0 {
			return nil, errors.New("不存在配置")
		}
		var infoData GlobalSettingData
		for _, datum := range defaultData {
			switch datum.Key {
			case GlobalSettingDataShowKey:
				var dataShow []string
				err = utils.JsonConvertor(datum.Value, &dataShow)
				if err != nil {
					return nil, err
				}
				infoData.DataShow = dataShow
			case GlobalSettingReportShowKey:
				var reportShow []string
				err = utils.JsonConvertor(datum.Value, &reportShow)
				if err != nil {
					return nil, err
				}
				infoData.ReportShow = reportShow
			default:
				continue
			}
		}
		return &infoData, nil
	}
	//解析配置
	var infoData GlobalSettingData
	err = utils.JsonConvertor(info.Payload, &infoData)
	if err != nil {
		return nil, errors.New(fmt.Sprint("配置解析失败", err.Error()))
	}
	return &infoData, nil
}

func ParkPeopleSetting(merchantId int) (*ParkPeopleSettingData, error) {
	defaultInfo := ParkPeopleSettingData{
		DataRange: []int{},
	}
	info, err := merchantconfig.InfoByKeyAndMerchantId(ParkPeopleSettingKey, merchantId, merchantId)
	if err != nil {
		return nil, err
	}
	//无数据
	if info == nil {
		return &defaultInfo, nil
	}
	//解析配置
	var infoData ParkPeopleSettingData
	err = utils.JsonConvertor(info.Payload, &infoData)
	if err != nil {
		return nil, errors.New(fmt.Sprint("配置解析失败", err.Error()))
	}
	return &infoData, nil
}

func SaveConfig(merchantId int, key string, data string) error {
	handlers := initSaveHandlers()
	handler, exists := handlers[key]
	if !exists {
		return szerrors.NewInvalidParamErrorWithText(fmt.Sprint("保存类型错误，类型", key))
	}
	return handler(merchantId, data)
}

func GetConfig(merchantId int, key string) (interface{}, error) {
	handlers := initGetHandler()
	handler, exists := handlers[key]
	if !exists {
		return nil, szerrors.NewInvalidParamErrorWithText(fmt.Sprint("查询类型错误，类型", key))
	}
	return handler(merchantId)
}

func BaseConfig(key string) (*CommonConfig, error) {
	//获取配置
	configStr := config.GetJson(key, enum.CacheTimeDay)
	if configStr == "" {
		return nil, errors.New("不存在默认定义")
	}
	//解析配置
	var configInfo CommonConfig
	err := json.Unmarshal([]byte(configStr), &configInfo)
	if err != nil {
		return nil, errors.New(fmt.Sprint("默认定义解析失败", err.Error()))
	}
	return &configInfo, nil
}

type SaveHandler func(merchantId int, data string) error

func initSaveHandlers() map[string]SaveHandler {
	return map[string]SaveHandler{
		GlobalSettingKey:     SaveGlobalSetting,
		ParkPeopleSettingKey: SaveParkPeopleSetting,
	}
}

func SaveGlobalSetting(merchantId int, data string) error {
	err := validateParams(merchantId, data)
	if err != nil {
		return err
	}
	var saveData GlobalSettingData
	err = json.Unmarshal([]byte(data), &saveData)
	if err != nil {
		return errors.New(fmt.Sprint("解析信息错误", err.Error()))
	}
	if len(saveData.ReportShow) < 1 {
		return errors.New("报表展示至少选一个")
	}
	setConfigParams := merchantconfig.SetConfigParams{
		Key:        GlobalSettingKey,
		MerchantId: merchantId,
		MemberId:   merchantId,
		Value:      saveData,
	}
	err = merchantconfig.SetConfigByKeyAndMerchantId(setConfigParams)
	if err != nil {
		return errors.New(fmt.Sprint("保存信息错误", err.Error()))
	}
	return nil
}

func SaveParkPeopleSetting(merchantId int, data string) error {
	err := validateParams(merchantId, data)
	if err != nil {
		return err
	}
	var saveData ParkPeopleSettingData
	err = json.Unmarshal([]byte(data), &saveData)
	if err != nil {
		return errors.New(fmt.Sprint("解析信息错误", err.Error()))
	}
	if len(saveData.DataRange) > DataRangeMaxNum {
		return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("数据范围选择数量最多不超过%d个", DataRangeMaxNum))
	}
	setConfigParams := merchantconfig.SetConfigParams{
		Key:        ParkPeopleSettingKey,
		MerchantId: merchantId,
		MemberId:   merchantId,
		Value:      saveData,
	}
	err = merchantconfig.SetConfigByKeyAndMerchantId(setConfigParams)
	if err != nil {
		return errors.New(fmt.Sprint("保存信息错误", err.Error()))
	}
	return nil
}

func validateParams(merchantId int, data string) error {
	if merchantId == 0 || data == "" {
		return szerrors.NewInvalidParamErrorWithText("参数错误")
	}
	return nil
}

type GetHandler func(merchantId int) (interface{}, error)

func initGetHandler() map[string]GetHandler {
	return map[string]GetHandler{
		GlobalSettingKey: func(merchantId int) (interface{}, error) {
			return GlobalSetting(merchantId)
		},
		ParkPeopleSettingKey: func(merchantId int) (interface{}, error) {
			return ParkPeopleSetting(merchantId)
		},
	}
}
