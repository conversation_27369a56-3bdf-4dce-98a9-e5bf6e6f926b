package config

type CommonConfig struct {
	Default    []SettingData    `json:"default"`
	Definition []DefinitionItem `json:"definition"`
}

type DefinitionItem struct {
	DefinitionItemCommon
	Child []DefinitionItemCommon `json:"child"`
}

type DefinitionItemCommon struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

type SettingData struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}
