package common

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/offlinepaymentservice"
	"report-service/pkg/utils"
)

func PayWayListConf(merchantId int, filterNoAliasSet bool) map[int]string {
	payModeMap := make(map[int]string)
	//设置默认数据， 不能payModeMap=enum.PaymodeMap, 后续操作会导致原始enum.PaymodeMap数据被覆盖
	for i, s := range enum.PaymodeMap {
		payModeMap[i] = s
	}
	//查询商户自定义收款名称
	offlinePayList, err := offlinepaymentservice.QueryOfflinePayChannelList(merchantId, true)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("获取线下自定义收款方式失败，商户ID：%d，错误信息：%s", merchantId, err.Error()))
		return payModeMap
	}
	if len(offlinePayList) == 0 {
		return payModeMap
	}
	var offlinePayCode []int
	for _, item := range offlinePayList {
		if _, ok := payModeMap[item.PayMode]; !ok {
			continue
		}
		//覆盖原先名称
		payModeMap[item.PayMode] = item.Name
		offlinePayCode = append(offlinePayCode, item.PayMode)
	}
	//移除商户未设置自定义收款名称的支付方式
	if filterNoAliasSet {
		for i := 76; i <= 85; i++ {
			if _, ok := payModeMap[i]; ok && !utils.Container(offlinePayCode, i) {
				delete(payModeMap, i)
			}
		}
	}

	return payModeMap
}
