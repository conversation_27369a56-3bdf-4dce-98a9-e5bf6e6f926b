package template

import (
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/report"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"unicode"
)

type DoCreate struct {
	DoCommonParams
	Category int `json:"category"`
	SdType   int `json:"sdtype"`
}

type DoCommonParams struct {
	MerchantId          int                        `json:"merchant_id"`
	MemberId            int                        `json:"member_id"`
	Title               string                     `json:"title"`
	Dimension           []string                   `json:"dimension"`
	Indicator           []string                   `json:"indicator"`
	SpecialDimension    common.DoSpecialDimension  `json:"special_dimension"`
	DimensionScheme     common.DoDimensionScheme   `json:"dimension_scheme"`
	DimensionTagGroup   map[string]string          `json:"dimension_tag_group"`
	SpecialProductRules common.SpecialProductRules `json:"special_product_rules"`
	CanQueryDimension   common.DoCanQueryDimension `json:"can_query_dimension"`
	MultipleMode        []string                   `json:"multiple_mode"`            //多模式
	OperateType         []int                      `json:"operate_type"`             //操作类型
	AgeGroup            []AgeGroupItem             `json:"age_group"`                //年龄段
	ChartSettings       *common.DoChartSettings    `json:"chart_settings,omitempty"` //图表设置
}

type AgeGroupItem struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

type DetailData struct {
	MerchantId int                    `json:"merchant_id"`
	MemberId   int                    `json:"member_id"`
	Id         int                    `json:"id"`
	Name       string                 `json:"name"`
	Payload    common.TemplatePayload `json:"payload"`
	OperatorId int                    `json:"operator_id"`
	Category   int                    `json:"category"`
}

type DoModify struct {
	DoCommonParams
	TemplateId int `json:"id"`
	SdType     int `json:"sdtype"`
}

func Create(params DoCreate) (int, error) {
	config, configErr := GetCategoryConfig(params.Category)
	if configErr != nil {
		return 0, configErr
	}
	//该报表没有列表权限
	if !config.Permission.Create {
		return 0, nil
	}
	//主账号不隔离，直接取主账号信息
	if !config.Permission.AccountIsolation {
		params.MemberId = params.MerchantId
	}
	//格式验证
	err := validateCreateParams(&params, config, params.Category)
	if err != nil {
		return 0, err
	}
	//验证是不是单模板模式
	if config.Permission.SingleTemplate {
		poTemplate := report.TemplateMerchantFind{
			MerchantId: params.MerchantId,
			MemberId:   params.MemberId,
			Category:   params.Category,
		}
		templateList, templateListErr := repository.ReportTemplateConfigRepository.MerchantFind(poTemplate)
		if templateListErr != nil {
			return 0, templateListErr
		}
		//如果是单模板，不能创建多个
		if len(templateList) > 0 {
			return 0, errors.New("不能创建多个模板")
		}
	}

	var payloadParams common.TemplatePayload
	err = utils.JsonConvertor(params, &payloadParams)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("template payload params error. %s", err))
		return 0, errors.New("参数格式问题，请检查")
	}
	//处理图表设置
	payloadParams.ClearChartSettings()
	//处理特殊产品规则
	payloadParams.HandleSpecialProductRules()
	//集团账号处理维度
	payloadParams.HandleDimension(params.SdType, params.Category)
	//集团账号处理标签汇总、及标签中心标签更新处理
	if err = payloadParams.HandleDimensionTagGroup(params.SdType, params.MerchantId, params.Category); err != nil {
		return 0, err
	}
	//处理年龄转方案地
	ageGroupSchemeId, ageGroupResErr := HandleAgeGroupToScheme(params.MerchantId, params.AgeGroup, params.MultipleMode, 0)
	if ageGroupResErr != nil {
		return 0, ageGroupResErr
	}
	payloadParams.DimensionScheme.AgeGroup = ageGroupSchemeId

	//处理扩展信息
	poTemplate := report.TemplateCreate{
		MerchantId: params.MerchantId,
		MemberId:   params.MemberId,
		Category:   params.Category,
		Name:       params.Title,
		OperatorId: params.MemberId,
		Payload:    payloadParams,
	}
	var templateId int
	templateId, err = repository.ReportTemplateConfigRepository.Create(poTemplate)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("template payload add error. %s, 写入数据：%+v", err, poTemplate))
		return 0, errors.New("新增失败，请重试")
	}

	return templateId, nil
}

func Modify(params DoModify) error {
	if params.TemplateId == 0 {
		return errors.New("编辑ID不能为空")
	}
	if params.MerchantId == 0 || params.MemberId == 0 {
		return errors.New("用户信息错误")
	}
	//验证是否存在
	templateInfo, err := existCheck(params.TemplateId, params.MerchantId, 0)
	if err != nil {
		return err
	}
	config, configErr := GetCategoryConfig(templateInfo.Category)
	if configErr != nil {
		return configErr
	}
	//该报表没有列表权限
	if !config.Permission.Modify {
		return nil
	}
	//主账号不隔离，直接取主账号信息
	if !config.Permission.AccountIsolation {
		params.MemberId = params.MerchantId
		//如果需要隔离需要验证
		if templateInfo.MemberId != params.MerchantId {
			return errors.New("无法编辑该模板，请重试")
		}
	} else {
		//如果需要隔离需要验证
		if templateInfo.MemberId != params.MemberId {
			return errors.New("无法编辑该模板，请重试")
		}
	}
	//格式验证
	err = validateModifyParams(&params, config, templateInfo.Category)
	if err != nil {
		return err
	}
	var payloadParams common.TemplatePayload
	err = utils.JsonConvertor(params, &payloadParams)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("template payload modify params error. %s", err))
		return errors.New("参数格式问题，请检查")
	}
	//处理可查询维度
	payloadParams.ClearCanQueryDimension()
	//处理图表设置
	payloadParams.ClearChartSettings()
	//处理特殊产品规则
	payloadParams.HandleSpecialProductRules()
	//集团账号处理维度
	payloadParams.HandleDimension(params.SdType, templateInfo.Category)
	//集团账号处理标签汇总、及标签中心标签更新处理
	if err = payloadParams.HandleDimensionTagGroup(params.SdType, params.MerchantId, templateInfo.Category); err != nil {
		return err
	}
	//处理年龄转方案地
	ageGroupSchemeId, ageGroupResErr := HandleAgeGroupToScheme(params.MerchantId, params.AgeGroup, params.MultipleMode, templateInfo.Payload.DimensionScheme.AgeGroup)
	if ageGroupResErr != nil {
		return ageGroupResErr
	}
	payloadParams.DimensionScheme.AgeGroup = ageGroupSchemeId

	//处理扩展信息
	poTemplate := report.TemplateUpdate{
		MerchantId: params.MerchantId,
		MemberId:   params.MemberId,
		Name:       params.Title,
		OperatorId: params.MemberId,
		Payload:    payloadParams,
		Id:         params.TemplateId,
	}
	err = repository.ReportTemplateConfigRepository.Update(poTemplate)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("template payload modify error. %s, 编辑数据：%+v", err, poTemplate))
		return errors.New("编辑失败，请重试")
	}

	return nil
}

func Delete(templateId int, merchantId int, memberId int) error {
	templateInfo, err := existCheck(templateId, merchantId, 0)
	if err != nil {
		return err
	}
	config, configErr := GetCategoryConfig(templateInfo.Category)
	if configErr != nil {
		return configErr
	}
	//该报表没有列表权限
	if !config.Permission.Delete {
		return nil
	}
	//主账号不隔离，直接取主账号信息
	if !config.Permission.AccountIsolation {
		memberId = merchantId
		//如果需要隔离需要验证
		if templateInfo.MemberId != merchantId {
			return errors.New("无法删除该模板，请重试")
		}
	} else {
		//如果需要隔离需要验证
		if templateInfo.MemberId != memberId {
			return errors.New("无法删除该模板，请重试")
		}
	}

	poTemplate := report.TemplateDelete{
		MerchantId: merchantId,
		MemberId:   memberId,
		Id:         templateId,
	}
	err = repository.ReportTemplateConfigRepository.Delete(poTemplate)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("template payload delete error. %s", err))
		return errors.New("删除失败，请重试")
	}

	//刪除成功
	global.LOG.Info(fmt.Sprintf("template del. %+v", templateInfo))

	return nil
}

// 批量清除模板里的方案ID
func BatchRmDimensionSchemeIdsByMerchantIdAndMemberId(merchantId int, schemeIds []int) error {
	TemplateList, err := repository.ReportTemplateConfigRepository.MerchantFindAll(merchantId)
	//无数据
	if TemplateList == nil || len(TemplateList) == 0 {
		return nil
	}

	var data = make(map[int]interface{})
	for _, templateItem := range TemplateList {
		var templateItemPayload common.TemplatePayload
		err = utils.JsonConvertor(templateItem.Payload, &templateItemPayload)
		if err != nil {
			continue
		}

		needUpdate := false

		//是否配置了汇总可查维度
		canQueryDimensionExtOptionUseOption := false
		//维度方案解析
		if templateItemPayload.DimensionScheme.Spu > 0 {
			if utils.Container(schemeIds, templateItemPayload.DimensionScheme.Spu) {
				templateItemPayload.DimensionScheme.Spu = 0
				needUpdate = true
				//如果勾选了使用分组方案，需要移除
				if templateItemPayload.CanQueryDimension.Option == enum.DimensionSpu {
					canQueryDimensionExtOptionUseOption = true
				}
			}
		}
		if templateItemPayload.DimensionScheme.Sku > 0 {
			if utils.Container(schemeIds, templateItemPayload.DimensionScheme.Sku) {
				templateItemPayload.DimensionScheme.Sku = 0
				needUpdate = true
				//如果勾选了使用分组方案，需要移除
				if templateItemPayload.CanQueryDimension.Option == enum.DimensionSku {
					canQueryDimensionExtOptionUseOption = true
				}
			}
		}
		if templateItemPayload.DimensionScheme.PayMode > 0 {
			if utils.Container(schemeIds, templateItemPayload.DimensionScheme.PayMode) {
				templateItemPayload.DimensionScheme.PayMode = 0
				needUpdate = true
			}
		}
		if templateItemPayload.DimensionScheme.SaleChannel > 0 {
			if utils.Container(schemeIds, templateItemPayload.DimensionScheme.SaleChannel) {
				templateItemPayload.DimensionScheme.SaleChannel = 0
				needUpdate = true
				//如果勾选了使用分组方案，需要移除
				if templateItemPayload.CanQueryDimension.Option == enum.DimensionSaleChannel {
					canQueryDimensionExtOptionUseOption = true
				}
			}
		}
		//移除汇总可查维度的扩展信息的使用方案选项
		if canQueryDimensionExtOptionUseOption == true &&
			utils.Container(templateItemPayload.CanQueryDimension.ExtOption, enum.TemplateExtOptionUseDimensionSchemeGroup) {
			var canQueryDimensionExtOption = templateItemPayload.CanQueryDimension.ExtOption
			canQueryDimensionExtOption = utils.RemoveIfEqual(canQueryDimensionExtOption, enum.TemplateExtOptionUseDimensionSchemeGroup)
			templateItemPayload.CanQueryDimension.ExtOption = canQueryDimensionExtOption
		}
		if needUpdate {
			data[templateItem.Id] = templateItemPayload
		}
	}

	//数据库更新
	if len(data) > 0 {
		err = repository.ReportTemplateConfigRepository.BatchMerchantUpdate(data)
		if err != nil {
			return err
		}
	}

	return nil
}

func existCheck(templateId int, merchantId int, memberId int) (*DetailData, error) {
	poTemplate := report.TemplateMerchantAndIdFind{
		MerchantId: merchantId,
		Id:         templateId,
	}
	if memberId != 0 {
		poTemplate.MemberId = memberId
	}
	var templateInfo *report.TemplateListItem
	var err error
	if memberId == 0 {
		templateInfo, err = repository.ReportTemplateConfigRepository.MainMerchantFirst(poTemplate)
	} else {
		templateInfo, err = repository.ReportTemplateConfigRepository.MerchantFirst(poTemplate)
	}
	if err != nil {
		return nil, err
	}
	var detail DetailData
	err = utils.JsonConvertor(templateInfo, &detail)
	if err != nil {
		return nil, err
	}
	return &detail, nil
}

func validateCreateParams(params *DoCreate, config *CategoryConfig, category int) error {
	var paramsData *DoCommonParams
	err := utils.JsonConvertor(params, &paramsData)
	if err != nil {
		return err
	}
	err = validateParams(paramsData, config, category)
	if err != nil {
		return err
	}
	if params.Category == 0 && FieldRequired(config.Fields.Category) {
		return errors.New("分类信息错误")
	}
	return nil
}

func validateModifyParams(params *DoModify, config *CategoryConfig, category int) error {
	var paramsData *DoCommonParams
	err := utils.JsonConvertor(params, &paramsData)
	if err != nil {
		return err
	}
	err = validateParams(paramsData, config, category)
	if err != nil {
		return err
	}
	return nil
}

func validateParams(params *DoCommonParams, config *CategoryConfig, category int) error {
	//模板名称验证
	if FieldRequired(config.Fields.Title) {
		if err := validateTitle(params.Title); err != nil {
			return err
		}
	}
	//模板维度验证
	if FieldRequired(config.Fields.Dimension) {
		if err := validateDimension(params.Dimension, config.Definition); err != nil {
			return err
		}
	}
	//模板指标验证
	if FieldRequired(config.Fields.Indicator) {
		if err := validateIndicator(params.Indicator, config.Definition); err != nil {
			return err
		}
	}
	//商户信息验证
	if params.MerchantId == 0 || params.MemberId == 0 {
		return errors.New("用户信息错误")
	}
	//方案id有效验证
	if err := validateDimensionScheme(params.DimensionScheme); err != nil {
		return err
	}
	//特殊维度配置限制
	if err := validateSpecialDimension(params.SpecialDimension, category); err != nil {
		return err
	}
	//多维模式验证
	if FieldRequired(config.Fields.MultipleMode) {
		if len(params.MultipleMode) == 0 {
			return errors.New("多维数据选项不能为空")
		}
	}
	//年龄段验证
	if err := validateAgeGroup(params.AgeGroup, params.MultipleMode); err != nil {
		return err
	}
	//图表设置验证
	if err := validateChartSettings(params.ChartSettings); err != nil {
		return err
	}
	return nil
}

func isValidChar(r rune) bool {
	return unicode.Is(unicode.Han, r) || unicode.IsLetter(r) || unicode.IsDigit(r)
}

func HandleAgeGroupToScheme(merchantId int, ageGroup []AgeGroupItem, MultipleMode []string, schemeId int) (int, error) {
	if MultipleMode == nil || len(MultipleMode) == 0 {
		return 0, nil
	}
	rmAgeGroup := false
	if !utils.Container(MultipleMode, enum.TemplateMultipleModeAge) {
		rmAgeGroup = true
	}
	var groups [][]int
	var allAge []int
	for _, item := range ageGroup {
		var tmp []int
		for i := item.Min; i < item.Max; i++ {
			allAge = append(allAge, i)
		}
		tmp = append(tmp, item.Min, item.Max)
		groups = append(groups, tmp)
	}
	if len(allAge) > 0 && utils.ExistDuplicate(allAge) {
		return 0, errors.New("年龄段存在重合，请重试")
	}
	otherGroup := make(map[int]string, 1)
	otherGroup[enum.AgeUnknownKey] = "未知"
	return dimensionscheme.HandleAgeGroup(merchantId, "年龄段方案", groups, otherGroup, schemeId, rmAgeGroup)
}

func validateDimensionScheme(dimensionScheme common.DoDimensionScheme) error {
	var schemeIds []int
	//维度方案解析
	if dimensionScheme.Spu > 0 {
		schemeIds = append(schemeIds, dimensionScheme.Spu)
	}
	if dimensionScheme.Sku > 0 {
		schemeIds = append(schemeIds, dimensionScheme.Sku)
	}
	if dimensionScheme.PayMode > 0 {
		schemeIds = append(schemeIds, dimensionScheme.PayMode)
	}
	if dimensionScheme.SaleChannel > 0 {
		schemeIds = append(schemeIds, dimensionScheme.SaleChannel)
	}

	if len(schemeIds) > 0 {
		err := dimensionscheme.ValidateSchemeIds(schemeIds)
		if err != nil {
			return err
		}
	}

	return nil
}

func validateSpecialDimension(specialDimension common.DoSpecialDimension, category int) error {
	limitNum := 100
	// 部分报表方框限制
	if utils.Container([]int{enum.TemplateCategoryTouristSourceArea}, category) {
		limitNum = 200
	}
	msg := "维度数据范围[%s],选择数量过大,最大可选%d"
	if len(specialDimension.Sku) > limitNum {
		return errors.New(fmt.Sprintf(msg, "票种", limitNum))
	}
	if len(specialDimension.Spu) > limitNum {
		return errors.New(fmt.Sprintf(msg, "产品", limitNum))
	}
	limitNum = 100
	if len(specialDimension.PayMode) > limitNum {
		return errors.New(fmt.Sprintf(msg, "支付方式", limitNum))
	}
	if len(specialDimension.SaleChannel) > limitNum {
		return errors.New(fmt.Sprintf(msg, "订单渠道", limitNum))
	}
	if len(specialDimension.SaleChannel) > limitNum {
		return errors.New(fmt.Sprintf(msg, "分销商", limitNum))
	}
	if len(specialDimension.Operator) > limitNum {
		return errors.New(fmt.Sprintf(msg, "操作员", limitNum))
	}
	if len(specialDimension.SellOperator) > limitNum {
		return errors.New(fmt.Sprintf(msg, "售票员", limitNum))
	}
	if len(specialDimension.SellSite) > limitNum {
		return errors.New(fmt.Sprintf(msg, "售票站点", limitNum))
	}
	return nil
}

func validateTitle(title string) error {
	if title == "" {
		return errors.New("名称不能为空")
	}
	if len([]rune(title)) > 20 {
		return errors.New("名称不能超过20个字")
	}
	for _, char := range title {
		if !isValidChar(char) {
			return errors.New("名称错误，仅支持中英文数字")
		}
	}
	return nil
}

func validateDimension(dimension []string, definition []common.DefinitionItem) error {
	if len(dimension) == 0 {
		return errors.New("统计维度不能为空")
	}
	if len(dimension) > 0 {
		for _, dimensionItem := range dimension {
			//维度比较
			dimensionExist := false
			for _, definitionItem := range definition {
				if definitionItem.Key == enum.TemplateDefinitionDimension {
					for _, dimensionInfo := range definitionItem.Child {
						if dimensionItem == dimensionInfo.Key {
							dimensionExist = true
						}
					}
				}
			}
			if _, ok := enum.TemplateDimensionFieldsMapNoValidate[dimensionItem]; !ok && !dimensionExist {
				return errors.New(fmt.Sprintf("统计维度，部分字段不存在 【%s】", dimensionItem))
			}
		}
	}
	return nil
}

func validateIndicator(indicator []string, definition []common.DefinitionItem) error {
	if len(indicator) == 0 {
		return errors.New("统计指标不能为空")
	}
	if len(indicator) > 0 {
		for _, indicatorItem := range indicator {
			//维度比较
			indicatorExist := false
			for _, definitionItem := range definition {
				if definitionItem.Key == enum.TemplateDefinitionIndicator {
					for _, indicatorInfo := range definitionItem.Child {
						if indicatorItem == indicatorInfo.Key {
							indicatorExist = true
						}
					}
				}
			}
			if !indicatorExist {
				return errors.New(fmt.Sprintf("统计指标，部分字段不存在 【%s】", indicatorItem))
			}
		}
	}
	return nil
}

func validateAgeGroup(ageGroup []AgeGroupItem, MultipleMode []string) error {
	if !utils.Container(MultipleMode, enum.TemplateMultipleModeAge) {
		return nil
	}
	if len(ageGroup) == 0 {
		return errors.New("年龄段配置为空，保存失败")
	}
	limitAgeGroupNum := 10
	if len(ageGroup) > limitAgeGroupNum {
		return errors.New(fmt.Sprintf("年龄段配置超过最大数量%d", limitAgeGroupNum))
	}

	return nil
}

func validateChartSettings(chartSettings *common.DoChartSettings) error {
	if chartSettings == nil {
		return nil
	}
	if chartSettings.Enable != 0 && chartSettings.Value <= 0 {
		return errors.New("图表设置错误")
	}
	limitValueNum := 999
	if chartSettings.Enable != 0 && chartSettings.Value > limitValueNum {
		return errors.New(fmt.Sprintf("图表设置错误，超出最大限制%d", limitValueNum))
	}
	return nil
}

// GetAllTemplatesByMerchantId 获取商户的所有模板
func GetAllTemplatesByMerchantId(merchantId int) ([]DetailData, error) {
	templateList, err := repository.ReportTemplateConfigRepository.MerchantFindAll(merchantId)
	if err != nil {
		return nil, err
	}

	// 转换为DetailData结构
	result := make([]DetailData, 0, len(templateList))
	for _, tpl := range templateList {
		var detailData DetailData
		var templatePayload common.TemplatePayload

		detailData.Id = tpl.Id
		detailData.MerchantId = tpl.MerchantId
		detailData.MemberId = tpl.MemberId
		detailData.Name = tpl.Name
		detailData.Category = tpl.Category
		detailData.OperatorId = tpl.OperatorId

		// 解析模板内容
		err = utils.JsonConvertor(tpl.Payload, &templatePayload)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("解析模板内容失败: %s", err))
			continue
		}
		detailData.Payload = templatePayload

		result = append(result, detailData)
	}

	return result, nil
}

// UpdateTemplate 更新模板
func UpdateTemplate(template DetailData) error {
	templateUpdate := report.TemplateUpdate{
		Id:         template.Id,
		MerchantId: template.MerchantId,
		MemberId:   template.MemberId,
		Name:       template.Name,
		Payload:    template.Payload,
		OperatorId: template.OperatorId,
	}

	err := repository.ReportTemplateConfigRepository.Update(templateUpdate)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("更新模板失败: %s", err))
		return err
	}

	return nil
}
