package template

import (
	"errors"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	reportcommon "report-service/internal/domain/logic/report/common"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/report"
	"report-service/pkg/sdk/api/landservice"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggroup"
	"report-service/pkg/sdk/api/ticketservice"
	"report-service/pkg/utils"
	"strings"

	"github.com/spf13/cast"
)

type DoListItem struct {
	Id                  int                        `json:"id"`
	Title               string                     `json:"title"`
	Category            int                        `json:"category"`
	Dimension           []string                   `json:"dimension"`
	Indicator           []string                   `json:"indicator"`
	SpecialDimension    common.SpecialDimension    `json:"special_dimension"`
	DimensionScheme     common.DimensionScheme     `json:"dimension_scheme"`
	DimensionTagGroup   map[string]common.TagGroup `json:"dimension_tag_group"`
	SpecialProductRules common.SpecialProductRules `json:"special_product_rules"`
	CanQueryDimension   common.DoCanQueryDimension `json:"can_query_dimension"`
	AgeGroup            []AgeGroupItem             `json:"age_group,omitempty"`      //年龄段
	MultipleMode        []string                   `json:"multiple_mode,omitempty"`  //多模式
	OperateType         []int                      `json:"operate_type,omitempty"`   //操作类型
	ChartSettings       *common.DoChartSettings    `json:"chart_settings,omitempty"` //图表设置
}

// List 接口废弃
func List(category int, merchantId int, memberId int) ([]DoListItem, error) {
	poTemplate := report.TemplateMerchantFind{
		MerchantId: merchantId,
		MemberId:   memberId,
		Category:   category,
	}
	TemplateList, err := repository.ReportTemplateConfigRepository.MerchantFind(poTemplate)
	if err != nil {
		return nil, err
	}
	//无数据
	if len(TemplateList) == 0 {
		return nil, nil
	}

	var result []DoListItem
	result, _ = handleTemplateList(TemplateList, false)

	return result, nil
}

func SimpleList(category int, merchantId int, memberId int) ([]DoListItem, error) {
	config, configErr := GetCategoryConfig(category)
	if configErr != nil {
		return nil, configErr
	}
	//该报表没有列表权限
	if !config.Permission.List {
		return nil, nil
	}
	//主账号不隔离，直接取主账号信息
	if !config.Permission.AccountIsolation {
		memberId = merchantId
	}
	//获取默认模板
	templateDefault, TemplateDefaultErr := HandleCategoryConfigDefault(config, merchantId, memberId)
	if TemplateDefaultErr != nil {
		return nil, TemplateDefaultErr
	}
	poTemplate := report.TemplateMerchantFind{
		MerchantId: merchantId,
		MemberId:   memberId,
		Category:   category,
	}
	templateList, err := repository.ReportTemplateConfigRepository.MerchantFind(poTemplate)
	if err != nil {
		return nil, err
	}
	//无数据
	if len(templateList) == 0 && templateDefault == nil {
		return nil, nil
	}
	//如果存在默认值，将默认值导入
	if len(templateList) == 0 && templateDefault != nil {
		templateList = append(templateList, *templateDefault)
	}
	//列表处理
	var result []DoListItem
	for _, templateItem := range templateList {
		var itemData DoListItem
		var templateItemPayload common.TemplatePayload
		err = utils.JsonConvertor(templateItem.Payload, &templateItemPayload)
		if err != nil {
			continue
		}
		itemData.Id = templateItem.Id
		itemData.Title = templateItem.Name
		itemData.SpecialProductRules = templateItemPayload.SpecialProductRules
		if len(templateItemPayload.Dimension) > 0 {
			itemData.Dimension = templateItemPayload.Dimension
		}
		if len(templateItemPayload.Indicator) > 0 {
			itemData.Indicator = templateItemPayload.Indicator
		}
		if templateItemPayload.CanQueryDimension.Enable == 0 && templateItemPayload.CanQueryDimension.ExtOption == nil {
			itemData.CanQueryDimension.ExtOption = []string{}
		}

		result = append(result, itemData)
	}

	return result, nil
}

func Detail(templateId int, category int, merchantId int, memberId int, optional ...interface{}) (*DoListItem, error) {
	if templateId == 0 && category == 0 {
		return nil, errors.New("查询详情参数错误")
	}
	onlyOriginalData := true
	if len(optional) > 0 {
		onlyOriginalData = cast.ToBool(optional[0])
	}
	//id查询报表详情
	var templateInfo *report.TemplateListItem
	if templateId != 0 {
		poTemplate := report.TemplateMerchantAndIdFind{
			MerchantId: merchantId,
			Id:         templateId,
		}
		templateInfoRes, templateInfoErr := repository.ReportTemplateConfigRepository.MainMerchantFirst(poTemplate)
		if templateInfoErr != nil {
			return nil, templateInfoErr
		}
		templateInfo = templateInfoRes
		if category == 0 {
			category = templateInfo.Category
		} else if category != templateInfo.Category {
			return nil, errors.New("模板不存在，无法查看")
		}
	}
	//报表配置
	config, configErr := GetCategoryConfig(category)
	if configErr != nil {
		return nil, configErr
	}
	//该报表没有列表权限
	if !config.Permission.Detail {
		return nil, nil
	}
	//主账号不隔离，直接取主账号信息
	if !config.Permission.AccountIsolation {
		if templateInfo != nil && templateInfo.MemberId != merchantId {
			return nil, errors.New("无法查看该模板")
		}
		memberId = merchantId
	} else {
		//账号隔离的话需要再验证下
		if templateInfo != nil && templateInfo.MemberId != memberId {
			return nil, errors.New("无法查看该模板")
		}
	}
	//获取默认模板
	templateDefault, TemplateDefaultErr := HandleCategoryConfigDefault(config, merchantId, memberId)
	if TemplateDefaultErr != nil {
		return nil, TemplateDefaultErr
	}
	if templateId == 0 && config.Permission.SingleTemplate {
		poTemplate := report.TemplateMerchantFind{
			MerchantId: merchantId,
			MemberId:   memberId,
			Category:   category,
		}
		templateList, templateListErr := repository.ReportTemplateConfigRepository.MerchantFind(poTemplate)
		if templateListErr != nil {
			return nil, templateListErr
		}
		if len(templateList) > 0 {
			templateInfo = &templateList[0]
		}
	}
	//id不存在时，支持模板默认配置
	if (templateInfo == nil || templateInfo.Category != category) && templateDefault != nil {
		templateInfo = templateDefault
	}
	if templateInfo == nil {
		return nil, errors.New("模板不存在，查询错误")
	}
	//详情解析
	var templateList []report.TemplateListItem
	var result []DoListItem
	var templateItem report.TemplateListItem
	err := utils.JsonConvertor(templateInfo, &templateItem)
	if err != nil {
		return nil, err
	}
	templateList = append(templateList, templateItem)
	result, _ = handleTemplateList(templateList, onlyOriginalData)

	if len(result) == 0 {
		return nil, errors.New("无数据")
	}
	return &result[0], nil
}

func handleTemplateList(listData []report.TemplateListItem, onlyOriginalData bool) ([]DoListItem, error) {
	//维度范围解析
	var spuIds []int
	var skuIds []int
	var memberIds []int
	var siteIds []int

	//维度方案解析
	var schemeIds []int
	var ageGroupSchemeId int

	//获取id信息
	if !onlyOriginalData {
		for _, templateItem := range listData {
			var templateItemPayload common.TemplatePayload
			err := utils.JsonConvertor(templateItem.Payload, &templateItemPayload)
			if err != nil {
				continue
			}

			var templateItemPayloadSpecialDimension common.DoSpecialDimension
			err = utils.JsonConvertor(templateItemPayload.SpecialDimension, &templateItemPayloadSpecialDimension)
			if err != nil {
				continue
			}

			//维度范围解析
			if len(templateItemPayloadSpecialDimension.Spu) > 0 {
				spuIds = append(spuIds, templateItemPayloadSpecialDimension.Spu...)
			}
			if len(templateItemPayloadSpecialDimension.Sku) > 0 {
				skuIds = append(skuIds, templateItemPayloadSpecialDimension.Sku...)
			}
			//分销商
			if len(templateItemPayloadSpecialDimension.Distributor) > 0 {
				memberIds = append(memberIds, templateItemPayloadSpecialDimension.Distributor...)
			}
			//维度方案解析
			if templateItemPayload.DimensionScheme.Spu > 0 {
				schemeIds = append(schemeIds, templateItemPayload.DimensionScheme.Spu)
			}
			if templateItemPayload.DimensionScheme.Sku > 0 {
				schemeIds = append(schemeIds, templateItemPayload.DimensionScheme.Sku)
			}
			if templateItemPayload.DimensionScheme.PayMode > 0 {
				schemeIds = append(schemeIds, templateItemPayload.DimensionScheme.PayMode)
			}
			if templateItemPayload.DimensionScheme.SaleChannel > 0 {
				schemeIds = append(schemeIds, templateItemPayload.DimensionScheme.SaleChannel)
			}
			//年龄段
			if templateItemPayload.DimensionScheme.AgeGroup > 0 {
				ageGroupSchemeId = templateItemPayload.DimensionScheme.AgeGroup
			}
			//售票员
			if len(templateItemPayloadSpecialDimension.SellOperator) > 0 {
				memberIds = append(memberIds, templateItemPayloadSpecialDimension.SellOperator...)
			}
			//售票站点
			if len(templateItemPayloadSpecialDimension.SellSite) > 0 {
				siteIds = append(siteIds, templateItemPayloadSpecialDimension.SellSite...)
			}
			//操作员
			if len(templateItemPayloadSpecialDimension.Operator) > 0 {
				memberIds = append(memberIds, templateItemPayloadSpecialDimension.Operator...)
			}
			//集团成功
			if len(templateItemPayloadSpecialDimension.GroupMember) > 0 {
				memberIds = append(memberIds, templateItemPayloadSpecialDimension.GroupMember...)
			}
		}
	}

	payModeMap := enum.PaymodeMap
	if len(listData) > 0 && !onlyOriginalData {
		//支付方式支持下商户自定义收款方式
		payModeMap = reportcommon.PayWayListConf(listData[0].MerchantId, false)
	}
	saleChannelMap := enum.OrderModeMap
	var spuMap, skuMap, schemeMap, memberNameMap, sellSiteNameMap map[int]string

	if len(spuIds) > 0 {
		spuMap = getSpuInfoByIds(spuIds)
	}
	if len(skuIds) > 0 {
		skuMap = getSkuInfoByIds(skuIds)
	}
	if len(schemeIds) > 0 {
		schemeMap = getSchemeInfoByIds(schemeIds)
	}
	if len(memberIds) > 0 {
		memberNameMap = getMemberNameMapByIds(memberIds)
	}
	if len(siteIds) > 0 {
		sellSiteNameMap = getSiteNameMapByIds(siteIds)
	}
	var ageGroupMap map[int][]AgeGroupItem
	var ageGroupNameMap map[int]string
	if ageGroupSchemeId > 0 {
		ageGroupMap, ageGroupNameMap = getSchemeGroupInfoById(listData[0].MerchantId, ageGroupSchemeId)
	}

	var result []DoListItem
	for _, templateItem := range listData {
		var itemData DoListItem
		var templateItemPayload common.TemplatePayload
		err := utils.JsonConvertor(templateItem.Payload, &templateItemPayload)
		if err != nil {
			continue
		}

		templateItemPayloadSpecialDimension := templateItemPayload.SpecialDimension

		itemData.Id = templateItem.Id
		itemData.Title = templateItem.Name
		itemData.Category = templateItem.Category
		itemData.CanQueryDimension = common.DoCanQueryDimension{
			Enable:    templateItemPayload.CanQueryDimension.Enable,
			Option:    templateItemPayload.CanQueryDimension.Option,
			ExtOption: []string{},
		}
		if len(templateItemPayload.CanQueryDimension.ExtOption) > 0 {
			itemData.CanQueryDimension.ExtOption = templateItemPayload.CanQueryDimension.ExtOption
		}
		itemData.MultipleMode = templateItemPayload.MultipleMode
		itemData.OperateType = templateItemPayload.OperateType
		itemData.ChartSettings = templateItemPayload.ChartSettings

		//兼容处理特殊产品规则
		templateItemPayload.HandleSpecialProductRules()

		itemData.SpecialProductRules.PackType = templateItemPayload.SpecialProductRules.PackType
		if len(itemData.SpecialProductRules.PackType) == 0 {
			itemData.SpecialProductRules.PackType = []int{}
		}
		itemData.SpecialProductRules.ShowBindType = templateItemPayload.SpecialProductRules.ShowBindType
		if len(itemData.SpecialProductRules.ShowBindType) == 0 {
			itemData.SpecialProductRules.ShowBindType = []int{}
		}
		itemData.SpecialProductRules.AnnualCardType = templateItemPayload.SpecialProductRules.AnnualCardType
		if len(itemData.SpecialProductRules.AnnualCardType) == 0 {
			itemData.SpecialProductRules.AnnualCardType = []int{}
		}
		itemData.SpecialProductRules.ExchangeCouponType = templateItemPayload.SpecialProductRules.ExchangeCouponType
		if len(itemData.SpecialProductRules.ExchangeCouponType) == 0 {
			itemData.SpecialProductRules.ExchangeCouponType = []int{}
		}
		if len(templateItemPayload.Dimension) > 0 {
			itemData.Dimension = templateItemPayload.Dimension
		}
		if len(templateItemPayload.Indicator) > 0 {
			itemData.Indicator = templateItemPayload.Indicator
		}

		//维度范围解析
		if len(templateItemPayloadSpecialDimension.Spu) > 0 {
			var spuList []common.ChildItem
			for _, spu := range templateItemPayloadSpecialDimension.Spu {
				var spuName = ""
				spuTitle, exist := spuMap[spu]
				if exist {
					spuName = spuTitle
				}
				spuList = append(spuList, common.ChildItem{
					Id:   spu,
					Name: spuName,
				})
			}
			itemData.SpecialDimension.Spu = spuList
		}

		if len(templateItemPayloadSpecialDimension.Sku) > 0 {
			var skuList []common.ChildItem
			for _, sku := range templateItemPayloadSpecialDimension.Sku {
				var skuName = ""
				skuTitle, exist := skuMap[sku]
				if exist {
					skuName = skuTitle
				}
				skuList = append(skuList, common.ChildItem{
					Id:   sku,
					Name: skuName,
				})
			}
			itemData.SpecialDimension.Sku = skuList
		}

		if len(templateItemPayloadSpecialDimension.PayMode) > 0 {
			var payModeList []common.ChildItem
			for _, payMode := range templateItemPayloadSpecialDimension.PayMode {
				var payModeName = ""
				payModeTitle, exist := payModeMap[payMode]
				if exist {
					payModeName = payModeTitle
				}
				payModeList = append(payModeList, common.ChildItem{
					Id:   payMode,
					Name: payModeName,
				})
			}
			itemData.SpecialDimension.PayMode = payModeList
		}

		if len(templateItemPayloadSpecialDimension.SaleChannel) > 0 {
			var saleChannelList []common.ChildItem
			for _, saleChannel := range templateItemPayloadSpecialDimension.SaleChannel {
				var saleChannelName = ""
				saleChannelTitle, exist := saleChannelMap[saleChannel]
				if exist {
					saleChannelName = saleChannelTitle
				}
				saleChannelList = append(saleChannelList, common.ChildItem{
					Id:   saleChannel,
					Name: saleChannelName,
				})
			}
			itemData.SpecialDimension.SaleChannel = saleChannelList
		}

		// 维度标签组
		itemData.DimensionTagGroup = make(map[string]common.TagGroup)
		for scene, tagGroupCode := range templateItemPayload.DimensionTagGroup {
			if tagGroupCode == "" {
				continue
			}
			tagCenterTagGroup, err := taggroup.TagGroupPage(&taggroup.MemberRequest{
				Code:     &tagGroupCode,
				MemberID: int64(templateItem.MerchantId),
				PageNum:  1,
				PageSize: 200,
			})
			if err != nil {
				return nil, err
			}
			name := ""
			if len(tagCenterTagGroup.Rows) > 0 {
				name = *tagCenterTagGroup.Rows[0].Name
			}
			itemData.DimensionTagGroup[scene] = common.TagGroup{
				Code: tagGroupCode,
				Name: name,
			}
		}

		//分销商
		if len(templateItemPayloadSpecialDimension.Distributor) > 0 {
			var distributorList []common.ChildItem
			for _, dId := range templateItemPayloadSpecialDimension.Distributor {
				var distributorName = ""
				distributorTitle, exist := memberNameMap[dId]
				if exist {
					distributorName = distributorTitle
				}
				distributorList = append(distributorList, common.ChildItem{
					Id:   dId,
					Name: distributorName,
				})
			}
			itemData.SpecialDimension.Distributor = distributorList
		}

		//维度方案解析
		if templateItemPayload.DimensionScheme.Spu > 0 {
			var schemeSpuName = ""
			schemeSpuTitle, exist := schemeMap[templateItemPayload.DimensionScheme.Spu]
			if exist {
				schemeSpuName = schemeSpuTitle
			}
			itemData.DimensionScheme.Spu = &common.ChildItem{
				Id:   templateItemPayload.DimensionScheme.Spu,
				Name: schemeSpuName,
			}
		}
		if templateItemPayload.DimensionScheme.Sku > 0 {
			var schemeSkuName = ""
			schemeSkuTitle, exist := schemeMap[templateItemPayload.DimensionScheme.Sku]
			if exist {
				schemeSkuName = schemeSkuTitle
			}
			itemData.DimensionScheme.Sku = &common.ChildItem{
				Id:   templateItemPayload.DimensionScheme.Sku,
				Name: schemeSkuName,
			}
		}
		if templateItemPayload.DimensionScheme.PayMode > 0 {
			var schemePayModeName = ""
			schemePayModeTitle, exist := schemeMap[templateItemPayload.DimensionScheme.PayMode]
			if exist {
				schemePayModeName = schemePayModeTitle
			}
			itemData.DimensionScheme.PayMode = &common.ChildItem{
				Id:   templateItemPayload.DimensionScheme.PayMode,
				Name: schemePayModeName,
			}
		}
		if templateItemPayload.DimensionScheme.SaleChannel > 0 {
			var schemeSaleChannelName = ""
			schemeSaleChannelTitle, exist := schemeMap[templateItemPayload.DimensionScheme.SaleChannel]
			if exist {
				schemeSaleChannelName = schemeSaleChannelTitle
			}
			itemData.DimensionScheme.SaleChannel = &common.ChildItem{
				Id:   templateItemPayload.DimensionScheme.SaleChannel,
				Name: schemeSaleChannelName,
			}
		}
		//年龄段
		if templateItemPayload.DimensionScheme.AgeGroup > 0 {
			itemData.AgeGroup = ageGroupMap[templateItemPayload.DimensionScheme.AgeGroup]
			var schemeAgeGroupName = ""
			schemeAgeGroupTitle, exist := ageGroupNameMap[templateItemPayload.DimensionScheme.AgeGroup]
			if exist {
				schemeAgeGroupName = schemeAgeGroupTitle
			}
			itemData.DimensionScheme.AgeGroup = &common.ChildItem{
				Id:   templateItemPayload.DimensionScheme.AgeGroup,
				Name: schemeAgeGroupName,
			}
		}
		//售票员
		if len(templateItemPayloadSpecialDimension.SellOperator) > 0 {
			var sellOperatorList []common.ChildItem
			for _, sellOperatorId := range templateItemPayloadSpecialDimension.SellOperator {
				var sellOperatorName = ""
				sellOperatorTitle, exist := memberNameMap[sellOperatorId]
				if exist {
					sellOperatorName = sellOperatorTitle
				}
				sellOperatorList = append(sellOperatorList, common.ChildItem{
					Id:   sellOperatorId,
					Name: sellOperatorName,
				})
			}
			itemData.SpecialDimension.SellOperator = sellOperatorList
		}
		//售票站点
		if len(templateItemPayloadSpecialDimension.SellSite) > 0 {
			var sellSiteList []common.ChildItem
			for _, sellSiteId := range templateItemPayloadSpecialDimension.SellSite {
				var sellSiteName = ""
				sellSiteTitle, exist := sellSiteNameMap[sellSiteId]
				if exist {
					sellSiteName = sellSiteTitle
				}
				sellSiteList = append(sellSiteList, common.ChildItem{
					Id:   sellSiteId,
					Name: sellSiteName,
				})
			}
			itemData.SpecialDimension.SellSite = sellSiteList
		}
		//操作员
		if len(templateItemPayloadSpecialDimension.Operator) > 0 {
			var operatorList []common.ChildItem
			for _, operatorId := range templateItemPayloadSpecialDimension.Operator {
				var operatorName = ""
				operatorTitle, exist := memberNameMap[operatorId]
				if exist {
					operatorName = operatorTitle
				}
				operatorList = append(operatorList, common.ChildItem{
					Id:   operatorId,
					Name: operatorName,
				})
			}
			itemData.SpecialDimension.Operator = operatorList
		}
		//集团成员
		if len(templateItemPayloadSpecialDimension.GroupMember) > 0 {
			var groupMemberList []common.ChildItem
			for _, groupMemberId := range templateItemPayloadSpecialDimension.GroupMember {
				var groupMemberName = ""
				if groupMemberTitle, ok := memberNameMap[groupMemberId]; ok {
					groupMemberName = groupMemberTitle
				}
				groupMemberList = append(groupMemberList, common.ChildItem{
					Id:   groupMemberId,
					Name: groupMemberName,
				})
			}
			itemData.SpecialDimension.GroupMember = groupMemberList
		}

		result = append(result, itemData)
	}

	return result, nil
}

// 获取Spu信息
func getSpuInfoByIds(spuIds []int) map[int]string {
	spuMap, err := landservice.QueryLandTitleByIds(spuIds)
	if err != nil {
		return nil
	}
	return spuMap
}

// 获取sku信息
func getSkuInfoByIds(skuIds []int) map[int]string {
	skuMap, err := ticketservice.QueryTicketTitleByIds(skuIds)
	if err != nil {
		return nil
	}
	return skuMap
}

// 获取方案信息
func getSchemeInfoByIds(schemeIds []int) map[int]string {
	schemeIds = utils.RemoveDuplicate(schemeIds)
	schemeMap, err := dimensionscheme.SchemeNameMapByIds(schemeIds)
	if err != nil {
		return nil
	}
	return schemeMap
}

// 获取商户名称
func getMemberNameMapByIds(memberIds []int) map[int]string {
	memberMap, err := pftmember.GetMemberNameAndIdMapWithIdKey(memberIds)
	if err != nil {
		return nil
	}
	return memberMap
}

// 获取站点名称map
func getSiteNameMapByIds(siteIds []int) map[int]string {
	siteInfoMap, err := platformrpcapi.QuerySiteNameMapByIds(siteIds)
	if err != nil {
		return nil
	}
	return siteInfoMap
}

// 获取方案信息
func getSchemeGroupInfoById(merchantId int, schemeId int) (map[int][]AgeGroupItem, map[int]string) {
	groupInfo, _, err := dimensionscheme.GroupPagination(merchantId, schemeId, 1, 100)
	if err != nil {
		return nil, nil
	}
	ageGroup := make(map[int][]AgeGroupItem)
	ageGroupName := make(map[int]string)
	for _, group := range groupInfo {
		ageGroupName[group.SchemeId] = group.Name
		nameMap := strings.Split(group.Name, "~")
		if len(nameMap) != 2 {
			continue
		}
		ageGroup[group.SchemeId] = append(ageGroup[group.SchemeId], AgeGroupItem{
			Min: cast.ToInt(nameMap[0]),
			Max: cast.ToInt(nameMap[1]),
		})
	}
	return ageGroup, ageGroupName
}

// GetAllMerchantWithTemplate 获取所有配置了模板的商户ID列表
func GetAllMerchantWithTemplate() ([]int, error) {
	// 直接通过数据库查询获取所有配置了模板的商户ID
	merchantIds, err := repository.ReportTemplateConfigRepository.GetAllMerchantIds()
	if err != nil {
		return nil, err
	}

	return merchantIds, nil
}
