package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/logic/report/template/categoryconfig"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository/report"
	"report-service/pkg/utils"
)

// CategoryConfig 使用 categoryconfig 包中的定义
type CategoryConfig = categoryconfig.CategoryConfig

// GetConfig 获取配置默认定义
func GetConfig(category int) ([]common.DefinitionItem, error) {
	configInfo, err := GetCategoryConfig(category)
	if err != nil {
		return nil, err
	}
	return configInfo.Definition, nil
}

// GetCategoryConfig 获取报表模板基础配置
func GetCategoryConfig(category int) (*CategoryConfig, error) {
	if category == 0 {
		return nil, errors.New("请求参数不能为空")
	}

	// 优先从本地配置读取
	if localConfig := getLocalCategoryConfig(category); localConfig != nil {
		return localConfig, nil
	}

	//获取配置
	configStr := config.GetJson(common.GetTemplateGlobalConfigKey(category), enum.CacheTimeDay)
	if configStr == "" {
		return nil, errors.New("该报表模板，不存在默认定义")
	}
	//解析配置
	var configInfo *CategoryConfig
	err := json.Unmarshal([]byte(configStr), &configInfo)
	if err != nil {
		return nil, fmt.Errorf("配置解析失败，错误信息：%s", err)
	}
	return configInfo, nil
}

// getLocalCategoryConfig 获取本地类别配置
func getLocalCategoryConfig(category int) *CategoryConfig {
	switch category {
	case enum.TemplateCategoryOperationData:
		return categoryconfig.GetOperationDataConfig()
	case enum.TemplateCategoryH5Pay:
		return categoryconfig.GetH5PayConfig()
	case enum.TemplateCategoryPay:
		return categoryconfig.GetPayConfig()
	case enum.TemplateCategoryVerify:
		return categoryconfig.GetVerifyConfig()
	case enum.TemplateCategoryH5Verify:
		return categoryconfig.GetH5VerifyConfig()
	case enum.TemplateCategoryTouristSourceArea:
		return categoryconfig.GetTouristSourceConfig()
	case enum.TemplateCategoryGroupPay:
		return categoryconfig.GetGroupPayConfig()
	case enum.TemplateCategoryGroupVerify:
		return categoryconfig.GetGroupVerifyConfig()
	default:
		return nil
	}
}

// HandleCategoryConfigDefault 处理默认配置
func HandleCategoryConfigDefault(config *CategoryConfig, merchantId int, memberId int) (*report.TemplateListItem, error) {
	if config.Default == nil {
		return nil, nil
	}
	var templateInfo report.TemplateListItem
	err := utils.JsonConvertor(config.Default, &templateInfo)
	if err != nil {
		return nil, err
	}
	if templateInfo.Payload == nil {
		return nil, nil
	}
	templateInfo.MemberId = memberId
	templateInfo.MerchantId = merchantId

	return &templateInfo, nil
}

// FieldRequired 字段必传验证
func FieldRequired(field string) bool {
	return field == "required"
}

// FieldOmitempty 字段非必传验证
func FieldOmitempty(field string) bool {
	return field == "omitempty"
}
