package defaultconfig

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template"
	templateCommon "report-service/internal/domain/logic/report/template/common"
)

func BusinessDataTemplate(tagGroupCode string) *template.DoListItem {
	return &template.DoListItem{
		Title:    "定制报表-营业日报表默认模板",
		Category: enum.TemplateCategoryVerify,
		Dimension: []string{
			enum.DimensionSku,
			enum.DimensionPayMode,
		},
		Indicator: []string{
			enum.IndicatorVerifyCount,
			enum.IndicatorVerifySalePrice,
			enum.IndicatorRevokeCount,
			enum.IndicatorRevokeSalePrice,
			enum.IndicatorAfterSaleCount,
			enum.IndicatorAfterSalePrice,
		},
		DimensionTagGroup: map[string]templateCommon.TagGroup{
			enum.DimensionToTagCenterSceneMapKeyTicket: {
				Code: tagGroupCode,
				Name: "营业日报表默认分组标签",
			},
		},
		SpecialProductRules: templateCommon.SpecialProductRules{
			PackType:           []int{1, 0},
			ShowBindType:       []int{1, 2, 0},
			AnnualCardType:     []int{1, 2, 0},
			ExchangeCouponType: []int{1, 2, 0},
		},
	}
}
