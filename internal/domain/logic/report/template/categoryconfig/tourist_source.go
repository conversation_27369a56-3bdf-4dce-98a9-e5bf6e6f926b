package categoryconfig

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template/common"
)

// GetTouristSourceConfig 获取多维统计/客源地配置 (category=6)
func GetTouristSourceConfig() *CategoryConfig {
	return &CategoryConfig{
		Fields: common.BaseConfigFields{
			Title:               "required",
			Category:            "required",
			Dimension:           "omitempty",
			Indicator:           "omitempty",
			DimensionScheme:     "omitempty",
			SpecialDimension:    "omitempty",
			SpecialProductRules: "omitempty",
		},
		Permission: common.BaseConfigPermission{
			List:             true,
			Create:           true,
			Delete:           true,
			Detail:           true,
			Modify:           true,
			AccountIsolation: true,
		},
		Default: map[string]interface{}{},
		Definition: []common.DefinitionItem{
			{
				Key:   "title",
				Child: []common.DefinitionItemChild{},
				Label: "模板名称",
			},
			{
				Key: "multiple_mode",
				Child: []common.DefinitionItemChild{
					{Key: enum.TemplateMultipleModeSourceArea, Label: "客源地"},
					{Key: enum.TemplateMultipleModeGender, Label: "性别"},
					{Key: enum.TemplateMultipleModeAge, Label: "年龄"},
				},
				Label: "多维数据",
			},
			{
				Key: "dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionRegion, Label: "地域"},
					{Key: enum.DimensionCountry, Label: "国家"},
					{Key: enum.DimensionProvince, Label: "省"},
					{Key: enum.DimensionCity, Label: "市"},
					{Key: enum.DimensionDistrict, Label: "区县"},
				},
				Label: "客源地维度",
				Bubble: &common.DefinitionItemBubble{
					Text: "图表仅支持统计省、市数据",
				},
			},
			{
				Key: "indicator",
				Child: []common.DefinitionItemChild{
					{Key: enum.IndicatorTouristCount, Label: "游客人数"},
				},
				Label: "统计指标",
			},
			{
				Key:   "age_group",
				Child: []common.DefinitionItemChild{},
				Label: "年龄段配置",
			},
			{
				Key: "operate_type",
				Child: []common.DefinitionItemChild{
					{Key: "1", Label: "支付"},
					{Key: "2", Label: "验证"},
					{Key: "3", Label: "取消"},
					{Key: "4", Label: "撤销"},
					{Key: "5", Label: "售后"},
				},
				Label: "数据来源",
			},
			{
				Key: "special_dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionSpu, Label: "产品"},
					{Key: enum.DimensionSku, Label: "票类"},
				},
				Label: "指定产品",
				Bubble: &common.DefinitionItemBubble{
					Text: "若无添加指定产品，表示统计所有自供应产品的订单",
				},
			},
			{
				Key: "chart_settings",
				Child: []common.DefinitionItemChild{
					{
						Key: "enable",
						Child: []common.DefinitionCommonItem{
							{Key: "1", Label: "开"},
							{Key: "0", Label: "关"},
						},
						Label: "选择",
					},
					{Key: "value", Label: "仅展示前[]项数据"},
				},
				Label: "图表设置",
				Bubble: &common.DefinitionItemBubble{
					Text: "该配置仅对客源地图表生效，超过n项数据将被归为其他",
				},
			},
			SpecialProductRulesDefinition,
		},
	}
}
