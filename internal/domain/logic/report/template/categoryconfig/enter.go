package categoryconfig

import "report-service/internal/domain/logic/report/template/common"

// CategoryConfig 报表类别配置结构体
type CategoryConfig struct {
	Fields     common.BaseConfigFields     `json:"fields"`
	Permission common.BaseConfigPermission `json:"permission"`
	Default    interface{}                 `json:"default"`
	Definition []common.DefinitionItem     `json:"definition"`
}

// SpecialProductRulesDefinition 特殊产品统计规则定义
var SpecialProductRulesDefinition = common.DefinitionItem{
	Key: "special_product_rules",
	Child: []common.DefinitionItemChild{
		{
			Key: "pack_type",
			Child: []common.DefinitionCommonItem{
				{Key: "1", Label: "统计主票"},
				{Key: "2", Label: "统计子票"},
			},
			Label: "套票",
		},
		{
			Key: "show_bind_type",
			Child: []common.DefinitionCommonItem{
				{Key: "1", Label: "统计主票"},
				{Key: "2", Label: "统计子票"},
			},
			Label: "捆绑票",
		},
		{
			Key: "annual_card_type",
			Child: []common.DefinitionCommonItem{
				{Key: "1", Label: "统计主卡"},
				{Key: "2", Label: "统计特权"},
			},
			Label: "年卡",
		},
		{
			Key: "exchange_coupon_type",
			Child: []common.DefinitionCommonItem{
				{Key: "1", Label: "统计券单"},
				{Key: "2", Label: "统计兑换订单"},
			},
			Label: "预售券",
		},
	},
	Label: "特殊产品统计规则",
}

// SpecialProductRulesDefault 特殊产品统计规则默认值
var SpecialProductRulesDefault = map[string]interface{}{
	"pack_type":            []int{1},
	"show_bind_type":       []int{1},
	"annual_card_type":     []int{1},
	"exchange_coupon_type": []int{1},
}
