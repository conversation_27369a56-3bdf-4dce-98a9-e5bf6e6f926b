package categoryconfig

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template/common"
)

// GetGroupPayConfig 获取集团报表-销售配置 (category=7)
func GetGroupPayConfig() *CategoryConfig {
	return &CategoryConfig{
		Fields: common.BaseConfigFields{
			Title:               "required",
			Category:            "required",
			Dimension:           "required",
			Indicator:           "required",
			DimensionScheme:     "omitempty",
			SpecialDimension:    "omitempty",
			SpecialProductRules: "omitempty",
		},
		Permission: common.BaseConfigPermission{
			List:             true,
			Create:           true,
			Delete:           true,
			Detail:           true,
			Modify:           true,
			AccountIsolation: true,
		},
		Default: map[string]interface{}{},
		Definition: []common.DefinitionItem{
			{
				Key:   "title",
				Child: []common.DefinitionItemChild{},
				Label: "模板名称",
			},
			{
				Key: "dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionDate, Label: "日期", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSpu, Label: "产品", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSku, Label: "票类", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionPayMode, Label: "支付方式", Bubble: &common.DefinitionItemBubble{Text: "卖出支付方式"}},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道", Bubble: &common.DefinitionItemBubble{Text: "订单销售渠道"}},
					{Key: enum.DimensionDistributor, Label: "分销商", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSellOperator, Label: "售票员", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSellSite, Label: "售票站点", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSaleUnitPrice, Label: "销售单价", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionCostUnitPrice, Label: "采购单价", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionTargetAudience, Label: "适用人群", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionGroupMember, Label: "集团成员", Bubble: &common.DefinitionItemBubble{}},
				},
				Label: "统计维度",
			},
			{
				Key: "indicator",
				Child: []common.DefinitionItemChild{
					{Key: enum.IndicatorPayCount, Label: "销售数量", Bubble: &common.DefinitionItemBubble{Text: "订单支付数量"}},
					{Key: enum.IndicatorPaySalePrice, Label: "销售金额", Bubble: &common.DefinitionItemBubble{Text: "支付收入"}},
					{Key: enum.IndicatorPayCostPrice, Label: "采购金额", Bubble: &common.DefinitionItemBubble{Text: "支付支出"}},
					{Key: enum.IndicatorCancelCount, Label: "取消数量", Bubble: &common.DefinitionItemBubble{Text: "订单取消数量"}},
					{Key: enum.IndicatorCancelSalePrice, Label: "取消支出金额", Bubble: &common.DefinitionItemBubble{Text: "取消支出"}},
					{Key: enum.IndicatorCancelCostPrice, Label: "取消收入金额", Bubble: &common.DefinitionItemBubble{Text: "取消收入"}},
					{Key: enum.IndicatorRevokeCount, Label: "撤销/撤改数", Bubble: &common.DefinitionItemBubble{Text: "订单撤销/撤改数量"}},
					{Key: enum.IndicatorRevokeSalePrice, Label: "撤销/撤改支出金额", Bubble: &common.DefinitionItemBubble{Text: "撤销支出"}},
					{Key: enum.IndicatorRevokeCostPrice, Label: "撤销/撤改收入金额", Bubble: &common.DefinitionItemBubble{Text: "撤销收入"}},
					{Key: enum.IndicatorActualSaleCount, Label: "净销售数量", Bubble: &common.DefinitionItemBubble{Text: "订单支付-取消-撤销-售后数量"}},
					{Key: enum.IndicatorActualSalePrice, Label: "净销售金额", Bubble: &common.DefinitionItemBubble{Text: "支付收入-取消支出-撤销支出-售后支出"}},
					{Key: enum.IndicatorActualCostPrice, Label: "净采购金额", Bubble: &common.DefinitionItemBubble{Text: "支付支出-取消收入-撤销收入-售后收入"}},
					{Key: enum.IndicatorActualProfit, Label: "利润", Bubble: &common.DefinitionItemBubble{Text: "净销售金额-净采购金额"}},
					{Key: enum.IndicatorRefundFeeProfit, Label: "退票手续费", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.IndicatorAfterSaleCount, Label: "售后数量", Bubble: &common.DefinitionItemBubble{Text: "订单售后数量"}},
					{Key: enum.IndicatorAfterSalePrice, Label: "售后支出金额", Bubble: &common.DefinitionItemBubble{Text: "售后支出"}},
					{Key: enum.IndicatorAfterCostPrice, Label: "售后收入金额", Bubble: &common.DefinitionItemBubble{Text: "售后收入"}},
				},
				Label: "统计维度",
			},
			{
				Key: "special_dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionSpu, Label: "产品"},
					{Key: enum.DimensionSku, Label: "票类"},
					{Key: enum.DimensionPayMode, Label: "支付方式"},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道"},
					{Key: enum.DimensionDistributor, Label: "分销商"},
					{Key: enum.DimensionSellOperator, Label: "售票员"},
					{Key: enum.DimensionSellSite, Label: "售票站点"},
					{Key: enum.DimensionGroupMember, Label: "集团成员"},
				},
				Label: "维度数据范围",
				Bubble: &common.DefinitionItemBubble{
					Text: "维度数据范围支持对应维度仅统计指定数据",
				},
			},
			SpecialProductRulesDefinition,
		},
	}
}
