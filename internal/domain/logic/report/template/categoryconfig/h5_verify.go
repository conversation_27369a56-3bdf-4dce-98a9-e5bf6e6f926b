package categoryconfig

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template/common"
)

// GetH5VerifyConfig 获取移动端验证报表配置 (category=5)
func GetH5VerifyConfig() *CategoryConfig {
	return &CategoryConfig{
		Fields: common.BaseConfigFields{
			Category:            "required",
			Dimension:           "required",
			Indicator:           "required",
			DimensionScheme:     "omitempty",
			SpecialDimension:    "omitempty",
			SpecialProductRules: "omitempty",
		},
		Permission: common.BaseConfigPermission{
			List:           true,
			Create:         true,
			Delete:         false,
			Detail:         true,
			Modify:         true,
			SingleTemplate: true,
		},
		Default: map[string]interface{}{
			"name": "验证报表模板",
			"payload": map[string]interface{}{
				"dimension":         []string{enum.DimensionSpu, enum.DimensionSku, enum.DimensionSaleUnitPrice, enum.DimensionSaleChannel},
				"indicator":         []string{enum.IndicatorActualVerifyCount, enum.IndicatorActualVerifySalePrice},
				"special_dimension": map[string]interface{}{},
				"can_query_dimension": map[string]interface{}{
					"enable":     0,
					"option":     "",
					"ext_option": []string{},
				},
				"dimension_tag_group": map[string]interface{}{
					enum.DimensionToTagCenterSceneMapKeyTicket:      "",
					enum.DimensionToTagCenterSceneMapKeyPayMode:     "",
					enum.DimensionToTagCenterSceneMapKeyProduct:     "",
					enum.DimensionToTagCenterSceneMapKeySaleChannel: "",
				},
				"special_product_rules": SpecialProductRulesDefault,
			},
		},
		Definition: []common.DefinitionItem{
			{
				Key: "dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionSku, Label: "票种"},
					{Key: enum.DimensionSpu, Label: "产品"},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道"},
					{Key: enum.DimensionSaleUnitPrice, Label: "销售单价"},
				},
				Label: "统计维度",
			},
			{
				Key: "indicator",
				Child: []common.DefinitionItemChild{
					{Key: enum.IndicatorActualVerifyCount, Label: "净验证数量"},
					{Key: enum.IndicatorActualVerifySalePrice, Label: "净验证金额"},
				},
				Label: "统计指标",
			},
			{
				Key: "dimension_tag_group",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionToTagCenterSceneMapKeyTicket, Label: "票种"},
					{Key: enum.DimensionToTagCenterSceneMapKeyProduct, Label: "产品"},
					{Key: enum.DimensionToTagCenterSceneMapKeySaleChannel, Label: "订单渠道"},
				},
				Label: "标签汇总",
				Bubble: &common.DefinitionItemBubble{
					Text: "标签汇总支持引用标签后，指定场景根据标签汇总统计",
				},
			},
			{
				Key: "special_dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionSku, Label: "票种"},
					{Key: enum.DimensionSpu, Label: "产品"},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道"},
				},
				Label: "维度数据范围",
				Bubble: &common.DefinitionItemBubble{
					Text: "维度数据范围支持对应维度仅统计指定数据",
				},
			},
			{
				Key: "can_query_dimension",
				Child: []common.DefinitionItemChild{
					{
						Key: "enable",
						Child: []common.DefinitionCommonItem{
							{Key: "1", Label: "开启"},
							{Key: "0", Label: "关闭"},
						},
						Label: "开关",
					},
					{
						Key: "option",
						Child: []common.DefinitionCommonItem{
							{Key: enum.DimensionToTagCenterSceneMapKeySaleChannel, Label: "订单渠道"},
							{Key: enum.DimensionToTagCenterSceneMapKeyTicket, Label: "票种"},
							{Key: enum.DimensionToTagCenterSceneMapKeyProduct, Label: "产品"},
						},
						Label: "选项",
					},
					{
						Key: "ext_option",
						Child: []common.DefinitionCommonItem{
							{Key: enum.TemplateExtOptionUseDimensionSchemeGroup, Label: "使用分组标签查询"},
						},
						Label: "额外选项",
					},
				},
				Label: "汇总可查维度",
			},
			SpecialProductRulesDefinition,
		},
	}
}
