package categoryconfig

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template/common"
)

// GetOperationDataConfig 获取营运数据报表配置 (category=1)
func GetOperationDataConfig() *CategoryConfig {
	return &CategoryConfig{
		Fields: common.BaseConfigFields{
			Title:               "required",
			Category:            "required",
			Dimension:           "required",
			Indicator:           "required",
			DimensionScheme:     "omitempty",
			SpecialDimension:    "omitempty",
			SpecialProductRules: "omitempty",
		},
		Permission: common.BaseConfigPermission{
			List:             true,
			Create:           true,
			Delete:           true,
			Detail:           true,
			Modify:           true,
			AccountIsolation: true,
		},
		Default: map[string]interface{}{},
		Definition: []common.DefinitionItem{
			{
				Key:   "title",
				Child: []common.DefinitionItemChild{},
				Label: "模板名称",
			},
			{
				Key: "dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionDate, Label: "日期"},
					{Key: enum.DimensionDistributor, Label: "分销商"},
					{Key: enum.DimensionPayMode, Label: "支付方式"},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道"},
					{Key: enum.DimensionSaleUnitPrice, Label: "销售单价"},
					{Key: enum.DimensionSku, Label: "票种"},
					{Key: enum.DimensionSpu, Label: "产品"},
				},
				Label: "统计维度",
			},
			{
				Key: "indicator",
				Child: []common.DefinitionItemChild{
					{Key: enum.IndicatorActualSaleCount, Label: "实售数量"},
					{Key: enum.IndicatorActualSalePrice, Label: "实售金额"},
					{Key: enum.IndicatorAfterSaleCount, Label: "售后数量"},
					{Key: enum.IndicatorAfterSalePrice, Label: "售后金额"},
					{Key: enum.IndicatorCancelCount, Label: "取消数量"},
					{Key: enum.IndicatorCancelSalePrice, Label: "取消金额"},
					{Key: enum.IndicatorPayCount, Label: "预订数量"},
					{Key: enum.IndicatorPaySalePrice, Label: "销售金额"},
					{Key: enum.IndicatorRevokeCount, Label: "撤销撤改数量"},
					{Key: enum.IndicatorRevokeSalePrice, Label: "撤销撤改金额"},
					{Key: enum.IndicatorVerifyCount, Label: "核销数量"},
					{Key: enum.IndicatorVerifySalePrice, Label: "核销金额"},
				},
				Label: "统计指标",
			},
			{
				Key: "dimension_tag_group",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionToTagCenterSceneMapKeyTicket, Label: "票种"},
					{Key: enum.DimensionToTagCenterSceneMapKeyProduct, Label: "产品"},
					{Key: enum.DimensionToTagCenterSceneMapKeyPayMode, Label: "支付方式"},
					{Key: enum.DimensionToTagCenterSceneMapKeySaleChannel, Label: "订单渠道"},
				},
				Label: "标签汇总",
				Bubble: &common.DefinitionItemBubble{
					Text: "标签汇总支持引用标签后，指定场景根据标签汇总统计",
				},
			},
			{
				Key: "special_dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionDistributor, Label: "分销商"},
					{Key: enum.DimensionPayMode, Label: "支付方式"},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道"},
					{Key: enum.DimensionSku, Label: "票种"},
					{Key: enum.DimensionSpu, Label: "产品"},
				},
				Label: "维度数据范围",
				Bubble: &common.DefinitionItemBubble{
					Text: "维度数据范围支持对应维度仅统计指定数据",
				},
			},
			SpecialProductRulesDefinition,
		},
	}
}
