package categoryconfig

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template/common"
)

// GetGroupVerifyConfig 获取集团报表-验证配置 (category=8)
func GetGroupVerifyConfig() *CategoryConfig {
	return &CategoryConfig{
		Fields: common.BaseConfigFields{
			Title:               "required",
			Category:            "required",
			Dimension:           "required",
			Indicator:           "required",
			DimensionScheme:     "omitempty",
			SpecialDimension:    "omitempty",
			SpecialProductRules: "omitempty",
		},
		Permission: common.BaseConfigPermission{
			List:             true,
			Create:           true,
			Delete:           true,
			Detail:           true,
			Modify:           true,
			AccountIsolation: true,
		},
		Default: map[string]interface{}{},
		Definition: []common.DefinitionItem{
			{
				Key:   "title",
				Child: []common.DefinitionItemChild{},
				Label: "模板名称",
			},
			{
				Key: "dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionDate, Label: "日期", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSpu, Label: "产品", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSku, Label: "票类", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionPayMode, Label: "支付方式", Bubble: &common.DefinitionItemBubble{Text: "卖出支付方式"}},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道", Bubble: &common.DefinitionItemBubble{Text: "订单销售渠道"}},
					{Key: enum.DimensionDistributor, Label: "分销商", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSellOperator, Label: "售票员", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSellSite, Label: "售票站点", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionOperator, Label: "操作员", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionSaleUnitPrice, Label: "销售单价", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionCostUnitPrice, Label: "采购单价", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionTargetAudience, Label: "适用人群", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.DimensionGroupMember, Label: "集团成员", Bubble: &common.DefinitionItemBubble{}},
				},
				Label: "统计维度",
			},
			{
				Key: "indicator",
				Child: []common.DefinitionItemChild{
					{Key: enum.IndicatorVerifyCount, Label: "验证数量", Bubble: &common.DefinitionItemBubble{Text: "订单验证数量"}},
					{Key: enum.IndicatorVerifySalePrice, Label: "验证金额", Bubble: &common.DefinitionItemBubble{Text: "验证收入"}},
					{Key: enum.IndicatorVerifyCostPrice, Label: "采购金额", Bubble: &common.DefinitionItemBubble{Text: "验证支出"}},
					{Key: enum.IndicatorRevokeCount, Label: "撤销/撤改数", Bubble: &common.DefinitionItemBubble{Text: "订单撤销/撤改数量"}},
					{Key: enum.IndicatorRevokeSalePrice, Label: "撤销/撤改支出金额", Bubble: &common.DefinitionItemBubble{Text: "撤销支出"}},
					{Key: enum.IndicatorRevokeCostPrice, Label: "撤销/撤改收入金额", Bubble: &common.DefinitionItemBubble{Text: "撤销收入"}},
					{Key: enum.IndicatorActualVerifyCount, Label: "净验证数量", Bubble: &common.DefinitionItemBubble{Text: "订单验证-撤销-售后数量"}},
					{Key: enum.IndicatorActualVerifySalePrice, Label: "净验证金额", Bubble: &common.DefinitionItemBubble{Text: "验证收入-撤销支出-售后支出"}},
					{Key: enum.IndicatorActualVerifyCostPrice, Label: "净采购金额", Bubble: &common.DefinitionItemBubble{Text: "验证支出-撤销收入-售后收入"}},
					{Key: enum.IndicatorActualVerifyProfit, Label: "利润", Bubble: &common.DefinitionItemBubble{Text: "净验证金额-净采购金额"}},
					{Key: enum.IndicatorRefundFeeProfit, Label: "退票手续费", Bubble: &common.DefinitionItemBubble{}},
					{Key: enum.IndicatorAfterSaleCount, Label: "售后数量", Bubble: &common.DefinitionItemBubble{Text: "订单售后数量"}},
					{Key: enum.IndicatorAfterSalePrice, Label: "售后支出金额", Bubble: &common.DefinitionItemBubble{Text: "售后支出"}},
					{Key: enum.IndicatorAfterCostPrice, Label: "售后收入金额", Bubble: &common.DefinitionItemBubble{Text: "售后收入"}},
				},
				Label: "统计指标",
			},
			{
				Key: "special_dimension",
				Child: []common.DefinitionItemChild{
					{Key: enum.DimensionSpu, Label: "产品"},
					{Key: enum.DimensionSku, Label: "票类"},
					{Key: enum.DimensionPayMode, Label: "支付方式"},
					{Key: enum.DimensionSaleChannel, Label: "订单渠道"},
					{Key: enum.DimensionDistributor, Label: "分销商"},
					{Key: enum.DimensionSellOperator, Label: "售票员"},
					{Key: enum.DimensionSellSite, Label: "售票站点"},
					{Key: enum.DimensionOperator, Label: "操作员"},
					{Key: enum.DimensionGroupMember, Label: "集团成员"},
				},
				Label: "维度数据范围",
				Bubble: &common.DefinitionItemBubble{
					Text: "维度数据范围支持对应维度仅统计指定数据",
				},
			},
			SpecialProductRulesDefinition,
		},
	}
}
