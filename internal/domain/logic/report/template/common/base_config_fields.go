package common

type BaseConfigFields struct {
	Title               string `json:"title,omitempty"`
	Dimension           string `json:"dimension,omitempty"`
	Indicator           string `json:"indicator,omitempty"`
	Category            string `json:"category,omitempty"`
	SpecialDimension    string `json:"special_dimension,omitempty"`
	DimensionScheme     string `json:"dimension_scheme,omitempty"`
	SpecialProductRules string `json:"special_product_rules,omitempty"`
	MultipleMode        string `json:"multiple_mode,omitempty"`
}
