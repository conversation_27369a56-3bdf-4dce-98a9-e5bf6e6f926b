package common

import (
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/pkg/utils"
)

type TemplatePayload struct {
	Dimension           []string            `json:"dimension"`
	Indicator           []string            `json:"indicator"`
	SpecialDimension    DoSpecialDimension  `json:"special_dimension"`
	DimensionScheme     DoDimensionScheme   `json:"dimension_scheme"`
	DimensionTagGroup   map[string]string   `json:"dimension_tag_group"`
	SpecialProductRules SpecialProductRules `json:"special_product_rules"`
	CanQueryDimension   DoCanQueryDimension `json:"can_query_dimension"`
	MultipleMode        []string            `json:"multiple_mode"`            //多模式
	OperateType         []int               `json:"operate_type"`             //操作类型
	ChartSettings       *DoChartSettings    `json:"chart_settings,omitempty"` //图表设置
}

// 解析使用
type DoSpecialDimension struct {
	Spu          []int `json:"spu"`
	Sku          []int `json:"sku"`
	PayMode      []int `json:"pay_mode"`
	SaleChannel  []int `json:"sale_channel"`
	Distributor  []int `json:"distributor"`
	Operator     []int `json:"operator"`
	SellOperator []int `json:"sell_operator"`
	SellSite     []int `json:"sell_site"`
	GroupMember  []int `json:"group_member"`
}

type DoDimensionScheme struct {
	Spu         int `json:"spu"`
	Sku         int `json:"sku"`
	PayMode     int `json:"pay_mode"`
	SaleChannel int `json:"sale_channel"`
	AgeGroup    int `json:"age_group"` //年龄段
}

// DoCanQueryDimension 汇总可查维度（移动端报表特有配置）
type DoCanQueryDimension struct {
	Enable    int      `json:"enable"`
	Option    string   `json:"option"`
	ExtOption []string `json:"ext_option"`
}

type DoChartSettings struct {
	Enable int `json:"enable"`
	Value  int `json:"value"`
}

func (t *TemplatePayload) ClearChartSettings() {
	if t.ChartSettings == nil {
		return
	}
	if t.ChartSettings.Enable == 0 {
		t.ChartSettings.Value = 0
	}
	return
}

func (t *TemplatePayload) ClearCanQueryDimension() {
	if t.CanQueryDimension.Enable == 0 {
		t.CanQueryDimension.Option = ""
		t.CanQueryDimension.ExtOption = []string{}
	}
	return
}

func (t *TemplatePayload) HandleSpecialProductRules() {
	if !utils.Container(t.SpecialProductRules.PackType, 0) {
		t.SpecialProductRules.PackType = append(t.SpecialProductRules.PackType, 0)
	}
	if !utils.Container(t.SpecialProductRules.ShowBindType, 0) {
		t.SpecialProductRules.ShowBindType = append(t.SpecialProductRules.ShowBindType, 0)
	}
	if !utils.Container(t.SpecialProductRules.AnnualCardType, 0) {
		t.SpecialProductRules.AnnualCardType = append(t.SpecialProductRules.AnnualCardType, 0)
	}
	if !utils.Container(t.SpecialProductRules.ExchangeCouponType, 0) {
		t.SpecialProductRules.ExchangeCouponType = append(t.SpecialProductRules.ExchangeCouponType, 0)
	}

	return
}

func (t *TemplatePayload) HandleDimension(sdType int, category int) {
	isGroupMerchant := sdType == enum.MerchantTypeGroup
	isGroupAccountInDimension := utils.Container(t.Dimension, enum.DimensionGroupAccount)
	isGroupReportCategory := utils.Container(enum.TemplateCategoryIsGroupReport, category)
	if isGroupMerchant && !isGroupAccountInDimension && isGroupReportCategory {
		//集团账号加入默认维度 group_account
		t.Dimension = append(t.Dimension, enum.DimensionGroupAccount)
	}
	return
}

func (t *TemplatePayload) HandleDimensionTagGroup(sdType int, merchantId int, category int) error {
	//集团账号不存在集团默认标签，则新增默认标签
	isGroupMerchant := sdType == enum.MerchantTypeGroup
	_, isDimensionTagGroup := t.DimensionTagGroup[enum.DimensionGroupAccount]
	isGroupReportCategory := utils.Container(enum.TemplateCategoryIsGroupReport, category)
	if !isDimensionTagGroup && isGroupMerchant && isGroupReportCategory {
		if t.DimensionTagGroup == nil {
			t.DimensionTagGroup = make(map[string]string)
		}
		//集团账号加入默认标签 {"group_account":集团账号id}
		t.DimensionTagGroup[enum.DimensionGroupAccount] = cast.ToString(merchantId)
	}

	// 如果使用维度标签组，需要去标签中心刷新关联关系
	for scene, tagGroupCode := range t.DimensionTagGroup {
		if _, ok := enum.DimensionToTagCenterSceneMapReverse[scene]; ok {
			err := dimensionscheme.RefreshTagRelationByTagGroupCode(scene, tagGroupCode, merchantId)
			if err != nil {
				return err
			}
		}
	}

	return nil
}
