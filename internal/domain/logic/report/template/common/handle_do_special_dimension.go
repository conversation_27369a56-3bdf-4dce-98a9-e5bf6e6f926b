package common

type SpecialDimension struct {
	Spu          []ChildItem `json:"spu,omitempty"`
	Sku          []ChildItem `json:"sku,omitempty"`
	PayMode      []ChildItem `json:"pay_mode,omitempty"`
	SaleChannel  []ChildItem `json:"sale_channel,omitempty"`
	Distributor  []ChildItem `json:"distributor,omitempty"`
	Operator     []ChildItem `json:"operator,omitempty"`
	SellOperator []ChildItem `json:"sell_operator,omitempty"`
	SellSite     []ChildItem `json:"sell_site,omitempty"`
	GroupMember  []ChildItem `json:"group_member,omitempty"`
}
