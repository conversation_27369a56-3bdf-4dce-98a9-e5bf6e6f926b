package common

// 基础配置定义输出格式
type DefinitionItem struct {
	Key    string                `json:"key"`
	Child  []DefinitionItemChild `json:"child,omitempty"`
	Label  string                `json:"label"`
	SubKey string                `json:"sub_key,omitempty"`
	Bubble *DefinitionItemBubble `json:"bubble,omitempty"`
}

type DefinitionItemChild struct {
	Key    string                 `json:"key"`
	Child  []DefinitionCommonItem `json:"child,omitempty"`
	Label  string                 `json:"label"`
	SubKey string                 `json:"sub_key,omitempty"`
	Bubble *DefinitionItemBubble  `json:"bubble,omitempty"`
}

type DefinitionCommonItem struct {
	Key    string                `json:"key"`
	Label  string                `json:"label"`
	SubKey string                `json:"sub_key,omitempty"`
	Bubble *DefinitionItemBubble `json:"bubble,omitempty"`
	Child  []struct {
		Key    string                `json:"key,omitempty"`
		Label  string                `json:"label,omitempty"`
		Bubble *DefinitionItemBubble `json:"bubble,omitempty"`
	} `json:"child,omitempty"`
}

// 气泡配置
type DefinitionItemBubble struct {
	Text string `json:"text"` //气泡提示文本
}
