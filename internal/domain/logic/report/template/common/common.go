package common

import (
	"fmt"
)

type ChildItem struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type SpecialProductRules struct {
	PackType           []int `json:"pack_type"`
	ShowBindType       []int `json:"show_bind_type"`
	AnnualCardType     []int `json:"annual_card_type"`
	ExchangeCouponType []int `json:"exchange_coupon_type"`
}

func GetTemplateGlobalConfigKey(category int) string {
	return fmt.Sprintf("template_v6_category_%d", category)
}
