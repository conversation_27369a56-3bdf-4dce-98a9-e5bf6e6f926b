package searchhandler

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	reportCommon "report-service/internal/domain/logic/report/common"
	searchCommon "report-service/internal/domain/logic/report/search/common"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/szerrors"
	"strconv"
)

func PayMode(params searchCommon.DoQueryParams) (interface{}, error) {
	payModeMap := make(map[int]string)
	//集团账号不需要处理自定义收款方式
	if params.IsGroupAccount() {
		for i, s := range enum.PaymodeMap {
			payModeMap[i] = s
		}
	} else {
		payModeMap = reportCommon.PayWayListConf(params.MerchantId, true)
	}
	return searchCommon.GetFilteredMapIntItems(
		payModeMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}

func SaleChannel(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.GetFilteredMapIntItems(
		enum.OrderModeMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}

func IdType(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.GetFilteredMapIntItems(
		enum.DWMTouristVoucherTypeMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}

func SellSite(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseDataByIdString
	memberIds := []int{}
	var err error
	if params.IsGroupAccount() {
		memberIds, err = dimensionscheme.GetSubjectIdListByMerchantId(params.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(params.MerchantId), strconv.Itoa(params.MerchantId))
		if err != nil {
			return nil, szerrors.NewLogicErrorWithText("查询集团成员信息失败")
		}
	} else {
		memberIds = []int{params.MerchantId}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	queryParams := platformrpcapi.DoQuerySiteListParams{
		Sids:     memberIds,
		PageNum:  params.PageNum,
		PageSize: params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.KeyWords = params.KeyWord
	}
	var list []platformrpcapi.SiteInfoData
	list, err = platformrpcapi.QuerySiteListBySids(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItemIdString{Id: item.SiteId, Name: item.SName, FullName: ""})
	}
	return result, nil
}
