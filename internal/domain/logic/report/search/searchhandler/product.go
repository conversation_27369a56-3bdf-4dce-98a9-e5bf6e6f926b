package searchhandler

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/domain/logic/permission/datajobslimit"
	searchCommon "report-service/internal/domain/logic/report/search/common"
	"report-service/pkg/sdk/api/distributioncenter/admin"
	"report-service/pkg/szerrors"
	"strconv"
)

func Spu(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseData
	var memberIds []int
	var err error
	if params.IsGroupAccount() {
		memberIds, err = dimensionscheme.GetSubjectIdListByMerchantId(params.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(params.MerchantId), strconv.Itoa(params.MerchantId))
		if err != nil {
			return nil, szerrors.NewLogicErrorWithText("查询集团成员信息失败")
		}
	} else {
		memberIds = []int{params.MerchantId}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	//数据岗位权限
	var dataLimit datajobslimit.LimitSpuConfig
	if !params.IsGroupAccount() {
		dataLimit = datajobslimit.QueryBusinessData(params.MerchantId, params.MemberId)
		if check, checkErr := dataLimit.AllLimit(); check {
			return nil, szerrors.NewLogicError(checkErr)
		}
	}
	queryParams := admin.DoQuerySaleSpuParams{
		FidList:         memberIds,
		QueryTotal:      0,
		NotType:         "Q",
		QueryExpiration: true,
		PageNum:         params.PageNum,
		PageSize:        params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.LandTitleLike = params.KeyWord
	}
	if dataLimit.IdList != nil && len(dataLimit.IdList) > 0 {
		queryParams.LidList = dataLimit.IdList
	}
	if dataLimit.NotIdList != nil && len(dataLimit.NotIdList) > 0 {
		queryParams.NotLidList = dataLimit.NotIdList
	}
	var list []admin.SaleSpuListResponseItem
	list, _, err = admin.QuerySaleSpuListByMoreParamsPaging(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItem{Id: item.Lid, Name: item.LandTitle, FullName: ""})
	}
	return result, nil
}

func SelfSupplySpu(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseData
	memberIds := []int{params.MerchantId}
	dataLimit := datajobslimit.QueryBusinessData(params.MerchantId, params.MemberId)
	if check, checkErr := dataLimit.AllLimit(); check {
		return nil, szerrors.NewLogicError(checkErr)
	}
	queryParams := admin.DoQuerySaleSpuParams{
		FidList:         memberIds,
		Sid:             params.MerchantId,
		QueryTotal:      0,
		NotType:         "Q",
		QueryExpiration: true,
		PageNum:         params.PageNum,
		PageSize:        params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.LandTitleLike = params.KeyWord
	}
	if dataLimit.IdList != nil && len(dataLimit.IdList) > 0 {
		queryParams.LidList = dataLimit.IdList
	}
	if dataLimit.NotIdList != nil && len(dataLimit.NotIdList) > 0 {
		queryParams.NotLidList = dataLimit.NotIdList
	}
	list, _, err := admin.QuerySaleSpuListByMoreParamsPaging(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItem{Id: item.Lid, Name: item.LandTitle, FullName: ""})
	}
	return result, nil
}

func Sku(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseData
	var memberIds []int
	var err error
	if params.IsGroupAccount() {
		memberIds, err = dimensionscheme.GetSubjectIdListByMerchantId(params.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(params.MerchantId), strconv.Itoa(params.MerchantId))
		if err != nil {
			return nil, szerrors.NewLogicErrorWithText("查询集团成员信息失败")
		}
	} else {
		memberIds = []int{params.MerchantId}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	//数据岗位权限
	var dataLimit datajobslimit.LimitSpuConfig
	if !params.IsGroupAccount() {
		dataLimit = datajobslimit.QueryBusinessData(params.MerchantId, params.MemberId)
		if check, checkErr := dataLimit.AllLimit(); check {
			return nil, szerrors.NewLogicError(checkErr)
		}
	}
	queryParams := admin.DoQuerySaleSkuParams{
		FidList:         memberIds,
		QueryTotal:      0,
		NotTypeList:     []string{"Q"},
		QueryExpiration: true,
		PageNum:         params.PageNum,
		PageSize:        params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.TicketTitleLike = params.KeyWord
	}
	if dataLimit.IdList != nil && len(dataLimit.IdList) > 0 {
		queryParams.LidList = dataLimit.IdList
	}
	if dataLimit.NotIdList != nil && len(dataLimit.NotIdList) > 0 {
		queryParams.NotLidList = dataLimit.NotIdList
	}
	var list []admin.SaleSkuListResponseItem
	list, _, err = admin.QuerySaleSkuListByMoreParamsPaging(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItem{Id: item.Tid, Name: item.TicketTitle, FullName: ""})
	}
	return result, nil
}

func SelfSupplySku(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseData
	memberIds := []int{params.MerchantId}
	dataLimit := datajobslimit.QueryBusinessData(params.MerchantId, params.MemberId)
	if check, checkErr := dataLimit.AllLimit(); check {
		return nil, szerrors.NewLogicError(checkErr)
	}
	queryParams := admin.DoQuerySaleSkuParams{
		FidList:         memberIds,
		Sid:             params.MerchantId,
		QueryTotal:      0,
		NotTypeList:     []string{"Q"},
		QueryExpiration: true,
		PageNum:         params.PageNum,
		PageSize:        params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.TicketTitleLike = params.KeyWord
	}
	if dataLimit.IdList != nil && len(dataLimit.IdList) > 0 {
		queryParams.LidList = dataLimit.IdList
	}
	if dataLimit.NotIdList != nil && len(dataLimit.NotIdList) > 0 {
		queryParams.NotLidList = dataLimit.NotIdList
	}
	list, _, err := admin.QuerySaleSkuListByMoreParamsPaging(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItem{Id: item.Tid, Name: item.TicketTitle, FullName: ""})
	}
	return result, nil
}
