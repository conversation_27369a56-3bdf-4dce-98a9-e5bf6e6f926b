package searchhandler

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	searchCommon "report-service/internal/domain/logic/report/search/common"
	"report-service/pkg/sdk/api/pftmember"
	usercenterAdmin "report-service/pkg/sdk/api/usercenter/admin"
	"report-service/pkg/szerrors"
	"strconv"
	"strings"
)

func GroupMembers(params searchCommon.DoQueryParams) (interface{}, error) {
	if !params.IsGroupAccount() {
		return nil, nil
	}
	var result searchCommon.ResponseData
	memberIds, err := dimensionscheme.GetSubjectIdListByMerchantId(params.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(params.MerchantId), strconv.Itoa(params.MerchantId))
	if err != nil {
		return nil, szerrors.NewLogicErrorWithText("查询集团成员信息失败")
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	memberIdKeyNameMap := make(map[int]string)
	memberIdKeyNameMap, err = pftmember.GetMemberIdKeyDNameMap(memberIds)
	if err != nil {
		return nil, szerrors.NewLogicErrorWithText("查询集团成员名称信息失败")
	}
	for _, id := range memberIds {
		if dName, ok := memberIdKeyNameMap[id]; ok {
			// 模糊匹配
			if params.KeyWord != "" && !strings.Contains(dName, params.KeyWord) {
				continue
			}
			result = append(result, searchCommon.SearchListItem{Id: id, Name: dName, FullName: ""})
		}
	}
	if len(result) == 0 {
		return nil, nil
	}
	if result != nil {
		// 调用通用分页方法
		if pagedResult := result.PaginateResult(params.PageNum, params.PageSize); len(pagedResult) != 0 {
			return pagedResult, nil
		}
	}
	return nil, nil
}

func SellOperator(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseData
	memberIds := []int{}
	var err error
	if params.IsGroupAccount() {
		memberIds, err = dimensionscheme.GetSubjectIdListByMerchantId(params.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(params.MerchantId), strconv.Itoa(params.MerchantId))
		if err != nil {
			return nil, szerrors.NewLogicErrorWithText("查询集团成员信息失败")
		}
	} else {
		memberIds = []int{params.MerchantId}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	queryParams := usercenterAdmin.DoQuerySubRelationParams{
		ParentIdList:     memberIds,
		ShipTypeList:     []int{1},    //关系类型:0:供销关系 1:从属关系 2:平级关系 3:推荐关系 4:资源中心关系
		SonIdTypeList:    []int{2},    //子账号类型 0分销商 1:资源方 2:员工 3:供应方（父ID为集团帐号情况下）
		MemberStatusList: []int{0, 1}, // 用户状态列表：0=正常 1=禁用 2=删除 3=未审核
		Self:             1,
		PageNum:          params.PageNum,
		PageSize:         params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.MemberNameLike = params.KeyWord
	}
	var list []usercenterAdmin.SubRelationListResponseItem
	list, _, err = usercenterAdmin.QuerySubRelationListByMoreParamsPaging(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItem{Id: item.MemberId, Name: item.MemberName, FullName: ""})
	}
	return result, nil
}

func Distributor(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseData
	memberIds := []int{}
	var err error
	if params.IsGroupAccount() {
		memberIds, err = dimensionscheme.GetSubjectIdListByMerchantId(params.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(params.MerchantId), strconv.Itoa(params.MerchantId))
		if err != nil {
			return nil, szerrors.NewLogicErrorWithText("查询集团成员信息失败")
		}
	} else {
		memberIds = []int{params.MerchantId}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	queryParams := usercenterAdmin.DoQuerySubRelationParams{
		ParentIdList:  memberIds,
		ShipTypeList:  []int{0}, //关系类型:0:供销关系 1:从属关系 2:平级关系 3:推荐关系 4:资源中心关系
		SonIdTypeList: []int{0}, //子账号类型列表：0=分销商 1=资源方 2=员工 3=供应方（父ID为集团帐号情况下）
		Self:          1,
		PageNum:       params.PageNum,
		PageSize:      params.PageSize,
	}
	if params.KeyWord != "" {
		queryParams.MemberNameLike = params.KeyWord
	}
	var list []usercenterAdmin.SubRelationListResponseItem
	list, _, err = usercenterAdmin.QuerySubRelationListByMoreParamsPaging(queryParams)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, item := range list {
		result = append(result, searchCommon.SearchListItem{Id: item.MemberId, Name: item.MemberName, FullName: ""})
	}
	return result, nil
}
