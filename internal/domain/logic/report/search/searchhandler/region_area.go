package searchhandler

import (
	"report-service/internal/domain/enum"
	searchCommon "report-service/internal/domain/logic/report/search/common"
	"report-service/pkg/sdk/api/baseservice/bizaddress"
)

func Province(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.QueryRegionAreaCommon(
		params,
		enum.RegionAreaLevelProvinceLevel,
		200, // 默认分页大小
	)
}

func City(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.QueryRegionAreaCommon(
		params,
		enum.RegionAreaLevelCityLevel,
		1000,
	)
}

func District(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.QueryRegionAreaCommon(
		params,
		enum.RegionAreaLevelDistrictLevel,
		1000,
	)
}

func Region(params searchCommon.DoQueryParams) (interface{}, error) {
	areaMap := make(map[int]string)
	// 处理"全部"和"未知"选项
	if (params.KeyWord == "" && params.PageNum == 1) || params.KeyWord == "全部" {
		areaMap[-1] = "全部"
	}
	if (params.KeyWord == "" && params.PageNum == 1) || params.KeyWord == "未知" {
		areaMap[0] = "未知"
	}
	for k, v := range enum.RegionKeyNameMap {
		if v == "" || k == enum.CommonUnknownKey {
			continue
		}
		areaMap[k] = v
	}
	return searchCommon.GetFilteredMapIntItems(
		areaMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}

func Country(params searchCommon.DoQueryParams) (interface{}, error) {
	areaMap := make(map[int]string)
	// 处理"全部"和"未知"选项
	if (params.KeyWord == "" && params.PageNum == 1) || params.KeyWord == "全部" {
		areaMap[-1] = "全部"
	}
	if (params.KeyWord == "" && params.PageNum == 1) || params.KeyWord == "未知" {
		areaMap[0] = "未知"
	}
	list, err := bizaddress.QueryAddressGlobalCountryList()
	if err != nil {
		return nil, err
	}
	for _, item := range list {
		areaMap[item.Id] = item.NameZh
	}
	return searchCommon.GetFilteredMapIntItems(
		areaMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}
