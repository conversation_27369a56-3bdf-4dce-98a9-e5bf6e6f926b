package searchhandler

import (
	"report-service/internal/domain/enum"
	searchCommon "report-service/internal/domain/logic/report/search/common"
	"report-service/pkg/sdk/api/tagcenter/tag"
)

func TargetAudience(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseDataByIdString
	pageNum := params.PageNum
	pageSize := params.PageSize
	if pageNum <= 0 || pageSize <= 0 {
		pageNum = 1
		pageSize = 200
	}
	var apiParams tag.QueryTagPaginateParams
	if params.KeyWord != "" {
		apiParams.Name = params.KeyWord
	}
	apiParams.GroupCode = enum.GroupApplicableCrowdManage
	apiParams.Type = 1 //系统预设
	apiParams.PageNum = pageNum
	apiParams.PageSize = pageSize
	responseData, err := tag.TagPaginate(apiParams)
	if err != nil || responseData == nil || len(responseData) == 0 {
		return nil, nil
	}
	for _, val := range responseData {
		result = append(result, searchCommon.SearchListItemIdString{Id: val.Code, Name: val.Name, FullName: ""})
	}
	if len(result) > 0 {
		return result, nil
	}

	return nil, nil
}

func MerchantTagQuery(params searchCommon.DoQueryParams) (interface{}, error) {
	var result searchCommon.ResponseDataByIdString
	pageNum := params.PageNum
	pageSize := params.PageSize
	if pageNum <= 0 || pageSize <= 0 {
		pageNum = 1
		pageSize = 200
	}
	var apiParams tag.QueryTagPaginateParams
	if params.KeyWord != "" {
		apiParams.Name = params.KeyWord
	}
	apiParams.GroupCode = enum.GroupApplicableCrowdManage
	apiParams.Type = 1 //系统预设
	apiParams.PageNum = pageNum
	apiParams.PageSize = pageSize
	responseData, err := tag.TagPaginate(apiParams)
	if err != nil || responseData == nil || len(responseData) == 0 {
		return nil, nil
	}
	for _, val := range responseData {
		result = append(result, searchCommon.SearchListItemIdString{Id: val.Code, Name: val.Name, FullName: ""})
	}
	if len(result) > 0 {
		return result, nil
	}

	return nil, nil
}
