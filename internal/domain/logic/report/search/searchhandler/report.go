package searchhandler

import (
	"report-service/internal/domain/enum"
	searchCommon "report-service/internal/domain/logic/report/search/common"
)

func OperateType(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.GetFilteredMapIntItems(
		enum.DWMOperateTypeMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}

func Gender(params searchCommon.DoQueryParams) (interface{}, error) {
	return searchCommon.GetFilteredMapIntItems(
		enum.GenderKeyNameMap,
		params.KeyWord,
		params.PageNum,
		params.PageSize,
	)
}
