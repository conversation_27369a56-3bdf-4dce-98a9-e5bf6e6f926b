package search

import (
	"fmt"
	"report-service/internal/domain/enum"
	searchCommon "report-service/internal/domain/logic/report/search/common"
	"report-service/internal/domain/logic/report/search/searchhandler"
	"report-service/pkg/szerrors"
)

type SearchHandler func(params searchCommon.DoQueryParams) (interface{}, error)

// 搜索维度处理映射
var searchHandlers = map[string]SearchHandler{
	enum.DimensionPayMode:         searchhandler.PayMode,        //支付方式维度查询
	enum.DimensionProvince:        searchhandler.Province,       //省份维度查询
	enum.DimensionCity:            searchhandler.City,           //城市维度查询
	enum.DimensionTargetAudience:  searchhandler.TargetAudience, //适用人群维度查询
	enum.DimensionGroupMember:     searchhandler.GroupMembers,   //集团成员维度查询
	enum.DimensionSpu:             searchhandler.Spu,            //spu维度查询
	enum.DimensionSku:             searchhandler.Sku,            //sku维度查询
	enum.DimensionSellOperator:    searchhandler.SellOperator,   //销售员维度查询
	enum.DimensionOperator:        searchhandler.SellOperator,   //操作员维度查询
	enum.DimensionDistributor:     searchhandler.Distributor,    //分销商维度查询
	enum.DimensionSellSite:        searchhandler.SellSite,       //销售站点维度查询
	enum.DimensionSaleChannel:     searchhandler.SaleChannel,    //销售渠道维度查询
	enum.DetailFieldOperateType:   searchhandler.OperateType,    //操作类型查询
	enum.DetailFieldIdType:        searchhandler.IdType,         //证件类型查询
	enum.DimensionRegion:          searchhandler.Region,         //地域维度查询
	enum.DimensionCountry:         searchhandler.Country,        //国家维度查询
	enum.DimensionDistrict:        searchhandler.District,       //区县维度查询
	enum.DimensionGender:          searchhandler.Gender,         //性别维度查询
	enum.SearchFieldSelfSupplySpu: searchhandler.SelfSupplySpu,  //自供应spu维度查询
	enum.SearchFieldSelfSupplySku: searchhandler.SelfSupplySku,  //自供应sku维度查询
}

// SearchDimensionByNeed 查询维度入口
func SearchDimensionByNeed(fieldType string, params searchCommon.DoQueryParams) (interface{}, error) {
	handler, exists := searchHandlers[fieldType]
	if !exists {
		return nil, szerrors.NewLogicErrorWithText(fmt.Sprintf("查询类型错误，类型：%s", fieldType))
	}
	return handler(params)
}
