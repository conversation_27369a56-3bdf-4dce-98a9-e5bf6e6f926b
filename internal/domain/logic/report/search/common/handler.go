package common

import (
	"report-service/pkg/sdk/api/baseservice/bizregionarea"
	"sort"
	"strings"
)

// GetFilteredMapIntItems 通用map过滤和排序函数
func GetFilteredMapIntItems(
	mapData map[int]string,
	keyword string,
	pageNum, pageSize int,
) (interface{}, error) {
	var result ResponseData

	// 过滤匹配keyword的项
	for id, val := range mapData {
		if keyword != "" && !strings.Contains(val, keyword) {
			continue
		}
		result = append(result, SearchListItem{
			Id:       id,
			Name:     val,
			FullName: "",
		})
	}

	if len(result) == 0 {
		return nil, nil
	}

	// 按ID排序
	sort.Sort(SortById{ResponseData: result})

	// 分页处理
	if pagedResult := result.PaginateResult(pageNum, pageSize); len(pagedResult) != 0 {
		return pagedResult, nil
	}

	return nil, nil
}

// QueryRegionAreaCommon 地区查询通用函数
func QueryRegionAreaCommon(
	params DoQueryParams,
	level int,
	defaultPageSize int,
) (interface{}, error) {
	var result ResponseDataByIdString

	// 设置分页参数
	pageNum := params.PageNum
	pageSize := params.PageSize
	if pageNum <= 0 || pageSize <= 0 {
		pageNum = 1
		pageSize = defaultPageSize
	}

	// 处理"全部"和"未知"选项
	if (params.KeyWord == "" && pageNum == 1) || params.KeyWord == "全部" {
		result = append(result, SearchListItemIdString{Id: "-1", Name: "全部", FullName: "全部"})
	}
	if (params.KeyWord == "" && pageNum == 1) || params.KeyWord == "未知" {
		result = append(result, SearchListItemIdString{Id: "0", Name: "未知", FullName: "未知"})
	}

	// 查询地区数据
	responseData, err := bizregionarea.QueryRegionAreaPaginate(
		params.KeyWord,
		0,
		level,
		pageNum,
		pageSize,
	)
	if err != nil || len(responseData.Rows) == 0 {
		if len(result) > 0 {
			return result, nil
		}
		return nil, nil
	}

	// 处理查询结果
	for _, val := range responseData.Rows {
		result = append(result, SearchListItemIdString{
			Id:       val.AreaCode,
			Name:     val.AreaName,
			FullName: val.AreaName,
		})
	}

	if len(result) > 0 {
		return result, nil
	}

	return nil, nil
}
