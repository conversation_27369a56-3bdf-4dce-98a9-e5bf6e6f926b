package common

type SearchListItem struct {
	Id       int    `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name,omitempty"`
}

type SearchListItemIdString struct {
	Id       string `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name,omitempty"`
}

type DoQueryParams struct {
	KeyWord      string `json:"key_word"`
	MerchantId   int    `json:"merchant_id"`
	MemberId     int    `json:"member_id"`
	MerchantType *int   `json:"merchant_type"`
	PageSize     int    `json:"page_size"`
	PageNum      int    `json:"page_num"`
}

type ResponseData []SearchListItem

type ResponseDataByIdString []SearchListItemIdString

type ResponseDataById struct{ ResponseData }

type SortById struct {
	ResponseData
}
