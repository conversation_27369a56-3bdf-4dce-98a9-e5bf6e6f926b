package datajobslimit

import (
	"errors"
	"report-service/internal/domain/enum"
	"report-service/pkg/sdk/api/merchantdatascopeservice"
	"report-service/pkg/utils"
)

// QueryBusinessData 查询业务数据并返回限制配置
// 该函数根据店铺ID(sid)和会员ID(memberId)查询业务数据，并设置限制配置
// 如果参数校验失败或查询数据出错，将返回默认配置
func QueryBusinessData(sid, memberId int) LimitSpuConfig {
	config := getDefault()
	if !checkParams(sid, memberId) {
		return config
	}
	//后续可以考虑放到登录信息里，这边不存在缓存和员工信息校验
	//获取配置
	data, err := merchantdatascopeservice.QueryBusinessData(memberId, enum.MerchantDataScopeSpuTag)
	if err != nil {
		return config
	}
	//无限制
	if len(data.IdList) == 0 && len(data.NotIdList) == 0 {
		return config
	}
	//全部限制
	if len(data.IdList) == 1 && utils.Container(data.IdList, enum.DataAuthLimitTypeAllMark) {
		config.LimitType = enum.DataAuthLimitTypeAll
		return config
	}
	//部分限制
	if len(data.IdList) > 0 || len(data.NotIdList) > 0 {
		config.LimitType = enum.DataAuthLimitTypePart
	}
	if len(data.IdList) > 0 {
		config.IdList = data.IdList
	}
	if len(data.NotIdList) > 0 {
		config.NotIdList = data.NotIdList
	}
	return config
}

// checkParams 检查参数有效性
// 该函数用于判断传入的学生ID(sid)和会员ID(memberId)是否符合特定条件
// 参数:
//
//	sid - 学生ID
//	memberId - 会员ID
//
// 返回值:
//
//	如果学生ID与会员ID相同或会员ID为0，则返回false，表示参数无效
//	否则返回true，表示参数有效
func checkParams(sid, memberId int) bool {
	//非员工或者不存在，直接返回
	if sid == memberId || memberId == 0 {
		return false
	}
	return true
}

// getDefault 返回一个默认配置的 LimitSpuConfig 实例。
// 该函数用于初始化 LimitSpuConfig 结构体，设置默认的限制类型和空的 ID 列表。
// 主要用途是在没有特定配置时，提供一个默认的配置实例，确保程序能够以一种预定义的、安全的方式运行。
//
// LimitType 设置为 enum.DataAuthLimitTypeNo，表示不限制数据访问。
// IdList 和 NotIdList 初始化为空的切片，表示没有特定的 ID 列表需要进行数据权限校验。
func getDefault() LimitSpuConfig {
	return LimitSpuConfig{
		LimitType: enum.DataAuthLimitTypeNo,
		IdList:    []int{},
		NotIdList: []int{},
	}
}

// NotLimit 判断当前配置是否不限制数据权限
// LimitSpuConfig 是一个配置结构体，包含限制类型等信息
// 返回值为布尔类型，表示是否不限制数据权限
func (d LimitSpuConfig) NotLimit() bool {
	if d.LimitType == enum.DataAuthLimitTypeNo || d.LimitType == 0 {
		return true
	}
	return false
}

// AllLimit 判断配置是否为全局限制
// 此函数用于检查当前配置的限制类型是否为“全部”，如果是，则返回true，并附带一个错误信息
// 表示没有数据查询权限。如果限制类型不是“全部”，则返回false和nil。
// 参数: 无
// 返回值:
//   - bool: 配置是否为全局限制
//   - error: 错误信息，如果没有数据查询权限则返回错误
func (d LimitSpuConfig) AllLimit() (bool, error) {
	if d.LimitType == enum.DataAuthLimitTypeAll {
		return true, errors.New("无数据查询权限")
	}
	return false, nil
}

// GetIdList 根据输入的ID列表和配置中的ID列表，返回它们的交集。
// 如果两个列表都为空，则返回配置中的ID列表。
// 如果输入的ID列表为空，但配置中的ID列表不为空，则返回配置中的ID列表。
// 如果输入的ID列表不为空，但配置中的ID列表为空，则返回输入的ID列表。
// 如果两个列表都没有交集，返回一个包含特殊值的列表，表示空结果。
func (d LimitSpuConfig) GetIdList(ids []int) []int {
	if len(ids) == 0 && len(d.IdList) == 0 {
		return d.IdList
	}
	if len(ids) == 0 && len(d.IdList) > 0 {
		return d.IdList
	}
	if len(ids) > 0 && len(d.IdList) == 0 {
		return ids
	}
	//取交集
	idList := make([]int, 0)
	for _, id := range ids {
		if utils.Container(d.IdList, id) {
			idList = append(idList, id)
		}
	}
	for _, id := range d.IdList {
		if utils.Container(ids, id) {
			idList = append(idList, id)
		}
	}
	idList = utils.RemoveDuplicate(idList)
	// 如果没有交集，返回一个不可能存在的枚举值，使得结果为空（代码调整较小）
	if len(idList) == 0 {
		return []int{-2 ^ 31}
	}
	return idList
}
