package module

import "gitee.com/golang-module/carbon/v2"

type OperateData struct {
	MerchantId int           `json:"merchant_id"`
	ModuleTag  string        `json:"module_tag"`
	ModuleId   int           `json:"module_id"`
	Action     int           `json:"action"`
	ChangeTime carbon.Carbon `json:"change_time"`
}

type ModuleProcessor interface {
	HandleOpen(o OperateData) error
	HandleClose(o OperateData) error
}
