package touristsourceareamodule

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/logic/permission/module"
	"report-service/pkg/szerrors"
)

type TouristSourceAreaModule struct{}

func (t TouristSourceAreaModule) HandleOpen(o module.OperateData) error {
	config := GetTouristSourceAreaConfig(o)
	var err error
	var startTime, endTime, currentDate, progressEndTime string
	var openStatus, percent int
	var isProcessing, isCleanUp bool
	if config == nil {
		//新增配置
		startTime = o.ChangeTime.AddDays(NeedRefreshDays).StartOfDay().ToDateTimeString()
		endTime = o.ChangeTime.AddDays(NeedUpdateEndDays).EndOfDay().ToDateTimeString()
		openStatus = HandleOpenStatusWait
		isProcessing = true
		isCleanUp = false
		percent = 0
		currentDate = ""
		progressEndTime = o.ChangeTime.AddHours(-1).EndOfHour().ToDateTimeString()
	} else {
		if config.IsEnable {
			//已是生效状态无需处理
			return nil
		}
		//更新配置
		startTime = config.StartTime
		endTime = o.ChangeTime.AddDays(NeedUpdateEndDays).EndOfDay().ToDateTimeString()
		openStatus = config.OpenStatus
		isProcessing = config.IsProcessing
		isCleanUp = config.IsCleanUp
		percent = config.Progress.Percent
		currentDate = config.Progress.CurrentDate
		progressEndTime = config.Progress.EndTime
		oldEndTime := carbon.Parse(config.EndTime)
		//商户结束时间小于操作时间于需要重新刷数据
		if o.ChangeTime.Gt(oldEndTime) {
			progressEndTime = o.ChangeTime.AddHours(-1).EndOfHour().ToDateTimeString()
			startTime = o.ChangeTime.AddDays(NeedRefreshDays).StartOfDay().ToDateTimeString()
			openStatus = HandleOpenStatusWait
			percent = 0
			currentDate = ""
			isProcessing = true
			isCleanUp = false
		}
	}
	err = SaveTouristSourceAreaConfig(o, true, startTime, endTime,
		openStatus, isProcessing, isCleanUp, percent, currentDate, progressEndTime)
	if err != nil {
		return err
	}
	return nil
}

func (t TouristSourceAreaModule) HandleClose(o module.OperateData) error {
	config := GetTouristSourceAreaConfig(o)
	if config == nil {
		return szerrors.NewLogicErrorWithText("没有配置无法关闭")
	}
	endTime := o.ChangeTime.AddDays(NotNormalDays).EndOfDay().ToDateTimeString() //失效更新结束时间
	err := SaveTouristSourceAreaConfig(o, false, config.StartTime, endTime,
		config.OpenStatus, config.IsProcessing, config.IsCleanUp, config.Progress.Percent,
		config.Progress.CurrentDate, config.Progress.EndTime)
	if err != nil {
		return szerrors.NewLogicErrorWithText(fmt.Sprint("保存信息错误", err.Error()))
	}
	return nil
}
