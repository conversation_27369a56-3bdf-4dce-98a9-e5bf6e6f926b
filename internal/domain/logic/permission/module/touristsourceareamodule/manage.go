package touristsourceareamodule

import (
	"fmt"
	"report-service/internal/domain/logic/permission/module"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

const (
	NotNormalDays           = 32    //失效后需要继续32天
	NeedRefreshDays         = -31   //需要刷数据的天数 31天
	NeedUpdateEndDays       = 36500 //结束时间加上100年
	HandleOpenStatusWait    = 1     //待处理
	HandleOpenStatusProcess = 2     //处理中
	HandleOpenStatusFinish  = 3     //处理完成
)

type ModuleConfig struct {
	IsEnable     bool                  `json:"is_enable"`
	StartTime    string                `json:"start_time"`
	EndTime      string                `json:"end_time"`
	OpenStatus   int                   `json:"open_status"`
	IsProcessing bool                  `json:"is_processing"`
	IsCleanUp    bool                  `json:"is_clean_up"`
	Progress     *ModuleConfigProgress `json:"progress"`
}

type ModuleConfigProgress struct {
	Percent     int    `json:"percent"`
	BeginTime   string `json:"begin_time"`
	EndTime     string `json:"end_time"`
	CurrentDate string `json:"current_date"`
}

func GetTouristSourceAreaConfig(o module.OperateData) *ModuleConfig {
	config := o.GetModuleConfig()
	if config == nil {
		return nil
	}
	return GetTouristSourceAreaConfigItem(config.Payload)
}

func GetTouristSourceAreaConfigItem(data interface{}) *ModuleConfig {
	var infoData ModuleConfig
	err := utils.JsonConvertor(data, &infoData)
	if err != nil {
		return nil
	}
	return &infoData
}

func SaveTouristSourceAreaConfig(
	o module.OperateData,
	isEnable bool,
	startTime string,
	endTime string,
	openStatus int,
	isProcessing bool,
	isCleanUp bool,
	percent int,
	currentDate string,
	progressEndTime string,
) error {
	config := &ModuleConfig{
		IsEnable:     isEnable,
		StartTime:    startTime,
		EndTime:      endTime,
		OpenStatus:   openStatus,
		IsProcessing: isProcessing,
		IsCleanUp:    isCleanUp,
		Progress: &ModuleConfigProgress{
			Percent:     percent,
			CurrentDate: currentDate,
			BeginTime:   startTime,
			EndTime:     progressEndTime,
		},
	}
	err := o.SaveModuleConfig(config)
	if err != nil {
		return szerrors.NewLogicErrorWithText(fmt.Sprint("新增信息错误", err.Error()))
	}
	return nil
}
