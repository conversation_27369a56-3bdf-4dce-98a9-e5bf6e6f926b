package module

import (
	"report-service/internal/domain/logic/config/merchantconfig"
)

func GetModuleConfigKey(key string) string {
	return "module_config_" + key
}

func (o OperateData) GetModuleConfig() *merchantconfig.MerchantConfig {
	moduleConfig, _ := merchantconfig.InfoByKeyAndMerchantId(GetModuleConfigKey(o.ModuleTag), o.MerchantId, o.MerchantId)
	return moduleConfig
}

func (o OperateData) SaveModuleConfig(saveData interface{}) error {
	setConfigParams := merchantconfig.SetConfigParams{
		Key:        GetModuleConfigKey(o.ModuleTag),
		MerchantId: o.MerchantId,
		MemberId:   o.MerchantId,
		Value:      saveData,
	}
	return merchantconfig.SetConfigByKeyAndMerchantId(setConfigParams)
}
