package accesswhitelist

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/business"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/utils"
)

type RepoForceRedirectItem struct {
	AddTime    string `json:"add_time"`
	MerchantId int    `json:"merchant_id"`
	Account    string `json:"account"`
	Name       string `json:"name"`
}

func ForceRedirectList(merchantId int, pageNum, pageSize int) ([]RepoForceRedirectItem, int, error) {
	status := enum.AccessWhiteListStatusEnabled
	accessType := enum.AccessWhiteListAccessTypeForceRedirect
	params := business.CommonSearch{
		PageNum:    &pageNum,
		PageSize:   &pageSize,
		Status:     &status,
		AccessType: &accessType,
	}
	if merchantId > 0 {
		params.MerchantIds = []int{merchantId}
	}
	whiteListRecords, total, err := repository.BusinessAccessWhiteListRepository.GetList(params)
	if err != nil {
		return nil, 0, err
	}
	merchantIds := make([]int, 0, len(whiteListRecords))
	for _, record := range whiteListRecords {
		merchantIds = append(merchantIds, record.MerchantId)
	}
	//查询商户信息
	merchantIds = utils.RemoveDuplicate(merchantIds)
	MemberInfoMap, err := pftmember.GetMemberBaseInfoMapByIds(merchantIds)
	if err != nil {
		return nil, 0, err
	}
	list := make([]RepoForceRedirectItem, 0, len(whiteListRecords))
	for _, record := range whiteListRecords {
		addTime := carbon.Parse(record.UpdatedAt)
		item := RepoForceRedirectItem{
			AddTime:    addTime.ToDateString(),
			MerchantId: record.MerchantId,
			Account:    MemberInfoMap[record.MerchantId].Account,
			Name:       MemberInfoMap[record.MerchantId].Dname,
		}
		list = append(list, item)
	}
	return list, total, nil
}
