package accesswhitelist

import (
	"fmt"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
)

// SyncAccessWhiteList 同步商户访问白名单信息
// enable: true表示开启白名单(新增正常+启用状态的记录)，false表示关闭白名单(将状态更新为禁用)
func SyncAccessWhiteList(merchantId int, enable bool) error {
	if merchantId == 0 {
		return nil
	}
	var err error
	if enable {
		err = EnableAccessWhiteList(merchantId)
	} else {
		err = DisableAccessWhiteList(merchantId)
	}
	if err != nil {
		global.LOG.Error("同步商户访问白名单信息失败",
			zap.Int("merchantId", merchantId),
			zap.Bool("enable", enable),
			zap.Error(err),
		)
		globalNotice.Error("同步商户访问白名单信息失败",
			fmt.Sprintf("商户ID：%d，是否启用：%t，错误信息：%s", merchantId, enable, err.Error()),
		)
		return err
	}
	// 同步商户访问白名单信息成功后，执行后续操作
	SyncAccessWhiteListAfter(merchantId, enable)
	return nil
}

// setAccessWhiteList 设置商户访问白名单
func setAccessWhiteList(merchantId int, accessType int, status int) error {
	if merchantId == 0 {
		return nil
	}
	// 检查是否已存在白名单记录
	existing, err := repository.BusinessAccessWhiteListRepository.Get(merchantId)
	if err != nil {
		global.LOG.Error("查询商户白名单信息失败", zap.Int("merchantId", merchantId), zap.Error(err))
		return err
	}
	// 如果记录已存在且状态为一样，则无需重复操作
	if existing != nil && existing.Status == status {
		return nil
	}
	err = repository.BusinessAccessWhiteListRepository.Set(merchantId, accessType, status)
	if err != nil {
		global.LOG.Error("操作商户白名单失败", zap.Int("merchantId", merchantId), zap.Error(err))
		return err
	}
	return nil
}

// EnableAccessWhiteList 开启商户访问白名单
func EnableAccessWhiteList(merchantId int) error {
	return setAccessWhiteList(merchantId, enum.AccessWhiteListAccessTypeNormal, enum.AccessWhiteListStatusEnabled)
}

// DisableAccessWhiteList 关闭商户访问白名单
func DisableAccessWhiteList(merchantId int) error {
	return setAccessWhiteList(merchantId, enum.AccessWhiteListAccessTypeNormal, enum.AccessWhiteListStatusDisabled)
}

// SyncAccessWhiteListAfter 同步商户访问白名单信息后执行
func SyncAccessWhiteListAfter(merchantId int, enable bool) {
	//清除商户缓存
	InvalidateMerchantForceRedirectCache(merchantId)
	//新增白名单操作
	if enable {
		//清除商户白名单缓存
		_ = ClearAllWhiteListCache()
	}

}
