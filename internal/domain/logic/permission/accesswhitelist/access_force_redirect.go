package accesswhitelist

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
)

const (
	CacheKeyPrefix = "access_white_list:"
)

// IsForceRedirect 判断商户是否开启强制跳转
func IsForceRedirect(merchantId int) (bool, error) {
	if merchantId == 0 {
		return false, nil
	}
	ctx, cacheKey := getForceRedirectCacheKey(merchantId)
	// 尝试从缓存获取
	if isCheck, err := global.REDIS.Get(ctx, cacheKey).Result(); err == nil && isCheck != "" {
		return cast.ToBool(isCheck), nil
	}
	// 获取商户的访问白名单信息
	res, err := repository.BusinessAccessWhiteListRepository.Get(merchantId)
	if err != nil {
		global.LOG.Error("获取商户的访问白名单信息失败", zap.Int("merchantId", merchantId), zap.Error(err))
		return false, err
	}
	// 如果没有找到记录，返回false
	result := false
	if res != nil {
		// 判断访问类型是否为强制跳转（accessType == 2）且状态为启用（status == 1）
		result = res.AccessType == enum.AccessWhiteListAccessTypeForceRedirect &&
			res.Status == enum.AccessWhiteListStatusEnabled
	}
	global.REDIS.Set(ctx, cacheKey, cast.ToInt(result), enum.CacheTimeHour)
	return result, nil
}

// InvalidateMerchantForceRedirectCache 清除所有白名单商户缓存
func InvalidateMerchantForceRedirectCache(merchantId int) {
	ctx, key := getForceRedirectCacheKey(merchantId)
	global.REDIS.Del(ctx, key)
	return
}

// getForceRedirectCacheKey 获取缓存key
func getForceRedirectCacheKey(merchantId int) (context.Context, string) {
	return context.Background(), fmt.Sprintf(CacheKeyPrefix+"merchant_id:%d", merchantId)
}
