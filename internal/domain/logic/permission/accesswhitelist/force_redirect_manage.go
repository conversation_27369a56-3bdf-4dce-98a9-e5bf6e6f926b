package accesswhitelist

import (
	"errors"
	"fmt"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/utils"
)

func ForceRedirectAdd(merchantId int) error {
	//限制账号类型
	MemberInfoMap, err := pftmember.GetMemberBaseInfoMapByIds([]int{merchantId})
	if err != nil {
		return fmt.Errorf("获取商户信息失败：%v", err)
	}
	info, ok := MemberInfoMap[merchantId]
	if !ok {
		return errors.New("商户信息获取失败，该商户不存在")
	}
	if !utils.Container([]int{enum.MerchantTypeSupplier, enum.MerchantTypeDistributor}, info.Dtype) {
		return errors.New("商户类型错误，请选择供应商或者分销商")
	}
	return setForceRedirect(merchantId, enum.AccessWhiteListAccessTypeForceRedirect)
}

func ForceRedirectRemove(merchantId int) error {
	return setForceRedirect(merchantId, enum.AccessWhiteListAccessTypeNormal)
}

func setForceRedirect(merchantId int, accessType int) error {
	if merchantId == 0 {
		return nil
	}
	// 检查是否已存在白名单记录
	existing, err := repository.BusinessAccessWhiteListRepository.Get(merchantId)
	if err != nil {
		global.LOG.Error("查询商户白名单信息失败", zap.Int("merchantId", merchantId), zap.Error(err))
		return err
	}
	if existing == nil || existing.Status != enum.AccessWhiteListStatusEnabled {
		return errors.New("未开通开放功能【新报表入口】，无法添加")
	}
	// 如果记录已存在且状态为一样，则无需重复操作
	if existing.AccessType == accessType {
		if accessType == enum.AccessWhiteListAccessTypeNormal {
			return errors.New("该账号已移除，无需重复移除")
		}
		return errors.New("该账号已添加，无需重复添加")
	}
	err = repository.BusinessAccessWhiteListRepository.Set(merchantId, accessType, existing.Status)
	if err != nil {
		global.LOG.Error("操作商户强制跳转失败", zap.Int("merchantId", merchantId), zap.Error(err))
		return err
	}
	//清除强制跳转缓存
	InvalidateMerchantForceRedirectCache(merchantId)
	//刷新缓存
	_, _ = IsForceRedirect(merchantId)
	return nil
}
