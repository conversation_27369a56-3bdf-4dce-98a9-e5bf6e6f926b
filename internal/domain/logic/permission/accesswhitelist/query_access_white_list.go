package accesswhitelist

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/utils"
	"strconv"
	"strings"
)

// GetAllWhiteListMerchants 获取全部白名单商户，并缓存24小时
func GetAllWhiteListMerchants() ([]int, error) {
	ctx, cacheKey := getAllWhiteListMerchantsCacheKey()
	// 尝试从缓存获取
	if cachedResult, err := global.REDIS.Get(ctx, cacheKey).Result(); err == nil {
		if cachedResult != "" {
			var ids []int
			if ids, err = decodeMerchantIds(cachedResult); err == nil {
				return ids, nil
			}
			global.LOG.Error("白名单缓存解析失败", zap.Error(err))
		}
	}

	// 分批获取所有启用状态的白名单商户
	var allMerchantIds []int
	pageSize := 1000
	page := 1
	for {
		whiteListRecords, err := repository.BusinessAccessWhiteListRepository.GetByPage(page, pageSize)
		if err != nil {
			global.LOG.Error("分页获取白名单商户失败",
				zap.Int("page", page),
				zap.Int("pageSize", pageSize),
				zap.Error(err))
			return nil, err
		}
		// 提取商户ID
		for _, record := range whiteListRecords {
			allMerchantIds = append(allMerchantIds, record.MerchantId)
		}
		// 如果当前页数据少于pageSize，说明已经是最后一页
		if len(whiteListRecords) < pageSize {
			break
		}
		page++
	}

	// 缓存24小时
	cacheValue := encodeMerchantIds(allMerchantIds)
	global.REDIS.Set(ctx, cacheKey, cacheValue, enum.CacheTimeDay)

	return utils.RemoveDuplicate(allMerchantIds), nil
}

// ClearAllWhiteListCache 清除所有白名单相关缓存
func ClearAllWhiteListCache() error {
	ctx, cacheKey := getAllWhiteListMerchantsCacheKey()
	if err := global.REDIS.Del(ctx, cacheKey).Err(); err != nil {
		global.LOG.Error("清除所有白名单商户缓存失败",
			zap.String("cacheKey", cacheKey),
			zap.Error(err))
		globalNotice.Error("清除所有白名单商户缓存失败",
			fmt.Sprintf("缓存key：%s", cacheKey),
			fmt.Sprintf("错误信息：%s", err.Error()),
		)
		return err
	}
	return nil
}

// getAllWhiteListMerchantsCacheKey 获取缓存key
func getAllWhiteListMerchantsCacheKey() (context.Context, string) {
	return context.Background(), fmt.Sprintf(CacheKeyPrefix + "all_merchants")
}

// encodeMerchantIds 将商户ID数组编码为字符串
func encodeMerchantIds(merchantIds []int) string {
	if len(merchantIds) == 0 {
		return ""
	}

	// 使用更高效的编码方式
	var builder strings.Builder
	for i, id := range merchantIds {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString(strconv.Itoa(id))
	}
	return builder.String()
}

// decodeMerchantIds 将字符串解码为商户ID数组
func decodeMerchantIds(encoded string) ([]int, error) {
	if encoded == "" {
		return []int{}, nil
	}

	parts := strings.Split(encoded, ",")
	result := make([]int, 0, len(parts))

	for _, part := range parts {
		if part != "" {
			id, err := strconv.Atoi(part)
			if err != nil {
				return nil, err
			}
			result = append(result, id)
		}
	}
	return result, nil
}
