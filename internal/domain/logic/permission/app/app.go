package app

import (
	"context"
	"encoding/json"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

const (
	CacheKeyPrefix = "app:"
	redisKeyFormat = CacheKeyPrefix + "app_id:%s"
)

type Item struct {
	AppId     string `json:"app_id"`     // 应用唯一标识
	AppSecret string `json:"app_secret"` // 应用密钥
	AppName   string `json:"app_name"`   // 应用名称
}

// SaveApp 保存应用信息
func SaveApp(appId string, appSecret string, appName string) error {
	err := repository.BusinessAppRepository.Set(appId, appSecret, appName)
	if err != nil {
		return err
	}
	//删除缓存
	global.REDIS.Del(context.Background(), fmt.Sprintf(redisKeyFormat, appId))
	return nil
}

// GetAppByAppId 根据appId获取应用信息
func GetAppByAppId(appId string) *Item {
	ctx := context.Background()
	key := fmt.Sprintf(redisKeyFormat, appId)
	// 尝试从缓存获取
	if str, err := global.REDIS.Get(ctx, key).Result(); err == nil && str != "" {
		var item *Item
		if err = json.Unmarshal([]byte(str), &item); err == nil {
			return item
		}
	}
	// 缓存未命中，查询数据库
	info, err := repository.BusinessAppRepository.GetAppByAppId(appId)
	if err != nil {
		return nil
	}
	// 转换数据结构
	var item *Item
	if err = utils.JsonConvertor(info, &item); err != nil {
		return nil
	}
	// 更新缓存
	var val []byte
	if val, err = json.Marshal(item); err == nil {
		global.REDIS.Set(ctx, key, string(val), enum.CacheTimeDay)
	}
	return item
}
