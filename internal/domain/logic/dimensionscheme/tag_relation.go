package dimensionscheme

import (
	"context"
	"encoding/json"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	repoDimensionscheme "report-service/internal/domain/repository/dimensionscheme"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggeddata"
	"report-service/pkg/szerrors"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
)

// RefreshTagRelationByTagGroupCode 根据标签组刷新标签关系
func RefreshTagRelationByTagGroupCode(scene string, tagGroupCode string, merchantId int) error {
	poTagRelations := make([]repoDimensionscheme.PoTagRelation, 0)
	var pageNum = 1
	var pageSize = 200
	for {
		res, err := taggeddata.TaggedDataSubjectGroup(&taggeddata.TaggedDataByGroupRequestVO{
			Subject:    scene,
			GroupCodes: []string{tagGroupCode},
			PageNum:    pageNum,
			PageSize:   pageSize,
		})
		if err != nil {
			return err
		}
		if len(res.Rows) == 0 {
			break
		}
		for _, taggedData := range res.Rows {
			if len(taggedData.Tags) > 1 {
				return szerrors.NewLogicErrorWithText(fmt.Sprintf("报表暂不支持主体多标签，主体[%s]", *taggedData.SubjectName))
			}
			if len(taggedData.Tags) != 1 {
				return szerrors.NewLogicErrorWithText(fmt.Sprintf("主体数据异常，必须关联标签，主体[%s]", *taggedData.SubjectName))
			}
			// 根据|分割，取出最后一段的值作为subjectId
			split := strings.Split(taggedData.SubjectID, "|")
			subjectId, err := cast.ToIntE(split[len(split)-1])
			if err != nil {
				return szerrors.NewLogicErrorWithText(fmt.Sprintf("主体数据异常，暂时仅支持整形ID，主体[%s]ID为[%s]", *taggedData.SubjectName, taggedData.SubjectID))
			}
			poTagRelations = append(poTagRelations, repoDimensionscheme.PoTagRelation{
				MerchantId:   merchantId,
				TagGroupCode: tagGroupCode,
				TagCode:      taggedData.Tags[0].TagCode,
				SubjectId:    subjectId,
				Source:       enum.DimensionToTagSourceMapKeyTagCenter,
			})
		}
		if len(res.Rows) < pageSize {
			break
		}
		pageNum++
	}

	return executeInTransaction(poTagRelations, merchantId, tagGroupCode, enum.DimensionToTagSourceMapKeyTagCenter)
}

// DeleteTagRelationsByGroupCode 根据标签组编码删除所有相关的标签关联记录
func DeleteTagRelationsByGroupCode(merchantId int, groupCode string, source int) error {
	err := repository.TagRelationRepository.DeleteByTagGroupCode(nil, merchantId, groupCode, source)
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

// GetSubjectIdListByMerchantId 根据商户ID获取标签关系
func GetSubjectIdListByMerchantId(merchantId int, source int, tagGroupCode string, tagCode string) ([]int, error) {
	ids := make([]int, 0)
	if merchantId == 0 {
		return ids, nil
	}
	//走缓存
	cacheKey := fmt.Sprintf("tag_group:%d_%d_%s_%s", merchantId, source, tagGroupCode, tagCode)
	str, err := global.REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(str), &ids)
		if err != nil {
			return ids, szerrors.NewLogicError(err)
		}
		return ids, nil
	}
	list, err := repository.TagRelationRepository.GetSubjectIdListByMerchantId(merchantId, source, tagGroupCode, tagCode)
	if err != nil {
		return ids, err
	}
	if len(list) > 0 {
		for _, relation := range list {
			ids = append(ids, relation.SubjectId)
		}
		//写入缓存下
		var jsonStr []byte
		jsonStr, err = json.Marshal(ids)
		if err == nil {
			_ = global.REDIS.Set(context.Background(), cacheKey, jsonStr, 3600*time.Second).Err()
		}
	}
	return ids, nil
}

// RefreshMemberTagRelationByGroupAccountId 根据集团账户ID刷新集团成员标签关系
func RefreshMemberTagRelationByGroupAccountId(groupAccountId int) ([]int, error) {
	memberList, err := pftmember.GetMemberBaseInfoMapByIds([]int{groupAccountId})
	if err != nil {
		return nil, szerrors.NewLogicError(err)
	}
	memberInfo, ok := memberList[groupAccountId]
	if !ok {
		return nil, szerrors.NewLogicErrorWithText(fmt.Sprintf("商户id[%d]不存在", groupAccountId))
	}
	if memberInfo.Dtype != enum.MerchantTypeGroup {
		return nil, szerrors.NewLogicErrorWithText(fmt.Sprintf("商户id[%d]不是集团商户类型", groupAccountId))
	}
	status := 0 //-1.查询全部 0.正常 1.失效
	responseData := make([]pftmember.MemberRelationDataItem, 0)
	responseData, err = pftmember.QueryRelationByGroupId(groupAccountId, status)
	if err != nil {
		return nil, szerrors.NewLogicErrorWithText(fmt.Sprintf("商户id[%d]查询集团成员信息失败", groupAccountId))
	}
	groupMemberIds := make([]int, 0)
	poTagRelations := make([]repoDimensionscheme.PoTagRelation, 0)
	for _, tmp := range responseData {
		poTagRelations = append(poTagRelations, repoDimensionscheme.PoTagRelation{
			MerchantId:   groupAccountId,
			TagGroupCode: strconv.Itoa(groupAccountId),
			TagCode:      strconv.Itoa(groupAccountId),
			SubjectId:    tmp.SonId,
			Source:       enum.DimensionToTagSourceMapKeyGroup,
		})
		groupMemberIds = append(groupMemberIds, tmp.SonId)
	}

	cacheKey := fmt.Sprintf("tag_group:%d_%d_%s_%s", groupAccountId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(groupAccountId), strconv.Itoa(groupAccountId))
	global.REDIS.Del(context.Background(), cacheKey)

	return groupMemberIds, executeInTransaction(poTagRelations, groupAccountId, strconv.Itoa(groupAccountId), enum.DimensionToTagSourceMapKeyGroup)
}

// AddMemberTagRelationByGroupAccountIdAndSonId 添加集团成员标签关系
func AddMemberTagRelationByGroupAccountIdAndSonId(groupAccountId int, sonId int) error {
	memberList, err := pftmember.GetMemberBaseInfoMapByIds([]int{groupAccountId})
	if err != nil {
		return szerrors.NewLogicError(err)
	}
	memberInfo, ok := memberList[groupAccountId]
	if !ok {
		return szerrors.NewLogicErrorWithText(fmt.Sprintf("商户id[%d]不存在", groupAccountId))
	}
	if memberInfo.Dtype != enum.MerchantTypeGroup {
		return szerrors.NewLogicErrorWithText(fmt.Sprintf("商户id[%d]不是集团商户类型", groupAccountId))
	}
	poTagRelations := make([]repoDimensionscheme.PoTagRelation, 0)
	poTagRelations = append(poTagRelations, repoDimensionscheme.PoTagRelation{
		MerchantId:   groupAccountId,
		TagGroupCode: strconv.Itoa(groupAccountId),
		TagCode:      strconv.Itoa(groupAccountId),
		SubjectId:    sonId,
		Source:       enum.DimensionToTagSourceMapKeyGroup,
	})
	err = repository.TagRelationRepository.InsertAll(nil, poTagRelations)
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}

	cacheKey := fmt.Sprintf("tag_group:%d_%d_%s_%s", groupAccountId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(groupAccountId), strconv.Itoa(groupAccountId))
	global.REDIS.Del(context.Background(), cacheKey)

	return nil
}

func executeInTransaction(poTagRelations []repoDimensionscheme.PoTagRelation, merchantId int, tagGroupCode string, source int) error {
	// 开启事务，主要为了绕过 SelectDB 异步提交
	tx, err := repository.TransRepo.Begin([]repository.DatabaseInstance{
		repository.TagRelationRepository,
	})
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	defer func() {
		if err != nil {
			_ = repository.TransRepo.Rollback(tx)
		}
	}()
	err = repository.TagRelationRepository.DeleteByTagGroupCode(tx, merchantId, tagGroupCode, source)
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	if len(poTagRelations) > 0 {
		err = repository.TagRelationRepository.InsertAll(tx, poTagRelations)
		if err != nil {
			return szerrors.NewDataPersistenceError(err)
		}
	}
	err = repository.TransRepo.Commit(tx)
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}
