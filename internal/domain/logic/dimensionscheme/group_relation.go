package dimensionscheme

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dimensionscheme"
	"report-service/pkg/szerrors"
)

type DoGroupRelation struct {
	SchemeId       int `json:"scheme_id" label:"方案ID"`
	GroupId        int `json:"group_id" label:"分组ID"`
	DimensionValue int `json:"dimension_value" label:"维度值"`
}

func GroupRelationList(merchantId int, schemeId int, dimensionValues []int) ([]DoGroupRelation, error) {
	err := ValidateSchemeMerchantId(merchantId, schemeId)
	if err != nil {
		return nil, err
	}

	if len(dimensionValues) >= enum.DimensionValuesMaxCount {
		return nil, szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("维度值数量不能超过%d个", enum.DimensionValuesMaxCount))
	}

	var poDimensionGroupRelations []dimensionscheme.PoGroupRelation
	if dimensionValues == nil || len(dimensionValues) == 0 {
		poDimensionGroupRelations, err = repository.GroupRelationRepository.ListBySchemeId(schemeId)
		if err != nil {
			return nil, err
		}
	} else {
		poDimensionGroupRelations, err = repository.GroupRelationRepository.ListBySchemeIdAndDimensionValues(schemeId, dimensionValues)
		if err != nil {
			return nil, err
		}
	}

	doSchemes := make([]DoGroupRelation, 0)
	for _, po := range poDimensionGroupRelations {
		doSchemes = append(doSchemes, DoGroupRelation{
			SchemeId:       po.SchemeId,
			GroupId:        po.GroupId,
			DimensionValue: po.DimensionValue,
		})
	}
	return doSchemes, nil
}
