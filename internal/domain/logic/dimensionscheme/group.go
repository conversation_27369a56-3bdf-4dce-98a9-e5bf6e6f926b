package dimensionscheme

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type DoGroup struct {
	Id       int    `json:"id" label:"ID"`
	SchemeId int    `json:"scheme_id" label:"方案ID"`
	Name     string `json:"name" label:"名称"`
}

func GroupCreate(merchantId int, schemeId int, name string) error {
	err := ValidateSchemeMerchantId(merchantId, schemeId)
	if err != nil {
		return err
	}

	err = ValidateGroupCreateParams(schemeId, name)
	if err != nil {
		return err
	}

	_, err = repository.GroupRepository.Insert(schemeId, name)
	if err != nil {
		return err
	}
	return nil
}

func GroupModify(merchantId int, id int, name string) error {
	group, err := repository.GroupRepository.Detail(id)
	if utils.IsRecordNotFoundErr(err) {
		return szerrors.NewInvalidParamErrorWithText("分组ID不存在")
	}
	if err != nil {
		return err
	}

	err = ValidateGroupModifyParams(group.SchemeId, id, name)
	if err != nil {
		return err
	}

	err = ValidateSchemeMerchantId(merchantId, group.SchemeId)
	if err != nil {
		return err
	}

	err = repository.GroupRepository.Modify(id, name)
	if err != nil {
		return err
	}
	return nil
}

func GroupDelete(merchantId int, id int) error {
	group, err := repository.GroupRepository.Detail(id)
	if utils.IsRecordNotFoundErr(err) {
		return szerrors.NewInvalidParamErrorWithText("分组ID不存在")
	}
	if err != nil {
		return err
	}

	err = ValidateSchemeMerchantId(merchantId, group.SchemeId)
	if err != nil {
		return err
	}

	err = repository.GroupRepository.Delete(id)
	if err != nil {
		return err
	}
	return nil
}

func GroupPagination(merchantId int, schemeId int, page int, pageSize int) ([]DoGroup, int, error) {
	err := ValidateSchemeMerchantId(merchantId, schemeId)
	if err != nil {
		return nil, 0, err
	}

	poGroups, err := repository.GroupRepository.Pagination(schemeId, page, pageSize)
	if err != nil {
		return nil, 0, err
	}
	doGroups := make([]DoGroup, 0)
	for _, poGroup := range poGroups {
		doGroups = append(doGroups, DoGroup{
			Id:       poGroup.Id,
			SchemeId: poGroup.SchemeId,
			Name:     poGroup.Name,
		})
	}

	total, err := repository.GroupRepository.Count(schemeId)
	if err != nil {
		return nil, 0, err
	}

	return doGroups, total, nil
}

func ValidateGroupCreateParams(schemeId int, name string) error {
	poGroups, err := repository.GroupRepository.Pagination(schemeId, 1, 100)
	if err != nil {
		return err
	}

	groupNameMap := make(map[string]bool)
	for _, poGroup := range poGroups {
		groupNameMap[poGroup.Name] = true
	}
	if _, ok := groupNameMap[name]; ok {
		return szerrors.NewInvalidParamErrorWithText("分组名称已存在")
	}

	if len(poGroups) >= enum.SchemeGroupMaxCount {
		return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("分组数量已达上限%d个", enum.SchemeGroupMaxCount))
	}
	return nil
}

func ValidateGroupModifyParams(schemeId int, id int, name string) error {
	poGroups, err := repository.GroupRepository.Pagination(schemeId, 1, 100)
	if err != nil {
		return err
	}

	groupNameMap := make(map[string]bool)
	for _, poGroup := range poGroups {
		if poGroup.Id == id {
			continue
		}
		groupNameMap[poGroup.Name] = true
	}
	if _, ok := groupNameMap[name]; ok {
		return szerrors.NewInvalidParamErrorWithText("分组名称已存在")
	}
	return nil
}

func GroupNameMapByIds(ids []int) (map[int]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	if len(ids) > enum.ListSizeMaxCount {
		return nil, szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("列表ID数量不能超过%d个", enum.ListSizeMaxCount))
	}
	groupMap := make(map[int]string)
	poGroups, err := repository.GroupRepository.ListByIds(ids)
	if err != nil {
		return nil, err
	}
	for _, poGroup := range poGroups {
		groupMap[poGroup.Id] = poGroup.Name
	}
	return groupMap, nil
}
