package dimensionscheme

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dimensionscheme"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type SchemeCreateParams struct {
	MerchantId    int    `json:"merchant_id" validate:"required"`
	Name          string `json:"name" validate:"required" label:"名称"`
	DimensionType int    `json:"dimension_type" validate:"required,gt=0" label:"维度类型：1spu，2sku，3支付方式，4订单渠道"`
	Groups        []struct {
		Name  string `json:"name" validate:"required" label:"分组名称"`
		Index *int   `json:"index" validate:"required" label:"分组序列"`
	} `json:"groups" validate:"required" label:"分组列表"`
	DimensionGroupRelation []struct {
		DimensionValue int  `json:"dimension_value" validate:"required,gt=0" label:"维度值"`
		GroupIndex     *int `json:"group_index" validate:"required" label:"分组序列"`
	} `json:"dimension_group_relation" label:"维度分组关系"`
}

type GroupsParams struct {
	Name  string `json:"name" validate:"required" label:"分组名称"`
	Index *int   `json:"index" validate:"required" label:"分组序列"`
}

type DimensionGroupRelationParams struct {
	DimensionValue int  `json:"dimension_value" validate:"required,gt=0" label:"维度值"`
	GroupIndex     *int `json:"group_index" validate:"required" label:"分组序列"`
}

type DoScheme struct {
	Id                     int               `json:"id" label:"ID"`
	Name                   string            `json:"name" label:"名称"`
	DimensionType          int               `json:"dimension_type" label:"维度类型：1spu，2sku，3支付方式，4订单渠道"`
	DimensionGroupRelation []DoGroupRelation `json:"dimension_group_relation" label:"维度分组关系"`
}

func SchemeCreate(params *SchemeCreateParams) (int, error) {
	err := ValidateSchemeCreateParams(params)
	if err != nil {
		return 0, err
	}

	schemeId, err := repository.SchemeRepository.Insert(params.MerchantId, params.DimensionType, params.Name)
	if err != nil {
		return 0, err
	}

	groupNameIndex := make(map[string]int)
	var poGroups []*dimensionscheme.PoGroup
	for _, group := range params.Groups {
		groupNameIndex[group.Name] = *group.Index
		poGroups = append(poGroups, &dimensionscheme.PoGroup{
			SchemeId: schemeId,
			Name:     group.Name,
		})
	}
	err = repository.GroupRepository.BatchSave(poGroups)
	if err != nil {
		return 0, err
	}

	groupIndexIds := make(map[int]int)
	for _, poGroup := range poGroups {
		groupIndexIds[groupNameIndex[poGroup.Name]] = poGroup.Id
	}

	var poDimensionGroupRelations []dimensionscheme.PoGroupRelation
	if params.DimensionGroupRelation != nil && len(params.DimensionGroupRelation) > 0 {
		for _, relation := range params.DimensionGroupRelation {
			if groupId, ok := groupIndexIds[*relation.GroupIndex]; !ok {
				return 0, szerrors.NewInvalidParamErrorWithText("分组序列不存在")
			} else {
				poDimensionGroupRelations = append(poDimensionGroupRelations, dimensionscheme.PoGroupRelation{
					SchemeId:       schemeId,
					GroupId:        groupId,
					DimensionValue: relation.DimensionValue,
				})
			}
		}
		err = repository.GroupRelationRepository.BatchSaveWithCleanBySchemeId(schemeId, poDimensionGroupRelations)
		if err != nil {
			return 0, err
		}
	}
	return schemeId, nil
}

type SchemeModifyParams struct {
	Id                     int    `json:"id" validate:"required"`
	MerchantId             int    `json:"merchant_id" validate:"required"`
	Name                   string `json:"name" validate:"required" label:"名称"`
	DimensionGroupRelation []struct {
		DimensionValue int `json:"dimension_value" validate:"required,gt=0" label:"维度值"`
		GroupId        int `json:"group_id" validate:"required" label:"分组ID"`
	} `json:"dimension_group_relation" label:"维度分组关系"`
}

func SchemeModify(params *SchemeModifyParams) error {
	poScheme, err := repository.SchemeRepository.Detail(params.Id)
	if err != nil {
		return err
	}
	if poScheme.MerchantId != params.MerchantId {
		return szerrors.NewInvalidParamErrorWithText("无权限操作")
	}

	err = ValidateSchemeModifyParams(params)
	if err != nil {
		return err
	}

	err = repository.SchemeRepository.Modify(params.Id, params.Name)
	if err != nil {
		return err
	}

	var poDimensionGroupRelations []dimensionscheme.PoGroupRelation
	if params.DimensionGroupRelation != nil && len(params.DimensionGroupRelation) > 0 {
		for _, relation := range params.DimensionGroupRelation {
			poDimensionGroupRelations = append(poDimensionGroupRelations, dimensionscheme.PoGroupRelation{
				SchemeId:       poScheme.Id,
				GroupId:        relation.GroupId,
				DimensionValue: relation.DimensionValue,
			})
		}
		err = repository.GroupRelationRepository.BatchSaveWithCleanBySchemeId(poScheme.Id, poDimensionGroupRelations)
		if err != nil {
			return err
		}
	} else {
		err = repository.GroupRelationRepository.DeleteBySchemeId(poScheme.Id)
		if err != nil {
			return err
		}
	}
	return nil
}

func Delete(merchantId int, id int) error {
	poScheme, err := repository.SchemeRepository.Detail(id)
	if err != nil {
		return err
	}
	if poScheme.MerchantId != merchantId {
		return szerrors.NewInvalidParamErrorWithText("无权限操作")
	}

	err = repository.SchemeRepository.Delete(id)
	if err != nil {
		return err
	}
	err = repository.GroupRepository.DeleteBySchemeId(id)
	if err != nil {
		return err
	}
	err = repository.GroupRelationRepository.DeleteBySchemeId(id)
	if err != nil {
		return err
	}
	return nil
}

func Pagination(merchantId int, dimensionType int, page int, pageSize int) ([]*DoScheme, int, error) {
	poSchemes, err := repository.SchemeRepository.Pagination(merchantId, dimensionType, page, pageSize)
	if err != nil {
		return nil, 0, err
	}
	var doSchemes []*DoScheme
	for _, poScheme := range poSchemes {
		doSchemes = append(doSchemes, &DoScheme{
			Id:            poScheme.Id,
			Name:          poScheme.Name,
			DimensionType: poScheme.DimensionType,
		})
	}

	total, err := repository.SchemeRepository.Count(merchantId, dimensionType)
	if err != nil {
		return nil, 0, err
	}
	return doSchemes, total, nil
}

func ValidateSchemeCreateParams(params *SchemeCreateParams) error {
	total, err := repository.SchemeRepository.Count(params.MerchantId, params.DimensionType)
	if err != nil {
		return err
	} else if total >= enum.MerchantSchemeMaxCount {
		if params.DimensionType != enum.MerchantSchemeDimensionTypeAgeGroup {
			return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("此维度分组方案已超过最大值%d", enum.MerchantSchemeMaxCount))
		}
	}

	if len(params.Groups) == 0 {
		return szerrors.NewInvalidParamErrorWithText("分组不能为空")
	}

	groupNames := make(map[string]int)
	groupIndexes := make(map[int]int)
	for _, group := range params.Groups {
		if _, ok := groupIndexes[*group.Index]; ok {
			return szerrors.NewInvalidParamErrorWithText("分组序列不能重复")
		} else {
			groupIndexes[*group.Index] = 1
		}
		if _, ok := groupNames[group.Name]; ok {
			return szerrors.NewInvalidParamErrorWithText("分组名称不能重复")
		} else {
			groupNames[group.Name] = 1
		}
	}

	dimensionValues := make(map[int]int)
	for _, relation := range params.DimensionGroupRelation {
		if _, ok := groupIndexes[*relation.GroupIndex]; !ok {
			return szerrors.NewInvalidParamErrorWithText("分组序列不存在")
		}
		if _, ok := dimensionValues[relation.DimensionValue]; !ok {
			dimensionValues[relation.DimensionValue] = 1
		} else {
			return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("维度值不能重复， dimension_value: %d", relation.DimensionValue))
		}
	}
	return nil
}

func ValidateSchemeModifyParams(params *SchemeModifyParams) error {
	groups := make(map[int]string)
	items, err := repository.GroupRepository.Pagination(params.Id, 1, 1000)
	if err != nil {
		return err
	}
	for _, item := range items {
		groups[item.Id] = item.Name
	}

	dimensionValues := make(map[int]int)
	for _, groupRelationItem := range params.DimensionGroupRelation {
		if _, ok := groups[groupRelationItem.GroupId]; !ok {
			return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("分组不存在, group_id: %d", groupRelationItem.GroupId))
		}
		if _, ok := dimensionValues[groupRelationItem.DimensionValue]; !ok {
			dimensionValues[groupRelationItem.DimensionValue] = 1
		} else {
			return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("维度值不能重复， dimension_value: %d", groupRelationItem.DimensionValue))
		}
	}
	return nil
}

func ValidateSchemeMerchantId(merchantId int, schemeId int) error {
	scheme, err := repository.SchemeRepository.Detail(schemeId)
	if err != nil {
		return err
	}
	if scheme.MerchantId != merchantId {
		return szerrors.NewInvalidParamErrorWithText("无权限操作")
	}
	return nil
}

func SchemeNameMapByIds(ids []int) (map[int]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	if len(ids) > enum.ListSizeMaxCount {
		return nil, szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("方案ID数量不能超过%d个", enum.ListSizeMaxCount))
	}
	schemes, err := repository.SchemeRepository.ListByIds(ids)
	if err != nil {
		return nil, err
	}
	schemeMap := make(map[int]string)
	for _, scheme := range schemes {
		schemeMap[scheme.Id] = scheme.Name
	}
	return schemeMap, nil
}

func ValidateSchemeIds(ids []int) error {
	if ids == nil || len(ids) == 0 {
		return szerrors.NewInvalidParamErrorWithText("ids不能为空")
	}

	ids = utils.RemoveDuplicate(ids)
	if len(ids) > enum.ListSizeMaxCount {
		return szerrors.NewInvalidParamErrorWithText(fmt.Sprintf("方案ID数量不能超过%d个", enum.ListSizeMaxCount))
	}
	schemes, err := repository.SchemeRepository.ListByIds(ids)
	if err != nil {
		return err
	}
	if len(schemes) != len(ids) {
		return szerrors.NewInvalidParamErrorWithText("存在无效的方案ID，请检查方案ID")
	}
	return nil
}

func HandleAgeGroup(merchantId int, name string, ageGroup [][]int, otherGroup map[int]string, schemeId int, rmAgeGroup bool) (int, error) {
	if schemeId > 0 {
		err := Delete(merchantId, schemeId)
		if err != nil {
			return schemeId, szerrors.NewLogicError(err)
		}
	}
	if rmAgeGroup == true {
		return 0, nil
	}
	//新增年龄段分组
	var params SchemeCreateParams
	params.MerchantId = merchantId
	params.Name = name
	params.DimensionType = enum.MerchantSchemeDimensionTypeAgeGroup
	indexMake := 0
	//年龄段处理
	for i, items := range ageGroup {
		if len(items) != 2 {
			continue
		}
		index := i + 1
		params.Groups = append(params.Groups, GroupsParams{
			Name:  fmt.Sprintf("%d~%d", items[0], items[1]),
			Index: &index,
		})
		for s := items[0]; s <= items[1]; s++ {
			params.DimensionGroupRelation = append(params.DimensionGroupRelation, DimensionGroupRelationParams{
				DimensionValue: s,
				GroupIndex:     &index,
			})
		}
		indexMake = i + 1
	}
	//其他分组处理
	for s, v := range otherGroup {
		index := indexMake
		index++
		params.Groups = append(params.Groups, GroupsParams{
			Name:  v,
			Index: &index,
		})
		params.DimensionGroupRelation = append(params.DimensionGroupRelation, DimensionGroupRelationParams{
			DimensionValue: s,
			GroupIndex:     &index,
		})
	}
	return SchemeCreate(&params)
}
