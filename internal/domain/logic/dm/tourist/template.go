package tourist

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func (d *DoDimension) FormatByTemplate(template template.DoListItem, subjectType int) map[string]interface{} {
	result := make(map[string]interface{})
	dimension, err := GetSubjectTypeDimension(template, []int{subjectType})
	if err != nil {
		return nil
	}
	if utils.Container(dimension, enum.DimensionRegion) {
		result[enum.DimensionRegion] = d.RegionName
	}
	if utils.Container(dimension, enum.DimensionCountry) {
		result[enum.DimensionCountry] = d.<PERSON>
	}
	if utils.Container(dimension, enum.DimensionProvince) {
		result[enum.DimensionProvince] = d.ProvinceName
	}
	if utils.Container(dimension, enum.DimensionCity) {
		result[enum.DimensionCity] = d.CityName
	}
	if utils.Container(dimension, enum.DimensionDistrict) {
		result[enum.DimensionDistrict] = d.DistrictName
	}
	if utils.Container(dimension, enum.DimensionGender) {
		result[enum.DimensionGender] = d.GenderName
	}
	if utils.Container(dimension, enum.DimensionAgeGroup) {
		if template.DimensionScheme.AgeGroup != nil {
			result[enum.DimensionAgeGroup] = d.AgeGroupName
		} else {
			result[enum.DimensionAgeGroup] = d.Age
		}
	}
	return result
}

func GetDimensionScheme(doTemplate template.DoListItem) (dimensionScheme map[string]int) {
	dimensionScheme = make(map[string]int)
	//年龄段处理同分组方案一样
	if doTemplate.DimensionScheme.AgeGroup != nil {
		dimensionScheme[enum.DimensionAgeGroup] = doTemplate.DimensionScheme.AgeGroup.Id
	}
	return
}

func GetDimensionRange(doDimensionRange dmcommon.DoDimensionRange, specialDimension common.SpecialDimension) (dimensionRange map[string][]int, dimensionRangeByString map[string][]string) {
	dimensionRange = make(map[string][]int)
	dimensionRange[enum.DimensionSaleChannel] = mergeDimensions(doDimensionRange.SaleChannel, specialDimension.SaleChannel)
	dimensionRange[enum.DimensionDistributor] = mergeDimensions(doDimensionRange.Distributor, specialDimension.Distributor)
	dimensionRange[enum.DimensionSpu] = mergeDimensions(doDimensionRange.Spu, specialDimension.Spu)
	dimensionRange[enum.DimensionSku] = mergeDimensions(doDimensionRange.Sku, specialDimension.Sku)
	dimensionRange[enum.DimensionPayMode] = mergeDimensions(doDimensionRange.PayMode, specialDimension.PayMode)
	dimensionRange[enum.DimensionOperator] = mergeDimensions(doDimensionRange.Operator, specialDimension.Operator)
	dimensionRange[enum.DimensionSellOperator] = mergeDimensions(doDimensionRange.SellOperator, specialDimension.SellOperator)
	dimensionRange[enum.DimensionSellSite] = mergeDimensions(doDimensionRange.SellSite, specialDimension.SellSite)

	dimensionRangeByString = make(map[string][]string)
	dimensionRangeByString[enum.DimensionRegion] = doDimensionRange.Region
	dimensionRangeByString[enum.DimensionCountry] = doDimensionRange.Country
	dimensionRangeByString[enum.DimensionProvince] = doDimensionRange.Province
	dimensionRangeByString[enum.DimensionCity] = doDimensionRange.City
	dimensionRangeByString[enum.DimensionDistrict] = doDimensionRange.District
	return
}

func GetCommonSearchModelFilter(doDimensionRange dmcommon.DoDimensionRange) (commonSearchFilter dm.CommonSearchModelFilter) {
	if len(doDimensionRange.Region) > 0 {
		commonSearchFilter.RegionIds = doDimensionRange.Region
	}
	if len(doDimensionRange.Country) > 0 {
		commonSearchFilter.CountryIds = doDimensionRange.Country
	}
	if len(doDimensionRange.Province) > 0 {
		commonSearchFilter.ProvinceIds = doDimensionRange.Province
	}
	if len(doDimensionRange.City) > 0 {
		commonSearchFilter.CityIds = doDimensionRange.City
	}
	if len(doDimensionRange.District) > 0 {
		commonSearchFilter.DistrictIds = doDimensionRange.District
	}
	return
}

func mergeDimensions(dimensions []int, templateDimensionItems []common.ChildItem) (mergeDimensions []int) {
	templateDimensions := make([]int, 0)
	for _, childItem := range templateDimensionItems {
		templateDimensions = append(templateDimensions, childItem.Id)
	}
	// 不填=全选
	if len(dimensions) == 0 {
		return templateDimensions
	}
	if len(templateDimensions) == 0 {
		return dimensions
	}
	// 取交集
	mergeDimensions = make([]int, 0)
	for _, dimension := range dimensions {
		if utils.Container(templateDimensions, dimension) {
			mergeDimensions = append(mergeDimensions, dimension)
		}
	}
	for _, dimension := range templateDimensions {
		if utils.Container(dimensions, dimension) {
			mergeDimensions = append(mergeDimensions, dimension)
		}
	}
	mergeDimensions = utils.RemoveDuplicate(mergeDimensions)
	// 如果没有交集，返回一个不可能存在的枚举值，使得结果为空（代码调整较小）
	if len(mergeDimensions) == 0 {
		return []int{-2 ^ 31}
	}
	return
}

func GetOperateTypes(doTemplate template.DoListItem) (operateType []int) {
	operateType = make([]int, 0)
	if doTemplate.OperateType != nil {
		operateType = doTemplate.OperateType
	}
	return
}

func GetSubjectTypeDimension(doTemplate template.DoListItem, subjectTypes []int) (dimension []string, err error) {
	dimension = make([]string, 0)
	for _, subjectType := range subjectTypes {
		switch subjectType {
		case enum.TouristSubjectTypeRegion:
			if utils.Container(doTemplate.Dimension, enum.DimensionRegion) {
				dimension = append(dimension, enum.DimensionRegion)
			}
		case enum.TouristSubjectTypeCountry:
			if utils.Container(doTemplate.Dimension, enum.DimensionCountry) {
				dimension = append(dimension, enum.DimensionCountry)
			}
		case enum.TouristSubjectTypeProvince:
			if utils.Container(doTemplate.Dimension, enum.DimensionProvince) {
				dimension = append(dimension, enum.DimensionProvince)
			}
		case enum.TouristSubjectTypeCity:
			if utils.Container(doTemplate.Dimension, enum.DimensionCity) {
				dimension = append(dimension, enum.DimensionCity)
			}
		case enum.TouristSubjectTypeDistrict:
			if utils.Container(doTemplate.Dimension, enum.DimensionDistrict) {
				dimension = append(dimension, enum.DimensionDistrict)
			}
		case enum.TouristSubjectTypeAge:
			if utils.Container(doTemplate.MultipleMode, enum.DimensionAgeGroup) {
				dimension = append(dimension, enum.DimensionAgeGroup)
			}
		case enum.TouristSubjectTypeGender:
			if utils.Container(doTemplate.MultipleMode, enum.DimensionGender) {
				dimension = append(dimension, enum.DimensionGender)
			}
		case enum.TouristSubjectTypeAreaChart:
			//仅支持省市
			if len(doTemplate.Dimension) > 0 {
				allowDimension := []string{
					enum.DimensionProvince,
					enum.DimensionCity,
				}
				intersect := utils.Intersection(allowDimension, doTemplate.Dimension)
				if len(intersect) != 0 {
					//如果包含，就直接取交集处理
					dimension = intersect
				}
			}
		case enum.TouristSubjectTypeGenderChart:
			if utils.Container(doTemplate.MultipleMode, enum.DimensionGender) {
				dimension = append(dimension, enum.DimensionGender)
			}
		case enum.TouristSubjectTypeAgeChart:
			if utils.Container(doTemplate.MultipleMode, enum.DimensionAgeGroup) {
				dimension = append(dimension, enum.DimensionAgeGroup)
			}
		case enum.TouristSubjectTypeRegionAll:
			for _, s := range enum.TouristAreaDimension {
				if !utils.Container(doTemplate.Dimension, s) {
					continue
				}
				dimension = append(dimension, s)
			}
		default:
			err = szerrors.NewInvalidParamErrorWithText("查询统计对象不存在")
		}
	}
	if len(dimension) == 0 {
		err = szerrors.NewInvalidParamErrorWithText("查询统计维度不存在")
	}
	return
}

func GetSubjectTypeDimensionScheme(doTemplate template.DoListItem, subjectType int) map[string]int {
	dimensionScheme := make(map[string]int)
	switch subjectType {
	case enum.TouristSubjectTypeAge:
		if utils.Container(doTemplate.MultipleMode, enum.DimensionAgeGroup) {
			dimensionScheme = GetDimensionScheme(doTemplate)
		}
	case enum.TouristSubjectTypeAgeChart:
		if utils.Container(doTemplate.MultipleMode, enum.DimensionAgeGroup) {
			dimensionScheme = GetDimensionScheme(doTemplate)
		}
	}
	return dimensionScheme
}

func GetAgeGroupDimensionScheme(doTemplate template.DoListItem, sid int, groupId *int) (*[]int, error) {
	if groupId == nil || doTemplate.DimensionScheme.AgeGroup == nil || doTemplate.DimensionScheme.AgeGroup.Id <= 0 {
		return nil, nil
	}
	//年龄段转换
	ageGroupItems, err := dimensionscheme.GroupRelationList(sid, doTemplate.DimensionScheme.AgeGroup.Id, []int{})
	if err != nil {
		return nil, err
	}
	var ageList []int
	var configAge []int
	for _, item := range ageGroupItems {
		configAge = append(configAge, item.DimensionValue)
		if item.GroupId != *groupId {
			continue
		}
		ageList = append(ageList, item.DimensionValue)
	}
	//其他年龄段 0-199岁
	if *groupId == 0 {
		for i := 0; i < 200; i++ {
			if !utils.Container(configAge, i) {
				ageList = append(ageList, i)
			}
		}
	}
	if len(ageList) == 0 {
		return nil, nil
	}

	return &ageList, nil
}
