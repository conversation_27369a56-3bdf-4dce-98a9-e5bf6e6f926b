package tourist

import (
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/sdk/api/baseservice/bizaddress"
	"report-service/pkg/sdk/api/baseservice/bizregionarea"
	"report-service/pkg/utils"
)

type SearchNameMaps struct {
	GroupIdKeyNameMap     map[int]string
	AreaCodeKeyNameMap    map[string]string
	CountryCodeKeyNameMap map[string]string
}

func GetMultiSearchName(items []DoDimensionIdNameField) (searchNameMaps SearchNameMaps, err error) {
	searchNameMaps.GroupIdKeyNameMap, err = getGroupIdKeyNameMap(items)
	if err != nil {
		return
	}
	searchNameMaps.AreaCodeKeyNameMap, err = getAreaCodeKeyNameMap(items)
	if err != nil {
		return
	}
	searchNameMaps.CountryCodeKeyNameMap, err = getCountryCodeKeyNameMap(items)
	if err != nil {
		return
	}
	return
}

func getGroupIdKeyNameMap(items []DoDimensionIdNameField) (groupIdKeyNameMap map[int]string, err error) {
	var groupIds []int
	for _, poItem := range items {
		if poItem.AgeGroupId != nil && *poItem.AgeGroupId > 0 {
			groupIds = append(groupIds, *poItem.AgeGroupId)
		}
	}
	if len(groupIds) == 0 {
		return
	}
	groupIdKeyNameMap, err = dimensionscheme.GroupNameMapByIds(groupIds)
	if err != nil {
		return
	}
	return
}

func getAreaCodeKeyNameMap(items []DoDimensionIdNameField) (areaCodeKeyNameMap map[string]string, err error) {
	var areaCodes []string
	for _, poItem := range items {
		if poItem.Province != nil && *poItem.Province != "" {
			areaCodes = append(areaCodes, *poItem.Province)
		}
		if poItem.City != nil && *poItem.City != "" {
			areaCodes = append(areaCodes, *poItem.City)
		}
		if poItem.District != nil && *poItem.District != "" {
			areaCodes = append(areaCodes, *poItem.District)
		}
	}
	if len(areaCodes) == 0 {
		return
	}
	areaCodeKeyNameMap, err = bizregionarea.QueryAreaNameMapByAreaCode(areaCodes)
	if err != nil {
		return
	}
	return
}

func getCountryCodeKeyNameMap(items []DoDimensionIdNameField) (countryCodeKeyNameMap map[string]string, err error) {
	var countryCodes []string
	for _, poItem := range items {
		if poItem.Country != nil && *poItem.Country != "" {
			countryCodes = append(countryCodes, *poItem.Country)
		}
	}
	if len(countryCodes) == 0 {
		return
	}
	countryCodeKeyNameMap, err = bizaddress.QueryCountryNameMapByCountryCode(countryCodes)
	if err != nil {
		return
	}
	return
}

func (item *DoDimensionIdNameField) FillName(
	doTemplate template.DoListItem,
	maps SearchNameMaps,
	subjectType int,
) {
	// 如果选择了维度分组，则相关维度原来的字段为空，只输出分组字段
	if doTemplate.DimensionScheme.AgeGroup != nil && doTemplate.DimensionScheme.AgeGroup.Id > 0 &&
		utils.Container([]int{enum.TouristSubjectTypeAge, enum.TouristSubjectTypeAgeChart}, subjectType) {
		item.AgeGroupName = getSchemeGroupName(item.AgeGroupId, maps.GroupIdKeyNameMap, "未知", "其他年龄段")
	} else {
		item.AgeGroupId = nil
	}
	//地域名称解析
	if utils.Container(doTemplate.Dimension, enum.DimensionRegion) {
		regionId := cast.ToInt(*item.Region)
		item.RegionName = getDimensionNameById(&regionId, enum.RegionKeyNameMap, "未知")
	}
	//国家
	if utils.Container(doTemplate.Dimension, enum.DimensionCountry) {
		item.CountryName = getDimensionNameByString(item.Country, maps.CountryCodeKeyNameMap, "未知")
	}
	//省
	if utils.Container(doTemplate.Dimension, enum.DimensionProvince) {
		item.ProvinceName = getDimensionNameByString(item.Province, maps.AreaCodeKeyNameMap, "未知")
	}
	//市
	if utils.Container(doTemplate.Dimension, enum.DimensionCity) {
		item.CityName = getDimensionNameByString(item.City, maps.AreaCodeKeyNameMap, "未知")
	}
	//区县
	if utils.Container(doTemplate.Dimension, enum.DimensionDistrict) {
		item.DistrictName = getDimensionNameByString(item.District, maps.AreaCodeKeyNameMap, "未知")
	}
	//性别
	if utils.Container(doTemplate.MultipleMode, enum.TemplateMultipleModeGender) && item.Gender != nil {
		item.GenderName = getDimensionNameById(item.Gender, enum.GenderKeyNameMap, "未知")
	}
}

func getDimensionNameById(id *int, nameMap map[int]string, params ...interface{}) *string {
	defaultName := ""
	if len(params) > 0 {
		defaultName = cast.ToString(params[0])
	}
	name := ""
	if id == nil {
		return &defaultName
	}
	name, ok := nameMap[*id]
	if !ok {
		name = defaultName
	}
	return &name
}

func getDimensionNameByString(key *string, nameMap map[string]string, params ...interface{}) *string {
	defaultName := ""
	if len(params) > 0 {
		defaultName = cast.ToString(params[0])
	}
	name := ""
	if key == nil {
		return &defaultName
	}
	name, ok := nameMap[*key]
	if !ok {
		name = defaultName
	}
	return &name
}

func getSchemeGroupName(groupId *int, groupMap map[int]string, params ...interface{}) *string {
	name := "未知"
	if len(params) > 0 {
		name = cast.ToString(params[0])
	}
	if groupId == nil {
		return &name
	}
	name, ok := groupMap[*groupId]
	if !ok {
		name = "未分组"
		if len(params) > 1 {
			name = cast.ToString(params[1])
		}
	}
	return &name
}
