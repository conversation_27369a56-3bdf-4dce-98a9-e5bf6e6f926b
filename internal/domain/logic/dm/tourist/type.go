package tourist

type DoDimension struct {
	DoDimensionIdNameField
}

type DoDimensionIdNameField struct {
	Region       *string `json:"region,omitempty"` //地域
	RegionName   *string `json:"region_name,omitempty"`
	Country      *string `json:"country,omitempty"` //国家
	CountryName  *string `json:"country_name,omitempty"`
	Province     *string `json:"province,omitempty"` //省
	ProvinceName *string `json:"province_name,omitempty"`
	City         *string `json:"city,omitempty"` //市
	CityName     *string `json:"city_name,omitempty"`
	District     *string `json:"district,omitempty"` //区
	DistrictName *string `json:"district_name,omitempty"`
	Age          *int    `json:"age,omitempty"`
	AgeName      *string `json:"age_name,omitempty"`
	Gender       *int    `json:"gender,omitempty"`
	GenderName   *string `json:"genderName,omitempty"`
	// 分组维度
	AgeGroupId   *int    `json:"age_group_id,omitempty"`   //年龄段分组
	AgeGroupName *string `json:"age_group_name,omitempty"` //年龄段分组名称
}

type DoIndicator struct {
	Count int `json:"count"` //游客人数
}
