package tourist

import (
	"errors"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func CheckReportTypeAndTimeRange(reportType int, startTime, endTime carbon.Carbon) (err error) {
	if startTime.IsZero() || endTime.IsZero() {
		return szerrors.NewInvalidParamErrorWithText("时间范围不能为空")
	}
	if startTime.Gt(endTime) {
		return szerrors.NewInvalidParamErrorWithText("开始时间不能大于结束时间")
	}
	switch reportType {
	case enum.ReportTypeDay:
		var yearDays int64 = 366
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * yearDays) { // 按秒级别校验
			return szerrors.NewInvalidParamErrorWithText("日报表时间范围不能超过一年")
		}
	case enum.ReportTypeHour:
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * 14) { // 按秒级别校验
			return szerrors.NewInvalidParamErrorWithText("小时报表时间范围不能超过两周")
		}
	case enum.ReportTypeDetail:
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * 31) {
			return szerrors.NewInvalidParamErrorWithText("明细报表时间范围不能超过一个月")
		}
	default:
		return szerrors.NewInvalidParamErrorWithText("不支持的报表类型")
	}
	return
}

func CheckSubjectType(
	doTemplate template.DoListItem,
	subjectType int,
) (err error) {
	switch subjectType {
	//图表需要仅支持单操作
	case enum.TouristSubjectTypeGenderChart, enum.TouristSubjectTypeAgeChart:
		if err = checkOnlyOneOperateType(doTemplate); err != nil {
			return
		}
	//客源地图表，仅支持省市，以及单操作
	case enum.TouristSubjectTypeAreaChart:
		if err = checkOnlyOneOperateType(doTemplate); err != nil {
			return
		}
		if err = checkOnlyProvinceCityDimension(doTemplate); err != nil {
			return
		}
	}
	return
}

func checkOnlyOneOperateType(doTemplate template.DoListItem) (err error) {
	//图表仅支持单操作类型
	if len(doTemplate.OperateType) != 1 {
		err = errors.New("图表仅支持单操作")
		return
	}
	return
}

func checkOnlyProvinceCityDimension(doTemplate template.DoListItem) (err error) {
	//仅支持省市
	if len(doTemplate.Dimension) > 0 {
		allowDimension := []string{
			enum.DimensionProvince,
			enum.DimensionCity,
		}
		intersect := utils.Intersection(allowDimension, doTemplate.Dimension)
		if len(intersect) == 0 {
			err = errors.New("图表仅支持省、市维度")
		}
	}
	return
}
