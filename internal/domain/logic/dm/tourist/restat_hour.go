package tourist

import (
	"encoding/json"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/segmentio/kafka-go"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/dwm"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
)

func BatchHandle(messages ...kafka.Message) error {
	for _, message := range messages {
		var messageItem dwm.EventTouristComplete
		var err error
		err = json.Unmarshal(message.Value, &messageItem)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("handle dwm tourist complete event is error. err:%s, msg:%+v", err.Error(), messages))
			continue
		}

		startTime := carbon.Parse(messageItem.StartAt)
		endTime := carbon.Parse(messageItem.EndAt)
		err = RestatHour(startTime, endTime, cast.ToInt(messageItem.MerchantId))
		if err != nil {
			errMsg := fmt.Sprintf("handle dwm tourist complete event restat is error. err:%s, msg:%+v", err.Error(), messages)
			globalNotice.Error(errMsg)
			global.LOG.Error(errMsg)
		}
	}
	return nil
}

func RestatHourTask(startTime, endTime carbon.Carbon, merchantId int) error {
	for date := startTime; date.Lte(endTime); date = date.AddHours(1) {
		_ = RestatHour(date.StartOfHour(), date.EndOfHour(), merchantId)
	}
	return nil
}

func RestatHour(startTime, endTime carbon.Carbon, merchantId int) error {
	statStartTime := carbon.Now()
	var err error
	defer func() {
		if r := recover(); r != nil {
			globalNotice.Error(fmt.Sprint("statistic tourist hour error, panic", r))
			return
		}
		if err != nil {
			globalNotice.Error(fmt.Sprint("statistic tourist hour error, ", err.Error()))
			return
		}
		global.LOG.Info(
			"statistic tourist hour success",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	err = repository.ReportDmTouristHourRepository.DeleteWhereTimeRange(startTime, endTime, merchantId)
	if err != nil {
		return err
	}

	err = repository.ReportDmTouristHourRepository.InsertIntoSelectDmCommon(startTime, endTime, merchantId)
	if err != nil {
		return err
	}

	if merchantId == 0 {
		err = config.UpdateLatestTime(enum.ConfigKeyDmTouristHourLastStatisticTime, endTime)
		if err != nil {
			globalNotice.Error(fmt.Sprint("statistic tourist hour error, set config error", err.Error()))
			return err
		}
	}

	return nil
}
