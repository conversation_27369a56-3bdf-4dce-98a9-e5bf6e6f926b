package tourist

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
)

func RestatCommonDayYesterday() {
	_ = RestatDay(
		carbon.Now().Yesterday().StartOfDay(),
		carbon.Now().Yesterday().EndOfDay(),
		0,
	)
}

func RestatTask(startTime, endTime carbon.Carbon, merchantId int) error {
	for date := startTime; date.Lte(endTime); date = date.AddDays(1) {
		_ = RestatDay(date.StartOfDay(), date.EndOfDay(), merchantId)
	}
	return nil
}

func RestatDay(startTime, endTime carbon.Carbon, merchantId int) error {
	statStartTime := carbon.Now()
	var err error
	defer func() {
		if r := recover(); r != nil {
			globalNotice.Error(fmt.Sprint("statistic tourist day error, panic", r))
			return
		}
		if err != nil {
			globalNotice.Error(fmt.Sprint("statistic tourist day error, ", err.Error()))
			return
		}
		global.LOG.Info(
			"statistic tourist day success",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	err = repository.ReportDmTouristDayRepository.DeleteWhereTimeRange(startTime, endTime, merchantId)
	if err != nil {
		return err
	}

	err = repository.ReportDmTouristDayRepository.InsertIntoSelectDmCommon(startTime, endTime, merchantId)
	if err != nil {
		return err
	}

	if merchantId == 0 {
		err = config.UpdateLatestTime(enum.ConfigKeyDmTouristDayLastStatisticTime, endTime)
		if err != nil {
			globalNotice.Error(fmt.Sprint("statistic tourist day error, set config error", err.Error()))
			return err
		}
	}

	return nil
}
