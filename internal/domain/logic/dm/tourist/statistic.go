package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	repodmtourist "report-service/internal/domain/repository/dm/tourist"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func Statistics(
	doTemplate template.DoListItem,
	merchantId int,
	reportType int,
	startTime, endTime carbon.Carbon,
	doDimensionRange common.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
) (statistics DoIndicator, err error) {
	if err = CheckReportTypeAndTimeRange(reportType, startTime, endTime); err != nil {
		return
	}

	//获取数据岗位限制产品和查询产品取交集
	doDimensionRange.Spu = dataLimit.GetIdList(doDimensionRange.Spu)

	// 解析、合并模版维度范围和用户维度范围
	dimensionRange, _ := GetDimensionRange(doDimensionRange, doTemplate.SpecialDimension)
	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	//通用查询过滤
	commonSearchModelFilter := GetCommonSearchModelFilter(doDimensionRange)
	//操作类型
	commonSearchModelFilter.OperateType = GetOperateTypes(doTemplate)

	//排查部分产品
	if len(dataLimit.NotIdList) > 0 {
		commonSearchModelFilter.NotSpuIds = dataLimit.NotIdList
	}

	var poStatistic repodmtourist.CommonModelIndicator
	switch reportType {
	case enum.ReportTypeDay:
		poStatistic, err = repository.ReportDmTouristDayRepository.Statistics(merchantId, startTime, endTime,
			dimensionRange, commonSearchModelSpecialProductRule, commonSearchModelFilter)
	case enum.ReportTypeHour:
		poStatistic, err = repository.ReportDmTouristHourRepository.Statistics(merchantId, startTime, endTime,
			dimensionRange, commonSearchModelSpecialProductRule, commonSearchModelFilter)
	default:
		err = szerrors.NewInvalidParamErrorWithText("不支持的报表类型")
		return
	}
	if err != nil {
		return
	}

	err = utils.JsonConvertor(poStatistic, &statistics)
	if err != nil {
		err = szerrors.NewLogicErrorWithText("统计结果解析失败")
		return
	}
	return
}
