package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/internal/domain/repository/dm/tourist"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type DoPaginationItem struct {
	DoDimension
	DoIndicator
}

func Paginate(
	doTemplate template.DoListItem,
	merchantId int,
	reportType int,
	subjectType int,
	startTime, endTime carbon.Carbon,
	doDimensionRange common.DoDimensionRange,
	dataLimit datajobslimit.LimitSpuConfig,
	page, pageSize int,
) (list []DoPaginationItem, total int, err error) {
	if err = CheckReportTypeAndTimeRange(reportType, startTime, endTime); err != nil {
		return
	}
	if err = CheckSubjectType(doTemplate, subjectType); err != nil {
		return
	}

	dimension, err := GetSubjectTypeDimension(doTemplate, []int{subjectType})
	if err != nil {
		return
	}
	doTemplate.Dimension = dimension

	// 解析维度方案配置
	dimensionScheme := GetSubjectTypeDimensionScheme(doTemplate, subjectType)

	//获取数据岗位限制产品和查询产品取交集
	doDimensionRange.Spu = dataLimit.GetIdList(doDimensionRange.Spu)

	// 解析、合并模版维度范围和用户维度范围
	dimensionRange, _ := GetDimensionRange(doDimensionRange, doTemplate.SpecialDimension)

	//特殊产品规则
	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	//通用查询过滤
	commonSearchModelFilter := GetCommonSearchModelFilter(doDimensionRange)
	//操作类型
	commonSearchModelFilter.OperateType = GetOperateTypes(doTemplate)
	//排查部分产品
	if len(dataLimit.NotIdList) > 0 {
		commonSearchModelFilter.NotSpuIds = dataLimit.NotIdList
	}

	// 查询仓储层
	var poList []tourist.PaginationItem
	switch reportType {
	case enum.ReportTypeDay:
		poList, total, err = repository.ReportDmTouristDayRepository.Paginate(merchantId, startTime, endTime,
			doTemplate.Dimension, dimensionScheme, dimensionRange, commonSearchModelSpecialProductRule,
			commonSearchModelFilter, page, pageSize)
	case enum.ReportTypeHour:
		poList, total, err = repository.ReportDmTouristHourRepository.Paginate(merchantId, startTime, endTime,
			doTemplate.Dimension, dimensionScheme, dimensionRange, commonSearchModelSpecialProductRule,
			commonSearchModelFilter, page, pageSize)
	default:
		err = szerrors.NewInvalidParamErrorWithText("不支持的报表类型")
		return
	}
	if err != nil {
		return
	}
	var doDimensionIdNameFields []DoDimensionIdNameField
	if err = utils.JsonConvertor(poList, &doDimensionIdNameFields); err != nil {
		err = szerrors.NewLogicErrorWithText("维度名称解析失败")
		return
	}
	maps, err := GetMultiSearchName(doDimensionIdNameFields)
	if err != nil {
		return
	}
	list = make([]DoPaginationItem, 0)
	for _, po := range poList {
		var doPaginationItem DoPaginationItem
		_ = utils.JsonConvertor(po, &doPaginationItem)
		_ = utils.JsonConvertor(po.CommonModelDimension, &doPaginationItem.DoDimension.DoDimensionIdNameField)
		_ = utils.JsonConvertor(po.CommonSearchModelRelationDimension, &doPaginationItem.DoDimension.DoDimensionIdNameField)
		doPaginationItem.DoDimension.DoDimensionIdNameField.FillName(doTemplate, maps, subjectType)
		list = append(list, doPaginationItem)
	}
	return
}
