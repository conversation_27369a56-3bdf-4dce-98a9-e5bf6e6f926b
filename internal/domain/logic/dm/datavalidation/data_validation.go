package datavalidation

import (
	"fmt"
	"reflect"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/datavalidation/common"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/utils"
	"strings"

	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

const (
	reportTypeOrder  = "order"
	reportTypeCheck  = "check"
	NewReportUserId  = 112
	SelfSale         = 20
	PackChildChannel = 23
)

var (
	OldReportSanKeIds = []int{10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 22, 66, 112}
)

func ReportValidationDataYesterday() {
	config, err := common.GetValidationDataConfig()
	if err != nil {
		global.LOG.Error("数据验证获取配置失败，", zap.Error(err))
		return
	}

	date := carbon.Yesterday()
	for _, v := range config {
		ValidationData(date, v.Fid, 0, v.ReportType)
	}
}

func ValidationData(date carbon.Carbon, optional ...interface{}) {
	defer func() {
		if r := recover(); r != nil {
			globalNotice.Error(fmt.Sprint("validation data error, panic", r))
			return
		}
	}()
	startTime, endTime := date.StartOfDay(), date.EndOfDay()
	var fid, tid int
	var reportType []string
	if len(optional) > 0 {
		fid = cast.ToInt(optional[0])
	}
	if len(optional) > 1 {
		tid = cast.ToInt(optional[1])
	}
	if len(optional) > 2 {
		reportType = cast.ToStringSlice(optional[2])
	} else {
		//默认值
		reportType = []string{
			"order",
			"check",
		}
	}
	if len(reportType) == 0 || fid == 0 {
		global.LOG.Error("params is error, reportType & fid ", zap.String("reportType", strings.Join(reportType, ",")), zap.Int("fid", fid), zap.Any("optional", optional))
		return
	}
	req := common.PaginationReq{
		StartTime:   startTime,
		EndTime:     endTime,
		Fid:         fid,
		Tid:         tid,
		Page:        1,
		PageSize:    100,
		ResellerIds: OldReportSanKeIds,
		Title:       "",
	}
	for _, r := range reportType {
		var query common.OldReportPagination
		switch r {
		case reportTypeOrder:
			req.Title = "预订报表数据比对"
			query = &common.OrderTwoPagination{PaginationReq: req}
		case reportTypeCheck:
			req.Title = "验证报表数据比对"
			query = &common.CheckTwoPagination{PaginationReq: req}
		default:
			continue
		}
		HandleValidation(query)
	}
}

func HandleValidation(query common.OldReportPagination) {
	batchId := carbon.Now().Format("YmdHis")
	noticeMsg := fmt.Sprintf("对比表：%s,r_report_dm_common_day\n日期：%s\n批次：%s", query.GetReq().Title, query.GetReq().StartTime, batchId)

	processCount, errorCount := 0, 0
	processTime := carbon.Now()

	page := 1
	pageSize := 100
	for {
		query.SetPage(page, pageSize)
		pagination, err := query.Pagination()
		if err != nil {
			globalNotice.Error("数据验证查询失败\n" + noticeMsg + "\n" + err.Error())
			global.LOG.Error("数据验证查询失败", zap.String("title", query.GetReq().Title), zap.Error(err), zap.String("batchId", batchId))
			return
		}
		if len(pagination) == 0 {
			break
		}
		for _, tmp := range pagination {
			// 跳过老散客
			if utils.Container(OldReportSanKeIds, tmp.GetDimensionRange().ResellerId) {
				//子票末级自采 会被处理为112
				if tmp.GetDimensionRange().ResellerId == NewReportUserId && tmp.GetDimensionRange().Channel != PackChildChannel {
					continue
				}
				if tmp.GetDimensionRange().ResellerId != NewReportUserId {
					continue
				}
			}
			dimensionRange := tmp.GetDimensionRange()
			//自供自销转换
			if tmp.GetDimensionRange().ResellerId == SelfSale {
				dimensionRange.ResellerId = dimensionRange.Fid
			}
			//子票末级自采 会被处理为112, 转换成末级自己
			if tmp.GetDimensionRange().ResellerId == NewReportUserId && tmp.GetDimensionRange().Channel == PackChildChannel {
				dimensionRange.ResellerId = dimensionRange.Fid
			}
			processCount++
			var newDifferences *common.DifferencesFields
			newDifferences, err = query.GetStatWithOldReport(*dimensionRange, nil)
			if err != nil {
				errorCount++
				global.LOG.Error("数据验证异常", zap.String("title", query.GetReq().Title), zap.Error(err), zap.String("batchId", batchId))
				continue
			}
			//旧报表比对字段
			oldDifferences := tmp.GetDifferencesId()
			//数据比对
			equal, differences := StructFieldsEqual(*oldDifferences, *newDifferences)
			if !equal && len(differences) > 0 {
				errorCount++
				err = fmt.Errorf(
					"%s， 指标比对异常，日期：%d，用户ID：%d，分销商ID：%d，门票ID：%d，支付方式：%d，销售渠道：%d，差异详情：%s",
					query.GetReq().Title,
					cast.ToInt(query.GetReq().StartTime.Format("Ymd")),
					query.GetReq().Fid,
					dimensionRange.ResellerId,
					dimensionRange.Tid,
					dimensionRange.PayWay,
					dimensionRange.Channel,
					strings.Join(differences, "，"),
				)
				global.LOG.Error("数据验证异常", zap.String("title", query.GetReq().Title), zap.Error(err), zap.String("batchId", batchId))
			}
		}
		page++
	}
	// 聚合散客进行验证
	processCount += 1
	indicator, err := query.Indicator()
	if err != nil {
		globalNotice.Error("散客数据验证查询失败\n" + noticeMsg + "\n" + err.Error())
		global.LOG.Error("散客数据验证查询失败", zap.String("title", query.GetReq().Title), zap.Error(err), zap.String("batchId", batchId))
		return
	}
	dimensionRange := map[string][]int{
		enum.DimensionDistributor: {NewReportUserId},
	}
	var newDifferences *common.DifferencesFields
	newDifferences, err = query.GetStatWithOldReport(common.DimensionRange{
		Date:       cast.ToInt(query.GetReq().StartTime.Format("Ymd")),
		Fid:        query.GetReq().Fid,
		Tid:        query.GetReq().Tid,
		ResellerId: NewReportUserId,
	}, &dimensionRange)
	if err != nil {
		errorCount++
		global.LOG.Error("散客数据验证异常", zap.String("title", query.GetReq().Title), zap.Error(err), zap.String("batchId", batchId))
		return
	}
	oldDifferences := indicator.GetDifferencesId()

	//数据比对
	equal, differences := StructFieldsEqual(*oldDifferences, *newDifferences)
	if !equal && len(differences) > 0 {
		errorCount++
		err = fmt.Errorf(
			"%s，指标比对异常，日期：%d，用户ID：%d，分销商ID：%d，门票ID：%d，支付方式：%d，销售渠道：%d，差异详情：%s",
			query.GetReq().Title,
			cast.ToInt(query.GetDate().Format("Ymd")),
			query.GetFid(),
			NewReportUserId,
			0, 00, 00,
			strings.Join(differences, "，"),
		)
		global.LOG.Error("散客数据验证异常", zap.String("title", query.GetReq().Title), zap.Error(err), zap.String("batchId", batchId))
	}
	duration := fmt.Sprintf("%.2f 秒", processTime.DiffAbsInDuration().Seconds())
	globalNotice.Warning(fmt.Sprintf("数据验证完成，验证数据%d条，异常数据%d条，耗时%s", processCount, errorCount, duration), noticeMsg)
	global.LOG.Info(
		"数据验证完成，"+noticeMsg,
		zap.Int("总条数", processCount),
		zap.Int("异常数", errorCount),
		zap.String("耗时", duration),
	)
}

func StructFieldsEqual(oldData, newData common.DifferencesFields) (bool, []string) {
	differences := make([]string, 0)
	valOld := reflect.ValueOf(oldData)
	valNew := reflect.ValueOf(newData)
	if valOld.Kind() != reflect.Struct || valNew.Kind() != reflect.Struct {
		return false, differences
	}
	typeA := valOld.Type()
	for i := 0; i < valOld.NumField(); i++ {
		fieldOld := valOld.Field(i)
		//指标比较int
		if fieldOld.Kind() != reflect.Int {
			return false, differences
		}
		aName := typeA.Field(i).Name
		message, exist := common.DifferencesFieldsMap[aName]
		if !exist {
			message = "旧：%d，新：%d"
		}
		fieldNew := valNew.FieldByName(typeA.Field(i).Name)

		if fieldOld.Interface() != fieldNew.Interface() {
			differences = append(differences, fmt.Sprintf(message, fieldOld.Interface(), fieldNew.Interface()))
			continue
		}
	}
	if len(differences) > 0 {
		return false, differences
	}
	return true, nil
}
