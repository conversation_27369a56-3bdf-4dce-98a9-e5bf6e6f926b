package datavalidation

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/utils"
	"strconv"
	"strings"

	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

func ValidationYesterday() {
	Validation(carbon.Yesterday(), 37045537) //茶卡盐湖ID
	Validation(carbon.Yesterday(), 37948500) //敬业集团ID
}

var (
	oldReportUserIds = []int{10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 66, 112}
	newReportUserId  = 112
)

func Validation(date carbon.Carbon, optional ...interface{}) {
	startTime, endTime := date.StartOfDay(), date.EndOfDay()

	batchId := carbon.Now().Format("YmdHis")
	noticeMsg := fmt.Sprintf("对比表：pft_report_order_two、r_report_dm_common_day\n日期：%s\n批次：%s", date.ToDateString(), batchId)

	processCount, errorCount := 0, 0
	processTime := carbon.Now()

	var fid, tid int
	if len(optional) > 0 {
		fid = cast.ToInt(optional[0])
	}
	if len(optional) > 1 {
		tid = cast.ToInt(optional[1])
	}

	page := 1
	pageSize := 100
	for {
		orderTwoList, err := oldReportOrderTwoPagination(startTime, endTime, fid, tid, page, pageSize)
		if err != nil {
			globalNotice.Error("数据验证查询失败\n" + noticeMsg + "\n" + err.Error())
			global.LOG.Error("数据验证查询失败", zap.Error(err), zap.String("batchId", batchId))
			return
		}
		if len(orderTwoList) == 0 {
			break
		}
		for _, orderTwo := range orderTwoList {
			// 跳过老散客
			if utils.Container(oldReportUserIds, orderTwo.ResellerId) {
				continue
			}
			processCount++
			err = statWithOldReportOrderTwo(orderTwo, nil)
			if err != nil {
				errorCount++
				global.LOG.Error("数据验证异常", zap.Error(err), zap.String("batchId", batchId))
			}
		}
		page++
	}

	// 聚合散客进行验证
	processCount += 1
	oldReportIndicator, err := oldReportOrderTwoStat(startTime, endTime, fid, tid, oldReportUserIds)
	if err != nil {
		globalNotice.Error("数据验证查询失败\n" + noticeMsg + "\n" + err.Error())
		global.LOG.Error("数据验证查询失败", zap.Error(err), zap.String("batchId", batchId))
		return
	}
	var orderTwoNew = OldReportOrderTwo{
		Date:               cast.ToInt(date.Format("Ymd")),
		Fid:                fid,
		ResellerId:         newReportUserId,
		Tid:                tid,
		OldReportIndicator: oldReportIndicator,
	}
	dimensionRange := map[string][]int{
		enum.DimensionDistributor: {newReportUserId, fid}, // 加上自供自销的情况
	}
	if tid > 0 {
		dimensionRange[enum.DimensionSku] = []int{tid}
	}
	err = statWithOldReportOrderTwo(orderTwoNew, &dimensionRange)
	if err != nil {
		errorCount++
		global.LOG.Error("散客数据验证异常", zap.Error(err), zap.String("batchId", batchId))
	}

	duration := fmt.Sprintf("%.2f 秒", processTime.DiffAbsInDuration().Seconds())
	globalNotice.Warning(fmt.Sprintf("数据验证完成，验证数据%d条，异常数据%d条，耗时%s", processCount, errorCount, duration), noticeMsg)
	global.LOG.Info(
		"数据验证完成，"+noticeMsg,
		zap.Int("总条数", processCount),
		zap.Int("异常数", errorCount),
		zap.String("耗时", duration),
	)
}

func statWithOldReportOrderTwo(orderTwo OldReportOrderTwo, dimensionRange *map[string][]int) (err error) {
	date := carbon.Parse(strconv.Itoa(orderTwo.Date))
	startTime, endTime := date.StartOfDay(), date.EndOfDay()
	if dimensionRange == nil {
		dimensionRange = &map[string][]int{
			enum.DimensionDistributor: {orderTwo.ResellerId},
			enum.DimensionSpu:         {orderTwo.Lid},
			enum.DimensionSku:         {orderTwo.Tid},
			enum.DimensionPayMode:     {orderTwo.PayWay},
			enum.DimensionSaleChannel: {orderTwo.Channel},
		}
	}
	var statistic dm.CommonStatistic
	statistic, err = repository.ReportDmCommonDateRepository.Statistics(dm.CommonSearch{
		MerchantId:     &orderTwo.Fid,
		StartTime:      startTime,
		EndTime:        endTime,
		DimensionRange: *dimensionRange,
	})
	if err != nil {
		return
	}
	return compareIndicators(orderTwo, statistic)
}

// 比较新旧报表的指标字段
// https://12301-cc.feishu.cn/wiki/Xt5bw2uefiRLwKkBiNmcTPMDnKd
func compareIndicators(oldReportOrderTwo OldReportOrderTwo, dmCommonDayStatistic dm.CommonStatistic) (err error) {
	differences := make([]string, 0)
	if oldReportOrderTwo.OrderTicket != dmCommonDayStatistic.PayCount {
		differences = append(differences, fmt.Sprintf("预订门票数：%d，新报表为%d", oldReportOrderTwo.OrderTicket, dmCommonDayStatistic.PayCount))
	}
	if oldReportOrderTwo.CancelTicket != dmCommonDayStatistic.CancelCount {
		differences = append(differences, fmt.Sprintf("取消门票数：%d，新报表为%d", oldReportOrderTwo.CancelTicket, dmCommonDayStatistic.CancelCount))
	}
	if oldReportOrderTwo.RevokeTicket != dmCommonDayStatistic.RevokeCount {
		differences = append(differences, fmt.Sprintf("撤销门票数：%d，新报表为%d", oldReportOrderTwo.RevokeTicket, dmCommonDayStatistic.RevokeCount))
	}
	if oldReportOrderTwo.CostMoney != (dmCommonDayStatistic.PayCostPrice - dmCommonDayStatistic.PayCostDiscountPrice) {
		differences = append(differences, fmt.Sprintf("预订花费金额：%d，新报表为%d", oldReportOrderTwo.CostMoney, dmCommonDayStatistic.PayCostPrice-dmCommonDayStatistic.PayCostDiscountPrice))
	}
	if oldReportOrderTwo.Fid != oldReportOrderTwo.ResellerId && // 自供自销不比对，零售价计算规则不同
		oldReportOrderTwo.ResellerId != newReportUserId && // 分销散客不比对，零售价计算规则不同
		oldReportOrderTwo.SaleMoney != (dmCommonDayStatistic.PaySalePrice-dmCommonDayStatistic.PaySaleDiscountPrice) {
		differences = append(differences, fmt.Sprintf("预定金额：%d，新报表为%d", oldReportOrderTwo.SaleMoney, dmCommonDayStatistic.PaySalePrice-dmCommonDayStatistic.PaySaleDiscountPrice))
	}
	if oldReportOrderTwo.CancelCostMoney != (dmCommonDayStatistic.CancelCostPrice - dmCommonDayStatistic.CancelCostDiscountPrice) {
		differences = append(differences, fmt.Sprintf("取消花费金额：%d，新报表为%d", oldReportOrderTwo.CancelCostMoney, dmCommonDayStatistic.CancelCostPrice-dmCommonDayStatistic.CancelCostDiscountPrice))
	}
	if oldReportOrderTwo.Fid != oldReportOrderTwo.ResellerId && // 自供自销不比对，零售价计算规则不同
		oldReportOrderTwo.ResellerId != newReportUserId && // 分销散客不比对，零售价计算规则不同
		oldReportOrderTwo.CancelSaleMoney != (dmCommonDayStatistic.CancelSalePrice-dmCommonDayStatistic.CancelSaleDiscountPrice) {
		differences = append(differences, fmt.Sprintf("取消金额：%d，新报表为%d", oldReportOrderTwo.CancelSaleMoney, dmCommonDayStatistic.CancelSalePrice-dmCommonDayStatistic.CancelSaleDiscountPrice))
	}
	if oldReportOrderTwo.RevokeCostMoney != (dmCommonDayStatistic.RevokeCostPrice - dmCommonDayStatistic.RevokeCostDiscountPrice) {
		differences = append(differences, fmt.Sprintf("撤销花费金额：%d，新报表为%d", oldReportOrderTwo.RevokeCostMoney, dmCommonDayStatistic.RevokeCostPrice-dmCommonDayStatistic.RevokeCostDiscountPrice))
	}
	if oldReportOrderTwo.Fid != oldReportOrderTwo.ResellerId && // 自供自销不比对，零售价计算规则不同
		oldReportOrderTwo.ResellerId != newReportUserId && // 分销散客不比对，零售价计算规则不同
		oldReportOrderTwo.RevokeSaleMoney != (dmCommonDayStatistic.RevokeSalePrice-dmCommonDayStatistic.RevokeSaleDiscountPrice) {
		differences = append(differences, fmt.Sprintf("撤销金额：%d，新报表为%d", oldReportOrderTwo.RevokeSaleMoney, dmCommonDayStatistic.RevokeSalePrice-dmCommonDayStatistic.RevokeSaleDiscountPrice))
	}
	newServiceMoney := dmCommonDayStatistic.CancelSaleFee - dmCommonDayStatistic.CancelCostFee + dmCommonDayStatistic.RevokeSaleFee - dmCommonDayStatistic.RevokeCostFee
	if newServiceMoney < 0 {
		newServiceMoney = 0
	}
	if oldReportOrderTwo.ServiceMoney != newServiceMoney {
		differences = append(differences, fmt.Sprintf("退款服务费：%d，新报表为%d", oldReportOrderTwo.ServiceMoney, newServiceMoney))
	}
	if oldReportOrderTwo.AfterSaleTicketNum != dmCommonDayStatistic.AfterSaleCount {
		differences = append(differences, fmt.Sprintf("售后数量：%d，新报表为%d", oldReportOrderTwo.AfterSaleTicketNum, dmCommonDayStatistic.AfterSaleCount))
	}
	if oldReportOrderTwo.AfterSaleRefundMoney != dmCommonDayStatistic.AfterSalePrice {
		differences = append(differences, fmt.Sprintf("售后退回金额：%d，新报表为%d", oldReportOrderTwo.AfterSaleRefundMoney, dmCommonDayStatistic.AfterSalePrice))
	}
	if oldReportOrderTwo.AfterSaleIncomeMoney != dmCommonDayStatistic.AfterCostPrice {
		differences = append(differences, fmt.Sprintf("售后收入金额：%d，新报表为%d", oldReportOrderTwo.AfterSaleIncomeMoney, dmCommonDayStatistic.AfterCostPrice))
	}
	if len(differences) > 0 {
		err = fmt.Errorf(
			"指标比对异常，日期：%d，用户ID：%d，分销商ID：%d，景区ID：%d，门票ID：%d，支付方式：%d，销售渠道：%d，差异详情：%s",
			oldReportOrderTwo.Date,
			oldReportOrderTwo.Fid,
			oldReportOrderTwo.ResellerId,
			oldReportOrderTwo.Lid,
			oldReportOrderTwo.Tid,
			oldReportOrderTwo.PayWay,
			oldReportOrderTwo.Channel,
			strings.Join(differences, "，"),
		)
	}
	return
}
