package common

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/utils"
	"strconv"

	"gitee.com/golang-module/carbon/v2"
)

type OrderTwoPagination struct {
	PaginationReq
}

type CheckTwoPagination struct {
	PaginationReq
}

func (o *OrderTwoPagination) Pagination() ([]OldReport, error) {
	pagination, err := oldReportOrderTwoPagination(o.StartTime, o.EndTime, o.Fid, o.Tid, o.Page, o.PageSize)
	if err != nil {
		return nil, err
	}
	var results []OrderTwo
	err = utils.JsonConvertor(pagination, &results)
	if err != nil {
		return nil, err
	}
	var list []OldReport
	for _, result := range results {
		list = append(list, result)
	}
	return list, err
}

func (o *OrderTwoPagination) Indicator() (OldReport, error) {
	indicator, err := oldReportOrderTwoStat(o.StartTime, o.EndTime, o.Fid, o.Tid, o.ResellerIds)
	if err != nil {
		return nil, err
	}
	var result OrderTwo
	err = utils.JsonConvertor(indicator, &result)
	if err != nil {
		return nil, err
	}
	var data OldReport = result
	return data, err
}

func (o *OrderTwoPagination) SetPage(page int, size int) {
	o.Page = page
	o.PageSize = size
	return
}

func (o *OrderTwoPagination) GetFid() int {
	return o.Fid
}

func (o *OrderTwoPagination) GetTid() int {
	return o.Tid
}

func (o *OrderTwoPagination) GetDate() carbon.Carbon {
	return o.StartTime
}

func (o *OrderTwoPagination) GetReq() PaginationReq {
	return o.PaginationReq
}

func (o *OrderTwoPagination) GetStatWithOldReport(oldReport DimensionRange, dimensionRange *map[string][]int) (*DifferencesFields, error) {
	statistic, err := statWithOldReport(oldReport, dimensionRange)
	if err != nil {
		return nil, err
	}
	result := &DifferencesFields{
		PayCount:        statistic.PayCount,
		CancelCount:     statistic.CancelCount,
		RevokeCount:     statistic.RevokeCount,
		PayCostPrice:    statistic.PayCostPrice - statistic.PayCostDiscountPrice,
		PaySalePrice:    0,
		CancelCostPrice: statistic.CancelCostPrice - statistic.CancelCostDiscountPrice,
		CancelSalePrice: 0,
		RevokeCostPrice: statistic.RevokeCostPrice - statistic.RevokeCostDiscountPrice,
		RevokeSalePrice: 0,
		ServicePrice:    0,
		AfterSaleCount:  statistic.AfterSaleCount,
		AfterCostPrice:  statistic.AfterCostPrice,
		AfterSalePrice:  statistic.AfterSalePrice,
	}
	//非自销的才比对
	if oldReport.Fid != oldReport.ResellerId {
		result.PaySalePrice = statistic.PaySalePrice - statistic.PaySaleDiscountPrice
		result.CancelSalePrice = statistic.CancelSalePrice - statistic.CancelSaleDiscountPrice
		result.RevokeSalePrice = statistic.RevokeSalePrice - statistic.RevokeSaleDiscountPrice
	}
	//手续费计算
	newServiceMoney := statistic.CancelSaleFee - statistic.CancelCostFee + statistic.RevokeSaleFee - statistic.RevokeCostFee
	if newServiceMoney < 0 {
		newServiceMoney = 0
	}
	result.ServicePrice = newServiceMoney
	return result, nil
}

func (o *CheckTwoPagination) Pagination() ([]OldReport, error) {
	pagination, err := oldReportCheckTwoPagination(o.StartTime, o.EndTime, o.Fid, o.Tid, o.Page, o.PageSize)
	if err != nil {
		return nil, err
	}
	var results []CheckTwo
	err = utils.JsonConvertor(pagination, &results)
	if err != nil {
		return nil, err
	}
	var list []OldReport
	for _, result := range results {
		list = append(list, result)
	}
	return list, err
}

func (o *CheckTwoPagination) Indicator() (OldReport, error) {
	indicator, err := oldReportCheckTwoStat(o.StartTime, o.EndTime, o.Fid, o.Tid, o.ResellerIds)
	if err != nil {
		return nil, err
	}
	var result CheckTwo
	err = utils.JsonConvertor(indicator, &result)
	if err != nil {
		return nil, err
	}
	var data OldReport = result
	return data, err
}

func (o *CheckTwoPagination) SetPage(page int, size int) {
	o.Page = page
	o.PageSize = size
	return
}

func (o *CheckTwoPagination) GetFid() int {
	return o.Fid
}

func (o *CheckTwoPagination) GetDate() carbon.Carbon {
	return o.StartTime
}

func (o *CheckTwoPagination) GetTid() int {
	return o.Tid
}

func (o *CheckTwoPagination) GetReq() PaginationReq {
	return o.PaginationReq
}

func (o *CheckTwoPagination) GetStatWithOldReport(oldReport DimensionRange, dimensionRange *map[string][]int) (*DifferencesFields, error) {
	statistic, err := statWithOldReport(oldReport, dimensionRange)
	if err != nil {
		return nil, err
	}
	result := &DifferencesFields{
		RevokeCount:     statistic.RevokeCount,
		VerifyCount:     statistic.VerifyCount,
		RevokeCostPrice: statistic.RevokeCostPrice - statistic.RevokeCostDiscountPrice,
		RevokeSalePrice: 0,
		VerifyCostPrice: statistic.VerifyCostPrice - statistic.VerifyCostDiscountPrice,
		VerifySalePrice: 0,
		ServicePrice:    0,
		AfterSaleCount:  statistic.AfterSaleCount,
		AfterCostPrice:  statistic.AfterCostPrice,
		AfterSalePrice:  statistic.AfterSalePrice,
	}
	//非自销的才比对
	if oldReport.Fid != oldReport.ResellerId {
		result.RevokeSalePrice = statistic.RevokeSalePrice - statistic.RevokeSaleDiscountPrice
		result.VerifySalePrice = statistic.VerifySalePrice - statistic.VerifySaleDiscountPrice
	}
	//手续费计算
	newServiceMoney := statistic.CancelSaleFee - statistic.CancelCostFee + statistic.RevokeSaleFee - statistic.RevokeCostFee
	if newServiceMoney < 0 {
		newServiceMoney = 0
	}
	result.ServicePrice = newServiceMoney

	return result, nil
}

func statWithOldReport(oldReport DimensionRange, dimensionRange *map[string][]int) (*dm.CommonStatistic, error) {
	date := carbon.Parse(strconv.Itoa(oldReport.Date))
	startTime, endTime := date.StartOfDay(), date.EndOfDay()
	if dimensionRange == nil {
		dimensionRange = &map[string][]int{
			enum.DimensionDistributor: {oldReport.ResellerId},
			enum.DimensionSku:         {oldReport.Tid},
			enum.DimensionPayMode:     {oldReport.PayWay},
			enum.DimensionSaleChannel: {oldReport.Channel},
		}
	}
	commonSearchModelCommonRule := dm.CommonSearchModelSpecialProductRule{}
	statistic, err := repository.ReportDmCommonDateRepository.Statistics(dm.CommonSearch{
		MerchantId:         &oldReport.Fid,
		StartTime:          startTime,
		EndTime:            endTime,
		DimensionRange:     *dimensionRange,
		SpecialProductRule: commonSearchModelCommonRule,
	})
	if err != nil {
		return nil, err
	}

	return &statistic, nil
}

type OldReportPagination interface {
	Pagination() ([]OldReport, error)
	Indicator() (OldReport, error)
	SetPage(page int, size int)
	GetDate() carbon.Carbon
	GetFid() int
	GetTid() int
	GetReq() PaginationReq
	GetStatWithOldReport(oldReport DimensionRange, dimensionRange *map[string][]int) (*DifferencesFields, error)
}
