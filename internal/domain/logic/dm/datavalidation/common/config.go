package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
)

type ConfigItem struct {
	Fid        int      `json:"fid"`
	ReportType []string `json:"report_type"`
}

func GetValidationDataConfig() ([]ConfigItem, error) {
	//获取配置
	configStr := config.GetJson(ConfigKey, enum.CacheTimeDay)
	if configStr == "" {
		return nil, errors.New("没有配置")
	}
	var configInfo []ConfigItem
	err := json.Unmarshal([]byte(configStr), &configInfo)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("配置解析失败，错误信息：%s", err))
	}
	return configInfo, nil
}
