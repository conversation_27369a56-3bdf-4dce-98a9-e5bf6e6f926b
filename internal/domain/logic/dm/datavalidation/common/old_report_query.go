package common

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/repository"
	"report-service/pkg/utils"
)

func oldReportOrderTwoPagination(startTime, endTime carbon.Carbon, fid, tid int, page, pageSize int) ([]OldReportOrderTwo, error) {
	groupBy := []string{"date", "fid", "reseller_id", "tid", "pay_way", "channel"}
	sumBy := []string{"order_ticket", "cancel_ticket", "revoke_ticket", "cost_money", "sale_money", "cancel_cost_money",
		"cancel_sale_money", "revoke_cost_money", "revoke_sale_money", "service_money", "after_sale_ticket_num",
		"after_sale_refund_money", "after_sale_income_money"}
	repoRecords, err := repository.OldReportOrderTwoRepo.Paginate(startTime, endTime, fid, tid, groupBy, sumBy, page, pageSize)
	if err != nil {
		return nil, err
	}
	list := make([]OldReportOrderTwo, len(repoRecords))
	err = utils.JsonConvertor(repoRecords, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func oldReportOrderTwoStat(startTime, endTime carbon.Carbon, fid, tid int, resellerIds []int) (OldReportOrderTwo, error) {
	var indicator OldReportOrderTwo
	repoRecord, err := repository.OldReportOrderTwoRepo.Statistic(startTime, endTime, fid, tid, resellerIds, true)
	if err != nil {
		return indicator, err
	}
	err = utils.JsonConvertor(repoRecord, &indicator)
	if err != nil {
		return indicator, err
	}
	return indicator, nil
}

func oldReportCheckTwoPagination(startTime, endTime carbon.Carbon, fid, tid int, page, pageSize int) ([]OldReportCheckTwo, error) {
	groupBy := []string{"date", "fid", "reseller_id", "tid", "pay_way", "channel"}
	sumBy := []string{"order_ticket", "finish_ticket", "revoke_ticket", "cost_money", "sale_money", "finish_cost_money",
		"finish_sale_money", "revoke_cost_money", "revoke_sale_money", "service_money", "after_sale_ticket_num",
		"after_sale_refund_money", "after_sale_income_money"}
	repoRecords, err := repository.OldReportCheckTwoRepo.Paginate(startTime, endTime, fid, tid, groupBy, sumBy, page, pageSize)
	if err != nil {
		return nil, err
	}
	list := make([]OldReportCheckTwo, len(repoRecords))
	err = utils.JsonConvertor(repoRecords, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func oldReportCheckTwoStat(startTime, endTime carbon.Carbon, fid, tid int, resellerIds []int) (OldReportCheckTwo, error) {
	var indicator OldReportCheckTwo
	repoRecord, err := repository.OldReportCheckTwoRepo.Statistic(startTime, endTime, fid, tid, resellerIds, true)
	if err != nil {
		return indicator, err
	}
	err = utils.JsonConvertor(repoRecord, &indicator)
	if err != nil {
		return indicator, err
	}
	return indicator, nil
}
