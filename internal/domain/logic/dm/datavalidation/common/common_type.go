package common

import "gitee.com/golang-module/carbon/v2"

type OldReportOrderTwo struct {
	Date       int `json:"date"`        // 日期 20171010
	Fid        int `json:"fid"`         // 用户ID
	ResellerId int `json:"reseller_id"` // 分销商ID
	Lid        int `json:"lid"`         // 景区ID
	Tid        int `json:"tid"`         // 门票ID
	PayWay     int `json:"pay_way"`     // 支付方式
	Channel    int `json:"channel"`     // 销售渠道
	OldReportOrderTwoIndicator
}

type OldReportOrderTwoIndicator struct {
	OrderTicket          int `json:"order_ticket"`            // 预订门票数
	CancelTicket         int `json:"cancel_ticket"`           // 取消门票数
	RevokeTicket         int `json:"revoke_ticket"`           // 撤销门票数
	CostMoney            int `json:"cost_money"`              // 预订花费金额
	SaleMoney            int `json:"sale_money"`              // 预定金额
	CancelCostMoney      int `json:"cancel_cost_money"`       // 取消花费金额
	CancelSaleMoney      int `json:"cancel_sale_money"`       // 取消金额
	RevokeCostMoney      int `json:"revoke_cost_money"`       // 撤销花费金额
	RevokeSaleMoney      int `json:"revoke_sale_money"`       // 撤销金额
	ServiceMoney         int `json:"service_money"`           // 退款服务费
	AfterSaleTicketNum   int `json:"after_sale_ticket_num"`   // 售后数量
	AfterSaleRefundMoney int `json:"after_sale_refund_money"` // 售后退回金额
	AfterSaleIncomeMoney int `json:"after_sale_income_money"` // 售后收入金额
}

type OldReportCheckTwo struct {
	Date       int `json:"date"`        // 日期 20171010
	Fid        int `json:"fid"`         // 用户ID
	ResellerId int `json:"reseller_id"` // 分销商ID
	Lid        int `json:"lid"`         // 景区ID
	Tid        int `json:"tid"`         // 门票ID
	PayWay     int `json:"pay_way"`     // 支付方式
	Channel    int `json:"channel"`     // 销售渠道
	OldReportCheckTwoIndicator
}

type OldReportCheckTwoIndicator struct {
	OrderTicket          int `json:"order_ticket"`            // 预订门票数
	FinishTicket         int `json:"finish_ticket"`           // 完结门票数
	RevokeTicket         int `json:"revoke_ticket"`           // 撤销门票数
	CostMoney            int `json:"cost_money"`              // 预订花费金额
	SaleMoney            int `json:"sale_money"`              // 预定金额
	FinishCostMoney      int `json:"finish_cost_money"`       // 完结花费金额
	FinishSaleMoney      int `json:"finish_sale_money"`       // 完结金额
	RevokeCostMoney      int `json:"revoke_cost_money"`       // 撤销花费金额
	RevokeSaleMoney      int `json:"revoke_sale_money"`       // 撤销金额
	ServiceMoney         int `json:"service_money"`           // 退款服务费
	AfterSaleTicketNum   int `json:"after_sale_ticket_num"`   // 售后数量
	AfterSaleRefundMoney int `json:"after_sale_refund_money"` // 售后退回金额
	AfterSaleIncomeMoney int `json:"after_sale_income_money"` // 售后收入金额
}

type DimensionRange struct {
	Date       int `json:"date"`
	Fid        int `json:"fid"`
	ResellerId int `json:"reseller_id"`
	Lid        int `json:"lid"`
	Tid        int `json:"tid"`
	PayWay     int `json:"pay_way"`
	Channel    int `json:"channel"`
}

type PaginationReq struct {
	StartTime   carbon.Carbon `json:"start_time"`
	EndTime     carbon.Carbon `json:"EndTime"`
	Fid         int           `json:"Fid"`
	Tid         int           `json:"tid"`
	Page        int           `json:"page"`
	PageSize    int           `json:"PageSize"`
	ResellerIds []int         `json:"reseller_ids"`
	Title       string        `json:"title"`
}

type DifferencesFields struct {
	PayCount        int `json:"pay_count"`         // 预订数量
	CancelCount     int `json:"cancel_count"`      // 取消数量
	RevokeCount     int `json:"revoke_count"`      // 撤销数量
	VerifyCount     int `json:"verify_count"`      // 核销数量
	PayCostPrice    int `json:"pay_cost_price"`    // 预订采购金额
	PaySalePrice    int `json:"pay_sale_price"`    // 预订销售金额
	CancelCostPrice int `json:"cancel_cost_price"` // 取消采购金额
	CancelSalePrice int `json:"cancel_sale_price"` // 取消销售金额
	RevokeCostPrice int `json:"revoke_cost_price"` // 撤销采购金额
	RevokeSalePrice int `json:"revoke_sale_price"` // 撤销销售金额
	VerifyCostPrice int `json:"verify_cost_price"` // 核销采购金额
	VerifySalePrice int `json:"verify_sale_price"` // 核销销售金额
	ServicePrice    int `json:"service_price"`     // 手续费
	AfterSaleCount  int `json:"after_sale_count"`  // 售后数量
	AfterCostPrice  int `json:"after_cost_price"`  // 售后采购金额
	AfterSalePrice  int `json:"after_sale_price"`  // 售后销售金额
}

var DifferencesFieldsMap = map[string]string{
	"PayCount":        "预订门票数：%d，新报表为%d",
	"CancelCount":     "取消门票数：%d，新报表为%d",
	"RevokeCount":     "撤销门票数：%d，新报表为%d",
	"VerifyCount":     "核销门票数：%d，新报表为%d",
	"PayCostPrice":    "预订花费金额：%d，新报表为%d",
	"PaySalePrice":    "预定金额：%d，新报表为%d",
	"CancelCostPrice": "取消花费金额：%d，新报表为%d",
	"CancelSalePrice": "取消金额：%d，新报表为%d",
	"RevokeCostPrice": "撤销花费金额：%d，新报表为%d",
	"RevokeSalePrice": "撤销金额：%d，新报表为%d",
	"VerifyCostPrice": "核销花费金额：%d，新报表为%d",
	"VerifySalePrice": "核销金额：%d，新报表为%d",
	"ServicePrice":    "退款服务费：%d，新报表为%d",
	"AfterSaleCount":  "售后数量：%d，新报表为%d",
	"AfterCostPrice":  "售后退回金额：%d，新报表为%d",
	"AfterSalePrice":  "售后收入金额：%d，新报表为%d",
}

var (
	OldReportUserIds = []int{10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 22, 66, 112}
	NewReportUserId  = 112
	SelfSale         = 20
	ConfigKey        = "data_validation_merchant"
)
