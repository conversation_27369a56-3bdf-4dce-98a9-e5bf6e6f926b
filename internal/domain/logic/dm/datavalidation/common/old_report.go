package common

import "report-service/pkg/utils"

type OrderTwo struct {
	OldReportOrderTwo
}

type CheckTwo struct {
	OldReportCheckTwo
}

func (o OrderTwo) GetDimensionRange() *DimensionRange {
	return &DimensionRange{
		Date:       o.Date,
		Fid:        o.Fid,
		ResellerId: o.ResellerId,
		Lid:        o.Lid,
		Tid:        o.Tid,
		PayWay:     o.PayWay,
		Channel:    o.Channel,
	}
}

func (o OrderTwo) GetDifferencesId() *DifferencesFields {
	result := &DifferencesFields{
		PayCount:        o.OrderTicket,
		CancelCount:     o.CancelTicket,
		RevokeCount:     o.RevokeTicket,
		PayCostPrice:    o.<PERSON>,
		PaySalePrice:    0,
		CancelCostPrice: o.Cancel<PERSON>ost<PERSON>oney,
		CancelSalePrice: 0,
		RevokeCostPrice: o.RevokeCostMoney,
		RevokeSalePrice: 0,
		ServicePrice:    o.<PERSON>,
		AfterSaleCount:  o.AfterSaleTicketNum,
		AfterCostPrice:  o.AfterSaleIncomeMoney,
		AfterSalePrice:  o.AfterSaleRefund<PERSON>oney,
	}
	if !utils.Container([]int{o.Fid, SelfSale}, o.ResellerId) {
		result.PaySalePrice = o.SaleMoney
		result.CancelSalePrice = o.CancelSaleMoney
		result.RevokeSalePrice = o.RevokeSaleMoney
	}
	return result
}

func (o CheckTwo) GetDimensionRange() *DimensionRange {
	return &DimensionRange{
		Date:       o.Date,
		Fid:        o.Fid,
		ResellerId: o.ResellerId,
		Lid:        o.Lid,
		Tid:        o.Tid,
		PayWay:     o.PayWay,
		Channel:    o.Channel,
	}
}

func (o CheckTwo) GetDifferencesId() *DifferencesFields {
	result := &DifferencesFields{
		RevokeCount:     o.RevokeTicket,
		VerifyCount:     o.OrderTicket + o.FinishTicket,
		RevokeCostPrice: o.RevokeCostMoney,
		RevokeSalePrice: 0,
		VerifyCostPrice: o.CostMoney + o.FinishCostMoney,
		VerifySalePrice: 0,
		ServicePrice:    o.ServiceMoney,
		AfterSaleCount:  o.AfterSaleTicketNum,
		AfterCostPrice:  o.AfterSaleIncomeMoney,
		AfterSalePrice:  o.AfterSaleRefundMoney,
	}
	if !utils.Container([]int{o.Fid, SelfSale}, o.ResellerId) {
		result.RevokeSalePrice = o.RevokeSaleMoney
		result.VerifySalePrice = o.SaleMoney + o.FinishSaleMoney
	}
	return result
}

type OldReport interface {
	GetDimensionRange() *DimensionRange
	GetDifferencesId() *DifferencesFields
}
