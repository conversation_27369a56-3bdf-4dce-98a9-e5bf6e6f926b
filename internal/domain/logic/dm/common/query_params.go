package common

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/pkg/utils"
)

func (d *DoQueryParams) checkNoNeedMerchantId() bool {
	return utils.Container(enum.TemplateQueryNoNeedMerchantId, d.DoTemplate.Category)
}

// 获取商户ID
func (d *DoQueryParams) getMerchantId() *int {
	if d.checkNoNeedMerchantId() {
		return nil
	}
	return &d.MerchantId
}

// 获取维度标签商户ID
func (d *DoQueryParams) getDimensionTagMerchantId() *int {
	return &d.MerchantId
}

// 获取维度标签组
func (d *DoQueryParams) getDimensionTagGroup(isOverall bool) map[string]string {
	dimensionTagGroup := make(map[string]string)
	//查询总计是维度标签组处理
	if isOverall {
		// 报表合计汇总数据时，可查维度开启标签查询（如h5报表，需要限制指定标签汇总配置），其次非商户id查询时，可能通过标签汇总配置的商户id限制也放行
		if d.DoTemplate.CanQueryDimension.Enable == 1 || d.checkNoNeedMerchantId() {
			for scene, tagGroup := range d.DoTemplate.DimensionTagGroup {
				dimensionTagGroup[scene] = tagGroup.Code
			}
		}
		return dimensionTagGroup
	}
	//查询列表分组汇总是处理
	for scene, tagGroup := range d.DoTemplate.DimensionTagGroup {
		dimensionTagGroup[scene] = tagGroup.Code
	}
	return dimensionTagGroup
}

// 获取维度标签组标签
func (d *DoQueryParams) getDimensionTagCodes() map[string][]string {
	dimensionTagCodes := make(map[string][]string)
	if d.DoTemplate.CanQueryDimension.Enable == 1 && d.DoTemplate.CanQueryDimension.Option != "" {
		if len(d.TagCodes) > 0 {
			dimensionTagCodes[d.DoTemplate.CanQueryDimension.Option] = d.TagCodes
		}
	}
	return dimensionTagCodes
}

// 获取数据岗位权限
func (d *DoQueryParams) getDataLimit() *datajobslimit.LimitSpuConfig {
	// 获取数据岗位限制
	if d.DataLimit == nil {
		dataLimitRes := datajobslimit.QueryBusinessData(d.MerchantId, d.MerchantId)
		d.DataLimit = &dataLimitRes
	}
	return d.DataLimit
}

func (d *DoQueryParams) getDimensions(isOverall bool) []string {
	dimensions := make([]string, 0)
	// 非总计需要带入维度分组查询
	if !isOverall {
		dimensions = d.DoTemplate.Dimension
	}
	//汇总可查维度处理
	if d.DoTemplate.CanQueryDimension.Enable == 1 && d.DoTemplate.CanQueryDimension.Option != "" {
		// 维度标签编码过滤，需要添加维度和维度标签关联查询
		if dimension, ok := enum.DimensionToTagCenterSceneMapReverse[d.DoTemplate.CanQueryDimension.Option]; ok {
			//由于标签codes过滤，需要依赖维度关联查询，如果维度没勾选，默认加个维度进去关联查询
			dimensions = append(dimensions, dimension)
		}
	}
	// 非商户id查询时，可能通过标签汇总配置的商户id查询，这边需要加上关联的维度
	if d.checkNoNeedMerchantId() {
		if fields, ok := enum.QueryNoNeedMerchantIdDimensionMap[d.DoTemplate.Category]; ok {
			dimensions = append(dimensions, fields...)
		}
	}

	return utils.RemoveDuplicate(dimensions)
}
