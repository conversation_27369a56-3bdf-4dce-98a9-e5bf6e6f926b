package common

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func Statistics(doQueryParams DoQueryParams) (statistics DoIndicator, err error) {
	if err = CheckReportTypeAndTimeRange(doQueryParams.ReportType, doQueryParams.StartTime, doQueryParams.EndTime); err != nil {
		return
	}

	// 获取数据岗位限制
	if doQueryParams.DataLimit == nil {
		dataLimitRes := datajobslimit.QueryBusinessData(doQueryParams.MerchantId, doQueryParams.MerchantId)
		doQueryParams.DataLimit = &dataLimitRes
	}
	// 解析、合并模版维度范围和用户维度范围
	dimensionRange := GetDimensionRange(doQueryParams.DimensionRange, doQueryParams.DoTemplate.SpecialDimension, doQueryParams.DataLimit)

	// 通用查询过滤
	commonSearchModelFilter := GetCommonSearchModelFilter(doQueryParams.DataLimit, doQueryParams.DimensionRange)

	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doQueryParams.DoTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	commonSearch := dm.CommonSearch{
		MerchantId:             doQueryParams.getMerchantId(),
		StartTime:              doQueryParams.StartTime,
		EndTime:                doQueryParams.EndTime,
		DimensionTagMerchantId: doQueryParams.getDimensionTagMerchantId(),
		DimensionTagCodes:      doQueryParams.getDimensionTagCodes(),
		Dimensions:             doQueryParams.getDimensions(true),
		DimensionTagGroup:      doQueryParams.getDimensionTagGroup(true),
		DimensionRange:         dimensionRange,
		SpecialProductRule:     commonSearchModelSpecialProductRule,
		DistributorRange: dm.DistributorRange{
			IncludeIds: doQueryParams.IncludeDistributorIds,
			ExcludeIds: doQueryParams.ExcludeDistributorIds,
		},
		OperateTypes:            doQueryParams.OperateTypes,
		CommonSearchModelFilter: commonSearchModelFilter,
	}
	var poStatistic dm.CommonStatistic
	switch doQueryParams.ReportType {
	case enum.ReportTypeDay:
		poStatistic, err = repository.ReportDmCommonDateRepository.Statistics(commonSearch)
	case enum.ReportTypeHour:
		poStatistic, err = repository.ReportDmCommonHourRepository.Statistics(commonSearch)
	case enum.ReportTypeRealTime:
		poStatistic, err = repository.ReportDmCommonRealtimeRepository.Statistics(commonSearch)
	default:
		err = szerrors.NewInvalidParamErrorWithText("不支持的报表类型")
		return
	}
	if err != nil {
		return
	}

	err = utils.JsonConvertor(poStatistic, &statistics)
	if err != nil {
		err = szerrors.NewLogicErrorWithText("统计结果解析失败")
		return
	}
	return
}
