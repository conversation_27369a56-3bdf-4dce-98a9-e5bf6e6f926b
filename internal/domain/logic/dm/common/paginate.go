package common

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/internal/domain/repository/dm/common"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type DoPaginationItem struct {
	DoDimension
	DoIndicator
}

func Paginate(doQueryParams DoQueryParams, page, pageSize int) (list []DoPaginationItem, total int, err error) {
	if err = CheckReportTypeAndTimeRange(doQueryParams.ReportType, doQueryParams.StartTime, doQueryParams.EndTime); err != nil {
		return
	}

	// 解析、合并模版维度范围和用户维度范围
	dimensionRange := GetDimensionRange(doQueryParams.DimensionRange, doQueryParams.DoTemplate.SpecialDimension, doQueryParams.getDataLimit())

	// 通用查询过滤
	commonSearchModelFilter := GetCommonSearchModelFilter(doQueryParams.getDataLimit(), doQueryParams.DimensionRange)

	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(doQueryParams.DoTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return
	}

	// 模版未选择按日期维度，则置空时间分组类型
	if !utils.Container(doQueryParams.DoTemplate.Dimension, enum.DimensionDate) {
		doQueryParams.TimeGroupType = nil
	} else {
		if doQueryParams.TimeGroupType == nil {
			err = szerrors.NewInvalidParamErrorWithText("时间分组类型不能为空")
			return
		}
	}

	commonSearch := dm.CommonSearch{
		MerchantId:             doQueryParams.getMerchantId(),
		StartTime:              doQueryParams.StartTime,
		EndTime:                doQueryParams.EndTime,
		TimeGroupType:          doQueryParams.TimeGroupType,
		Dimensions:             doQueryParams.getDimensions(false),
		DimensionTagMerchantId: doQueryParams.getDimensionTagMerchantId(),
		DimensionTagGroup:      doQueryParams.getDimensionTagGroup(false),
		DimensionTagCodes:      doQueryParams.getDimensionTagCodes(),
		DimensionRange:         dimensionRange,
		SpecialProductRule:     commonSearchModelSpecialProductRule,
		OperateTypes:           doQueryParams.OperateTypes,
		DistributorRange: dm.DistributorRange{
			IncludeIds: doQueryParams.IncludeDistributorIds,
			ExcludeIds: doQueryParams.ExcludeDistributorIds,
		},
		CommonSearchModelFilter: commonSearchModelFilter,
	}

	// 查询仓储层
	var poList []common.PaginationItem
	switch doQueryParams.ReportType {
	case enum.ReportTypeDay:
		poList, total, err = repository.ReportDmCommonDateRepository.Paginate(commonSearch, page, pageSize)
	case enum.ReportTypeHour:
		poList, total, err = repository.ReportDmCommonHourRepository.Paginate(commonSearch, page, pageSize)
	case enum.ReportTypeRealTime:
		poList, total, err = repository.ReportDmCommonRealtimeRepository.Paginate(commonSearch, page, pageSize)
	default:
		err = szerrors.NewInvalidParamErrorWithText("不支持的报表类型")
		return
	}
	if err != nil {
		return
	}

	var doDimensionIdNameFields []DoDimensionIdNameField
	if err = utils.JsonConvertor(poList, &doDimensionIdNameFields); err != nil {
		err = szerrors.NewLogicErrorWithText("维度名称解析失败")
		return
	}
	maps, err := GetMultiSearchName(doDimensionIdNameFields, doQueryParams)
	if err != nil {
		return
	}

	list = make([]DoPaginationItem, 0)
	for _, po := range poList {
		var doPaginationItem DoPaginationItem
		_ = utils.JsonConvertor(po, &doPaginationItem)
		_ = utils.JsonConvertor(po.CommonModelDimension, &doPaginationItem.DoDimension.DoDimensionIdNameField)
		_ = utils.JsonConvertor(po.CommonSearchModelRelationDimension, &doPaginationItem.DoDimension.DoDimensionIdNameField)
		doPaginationItem.DoDimension.Fill(doQueryParams.DoTemplate, doQueryParams.TimeGroupType)
		doPaginationItem.DoDimension.DoDimensionIdNameField.FillName(doQueryParams.DoTemplate, maps)
		list = append(list, doPaginationItem)
	}
	return
}
