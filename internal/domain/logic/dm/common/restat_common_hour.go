package common

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"

	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
)

func RestatCommonHourOneHourAgo() {
	_ = RestatCommonHour(
		carbon.Now().SubHour().StartOfHour(),
		carbon.Now().SubHour().EndOfHour(),
	)
}

func RestatCommonHour(startTime, endTime carbon.Carbon) error {
	statStartTime := carbon.Now()
	var err error
	defer func() {
		if r := recover(); r != nil {
			globalNotice.Error(fmt.Sprint("statistic common hour error, panic", r))
			return
		}
		if err != nil {
			globalNotice.Error(fmt.Sprint("statistic common hour error, ", err.Error()))
			return
		}
		global.LOG.Info(
			"statistic common hour success",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	err = repository.ReportDmCommonHourRepository.DeleteWhereTimeRange(startTime, endTime, 0)
	if err != nil {
		return err
	}

	err = repository.ReportDmCommonHourRepository.InsertIntoSelectDmCommon(startTime, endTime, 0)
	if err != nil {
		return err
	}

	err = config.UpdateLatestTime(enum.ConfigKeyDmCommonHourLastStatisticTime, endTime)
	if err != nil {
		globalNotice.Error(fmt.Sprint("statistic common hour error, set config error", err.Error()))
		return err
	}

	return nil
}
