package common

import (
	"fmt"
	"report-service/internal/domain/logic/config/outdateddata"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"

	"github.com/spf13/cast"
	"go.uber.org/zap"
)

var RestatedDays = make(map[string]bool)

func RestatOutdatedData() {
	outdatedData, err := outdateddata.Get()
	if err != nil {
		globalNotice.Error("restat outdated data error, " + err.Error())
		return
	}
	if len(outdatedData) == 0 {
		return
	}
	global.LOG.Info("restat outdated data", zap.Int("count", len(outdatedData)))
	RestatedDays = make(map[string]bool)
	for _, item := range outdatedData {
		restatOutdatedItemDay(item)
		restatOutdatedItemHour(item)
		err = outdateddata.Del(item)
		if err != nil {
			globalNotice.Error(fmt.Sprintf("restat outdated data error, merchant_id: %d, operated_at: %s, %s", item.MerchantId, item.OperatedAt.ToDateTimeString(), err.Error()))
			continue
		}
		globalNotice.Warning("重算数据成功", fmt.Sprintf("商户ID: %d", item.MerchantId), fmt.Sprintf("操作时间: %s", item.OperatedAt.ToDateTimeString()))
	}
}

func restatOutdatedItemDay(item outdateddata.Item) {
	if _, ok := RestatedDays[item.OperatedAt.StartOfDay().ToDateString()]; ok {
		return
	}
	if item.OperatedAt.IsToday() {
		return
	}
	var err error
	defer func() {
		messages := []string{
			"Start Time: " + item.OperatedAt.StartOfDay().ToDateTimeString(),
			"End Time: " + item.OperatedAt.EndOfDay().ToDateTimeString(),
			"Merchant Id: " + cast.ToString(item.MerchantId),
		}
		if r := recover(); r != nil {
			messages = append([]string{fmt.Sprintf("restat day outdated panic: %v", r)}, messages...)
			globalNotice.Error(messages...)
			return
		}
		if err != nil {
			messages = append([]string{fmt.Sprintf("restat day outdated error: %s", err.Error())}, messages...)
			globalNotice.Error(messages...)
			return
		}
		global.LOG.Info("restat day outdated data success", zap.Strings("messages", messages))
	}()
	RestatedDays[item.OperatedAt.StartOfDay().ToDateString()] = true
	err = repository.ReportDmCommonDateRepository.DeleteWhereTimeRange(item.OperatedAt.StartOfDay(), item.OperatedAt.EndOfDay(), item.MerchantId)
	if err != nil {
		return
	}
	err = repository.ReportDmCommonDateRepository.InsertIntoSelectDmCommon(item.OperatedAt.StartOfDay(), item.OperatedAt.EndOfDay(), item.MerchantId)
	if err != nil {
		return
	}
}

func restatOutdatedItemHour(item outdateddata.Item) {
	if item.OperatedAt.DiffAbsInMinutes() < 65 {
		return
	}
	var err error
	defer func() {
		messages := []string{
			"Start Time: " + item.OperatedAt.StartOfDay().ToDateTimeString(),
			"End Time: " + item.OperatedAt.EndOfDay().ToDateTimeString(),
			"Merchant Id: " + cast.ToString(item.MerchantId),
		}
		if r := recover(); r != nil {
			messages = append([]string{fmt.Sprintf("restat hour outdated panic: %v", r)}, messages...)
			globalNotice.Error(messages...)
			return
		}
		if err != nil {
			messages = append([]string{fmt.Sprintf("restat hour outdated error: %s", err.Error())}, messages...)
			globalNotice.Error(messages...)
			return
		}
		global.LOG.Info("restat hour outdated data success", zap.Strings("messages", messages))
	}()
	err = repository.ReportDmCommonHourRepository.DeleteWhereTimeRange(item.OperatedAt.StartOfHour(), item.OperatedAt.EndOfHour(), item.MerchantId)
	if err != nil {
		return
	}
	err = repository.ReportDmCommonHourRepository.InsertIntoSelectDmCommon(item.OperatedAt.StartOfHour(), item.OperatedAt.EndOfHour(), item.MerchantId)
	if err != nil {
		return
	}
}
