package common

import (
	"report-service/internal/domain/enum"
	reportcommon "report-service/internal/domain/logic/report/common"
	"report-service/internal/domain/logic/report/template"
	templateCommon "report-service/internal/domain/logic/report/template/common"
	"report-service/pkg/sdk/api/landservice"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/platformrpcapi"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/tag"
	tagCenterTag "report-service/pkg/sdk/api/tagcenter/tag"
	"report-service/pkg/sdk/api/ticketservice"
	"report-service/pkg/utils"
)

type SearchNameMaps struct {
	TargetAudienceKeyNameMap     map[string]string
	SpuIdKeyNameMap              map[int]string
	SkuIdKeyNameMap              map[int]string
	MemberIdKeyNameMap           map[int]string
	SiteIdKeyNameMap             map[int]string
	PayModeKeyNameMap            map[int]string
	SpuTagCodeKeyNameMap         map[string]string
	SkuTagCodeKeyNameMap         map[string]string
	PayModeTagCodeKeyNameMap     map[string]string
	SaleChannelTagCodeKeyNameMap map[string]string
}

// GetMultiSearchName 获取多维度名称
func GetMultiSearchName(items []DoDimensionIdNameField, doQueryParams DoQueryParams) (searchNameMaps SearchNameMaps, err error) {
	searchNameMaps.SpuIdKeyNameMap, err = getSpuIdKeyNameMap(items)
	if err != nil {
		return
	}
	searchNameMaps.SkuIdKeyNameMap, err = getSkuIdKeyNameMap(items)
	if err != nil {
		return
	}
	searchNameMaps.MemberIdKeyNameMap, err = getMemberIdKeyNameMap(items)
	if err != nil {
		return
	}
	searchNameMaps.SiteIdKeyNameMap, err = getSiteIdKeyNameMap(items)
	if err != nil {
		return
	}
	searchNameMaps.PayModeKeyNameMap, err = getPayModeKeyNameMap(items, doQueryParams.MerchantId)
	if err != nil {
		return
	}
	searchNameMaps.SpuTagCodeKeyNameMap, err = getSpuTagCodeKeyNameMap(items, doQueryParams.DoTemplate.DimensionTagGroup, doQueryParams.MerchantId)
	if err != nil {
		return
	}
	searchNameMaps.SkuTagCodeKeyNameMap, err = getSkuTagCodeKeyNameMap(items, doQueryParams.DoTemplate.DimensionTagGroup, doQueryParams.MerchantId)
	if err != nil {
		return
	}
	searchNameMaps.PayModeTagCodeKeyNameMap, err = getPayModeTagCodeKeyNameMap(items, doQueryParams.DoTemplate.DimensionTagGroup, doQueryParams.MerchantId)
	if err != nil {
		return
	}
	searchNameMaps.SaleChannelTagCodeKeyNameMap, err = getSaleChannelTagCodeKeyNameMap(items, doQueryParams.DoTemplate.DimensionTagGroup, doQueryParams.MerchantId)
	if err != nil {
		return
	}
	searchNameMaps.TargetAudienceKeyNameMap, err = getTargetAudienceKeyNameMap(items)
	if err != nil {
		return
	}
	return
}

func getSpuIdKeyNameMap(items []DoDimensionIdNameField) (spuIdKeyNameMap map[int]string, err error) {
	var spuIds []int
	for _, poItem := range items {
		if poItem.SpuId != nil && *poItem.SpuId > 0 {
			spuIds = append(spuIds, *poItem.SpuId)
		}
	}
	if len(spuIds) == 0 {
		return
	}
	spuIdKeyNameMap, err = landservice.QueryLandTitleByIds(spuIds)
	if err != nil {
		return
	}
	return
}

func getSkuIdKeyNameMap(items []DoDimensionIdNameField) (skuIdKeyNameMap map[int]string, err error) {
	var skuIds []int
	for _, poItem := range items {
		if poItem.SkuId != nil && *poItem.SkuId > 0 {
			skuIds = append(skuIds, *poItem.SkuId)
		}
	}
	if len(skuIds) == 0 {
		return
	}
	skuIdKeyNameMap, err = ticketservice.QueryTicketTitleByIds(skuIds)
	if err != nil {
		return
	}
	return
}

func getMemberIdKeyNameMap(items []DoDimensionIdNameField) (memberIdKeyNameMap map[int]string, err error) {
	var memberIds []int
	for _, poItem := range items {
		if poItem.MerchantId != nil && *poItem.MerchantId > 0 {
			memberIds = append(memberIds, *poItem.MerchantId)
		}
		if poItem.ParentMerchantId != nil && *poItem.ParentMerchantId > 0 {
			memberIds = append(memberIds, *poItem.ParentMerchantId)
		}
		if poItem.DistributorId != nil && *poItem.DistributorId > 0 {
			memberIds = append(memberIds, *poItem.DistributorId)
		}
		if poItem.SellOperatorId != nil && *poItem.SellOperatorId > 0 {
			memberIds = append(memberIds, *poItem.SellOperatorId)
		}
		if poItem.OperatorId != nil && *poItem.OperatorId > 0 {
			memberIds = append(memberIds, *poItem.OperatorId)
		}

	}
	if len(memberIds) == 0 {
		return
	}
	memberIdKeyNameMap, err = pftmember.GetMemberIdKeyDNameMap(memberIds)
	if err != nil {
		return
	}
	return
}

func getSiteIdKeyNameMap(items []DoDimensionIdNameField) (siteIdKeyNameMap map[int]string, err error) {
	siteIdKeyNameMap = make(map[int]string)
	siteIds := make([]int, 0)
	for _, item := range items {
		if item.SellSiteId != nil && *item.SellSiteId > 0 {
			siteIds = append(siteIds, *item.SellSiteId)
		}
	}
	if len(siteIds) == 0 {
		return
	}
	siteIds = utils.RemoveDuplicate(siteIds)
	siteIdKeyNameMap, err = platformrpcapi.QuerySiteNameMapByIds(siteIds)
	return
}

func getPayModeKeyNameMap(items []DoDimensionIdNameField, merchantId int) (payModeKeyNameMap map[int]string, err error) {
	payModeKeyNameMap = reportcommon.PayWayListConf(merchantId, false)
	return
}

// 根据标签组Code查询标签中心，遍历匹配出所需标签Code和名称
func getTagCodeKeyNameMapByTagGroupCode(tagGroupCode string, merchantId int, tagCodes []string) (tagCodeKeyNameMap map[string]string, err error) {
	tagGroupPage, err := tag.TagPage(&tag.TagPageRequestVO{
		GroupCode: tagGroupCode,
		MemberId:  int64(merchantId),
		PageNum:   1,
		PageSize:  200, // 标签中心目前一个标签组下最多200个标签，所以查询一次即可
	})
	if err != nil {
		return
	}
	tagCodeKeyNameMap = make(map[string]string)
	for _, item := range tagGroupPage.Rows {
		if utils.Container(tagCodes, *item.Code) {
			tagCodeKeyNameMap[*item.Code] = *item.Name
		}
	}
	return
}

func getSpuTagCodeKeyNameMap(items []DoDimensionIdNameField, dimensionTagGroup map[string]templateCommon.TagGroup, merchantId int) (spuTagCodeKeyNameMap map[string]string, err error) {
	tagGroup := dimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyProduct]
	if tagGroup.Code == "" {
		return
	}
	tagCodes := make([]string, 0)
	for _, item := range items {
		if item.SpuTagCode != nil && *item.SpuTagCode != "" {
			tagCodes = append(tagCodes, *item.SpuTagCode)
		}
	}
	spuTagCodeKeyNameMap, err = getTagCodeKeyNameMapByTagGroupCode(tagGroup.Code, merchantId, tagCodes)
	if err != nil {
		return
	}
	return
}

func getSkuTagCodeKeyNameMap(items []DoDimensionIdNameField, dimensionTagGroup map[string]templateCommon.TagGroup, merchantId int) (skuTagCodeKeyNameMap map[string]string, err error) {
	tagGroup := dimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyTicket]
	if tagGroup.Code == "" {
		return
	}
	tagCodes := make([]string, 0)
	for _, item := range items {
		if item.SkuTagCode != nil && *item.SkuTagCode != "" {
			tagCodes = append(tagCodes, *item.SkuTagCode)
		}
	}
	skuTagCodeKeyNameMap, err = getTagCodeKeyNameMapByTagGroupCode(tagGroup.Code, merchantId, tagCodes)
	if err != nil {
		return
	}
	return
}

func getPayModeTagCodeKeyNameMap(items []DoDimensionIdNameField, dimensionTagGroup map[string]templateCommon.TagGroup, merchantId int) (payModeTagCodeKeyNameMap map[string]string, err error) {
	tagGroup := dimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyPayMode]
	if tagGroup.Code == "" {
		return
	}
	tagCodes := make([]string, 0)
	for _, item := range items {
		if item.PayModeTagCode != nil && *item.PayModeTagCode != "" {
			tagCodes = append(tagCodes, *item.PayModeTagCode)
		}
	}
	payModeTagCodeKeyNameMap, err = getTagCodeKeyNameMapByTagGroupCode(tagGroup.Code, merchantId, tagCodes)
	if err != nil {
		return
	}
	return
}

func getSaleChannelTagCodeKeyNameMap(items []DoDimensionIdNameField, dimensionTagGroup map[string]templateCommon.TagGroup, merchantId int) (saleChannelTagCodeKeyNameMap map[string]string, err error) {
	tagGroup := dimensionTagGroup[enum.DimensionToTagCenterSceneMapKeySaleChannel]
	if tagGroup.Code == "" {
		return
	}
	tagCodes := make([]string, 0)
	for _, item := range items {
		if item.SaleChannelTagCode != nil && *item.SaleChannelTagCode != "" {
			tagCodes = append(tagCodes, *item.SaleChannelTagCode)
		}
	}
	saleChannelTagCodeKeyNameMap, err = getTagCodeKeyNameMapByTagGroupCode(tagGroup.Code, merchantId, tagCodes)
	if err != nil {
		return
	}
	return
}

func (item *DoDimensionIdNameField) FillName(doTemplate template.DoListItem, maps SearchNameMaps) {
	//分销商名称解析
	if utils.Container(doTemplate.Dimension, enum.DimensionDistributor) {
		item.DistributorName = getDimensionName(item.DistributorId, maps.MemberIdKeyNameMap)
	}
	//适用人群名称解析
	if utils.Container(doTemplate.Dimension, enum.DimensionTargetAudience) {
		item.TargetAudienceName = getDimensionNameByStringKey(item.TargetAudience, maps.TargetAudienceKeyNameMap)
	}
	//产品标签名称解析
	if tagGroupCode, ok := doTemplate.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyProduct]; ok && tagGroupCode.Code != "" {
		item.SpuTagName = getTagName(item.SpuTagCode, maps.SpuTagCodeKeyNameMap)
	} else {
		if utils.Container(doTemplate.Dimension, enum.DimensionSpu) {
			item.SpuName = getDimensionName(item.SpuId, maps.SpuIdKeyNameMap)
		}
	}
	//票种标签名称解析
	if tagGroupCode, ok := doTemplate.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyTicket]; ok && tagGroupCode.Code != "" {
		item.SkuTagName = getTagName(item.SkuTagCode, maps.SkuTagCodeKeyNameMap)
	} else {
		if utils.Container(doTemplate.Dimension, enum.DimensionSku) {
			item.SkuName = getDimensionName(item.SkuId, maps.SkuIdKeyNameMap)
		}
	}
	//支付方式标签名称解析
	if tagGroupCode, ok := doTemplate.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyPayMode]; ok && tagGroupCode.Code != "" {
		item.PayModeTagName = getTagName(item.PayModeTagCode, maps.PayModeTagCodeKeyNameMap)
	} else {
		if utils.Container(doTemplate.Dimension, enum.DimensionPayMode) {
			item.SalePayModeName = getDimensionName(item.SalePayMode, maps.PayModeKeyNameMap)
		}
	}
	//销售渠道标签名称解析
	if tagGroupCode, ok := doTemplate.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeySaleChannel]; ok && tagGroupCode.Code != "" {
		item.SaleChannelTagName = getTagName(item.SaleChannelTagCode, maps.SaleChannelTagCodeKeyNameMap)
	} else {
		if utils.Container(doTemplate.Dimension, enum.DimensionSaleChannel) {
			item.SaleChannelName = getDimensionName(item.SaleChannel, enum.OrderModeMap)
		}
	}
	//操作人员名称解析
	if utils.Container(doTemplate.Dimension, enum.DimensionOperator) {
		item.OperatorName = getDimensionName(item.OperatorId, maps.MemberIdKeyNameMap)
	}
	//销售人员名称解析
	if utils.Container(doTemplate.Dimension, enum.DimensionSellOperator) {
		item.SellOperatorName = getDimensionName(item.SellOperatorId, maps.MemberIdKeyNameMap)
	}
	//销售站点名称解析
	if utils.Container(doTemplate.Dimension, enum.DimensionSellSite) {
		item.SellSiteName = getDimensionName(item.SellSiteId, maps.SiteIdKeyNameMap)
	}
	//集团成员名称
	if utils.Container(doTemplate.Dimension, enum.DimensionGroupMember) {
		item.GroupMemberName = getDimensionName(item.MerchantId, maps.MemberIdKeyNameMap)
		item.GroupMember = item.MerchantId
	}
}

func getTagName(tagCode *string, tagNameMap map[string]string) *string {
	name := "未分组"
	if tagCode == nil {
		return &name
	}
	name, ok := tagNameMap[*tagCode]
	if !ok {
		name = "未分组"
	}
	return &name
}

func getDimensionName(id *int, nameMap map[int]string) *string {
	name := ""
	if id == nil {
		return &name
	}
	name, ok := nameMap[*id]
	if !ok {
		name = ""
	}
	return &name
}

func getDimensionNameByStringKey(id *string, nameMap map[string]string) *string {
	name := ""
	if id == nil {
		return &name
	}
	name, ok := nameMap[*id]
	if !ok {
		name = ""
	}
	return &name
}

func getTargetAudienceKeyNameMap(items []DoDimensionIdNameField) (TargetAudienceKeyNameMap map[string]string, err error) {
	TargetAudienceKeyNameMap = make(map[string]string)
	codes := make([]string, 0)
	for _, item := range items {
		if item.TargetAudience != nil && *item.TargetAudience != "" {
			codes = append(codes, *item.TargetAudience)
		}
	}
	if len(codes) == 0 {
		return
	}
	codes = utils.RemoveDuplicate(codes)
	var params tagCenterTag.QueryTagPaginateAggregateParams
	params.Codes = codes
	params.PageNum = 1
	params.PageSize = len(codes)
	TargetAudienceKeyNameMap, err = tagCenterTag.TagPaginateAggregateCodeAndNameMap(params)
	return
}
