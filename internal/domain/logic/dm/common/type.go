package common

import (
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"

	"gitee.com/golang-module/carbon/v2"
)

type DoDimension struct {
	Date          *string `json:"date,omitempty"`
	CostUnitPrice *int    `json:"cost_unit_price,omitempty"` // 采购单价
	SaleUnitPrice *int    `json:"sale_unit_price,omitempty"` // 销售单价
	DoDimensionIdNameField
}

type DoDimensionIdNameField struct {
	MerchantId         *int    `json:"merchant_id,omitempty"`          // 商户ID
	MerchantName       *string `json:"merchant_name,omitempty"`        // 商户名称
	ParentMerchantId   *int    `json:"parent_merchant_id,omitempty"`   // 上级商户ID
	ParentMerchantName *string `json:"parent_merchant_name,omitempty"` // 上级商户名称
	DistributorId      *int    `json:"distributor_id,omitempty"`       // 分销商户ID
	DistributorName    *string `json:"distributor_name,omitempty"`     // 分销商名称
	PoiId              *int    `json:"poi_id,omitempty"`               // PoiID
	PoiName            *string `json:"poi_name,omitempty"`             // Poi名称
	SpuId              *int    `json:"spu_id,omitempty"`               // SpuID
	SpuName            *string `json:"spu_name,omitempty"`             // Spu名称
	SkuId              *int    `json:"sku_id,omitempty"`               // SkuID
	SkuName            *string `json:"sku_name,omitempty"`             // Sku名称
	SaleChannel        *int    `json:"sale_channel,omitempty"`         // 销售渠道
	SaleChannelName    *string `json:"sale_channel_name,omitempty"`    // 销售渠道名称
	CostPayMode        *int    `json:"cost_pay_mode,omitempty"`        // 采购支付方式
	CostPayModeName    *string `json:"cost_pay_mode_name,omitempty"`   // 采购支付方式名称
	SalePayMode        *int    `json:"sale_pay_mode,omitempty"`        // 销售支付方式
	SalePayModeName    *string `json:"sale_pay_mode_name,omitempty"`   // 销售支付方式名称
	SellOperatorId     *int    `json:"sell_operator_id,omitempty"`     // 售票员ID
	SellOperatorName   *string `json:"sell_operator_name,omitempty"`   // 售票员名称
	OperatorId         *int    `json:"operator_id,omitempty"`          // 操作人ID
	OperatorName       *string `json:"operator_name,omitempty"`        // 操作人名称
	SellSiteId         *int    `json:"sell_site_id,omitempty"`         // 售票站点ID
	SellSiteName       *string `json:"sell_site_name,omitempty"`       // 售票站点名称
	TargetAudience     *string `json:"target_audience,omitempty"`      //适用人群
	TargetAudienceName *string `json:"target_audience_name,omitempty"` //适用人群名称
	GroupMember        *int    `json:"group_member,omitempty"`         //集团成员
	GroupMemberName    *string `json:"group_member_name,omitempty"`    //集团成员名称
	// 标签维度
	SpuTagCode         *string `json:"spu_tag_code,omitempty"`          // SPU标签编码
	SpuTagName         *string `json:"spu_tag_name,omitempty"`          // SPU标签名称
	SkuTagCode         *string `json:"sku_tag_code,omitempty"`          // SKU标签编码
	SkuTagName         *string `json:"sku_tag_name,omitempty"`          // SKU标签名称
	PayModeTagCode     *string `json:"pay_mode_tag_code,omitempty"`     // 支付方式标签编码
	PayModeTagName     *string `json:"pay_mode_tag_name,omitempty"`     // 支付方式标签名称
	SaleChannelTagCode *string `json:"sale_channel_tag_code,omitempty"` // 销售渠道标签编码
	SaleChannelTagName *string `json:"sale_channel_tag_name,omitempty"` // 销售渠道标签名称
}

type DoIndicator struct {
	PayCount                int `json:"pay_count"`                  // 预订数量
	PayCostPrice            int `json:"pay_cost_price"`             // 预订采购金额
	PayCostDiscountPrice    int `json:"pay_cost_discount_price"`    // 预订采购优惠金额
	PaySalePrice            int `json:"pay_sale_price"`             // 预订销售金额
	PaySaleDiscountPrice    int `json:"pay_sale_discount_price"`    // 预订销售优惠金额
	VerifyCount             int `json:"verify_count"`               // 核销数量
	VerifyCostPrice         int `json:"verify_cost_price"`          // 核销采购金额
	VerifyCostDiscountPrice int `json:"verify_cost_discount_price"` // 核销采购优惠金额
	VerifySalePrice         int `json:"verify_sale_price"`          // 核销销售金额
	VerifySaleDiscountPrice int `json:"verify_sale_discount_price"` // 核销销售优惠金额
	CancelCount             int `json:"cancel_count"`               // 取消数量
	CancelCostPrice         int `json:"cancel_cost_price"`          // 取消采购金额
	CancelCostDiscountPrice int `json:"cancel_cost_discount_price"` // 取消采购优惠金额
	CancelCostFee           int `json:"cancel_cost_fee"`            // 取消采购手续费
	CancelSalePrice         int `json:"cancel_sale_price"`          // 取消销售金额
	CancelSaleDiscountPrice int `json:"cancel_sale_discount_price"` // 取消销售优惠金额
	CancelSaleFee           int `json:"cancel_sale_fee"`            // 取消销售手续费
	RevokeCount             int `json:"revoke_count"`               // 撤销数量
	RevokeCostPrice         int `json:"revoke_cost_price"`          // 撤销采购金额
	RevokeCostDiscountPrice int `json:"revoke_cost_discount_price"` // 撤销采购优惠金额
	RevokeCostFee           int `json:"revoke_cost_fee"`            // 撤销采购手续费
	RevokeSalePrice         int `json:"revoke_sale_price"`          // 撤销销售金额
	RevokeSaleDiscountPrice int `json:"revoke_sale_discount_price"` // 撤销销售优惠金额
	RevokeSaleFee           int `json:"revoke_sale_fee"`            // 撤销销售手续费
	AfterSaleCount          int `json:"after_sale_count"`           // 售后数量
	AfterCostPrice          int `json:"after_cost_price"`           // 售后采购金额
	AfterSalePrice          int `json:"after_sale_price"`           // 售后销售金额
	CollectCount            int `json:"collect_count"`              // 取票数量
	ReprintCount            int `json:"reprint_count"`              // 重打印数量
}

type DoDimensionRange struct {
	SaleChannel    []int    `json:"sale_channel"`    // 订单渠道
	Distributor    []int    `json:"distributor"`     // 分销商
	Spu            []int    `json:"spu"`             // 产品
	Sku            []int    `json:"sku"`             // 票种
	PayMode        []int    `json:"pay_mode"`        // 支付方式
	Operator       []int    `json:"operator"`        // 操作人
	SellOperator   []int    `json:"sell_operator"`   // 售票员
	SellSite       []int    `json:"sell_site"`       // 售票站点
	Region         []string `json:"region"`          //地域编号
	Country        []string `json:"country"`         //国家编号
	Province       []string `json:"province"`        //省编号
	City           []string `json:"city"`            //市编号
	District       []string `json:"district"`        //区编号
	TargetAudience []string `json:"target_audience"` //适用人群
	GroupMember    []int    `json:"group_member"`    //集团成员
}

type DoQueryParams struct {
	DoTemplate            template.DoListItem           `json:"do_template"`             // 报表模版
	MerchantId            int                           `json:"merchant_id"`             // 商户ID
	ReportType            int                           `json:"report_type"`             // 报表类型
	StartTime             carbon.Carbon                 `json:"start_time"`              // 开始时间
	EndTime               carbon.Carbon                 `json:"end_time"`                // 结束时间
	TimeGroupType         *int                          `json:"time_group_type"`         // 时间分组类型
	SchemeGroupIds        []int                         `json:"scheme_group_ids"`        // 维度方案组ID
	TagCodes              []string                      `json:"tag_codes"`               // 标签编码
	DimensionRange        DoDimensionRange              `json:"dimension_range"`         // 维度范围
	OperateTypes          []int                         `json:"operate_types"`           // 操作类型
	IncludeDistributorIds []int                         `json:"include_distributor_ids"` // 包含分销商ID
	ExcludeDistributorIds []int                         `json:"exclude_distributor_ids"` // 排除分销商ID
	DataLimit             *datajobslimit.LimitSpuConfig `json:"data_limit"`              //数据岗位限制
}
