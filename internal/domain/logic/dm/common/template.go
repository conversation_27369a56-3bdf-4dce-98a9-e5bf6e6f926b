package common

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
)

func (d *DoDimension) Fill(doTemplate template.DoListItem, timeGroupType *int) {
	if utils.Container(doTemplate.Dimension, enum.DimensionDate) {
		format := "Y-m-d"
		if timeGroupType != nil {
			switch *timeGroupType {
			case enum.ReportTimeGroupTypeHour:
				format = "Y-m-d H"
			case enum.ReportTimeGroupTypeDay:
				format = "Y-m-d"
			case enum.ReportTimeGroupTypeMonth:
				format = "Y-m"
			case enum.ReportTimeGroupTypeYear:
				format = "Y"
			}
		}
		date := carbon.Parse(*d.Date).Format(format)
		d.Date = &date
	}
	if !utils.Container(doTemplate.Dimension, enum.DimensionSaleUnitPrice) {
		d.SaleUnitPrice = nil
	}
}

func (d *DoDimension) FormatByTemplate(template template.DoListItem) map[string]interface{} {
	result := make(map[string]interface{})
	// 日期字段
	if utils.Container(template.Dimension, enum.DimensionDate) {
		result[enum.DimensionDate] = d.Date
	}
	// 标签分组字段
	if utils.Container(template.Dimension, enum.DimensionSpu) {
		if tagGroup, ok := template.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyProduct]; ok && tagGroup.Code != "" {
			result[enum.DimensionSpu] = d.SpuTagName
		} else {
			result[enum.DimensionSpu] = d.SpuName
		}
	}
	if utils.Container(template.Dimension, enum.DimensionSku) {
		if tagGroup, ok := template.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyTicket]; ok && tagGroup.Code != "" {
			result[enum.DimensionSku] = d.SkuTagName
		} else {
			result[enum.DimensionSku] = d.SkuName
		}
	}
	if utils.Container(template.Dimension, enum.DimensionPayMode) {
		if tagGroup, ok := template.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeyPayMode]; ok && tagGroup.Code != "" {
			result[enum.DimensionPayMode] = d.PayModeTagName
		} else {
			result[enum.DimensionPayMode] = d.SalePayModeName
		}
	}
	// 其他字段
	if utils.Container(template.Dimension, enum.DimensionSaleChannel) {
		if tagGroup, ok := template.DimensionTagGroup[enum.DimensionToTagCenterSceneMapKeySaleChannel]; ok && tagGroup.Code != "" {
			result[enum.DimensionSaleChannel] = d.SaleChannelTagName
		} else {
			result[enum.DimensionSaleChannel] = d.SaleChannelName
		}
	}
	if utils.Container(template.Dimension, enum.DimensionDistributor) {
		result[enum.DimensionDistributor] = d.DistributorName
	}
	if utils.Container(template.Dimension, enum.DimensionSellOperator) {
		result[enum.DimensionSellOperator] = d.SellOperatorName
	}
	if utils.Container(template.Dimension, enum.DimensionSellSite) {
		result[enum.DimensionSellSite] = d.SellSiteName
	}
	if utils.Container(template.Dimension, enum.DimensionCostUnitPrice) {
		result[enum.DimensionCostUnitPrice] = utils.FormatPrice(*d.CostUnitPrice)
	}
	if utils.Container(template.Dimension, enum.DimensionSaleUnitPrice) {
		result[enum.DimensionSaleUnitPrice] = utils.FormatPrice(*d.SaleUnitPrice)
	}
	//适用人群
	if utils.Container(template.Dimension, enum.DimensionTargetAudience) {
		result[enum.DimensionTargetAudience] = d.TargetAudienceName
	}
	//集团成员
	if utils.Container(template.Dimension, enum.DimensionGroupMember) {
		result[enum.DimensionGroupMember] = d.GroupMemberName
	}
	return result
}

func GetDimensionScheme(doTemplate template.DoListItem) (dimensionScheme map[string]int) {
	dimensionScheme = make(map[string]int)
	if doTemplate.DimensionScheme.Spu != nil {
		dimensionScheme[enum.DimensionSpu] = doTemplate.DimensionScheme.Spu.Id
	}
	if doTemplate.DimensionScheme.Sku != nil {
		dimensionScheme[enum.DimensionSku] = doTemplate.DimensionScheme.Sku.Id
	}
	if doTemplate.DimensionScheme.SaleChannel != nil {
		dimensionScheme[enum.DimensionSaleChannel] = doTemplate.DimensionScheme.SaleChannel.Id
	}
	if doTemplate.DimensionScheme.PayMode != nil {
		dimensionScheme[enum.DimensionPayMode] = doTemplate.DimensionScheme.PayMode.Id
	}
	if doTemplate.DimensionScheme.Operator != nil {
		dimensionScheme[enum.DimensionOperator] = doTemplate.DimensionScheme.Operator.Id
	}
	if doTemplate.DimensionScheme.SellOperator != nil {
		dimensionScheme[enum.DimensionSellOperator] = doTemplate.DimensionScheme.SellOperator.Id
	}
	if doTemplate.DimensionScheme.SellSite != nil {
		dimensionScheme[enum.DimensionSellSite] = doTemplate.DimensionScheme.SellSite.Id
	}
	return
}

func GetDimensionRange(doDimensionRange DoDimensionRange, specialDimension common.SpecialDimension, dataLimit *datajobslimit.LimitSpuConfig) (dimensionRange map[string][]int) {
	dimensionRange = make(map[string][]int)
	//获取数据岗位限制产品和查询产品取交集
	if dataLimit != nil {
		doDimensionRange.Spu = dataLimit.GetIdList(doDimensionRange.Spu)
	}
	dimensionRange[enum.DimensionSaleChannel] = mergeDimensions(doDimensionRange.SaleChannel, specialDimension.SaleChannel)
	dimensionRange[enum.DimensionDistributor] = mergeDimensions(doDimensionRange.Distributor, specialDimension.Distributor)
	dimensionRange[enum.DimensionSpu] = mergeDimensions(doDimensionRange.Spu, specialDimension.Spu)
	dimensionRange[enum.DimensionSku] = mergeDimensions(doDimensionRange.Sku, specialDimension.Sku)
	dimensionRange[enum.DimensionPayMode] = mergeDimensions(doDimensionRange.PayMode, specialDimension.PayMode)
	dimensionRange[enum.DimensionOperator] = mergeDimensions(doDimensionRange.Operator, specialDimension.Operator)
	dimensionRange[enum.DimensionSellOperator] = mergeDimensions(doDimensionRange.SellOperator, specialDimension.SellOperator)
	dimensionRange[enum.DimensionSellSite] = mergeDimensions(doDimensionRange.SellSite, specialDimension.SellSite)
	dimensionRange[enum.DimensionGroupMember] = mergeDimensions(doDimensionRange.GroupMember, specialDimension.GroupMember)
	return
}

func mergeDimensions(dimensions []int, templateDimensionItems []common.ChildItem) (mergeDimensions []int) {
	templateDimensions := make([]int, 0)
	for _, childItem := range templateDimensionItems {
		templateDimensions = append(templateDimensions, childItem.Id)
	}
	// 不填=全选
	if len(dimensions) == 0 {
		return templateDimensions
	}
	if len(templateDimensions) == 0 {
		return dimensions
	}
	// 取交集
	mergeDimensions = make([]int, 0)
	for _, dimension := range dimensions {
		if utils.Container(templateDimensions, dimension) {
			mergeDimensions = append(mergeDimensions, dimension)
		}
	}
	for _, dimension := range templateDimensions {
		if utils.Container(dimensions, dimension) {
			mergeDimensions = append(mergeDimensions, dimension)
		}
	}
	mergeDimensions = utils.RemoveDuplicate(mergeDimensions)
	// 如果没有交集，返回一个不可能存在的枚举值，使得结果为空（代码调整较小）
	if len(mergeDimensions) == 0 {
		return []int{-2 ^ 31}
	}
	return
}

// GetCommonSearchModelFilter 根据数据限制配置生成通用搜索模型的过滤条件。
// 此函数主要用于处理搜索时的排除条件，例如特定的产品ID列表。
// 参数:
//
//	dataLimit *datajobslimit.LimitSpuConfig: 数据限制配置，可能包含需要排除的产品ID列表。
//
// 返回值:
//
//	dm.CommonSearchModelFilter: 通用搜索模型的过滤条件，可能包括排除的产品ID列表。

func GetCommonSearchModelFilter(dataLimit *datajobslimit.LimitSpuConfig, doDimensionRange DoDimensionRange) (commonSearchFilter dm.CommonSearchModelFilter) {

	//排查部分产品
	if dataLimit != nil && len(dataLimit.NotIdList) > 0 {
		commonSearchFilter.NotSpuIds = dataLimit.NotIdList
	}
	//适用人群标签处理
	if doDimensionRange.TargetAudience != nil && len(doDimensionRange.TargetAudience) > 0 {
		commonSearchFilter.TargetAudience = doDimensionRange.TargetAudience
	}

	return
}
