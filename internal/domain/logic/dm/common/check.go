package common

import (
	"report-service/internal/domain/enum"
	"report-service/pkg/szerrors"

	"gitee.com/golang-module/carbon/v2"
)

func CheckReportTypeAndTimeRange(reportType int, startTime, endTime carbon.Carbon) (err error) {
	if startTime.IsZero() || endTime.IsZero() {
		return szerrors.NewInvalidParamErrorWithText("时间范围不能为空")
	}
	if startTime.Gt(endTime) {
		return szerrors.NewInvalidParamErrorWithText("开始时间不能大于结束时间")
	}
	switch reportType {
	case enum.ReportTypeDay:
		var yearDays int64 = 366
		//var yearDays int64 = 365
		//if startTime.IsLeapYear() || endTime.IsLeapYear() {
		//	yearDays = 366
		//}
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * yearDays) { // 按秒级别校验
			return szerrors.NewInvalidParamErrorWithText("日报表时间范围不能超过一年")
		}
	case enum.ReportTypeHour:
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * 14) { // 按秒级别校验
			return szerrors.NewInvalidParamErrorWithText("小时报表时间范围不能超过两周")
		}
	case enum.ReportTypeDetail:
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * 31) {
			return szerrors.NewInvalidParamErrorWithText("明细报表时间范围不能超过一个月")
		}
	case enum.ReportTypeRealTime:
		// 实时报表支持一个月前的1号至今的时间范围，但开始和结束时间不能超过7天
		oneMonthAgo := endTime.SubMonth().StartOfMonth()
		if startTime.Lt(oneMonthAgo) {
			return szerrors.NewInvalidParamErrorWithText("实时报表开始时间不能早于一个月前的1号")
		}
		if endTime.Gt(carbon.Now()) {
			return szerrors.NewInvalidParamErrorWithText("实时报表结束时间不能晚于当前时间")
		}
		// 开始和结束时间不能超过7天，校验精确到秒
		if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * 7) {
			return szerrors.NewInvalidParamErrorWithText("实时报表开始和结束时间不能超过7天")
		}
	default:
		return szerrors.NewInvalidParamErrorWithText("不支持的报表类型")
	}
	return
}
