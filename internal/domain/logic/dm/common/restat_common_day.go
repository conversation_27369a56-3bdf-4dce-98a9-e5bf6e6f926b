package common

import (
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"

	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
)

func RestatCommonDayYesterday() {
	_ = RestatCommonDay(
		carbon.Now().Yesterday().StartOfDay(),
		carbon.Now().Yesterday().EndOfDay(),
	)
}

func RestatCommonDay(startTime, endTime carbon.Carbon) error {
	statStartTime := carbon.Now()
	var err error
	defer func() {
		if r := recover(); r != nil {
			globalNotice.Error(fmt.Sprint("statistic common day error, panic", r))
			return
		}
		if err != nil {
			globalNotice.Error(fmt.Sprint("statistic common day error, ", err.Error()))
			return
		}
		global.LOG.Info(
			"statistic common day success",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	err = repository.ReportDmCommonDateRepository.DeleteWhereTimeRange(startTime, endTime, 0)
	if err != nil {
		return err
	}

	err = repository.ReportDmCommonDateRepository.InsertIntoSelectDmCommon(startTime, endTime, 0)
	if err != nil {
		return err
	}

	err = config.UpdateLatestTime(enum.ConfigKeyDmCommonDayLastStatisticTime, endTime)
	if err != nil {
		globalNotice.Error(fmt.Sprint("statistic common day error, set config error", err.Error()))
		return err
	}

	return nil
}
