package reportvisitsstatistics

import (
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm/customized"
	"report-service/pkg/utils"
)

// BatchSave 批量保存
func BatchSave(models []Model) error {
	insertItems := make([]customized.VisitsStatisticsDayModel, 0)
	err := utils.JsonConvertor(models, &insertItems)
	if err != nil {
		return err
	}
	if len(insertItems) == 0 {
		return nil
	}
	err = repository.ReportDmVisitsStatisticsDayRepository.Save(insertItems)
	if err != nil {
		return err
	}
	return nil
}
