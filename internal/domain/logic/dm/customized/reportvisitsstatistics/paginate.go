package reportvisitsstatistics

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/business"
	"report-service/internal/domain/repository/dm/customized"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/utils"
)

type RepoListItem struct {
	MerchantId     int    `json:"merchant_id"`
	Account        string `json:"account"`
	Name           string `json:"name"`
	OldPayCount    int    `json:"old_pay_count"`
	OldVerifyCount int    `json:"old_verify_count"`
	OldTotalCount  int    `json:"old_total_count"`
	NewPayCount    int    `json:"new_pay_count"`
	NewVerifyCount int    `json:"new_verify_count"`
	NewTotalCount  int    `json:"new_total_count"`
	IsNewReport    bool   `json:"is_new_report"`
}

type DoQueryParams struct {
	StartTime  carbon.Carbon `json:"start_time"`  // 开始时间
	EndTime    carbon.Carbon `json:"end_time"`    // 结束时间
	MerchantId *int          `json:"merchant_id"` // 商户ID
	OrderBy    *string       `json:"order_by"`
	PageNum    *int          `json:"page_num"`  // 页码
	PageSize   *int          `json:"page_size"` // 页大小
}

func Paginate(doQueryParams DoQueryParams) (list []RepoListItem, total int, err error) {
	if err = CheckTimeRange(doQueryParams.StartTime, doQueryParams.EndTime); err != nil {
		return
	}
	params := customized.CommonSearch{
		StartTime:  doQueryParams.StartTime,
		EndTime:    doQueryParams.EndTime,
		MerchantId: doQueryParams.MerchantId,
		OrderBy:    doQueryParams.OrderBy,
		PageNum:    doQueryParams.PageNum,
		PageSize:   doQueryParams.PageSize,
	}
	var poList []customized.VisitsStatisticsDayModel
	poList, total, err = repository.ReportDmVisitsStatisticsDayRepository.PageQuery(params)
	if err != nil {
		return
	}
	if total == 0 {
		return
	}
	merchantIds := make([]int, 0)
	for _, po := range poList {
		merchantIds = append(merchantIds, po.MerchantId)
	}
	//查询商户信息
	merchantIds = utils.RemoveDuplicate(merchantIds)
	MemberInfoMap, err := pftmember.GetMemberBaseInfoMapByIds(merchantIds)
	if err != nil {
		return
	}
	//查询新报表开通情况
	enableMerchantIds, err := getAccessWhiteListEnableMerchantIds(merchantIds)
	if err != nil {
		return
	}
	//组装返回数据
	list = make([]RepoListItem, 0, len(poList))
	for _, po := range poList {
		item := RepoListItem{
			MerchantId:     po.MerchantId,
			Name:           MemberInfoMap[po.MerchantId].Dname,
			Account:        MemberInfoMap[po.MerchantId].Account,
			IsNewReport:    utils.Container(enableMerchantIds, po.MerchantId),
			NewPayCount:    po.NewPayCount,
			NewVerifyCount: po.NewVerifyCount,
			OldPayCount:    po.OldPayCount,
			OldVerifyCount: po.OldVerifyCount,
			NewTotalCount:  po.NewPayCount + po.NewVerifyCount, //为避免总数差异，展示上重算总数
			OldTotalCount:  po.OldPayCount + po.OldVerifyCount, //为避免总数差异，展示上重算总数
		}
		list = append(list, item)
	}
	return list, total, nil
}

func getAccessWhiteListEnableMerchantIds(merchantIds []int) ([]int, error) {
	if len(merchantIds) == 0 {
		return nil, nil
	}
	status := enum.AccessWhiteListStatusEnabled
	pageNum := 1
	pageSize := len(merchantIds)
	params := business.CommonSearch{
		PageNum:  &pageNum,
		PageSize: &pageSize,
		Status:   &status,
	}
	if len(merchantIds) > 0 {
		params.MerchantIds = merchantIds
	}
	whiteListRecords, _, err := repository.BusinessAccessWhiteListRepository.GetList(params)
	if err != nil {
		return nil, err
	}
	ids := make([]int, 0)
	for _, record := range whiteListRecords {
		ids = append(ids, record.MerchantId)
	}
	return ids, nil

}
