package reportvisitsstatistics

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/pkg/utils"
)

type HandleDataItem struct {
	SerialNo   int         `json:"serial_no"`   // 流水号
	BusinessNo int         `json:"business_no"` // 业务编号
	MerchantID int         `json:"merchant_id"` // 商户ID
	MemberID   int         `json:"member_id"`   // 用户ID
	ModuleKey  string      `json:"module_key"`  // 模块标识
	IncrCnt    int         `json:"incr_cnt"`    // 增量数
	OperatedAt string      `json:"operated_at"` // 操作时间
	Extra      interface{} `json:"extra"`       // 增量数据
}

// HandleDataCleaner 访问量数据清理
func HandleDataCleaner(data []HandleDataItem) error {
	ModelsMap := make(map[string]Model)
	for _, item := range data {
		operatedAt := carbon.Parse(item.OperatedAt)
		merchantId := item.MerchantID
		uniqueKey := operatedAt.ToDateString() + "_" + cast.ToString(merchantId)
		record, ok := ModelsMap[uniqueKey]
		if !ok {
			record = Model{
				Year:           operatedAt.Year(),
				Month:          operatedAt.Month(),
				Date:           operatedAt.ToDateString(),
				MerchantId:     merchantId,
				NewPayCount:    0,
				NewVerifyCount: 0,
				OldPayCount:    0,
				OldVerifyCount: 0,
				NewTotalCount:  0,
				OldTotalCount:  0,
			}
		}
		record.FillIndicatorData(item)
		// 保存
		ModelsMap[uniqueKey] = record
	}
	err := BatchSave(utils.MapToSlice(ModelsMap))
	if err != nil {
		return err
	}
	return nil
}

// FillIndicatorData 填充指标数据
func (m *Model) FillIndicatorData(item HandleDataItem) {
	switch item.ModuleKey {
	case enum.OldReportPayPageModuleKey:
		m.OldPayCount += item.IncrCnt
		m.OldTotalCount += item.IncrCnt
	case enum.OldReportVerifyPageModuleKey:
		m.OldVerifyCount += item.IncrCnt
		m.OldTotalCount += item.IncrCnt
	case enum.NewReportPayPageModuleKey:
		m.NewPayCount += item.IncrCnt
		m.NewTotalCount += item.IncrCnt
	case enum.NewReportVerifyPageModuleKey:
		m.NewVerifyCount += item.IncrCnt
		m.NewTotalCount += item.IncrCnt
	}
}
