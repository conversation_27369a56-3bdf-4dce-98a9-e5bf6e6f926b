package paymodemoney

type DoDimension struct {
	DoDimensionIdNameField
}
type DoDimensionIdNameField struct {
	DataSource     *int    `json:"data_source,omitempty"`      // 数据来源
	DataSourceName *string `json:"data_source_name,omitempty"` // 数据来源名称
	ProjectID      *int    `json:"project_id,omitempty"`       // 项目
	ProjectIDName  *string `json:"project_id_name,omitempty"`  // 项目名称
	TimeType       *int    `json:"time_type,omitempty"`        // 时间类型
	TimeTypeName   *string `json:"time_type_name,omitempty"`   // 时间类型名称
}

type DoIndicator struct {
	EntranceCount               int `json:"entrance_count"`                // 入园人数
	TicketRevenue               int `json:"ticket_revenue"`                // 门票收入
	PrepaidConsumptionRevenue   int `json:"prepaid_consumption_revenue"`   // 二消收入(预付卡)
	TotalAmount                 int `json:"total_amount"`                  // 合计
	OperatingRevenue            int `json:"operating_revenue"`             // 营收
	DiscountAmount              int `json:"discount_amount"`               // 折扣
	AccountsReceivablePayment   int `json:"accounts_receivable_payment"`   // 挂账
	EntertainmentExpensePayment int `json:"entertainment_expense_payment"` // 招待
	CashPayment                 int `json:"cash_payment"`                  // 现金支付
	UnionPayPayment             int `json:"union_pay_payment"`             // 银联支付
	StoredValueCardPayment      int `json:"stored_value_card_payment"`     // 储值卡支付
	YinbaoPayPayment            int `json:"yinbao_pay_payment"`            // 银豹付支付
	RuralCommercialBankPayment  int `json:"rural_commercial_bank_payment"` // 农商行收银宝
	CreditPayment               int `json:"credit_payment"`                // 授信支付
	YibaoPayment                int `json:"yibao_payment"`                 // 易宝
	AlipayPayment               int `json:"alipay_payment"`                // 支付宝
	WechatPayment               int `json:"wechat_payment"`                // 微信
	PrepaidCardPayment          int `json:"prepaid_card_payment"`          // 预付卡
	OtherPayment                int `json:"other_payment"`                 // 其他
	MeituanCouponPayment        int `json:"meituan_coupon_payment"`        // 美团优惠券
	TotalIncome                 int `json:"total_income"`                  // 收入总计
}
