package paymodemoney

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/pkg/szerrors"
)

func CheckReportTypeAndTimeRange(startTime, endTime carbon.Carbon) (err error) {
	if startTime.IsZero() || endTime.IsZero() {
		return szerrors.NewInvalidParamErrorWithText("时间范围不能为空")
	}
	if startTime.Gt(endTime) {
		return szerrors.NewInvalidParamErrorWithText("开始时间不能大于结束时间")
	}
	if endTime.DiffAbsInSeconds(startTime) > (24 * 60 * 60 * 14) { // 按秒级别校验
		return szerrors.NewInvalidParamErrorWithText("报表时间范围不能超过两周")
	}
	return
}
