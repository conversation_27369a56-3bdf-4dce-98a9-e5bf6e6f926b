package paymodemoney

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	repoDm "report-service/internal/domain/repository/dm"
	"report-service/pkg/utils"
)

type DoPaginationItem struct {
	DoDimension
	DoIndicator
}

func AggregateData(
	merchantId int,
	startTime, endTime carbon.Carbon,
) (list []DoPaginationItem, err error) {
	timeGroupType := 1
	poList, err := repository.ReportDmPayModeMoneyRepository.AggregateData(repoDm.CommonSearch{
		MerchantId:    &merchantId,
		StartTime:     startTime,
		EndTime:       endTime,
		TimeGroupType: &timeGroupType,
		Dimensions: []string{
			enum.PayModeMoneyDimensionMerchantId,
			enum.PayModeMoneyDimensionDataSource,
			enum.PayModeMoneyDimensionProjectId,
			enum.PayModeMoneyDimensionTimeType,
		},
	})
	if err != nil {
		return
	}

	list = make([]DoPaginationItem, 0)
	for _, po := range poList {
		var doPaginationItem DoPaginationItem
		_ = utils.JsonConvertor(po, &doPaginationItem)
		dataSource := po.DataSource
		projectID := po.ProjectID
		timeType := po.TimeType
		var projectName, dataSourceName, timeTypeName string
		var exists bool
		// 数据源名称解析
		dataSourceName, exists = enum.PayModeMoneyDataSourceNameMap[dataSource]
		if !exists {
			continue
		}
		// 时间类型名称解析
		timeTypeName, exists = enum.PayModeMoneyTimeTypeNameMap[timeType]
		if !exists {
			continue
		}
		// 项目名称解析
		switch dataSource {
		case enum.PayModeMoneyDataSourceBusinessYb, enum.PayModeMoneyDataSourceCateringYb, enum.PayModeMoneyDataSourceRecharge:
			projectName, exists = enum.PayModeMoneyProjectIdNameMap[projectID]
			if !exists {
				// 默认使用项目ID
				projectName = cast.ToString(projectID)
			}
		default:
			projectName = cast.ToString(projectID)
		}
		// 名称解析赋值
		doPaginationItem.DataSource = &dataSource
		doPaginationItem.DataSourceName = &dataSourceName
		doPaginationItem.ProjectID = &projectID
		doPaginationItem.ProjectIDName = &projectName
		doPaginationItem.TimeType = &timeType
		doPaginationItem.TimeTypeName = &timeTypeName

		//加入到结果集
		list = append(list, doPaginationItem)
	}

	return list, err
}
