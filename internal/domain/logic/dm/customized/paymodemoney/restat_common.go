package paymodemoney

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
)

func RestatOutdatedItemHour(startTime, endTime carbon.Carbon, merchantId, projectId int) {
	var err error
	defer func() {
		messages := []string{
			"Start Time: " + startTime.ToDateTimeString(),
			"End Time: " + endTime.ToDateTimeString(),
			"Merchant Id: " + cast.ToString(merchantId),
		}
		if r := recover(); r != nil {
			messages = append([]string{fmt.Sprintf("statistic pay mode money hour outdated panic: %v", r)}, messages...)
			globalNotice.Error(messages...)
			return
		}
		if err != nil {
			messages = append([]string{fmt.Sprintf("statistic pay mode money hour outdated error: %s", err.Error())}, messages...)
			globalNotice.Error(messages...)
			return
		}
		global.LOG.Info("statistic pay mode money hour outdated data success", zap.Strings("messages", messages))
	}()
	err = repository.ReportDmPayModeMoneyRepository.DeleteWhereTimeRange(startTime, endTime, merchantId, projectId)
	if err != nil {
		return
	}
	err = repository.ReportDmPayModeMoneyRepository.InsertIntoSelectDmCommon(startTime, endTime, merchantId, projectId)
	if err != nil {
		return
	}
	//存在项目ID，则不更新全局统计时间
	if projectId != 0 {
		return
	}
	//更新统计时间
	err = config.UpdateLatestTimeByMerchant(enum.ConfigKeyDmPayModeMoneyLastStatisticTime, merchantId, endTime)
	if err != nil {
		globalNotice.Error(fmt.Sprint("statistic pay mode money hour error, set config error", err.Error()))
		return
	}
}
