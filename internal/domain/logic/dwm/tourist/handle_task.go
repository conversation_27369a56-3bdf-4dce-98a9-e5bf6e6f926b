package tourist

import (
	"encoding/json"
	"errors"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"math"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/dwm"
	"report-service/internal/domain/logic/config/merchantconfig"
	"report-service/internal/domain/logic/dwd/business"
	"report-service/internal/domain/logic/dwd/handlecommon"
	"report-service/internal/domain/logic/ods"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
	"report-service/internal/global"
	szkafka "report-service/internal/global/kafka"
	"report-service/internal/global/notice"
	"report-service/pkg/sdk/api/ordertouristtrackqueryservice"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
	"strings"
	"sync"
	"time"
)

const (
	PageChainSize        = 3
	PullPageSize         = 200
	pageSleepMaxSecond   = 600 //设置最大等待时间 10分钟 600秒
	pageSleepOtherSecond = 5   //设置额外等待时间 5秒
)

type OptionalParams struct {
	MerchantId int  `json:"merchant_id"`
	IsReStat   bool `json:"is_re_stat"`
}

func HandleTask() {
	HourOperateTask(
		carbon.Now().SubHours(1).StartOfHour(),
		carbon.Now().SubHours(1).EndOfHour(),
		OptionalParams{IsReStat: true},
	)
}

func OperateTask(startTime, endTime carbon.Carbon, optional ...interface{}) error {
	statStartTime := carbon.Now()
	defer func() {
		global.LOG.Info(
			"tourist operate task success.",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	params := getOptional(optional)
	for date := startTime; date.Lte(endTime); date = date.AddHours(1) {
		HourOperateTask(date.StartOfHour(), date.EndOfHour(), params)
	}
	return nil
}

func HourOperateTask(startTime, endTime carbon.Carbon, params OptionalParams) {
	statStartTime := carbon.Now()
	var err error
	defer func() {
		if r := recover(); r != nil {
			notice.Error(fmt.Sprint("客源地报表小时汇总执行失败, panic", r))
			return
		}
		if err != nil {
			errMsg := fmt.Sprintf("客源地报表小时汇总执行失败，执行时间：%s~%s， 错误信息：%s",
				startTime.ToDateString(),
				endTime.ToDateString(),
				err.Error())
			//日志记录
			global.LOG.Error(errMsg)
			//推送消息预警
			notice.Error(errMsg)
		}

		global.LOG.Info(
			"tourist hour operate task success.",
			zap.String("start_time", startTime.ToDateTimeString()),
			zap.String("end_time", endTime.ToDateTimeString()),
			zap.Float64("duration", statStartTime.DiffAbsInDuration().Seconds()),
		)
	}()
	key := module.GetModuleConfigKey(enum.ModuleTagTouristSourceArea)
	pageNum := 1
	pageSize := 10
	runTotalNum := 0
	for {
		var list []merchantconfig.MerchantConfig
		list, err = merchantconfig.PaginateByKey(key, pageNum, pageSize)
		if err != nil {
			err = errors.New(fmt.Sprintf("获取商户配置失败, %s", err.Error()))
			return
		}
		if len(list) == 0 {
			break
		}
		var merchantIds []int
		for _, item := range list {
			value := touristsourceareamodule.GetTouristSourceAreaConfigItem(item.Payload)
			if value == nil {
				continue
			}
			if params.MerchantId > 0 && params.MerchantId != item.MerchantId {
				continue
			}
			//还在有效期内，且账号数据未被清除
			if endTime.Lte(carbon.Parse(value.EndTime)) && startTime.Gte(carbon.Parse(value.StartTime)) && !value.IsCleanUp {
				merchantIds = append(merchantIds, item.MerchantId)
			}
		}
		if len(merchantIds) > 0 {
			total := 0
			err, total = HandleMerchantHourOperate(startTime, endTime, merchantIds)
			if err != nil {
				err = errors.New(fmt.Sprintf("执行商户小时数据清洗异常, %s", err.Error()))
				return
			}
			if total > 0 {
				runTotalNum = runTotalNum + total
			}
		}
		pageNum += 1
	}

	if runTotalNum > 0 && enum.EnvGray != global.CONFIG.System.Env && params.IsReStat {
		pageSleep := int(math.Ceil(float64(runTotalNum)/float64(PullPageSize)))*10 + pageSleepOtherSecond
		if (pageSleep - pageSleepMaxSecond) > pageSleepOtherSecond {
			notice.Warning(fmt.Sprintf("游客明细完成事件等待推送，等待时间超过限制 %d 秒， 实际 %d 秒，请留意数据完整性！", pageSleepMaxSecond, pageSleep))
			pageSleep = pageSleepMaxSecond
		}
		global.LOG.Info(fmt.Sprintf("游客明细完成事件等待推送，等待时间 %d 秒", pageSleep))
		//等待时间
		time.Sleep(time.Second * time.Duration(pageSleep))
		//推送完成事件
		_ = szkafka.Send(dwm.TopicEventTouristComplete, "tourist:"+cast.ToString(carbon.Now().Timestamp()), dwm.EventTouristComplete{
			MerchantId: &params.MerchantId,
			StartAt:    startTime.ToDateTimeString(),
			EndAt:      endTime.ToDateTimeString(),
		})
	}
}

func HandleMerchantHourOperate(startTime, endTime carbon.Carbon, merchantIds []int) (error, int) {
	pageSize := PullPageSize
	_, total, err := getTouristTrackList(startTime, endTime, merchantIds, 1, pageSize)
	if err != nil {
		return err, 0
	}
	if total == 0 {
		return nil, 0
	}
	totalPage := int(math.Ceil(float64(total) / float64(pageSize)))
	sem := make(chan struct{}, PageChainSize)
	var wg sync.WaitGroup
	errChan := make(chan error, totalPage)
	for page := 1; page <= totalPage; page++ {
		wg.Add(1)
		sem <- struct{}{}
		go func(page int) {
			defer func() {
				wg.Done()
				<-sem
			}()
			list, _, resErr := getTouristTrackList(startTime, endTime, merchantIds, page, pageSize)
			if resErr != nil {
				errChan <- szerrors.NewLogicError(resErr)
				return
			}
			// 处理当前页面的数据
			processErr := processTouristTrackList(list)
			if processErr != nil {
				errChan <- szerrors.NewLogicError(processErr)
			}
		}(page)
	}

	// 等待所有goroutine完成并检查错误
	wg.Wait()
	close(errChan)

	err = checkErrors(errChan)

	return err, total
}

func processTouristTrackList(list []ordertouristtrackqueryservice.TouristTrackListResponse) error {
	var err error
	if len(list) == 0 {
		return nil
	}

	//前置过滤清洗
	data, cancelOrderList, orderIds, skuIds, trackIds := preWashData(list)
	if len(data) == 0 {
		return nil
	}

	//批量获取业务操作编号
	businessNoMap := ods.BatchCreateBusinessOperationNumber(enum.BusinessOperationNumberTypeTouristTrack, trackIds)

	//获取消操作的订单号对应的有效出票记录
	payRecordIdMap := make(map[string]int)
	if len(cancelOrderList) > 0 {
		payRecordIdMap, err = getTouristTrackPayIdByOrderNos(cancelOrderList)
		if err != nil {
			return err
		}
	}

	//批量获取订单号
	orderInfoMap := make(map[string]ods.OrderInfoData)
	orderInfoMap, err = ods.OrderInfoByOrderNos(orderIds)
	if err != nil {
		return err
	}

	//数据再次清洗
	dataAgain, mainOrderNos := againWashData(data, orderInfoMap)
	if len(dataAgain) == 0 {
		return nil
	}

	//批量获取主订单信息
	mainOrderInfoMap, mainSkuIds, mainOrderInfoErr := getMainOrderInfo(mainOrderNos)
	if mainOrderInfoErr != nil {
		return mainOrderInfoErr
	}

	skuIds = utils.MergeSlice(skuIds, mainSkuIds)
	spuDateMap := make(map[int]ods.SpuInfoItem)
	spuDateMap, err = ods.SpuInfoBySkuIds(skuIds)
	if err != nil {
		return errors.New(fmt.Sprintf("spu info error. info:%s", err.Error()))
	}
	// 数据组装和写入
	var insetData []Model
	for _, po := range dataAgain {
		orderInfo := orderInfoMap[po.OrderTouristTrack.OrderNum]

		spuInfo, spuExist := spuDateMap[orderInfo.SpuId]
		if !spuExist {
			continue
		}

		businessNo, businessNoExist := businessNoMap[po.OrderTouristTrack.Id]
		if !businessNoExist {
			global.LOG.Error(fmt.Sprintf("business no is error. track_id:%d", po.OrderTouristTrack.Id))
			continue
		}

		//游客取消操作需要判断是不是支付前
		if checkCancelBeforePay(po.OrderTouristTrack, payRecordIdMap) {
			continue
		}

		//套票信息
		var mainSpuId = 0    //主票ID
		var mainOrderNo = "" //主票订单号
		if orderInfo.OrderMode == 23 {
			if orderInfo.Addon.PackOrder != "" && orderInfo.Addon.PackOrder != "1" {
				mainOrderNo = orderInfo.Addon.PackOrder
				mainOrderInfo, mainOrderInfoExist := mainOrderInfoMap[mainOrderNo]
				if mainOrderInfoExist {
					mainSpuId = mainOrderInfo.SpuId
				}
			}
		}

		//子票订单标记主票
		mainSpuInfo, mainSpuExist := spuDateMap[mainSpuId]
		mainApplyDid := 0
		mainProductType := ""
		if mainSpuExist {
			mainApplyDid = mainSpuInfo.ApplyDid
			mainProductType = mainSpuInfo.PType
		}

		//一级分销商
		distributorId := GetFirstDistributorId(orderInfo)

		//产品特殊规则
		productRulesParams := handlecommon.ProductRulesParams{
			SaleChannel:         orderInfo.OrderMode,
			ProductType:         spuInfo.PType,
			SubType:             spuInfo.SubType,
			IfPack:              orderInfo.Addon.IfPack,
			ParentOrderNo:       mainOrderNo,
			ParentProductType:   mainProductType,
			ParentOrderApplyDid: mainApplyDid,
			MerchantId:          po.OrderTouristTrack.ApplyDid,
		}
		productRules := productRulesParams.HandleProductRules()

		//指标格式化
		count := formatNumberWithSign(po.OrderTouristTrack.TrackAction, po.OrderTouristTrack.Tnum)

		//数据组装
		insetItem := Model{
			BusinessNo:    businessNo,
			SpuId:         spuInfo.Id,
			Count:         count,
			DistributorId: distributorId,
		}
		//基础数据填充
		insetItem.fillCommonFields(po, productRules, orderInfo)
		//可能为空的数据填充
		insetItem.fillIfNullFields(po)
		//合并数据
		insetData = append(insetData, insetItem)
		//写入数据
		insetData, err = ResetInsertDataIfFull(insetData, false)
		if err != nil {
			continue
		}
	}

	//剩余数据写入
	_, err = ResetInsertDataIfFull(insetData, true)
	if err != nil {
		return err
	}

	return nil
}

func getOptional(optional []interface{}) OptionalParams {
	params := OptionalParams{}
	if len(optional) > 0 {
		params.MerchantId = cast.ToInt(optional[0])
	}
	if len(optional) > 1 {
		params.IsReStat = cast.ToBool(optional[1])
	}
	return params
}

func getTouristTrackList(
	startTime, endTime carbon.Carbon,
	merchantIds []int, page int, size int) (response []ordertouristtrackqueryservice.TouristTrackListResponse, total int, err error) {
	trackActionList := getTouristTrackAction()
	result, err := ordertouristtrackqueryservice.QueryCompositeTouristTrackByParam(
		trackActionList,
		merchantIds,
		startTime.Timestamp(),
		endTime.Timestamp(),
		page,
		size,
	)
	if err != nil {
		return
	}
	total = result.Total
	response = result.List
	return
}

func getTouristTrackAction() []int {
	maps := enum.TouristTrackActionToOperateTypeMap
	keys := make([]int, 0, len(maps))
	for k := range maps {
		keys = append(keys, k)
	}
	return keys
}

func checkTouristTrackItem(response ordertouristtrackqueryservice.TouristTrackListResponse) bool {
	if response.OrderTouristTrack == nil || response.OrderTouristInfo == nil {
		return false
	}
	maps := enum.TouristTrackActionToOperateTypeMap
	if _, ok := maps[response.OrderTouristTrack.TrackAction]; !ok {
		return false
	}
	//过滤非首次入园，不存在，默认是首次入园
	if response.OrderTouristTrack.TrackAction == enum.TouristTrackActionVerify {
		var extContent ordertouristtrackqueryservice.TouristTrackListOrderTouristTrackExtContent
		_ = json.Unmarshal([]byte(response.OrderTouristTrack.ExtContent), &extContent)
		if extContent.IsFirstUsed != nil && *extContent.IsFirstUsed != 1 {
			return false
		}
	}
	return true
}

func getTouristTrackPayIdByOrderNos(orderNums []string) (result map[string]int, err error) {
	result = make(map[string]int)
	result, err = ordertouristtrackqueryservice.QueryOrderTrackIdByOrderNos(
		orderNums,
		enum.TouristTrackActionPay,
	)
	if err != nil {
		return
	}
	return
}

func checkOrderPay(payStatus int, orderNo string) bool {
	if payStatus == 2 {
		global.LOG.Error(fmt.Sprintf("订单未支付，单号：%s", orderNo))
		return false
	}

	return true
}

func checkCancelBeforePay(
	orderTouristTrack *ordertouristtrackqueryservice.TouristTrackListOrderTouristTrackResponse,
	payRecordIdMap map[string]int,
) bool {
	if orderTouristTrack.TrackAction == enum.TouristTrackActionCancel {
		//游客取消操作需要判断是不是支付前
		orderIdxKey := orderTouristTrack.GetOrderIdxKey()
		if payRecordId, ok := payRecordIdMap[orderIdxKey]; ok {
			if payRecordId > orderTouristTrack.Id {
				return true
			}
		} else {
			//不存在，不记录
			return true
		}
	}
	return false
}

func formatNumberWithSign(operateType int, number int) int {
	switch operateType {
	case enum.TouristTrackActionPay, enum.TouristTrackActionVerify:
		return number
	case enum.TouristTrackActionCancel, enum.TouristTrackActionRevoke, enum.TouristTrackActionAfterSale:
		if number > 0 {
			number = number * -1
		}
	}
	return number
}

func (m *Model) fillCommonFields(po ordertouristtrackqueryservice.TouristTrackListResponse, productRules business.CommonPayloadProductRules, orderInfo ods.OrderInfoData) {
	m.OperatedAt = po.OrderTouristTrack.GetChangeTime().ToDateTimeString()
	m.MerchantId = po.OrderTouristTrack.ApplyDid
	m.SortKey = po.OrderTouristTrack.GetChangeTime().ToTimestampStruct().String() + cast.ToString(m.BusinessNo)
	m.OrderNo = po.OrderTouristTrack.OrderNum
	m.SkuId = po.OrderTouristTrack.Tid
	m.OperateType = enum.TouristTrackActionToOperateTypeMap[po.OrderTouristTrack.TrackAction]
	m.PackType = productRules.PackType
	m.ShowBindType = productRules.ShowBindType
	m.AnnualCardType = productRules.AnnualCardType
	m.ExchangeCouponType = productRules.ExchangeCouponType
	m.BusinessIdx = po.OrderTouristTrack.Idx
	m.BusinessCode = po.OrderTouristInfo.ChkCode
	m.IdNumber = po.OrderTouristInfo.IdCard
	m.IdType = po.OrderTouristInfo.VoucherType
	m.OperatorId = po.OrderTouristTrack.OperMember
	m.OperateSiteId = po.OrderTouristTrack.SalerId
	m.OperateChannel = po.OrderTouristTrack.Source
	m.OrderChannel = orderInfo.OrderMode
	m.Nickname = po.OrderTouristInfo.Tourist
	m.Mobile = po.OrderTouristInfo.Mobile

}

func (m *Model) fillIfNullFields(po ordertouristtrackqueryservice.TouristTrackListResponse) {
	//年龄处理
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Age == nil {
		m.Age = enum.AgeUnknownKey
	} else {
		m.Age = cast.ToInt(*po.OrderTouristExtend.ExtInfo.Age)
	}
	//性别处理
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Sex == nil {
		m.Gender = enum.GenderUnknownKey
	} else {
		m.Gender = cast.ToInt(*po.OrderTouristExtend.ExtInfo.Sex)
	}
	//地域
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Area.Region == nil {
		m.Region = cast.ToString(enum.CommonUnknownKey)
	} else {
		m.Region = *po.OrderTouristExtend.ExtInfo.Area.Region
	}
	//国家
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Area.Country == nil {
		m.Country = cast.ToString(enum.CommonUnknownKey)
	} else {
		m.Country = *po.OrderTouristExtend.ExtInfo.Area.Country
	}
	//省
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Area.Province == nil {
		m.Province = cast.ToString(enum.CommonUnknownKey)
	} else {
		m.Province = *po.OrderTouristExtend.ExtInfo.Area.Province
	}
	//市
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Area.City == nil {
		m.City = cast.ToString(enum.CommonUnknownKey)
	} else {
		m.City = *po.OrderTouristExtend.ExtInfo.Area.City
	}
	//区县
	if po.OrderTouristExtend == nil || po.OrderTouristExtend.ExtInfo.Area.District == nil {
		m.District = cast.ToString(enum.CommonUnknownKey)
	} else {
		m.District = *po.OrderTouristExtend.ExtInfo.Area.District
	}
}

func preWashData(list []ordertouristtrackqueryservice.TouristTrackListResponse) (
	data []ordertouristtrackqueryservice.TouristTrackListResponse,
	cancelOrderList []string,
	orderIds []string,
	skuIds []int,
	trackIds []int,
) {
	for _, response := range list {
		if !checkTouristTrackItem(response) {
			continue
		}
		maps := enum.TouristTrackActionToOperateTypeMap
		if operateType, ok := maps[response.OrderTouristTrack.TrackAction]; ok && operateType == enum.DWMOperateTypeCancel {
			cancelOrderList = append(cancelOrderList, response.OrderTouristTrack.OrderNum)
		}
		data = append(data, response)
		orderIds = append(orderIds, response.OrderTouristTrack.OrderNum)
		skuIds = append(skuIds, response.OrderTouristTrack.Tid)
		trackIds = append(trackIds, response.OrderTouristTrack.Id)
	}
	return
}

func againWashData(
	list []ordertouristtrackqueryservice.TouristTrackListResponse,
	orderInfoMap map[string]ods.OrderInfoData,
) (
	dataAgain []ordertouristtrackqueryservice.TouristTrackListResponse,
	mainOrderNos []string,
) {
	for _, datum := range list {
		orderInfo, orderInfoExist := orderInfoMap[datum.OrderTouristTrack.OrderNum]
		if !orderInfoExist {
			continue
		}
		// 订单未支付
		if !checkOrderPay(orderInfo.PayStatus, datum.OrderTouristTrack.OrderNum) {
			continue
		}
		// 套票主票订单号收集
		if orderInfo.OrderMode == 23 {
			if orderInfo.Addon.PackOrder != "" && orderInfo.Addon.PackOrder != "1" {
				mainOrderNos = append(mainOrderNos, orderInfo.Addon.PackOrder)
			}
		}
		dataAgain = append(dataAgain, datum)
	}
	return
}

func getMainOrderInfo(mainOrderNos []string) (
	mainOrderInfoMap map[string]ods.OrderInfoData,
	mainSkuIds []int,
	err error,
) {
	if len(mainOrderNos) <= 0 {
		return
	}
	//批量获取主订单信息
	mainOrderInfoMap, err = ods.OrderInfoByOrderNos(mainOrderNos)
	if err != nil {
		err = errors.New(fmt.Sprintf("main order info error. info:%s", err.Error()))
		return
	}
	for _, mainOrderInfo := range mainOrderInfoMap {
		mainSkuIds = append(mainSkuIds, mainOrderInfo.SkuId)
	}
	return
}

func checkErrors(errChan <-chan error) error {
	for err := range errChan {
		if err != nil {
			return err
		}
	}
	return nil
}

func GetFirstDistributorId(orderInfo ods.OrderInfoData) int {
	distributorId := 0
	if orderInfo.FxDetails.Aids != "" {
		aids := strings.Split(orderInfo.FxDetails.Aids, ",")
		distributorId = orderInfo.Member
		if len(aids) > 0 {
			distributorId = cast.ToInt(aids[1])
		}
	} else {
		distributorId = orderInfo.Member
	}
	return distributorId
}
