package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

type DoFieldsFilter struct {
	Region            *string    `json:"region"`              // 地域编号
	Country           *string    `json:"country"`             // 国家编号
	Province          *string    `json:"province"`            // 省编号
	City              *string    `json:"city"`                // 市编号
	District          *string    `json:"district"`            // 区县编号
	Gender            *int       `json:"gender"`              // 性别类型
	Age               *int       `json:"age"`                 // 年龄
	AgeGroupId        *int       `json:"age_group_id"`        //年龄段分组
	OperatedTimeRange *TimeRange `json:"operated_time_range"` // 操作时间
	Operator          *int       `json:"operator"`            // 操作人员
	OperateType       *int       `json:"operate_type"`        // 操作类型
	OperateSite       *int       `json:"operate_site"`        // 操作站点
	OrderNo           *string    `json:"order_no"`            // 订单号
	BusinessCode      *string    `json:"business_code"`       // 门票码
	Spu               *int       `json:"spu"`                 // 产品
	Sku               *int       `json:"sku"`                 // 票种
	SaleChannel       *int       `json:"sale_channel"`        // 订单渠道
	Nickname          *string    `json:"nickname"`            // 游客姓名
	Mobile            *string    `json:"mobile"`              // 手机号
	IdType            *int       `json:"id_type"`             // 证件类型
	IdNumber          *string    `json:"id_number"`           // 证件号
}

type TimeRange struct {
	StartTime *string `json:"start_time"` // 开始时间
	EndTime   *string `json:"end_time"`   //结束时间
}

type DoQueryParams struct {
	DoTemplate         template.DoListItem          `json:"do_template"`
	MerchantId         int                          `json:"merchant_id"`
	StartTime          carbon.Carbon                `json:"start_time"`
	EndTime            carbon.Carbon                `json:"end_time"`
	DimensionRange     dmcommon.DoDimensionRange    `json:"dimension_range"`
	FieldsFilter       DoFieldsFilter               `json:"fields_range"`
	DataLimit          datajobslimit.LimitSpuConfig `json:"data_limit"`
	Fields             []string                     `json:"fields"`
	RequireCount       bool                         `json:"with_total"`
	RequirePrevSortKey bool                         `json:"require_prev_sort_key"`
}

type ResultExt struct {
	Total       *int    `json:"total,omitempty"`
	PrevSortKey *string `json:"prev_sort_key"`
}

type PaginateResult struct {
	List        []Model    `json:"list"`
	NextSortKey string     `json:"next_sort_key"`
	Ext         *ResultExt `json:"ext"`
}

type FieldFilterHandler struct {
	FieldInt          *int
	FieldArrayInt     *[]int
	FieldString       *string
	FieldArrayString  *[]string
	DimensionKey      string
	FilterFieldInt    *[]int
	FilterFieldString *[]string
	IsArrayFilter     bool // 是否数组匹配（使用Intersection）
}

type TouristStatistics struct {
	// 指标
	Count int `json:"count"`
}
