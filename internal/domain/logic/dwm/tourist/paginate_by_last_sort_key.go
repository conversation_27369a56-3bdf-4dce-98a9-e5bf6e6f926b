package tourist

import (
	"report-service/internal/domain/enum"
	dmtourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/internal/domain/repository/dwm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func PaginateByLastSortKey(
	params DoQueryParams,
	lastSortKey *string,
	pageSize int,
) (result *PaginateResult, err error) {
	result = &PaginateResult{}

	var filter *dwm.TouristFilterFields
	filter, err = BuildTouristFilter(params)
	if err != nil {
		return
	}
	result.Ext = &ResultExt{}
	//获取总条数
	if params.RequireCount {
		total := 0
		total, err = repository.ReportDwmTouristRepository.PaginateCount(*filter)
		if err != nil || total == 0 {
			return
		}
		result.Ext.Total = &total
	}

	//获取上一页last标识
	if params.RequirePrevSortKey && lastSortKey != nil && *lastSortKey != "" {
		prevSortKey := ""
		prevSortKey, err = repository.ReportDwmTouristRepository.GetPrevSortKey(*filter, lastSortKey, pageSize)
		if err != nil {
			return
		}
		result.Ext.PrevSortKey = &prevSortKey
	}

	//获取明细数据
	var poList []dwm.TouristModel
	nextSortKey := ""
	poList, nextSortKey, err = repository.ReportDwmTouristRepository.PaginateByLastSortKey(*filter, lastSortKey, pageSize)
	if err != nil {
		return
	}
	err = utils.JsonConvertor(poList, &result.List)
	if err != nil {
		return
	}
	result.NextSortKey = nextSortKey
	return
}

// BuildTouristFilter 构建过滤条件
func BuildTouristFilter(params DoQueryParams) (*dwm.TouristFilterFields, error) {
	//获取数据岗位限制产品和查询产品取交集
	params.DimensionRange.Spu = params.DataLimit.GetIdList(params.DimensionRange.Spu)

	// 解析、合并模版维度范围和用户维度范围
	filterFieldsRange, filterStringFieldsRange := dmtourist.GetDimensionRange(params.DimensionRange, params.DoTemplate.SpecialDimension)

	//操作类型
	operateTypes := dmtourist.GetOperateTypes(params.DoTemplate)
	filterFieldsRange[enum.TemplateDefinitionOperateType] = operateTypes

	//年龄段
	filterAges, err := dmtourist.GetAgeGroupDimensionScheme(params.DoTemplate, params.MerchantId, params.FieldsFilter.AgeGroupId)
	if err != nil {
		return nil, err
	}
	if filterAges != nil {
		filterFieldsRange[enum.DimensionAgeGroup] = *filterAges
	}

	//特殊产品规则
	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err = utils.JsonConvertor(params.DoTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return nil, err
	}

	filter := dwm.TouristFilterFields{
		OperateTypes:        operateTypes,
		OperatedAtStart:     &params.StartTime,
		OperatedAtEnd:       &params.EndTime,
		MerchantId:          &params.MerchantId,
		PackTypes:           commonSearchModelSpecialProductRule.PackType,
		ShowBindTypes:       commonSearchModelSpecialProductRule.ShowBindType,
		AnnualCardTypes:     commonSearchModelSpecialProductRule.AnnualCardType,
		ExchangeCouponTypes: commonSearchModelSpecialProductRule.ExchangeCouponType,
		OperateSiteIds:      []int{},
		OrderNos:            []string{},
		BusinessCodes:       []string{},
		Nicknames:           []string{},
		Mobiles:             []string{},
		IdTypes:             []int{},
		IdNumbers:           []string{},
		RegionIds:           []string{},
		CountryIds:          []string{},
		ProvinceIds:         []string{},
		CityIds:             []string{},
		DistrictIds:         []string{},
		Ages:                []int{},
		Genders:             []int{},
		SpuIds:              []int{},
		SkuIds:              []int{},
		OperatorIds:         []int{},
	}

	//单独过滤字段处理
	SetFieldsFilters(&filter, params)
	//过滤操作时间范围处理
	SetFieldsOperatedAtFilters(&filter, params)
	//单独过滤字段和维度过滤字段处理
	SetFieldsAndDimensionRangeFilters(&filter, params, filterFieldsRange, filterStringFieldsRange)

	return &filter, nil
}
