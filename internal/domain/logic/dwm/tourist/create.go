package tourist

import (
	"go.uber.org/zap"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwm"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

const (
	InsertThreshold = 1000
)

func BatchCreate(models []Model) error {
	insertItems := make([]dwm.TouristModel, 0)
	err := utils.JsonConvertor(models, &insertItems)
	if err != nil {
		return err
	}
	if len(insertItems) == 0 {
		return nil
	}
	err = repository.ReportDwmTouristRepository.Insert(insertItems)
	if err != nil {
		return err
	}
	global.LOG.Debug("Insert DWMTourist success.", zap.Int("count", len(insertItems)))
	return nil
}

func ResetInsertDataIfFull(insetData []Model, isLast bool) ([]Model, error) {
	if len(insetData) == 0 {
		return []Model{}, nil
	}
	batchCreateAndReturnEmpty := func(data []Model) ([]Model, error) {
		err := BatchCreate(data)
		if err != nil {
			return data, err
		}
		return []Model{}, nil
	}
	// 最后一片，直接写入
	if isLast {
		return batchCreateAndReturnEmpty(insetData)
	}
	// 数据达到阈值，写入
	if len(insetData) >= InsertThreshold {
		return batchCreateAndReturnEmpty(insetData)
	}
	// 数据未达阈值，直接返回
	return insetData, nil
}
