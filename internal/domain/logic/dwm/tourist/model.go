package tourist

type Model struct {
	//操作唯一信息
	OperatedAt string `json:"operated_at"`
	MerchantId int    `json:"merchant_id"`
	BusinessNo int    `json:"business_no"`
	SortKey    string `json:"sort_key"`
	OrderNo    string `json:"order_no"`
	//维度
	Region             string `json:"region"`   //地域
	Country            string `json:"country"`  //国家
	Province           string `json:"province"` //省
	City               string `json:"city"`     //市
	District           string `json:"district"` //区
	Age                int    `json:"age"`
	Gender             int    `json:"gender"`
	PoiId              int    `json:"poi_id"`
	SpuId              int    `json:"spu_id"`
	SkuId              int    `json:"sku_id"`
	DistributorId      int    `json:"distributor_id"`
	OperateType        int    `json:"operate_type"`
	PackType           int    `json:"pack_type"`
	ShowBindType       int    `json:"show_bind_type"`
	AnnualCardType     int    `json:"annual_card_type"`
	ExchangeCouponType int    `json:"exchange_coupon_type"`
	BusinessIdx        int    `json:"business_idx"`
	BusinessCode       string `json:"business_code"`
	IdNumber           string `json:"id_number"`
	IdType             int    `json:"id_type"`
	OperatorId         int    `json:"operator_id"`
	OperateSiteId      int    `json:"operate_site_id"`
	OperateChannel     int    `json:"operate_channel"`
	OrderChannel       int    `json:"order_channel"`
	Nickname           string `json:"nickname"`
	Mobile             string `json:"mobile"`
	//指标
	Count int `json:"count"`
}
