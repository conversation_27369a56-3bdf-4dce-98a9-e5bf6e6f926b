package tourist

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"math"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository/dwm"
	"report-service/pkg/utils"
)

func SetFieldsFilters(filter *dwm.TouristFilterFields, params DoQueryParams) {
	//排查部分产品
	if params.DataLimit.NotIdList != nil {
		filter.NotSpuIds = params.DataLimit.NotIdList
	}
	//操作站点
	if params.FieldsFilter.OperateSite != nil {
		filter.OperateSiteIds = []int{*params.FieldsFilter.OperateSite}
	}
	//订单号
	if params.FieldsFilter.OrderNo != nil {
		filter.OrderNos = []string{*params.FieldsFilter.OrderNo}
	}
	//门票码
	if params.FieldsFilter.BusinessCode != nil {
		filter.BusinessCodes = []string{*params.FieldsFilter.BusinessCode}
	}
	//游客名称
	if params.FieldsFilter.Nickname != nil {
		filter.Nicknames = []string{*params.FieldsFilter.Nickname}
	}
	//游客手机号
	if params.FieldsFilter.Mobile != nil {
		filter.Mobiles = []string{*params.FieldsFilter.Mobile}
	}
	//游客证件类型
	if params.FieldsFilter.IdType != nil {
		filter.IdTypes = []int{*params.FieldsFilter.IdType}
	}
	//游客证件号
	if params.FieldsFilter.IdNumber != nil {
		filter.IdNumbers = []string{*params.FieldsFilter.IdNumber}
	}
	//性别
	if params.FieldsFilter.Gender != nil {
		filter.Genders = []int{*params.FieldsFilter.Gender}
	}
	return
}

func SetFieldsOperatedAtFilters(filter *dwm.TouristFilterFields, params DoQueryParams) {
	//选择的时间范围
	filter.OperatedAtStart = &params.StartTime
	filter.OperatedAtEnd = &params.EndTime
	if params.FieldsFilter.OperatedTimeRange == nil {
		return
	}
	//对比搜索的操作时间范围
	startTime := params.FieldsFilter.OperatedTimeRange.StartTime
	endTime := params.FieldsFilter.OperatedTimeRange.EndTime
	if startTime == nil || endTime == nil {
		return
	}
	start := carbon.Parse(*startTime)
	end := carbon.Parse(*endTime)
	//start和end是否包含在时间范围内
	if start.BetweenIncludedBoth(params.StartTime, params.EndTime) && end.BetweenIncludedBoth(params.StartTime, params.EndTime) && start.Lte(end) {
		//包含则使用搜索的时间范围
		filter.OperatedAtStart = &start
		filter.OperatedAtEnd = &end
	}
	return
}

func SetFieldsAndDimensionRangeFilters(filter *dwm.TouristFilterFields, params DoQueryParams, filterFieldsRange map[string][]int, filterStringFieldsRange map[string][]string) {
	//int字段
	handleSingleFilter(params.FieldsFilter.SaleChannel, filterFieldsRange, enum.DimensionSaleChannel, &filter.SaleChannels)
	handleSingleFilter(params.FieldsFilter.Spu, filterFieldsRange, enum.DimensionSpu, &filter.SpuIds)
	handleSingleFilter(params.FieldsFilter.Sku, filterFieldsRange, enum.DimensionSku, &filter.SkuIds)
	handleSingleFilter(params.FieldsFilter.Operator, filterFieldsRange, enum.DimensionOperator, &filter.OperatorIds)
	handleSingleFilter(params.FieldsFilter.Age, filterFieldsRange, enum.DimensionAgeGroup, &filter.Ages)
	handleSingleFilter(params.FieldsFilter.OperateType, filterFieldsRange, enum.TemplateDefinitionOperateType, &filter.OperateTypes)

	//string字段
	handleStringFilter(params.FieldsFilter.Region, filterStringFieldsRange, enum.DimensionRegion, &filter.RegionIds)
	handleStringFilter(params.FieldsFilter.Country, filterStringFieldsRange, enum.DimensionCountry, &filter.CountryIds)
	handleStringFilter(params.FieldsFilter.Province, filterStringFieldsRange, enum.DimensionProvince, &filter.ProvinceIds)
	handleStringFilter(params.FieldsFilter.City, filterStringFieldsRange, enum.DimensionCity, &filter.CityIds)
	handleStringFilter(params.FieldsFilter.District, filterStringFieldsRange, enum.DimensionDistrict, &filter.DistrictIds)
	return
}

func handleSingleFilter(field *int, filterFieldsRange map[string][]int, fieldKey string, filterVal *[]int) {
	val, ok := filterFieldsRange[fieldKey]
	//单独字段查询不存在，则使用维度字段过滤
	if field == nil {
		if ok && len(val) > 0 {
			*filterVal = val
		}
		return
	}
	//单独字段查询存在，维度过滤不存在，则使用单独字段过滤
	if !ok || len(val) == 0 {
		*filterVal = []int{*field}
		return
	}
	//单独字段过滤存在，维度过滤存在，则判断是否包含
	if utils.Container(val, *field) {
		*filterVal = []int{*field}
	} else {
		*filterVal = []int{math.MinInt32}
	}
	return
}

func handleStringFilter(field *string, filterStringFieldsRange map[string][]string, fieldKey string, filterVal *[]string) {
	val, ok := filterStringFieldsRange[fieldKey]
	//单独字段查询不存在，则使用维度字段过滤
	if field == nil || *field == "" {
		if ok && len(val) > 0 {
			*filterVal = val
		}
		return
	}
	//单独字段查询存在，维度过滤不存在，则使用单独字段过滤
	if !ok || len(val) == 0 {
		*filterVal = []string{*field}
		return
	}
	//单独字段过滤存在，维度过滤存在，则判断是否包含
	if utils.Container(val, *field) {
		*filterVal = []string{*field}
	} else {
		*filterVal = []string{cast.ToString(math.MinInt32)}
	}
	return
}
