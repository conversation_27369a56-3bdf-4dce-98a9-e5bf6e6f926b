package tourist

import (
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwm"
	"report-service/pkg/utils"
)

func Statistics(
	params DoQueryParams,
) (result *TouristStatistics, err error) {
	var filter *dwm.TouristFilterFields
	filter, err = BuildTouristFilter(params)
	if err != nil {
		return
	}

	//获取明细数据
	var poStatistic dwm.TouristStatistics
	poStatistic, err = repository.ReportDwmTouristRepository.Statistics(*filter)
	if err != nil {
		return
	}
	err = utils.JsonConvertor(poStatistic, &result)
	if err != nil {
		return
	}
	return
}
