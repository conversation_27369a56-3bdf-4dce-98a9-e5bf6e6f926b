package customized

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/semaphore"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"sync"
	"time"
)

// ProcessManager 流程管理器
type ProcessManager struct {
	processes       map[string]common.Process
	processesParams map[string]common.ProcessParams
	results         map[string]*common.ProcessResult
	resultsMu       sync.RWMutex
	maxWorkers      int
	isDebug         bool
}

func NewProcessManager(opt ...ProcessorOption) *ProcessManager {
	pm := &ProcessManager{
		processes:       make(map[string]common.Process),
		processesParams: make(map[string]common.ProcessParams),
		results:         make(map[string]*common.ProcessResult),
	}
	for _, option := range opt {
		option(pm)
	}
	return pm
}

// Register 注册流程
func (pm *ProcessManager) Register(process common.Process, params common.ProcessParams) {
	pm.processes[process.Name()] = process
	pm.processesParams[process.Name()] = params
}

// GetResult 获取流程执行结果
func (pm *ProcessManager) GetResult(name string) (*common.ProcessResult, bool) {
	pm.resultsMu.RLock()
	defer pm.resultsMu.RUnlock()

	result, exists := pm.results[name]
	return result, exists
}

// ExecuteAll 执行所有注册的流程
func (pm *ProcessManager) ExecuteAll(ctx context.Context) error {
	return pm.executeProcesses(ctx, pm.getAllProcessNames())
}

// Execute 执行指定的流程
func (pm *ProcessManager) Execute(ctx context.Context, processNames ...string) error {
	return pm.executeProcesses(ctx, processNames)
}

// NotifyResultException 结果异常通知推送
func (pm *ProcessManager) NotifyResultException(name string) {
	// 查看执行结果
	result, exists := pm.GetResult(name)
	if !exists {
		//错误预警
		global.LOG.Error("未找到执行结果，", zap.String("name", name))
		globalNotice.Warning(fmt.Sprintf("未找到执行结果，名称: %s", name))
	}
	// 结果处理
	switch {
	case result.Error != nil || result.Status == common.ProcessFailed:
		global.LOG.Error("执行异常", zap.String("name", name), zap.Error(result.Error))
		globalNotice.Error(fmt.Sprintf("执行异常, 名称: %s, 错误：%v", name, result.Error))
	case result.Status != common.ProcessCompleted:
		global.LOG.Error("执行异常, 未完成", zap.String("name", name))
		globalNotice.Error(fmt.Sprintf("执行异常, 未完成, 名称: %s", name))
	case pm.isDebug:
		global.LOG.Info("执行完成", zap.String("name", name), zap.Duration("cost", result.CostTime))
		fmt.Printf("执行完成, 名称: %s, 耗时: %v\n", name, result.CostTime)
	}
}

// NotifyAllResultException 全部结果异常通知推送
func (pm *ProcessManager) NotifyAllResultException() {
	allProcess := pm.getAllProcessNames()
	for _, name := range allProcess {
		// 查看执行结果
		pm.NotifyResultException(name)
	}
}

// 获取所有流程名称
func (pm *ProcessManager) getAllProcessNames() []string {
	names := make([]string, 0, len(pm.processes))
	for name := range pm.processes {
		names = append(names, name)
	}
	return names
}

// 执行流程的核心逻辑
func (pm *ProcessManager) executeProcesses(ctx context.Context, processNames []string) error {
	// 使用 errgroup 控制并发执行
	g, ctx := errgroup.WithContext(ctx)
	if pm.maxWorkers <= 0 {
		pm.maxWorkers = 1
	}
	sem := semaphore.NewWeighted(int64(pm.maxWorkers))

	// 按顺序执行流程
	for _, pName := range processNames {
		processName := pName
		process := pm.processes[processName]
		params := pm.processesParams[processName]

		g.Go(func() error {
			return pm.executeProcess(ctx, sem, process, params)
		})
	}

	return g.Wait()
}

// 执行单个流程
func (pm *ProcessManager) executeProcess(
	ctx context.Context,
	sem *semaphore.Weighted,
	process common.Process,
	params common.ProcessParams,
) error {
	// 更新流程状态为运行中
	pm.updateProcessStatus(process.Name(), common.ProcessRunning, nil)

	// 获取信号量
	if err := sem.Acquire(ctx, 1); err != nil {
		pm.updateProcessStatus(process.Name(), common.ProcessFailed, err)
		return fmt.Errorf("acquire semaphore failed for process %s: %w", process.Name(), err)
	}
	defer sem.Release(1)

	startTime := time.Now()

	// 执行流程
	err := process.Execute(params)

	endTime := time.Now()

	// 更新流程状态
	if err != nil {
		pm.updateProcessStatus(process.Name(), common.ProcessFailed, err)
		return fmt.Errorf("process %s failed: %w", process.Name(), err)
	}

	pm.updateProcessResult(process.Name(), common.ProcessCompleted, nil, startTime, endTime)
	return nil
}

// 更新流程状态
func (pm *ProcessManager) updateProcessStatus(name string, status common.ProcessStatus, err error) {
	pm.resultsMu.Lock()
	defer pm.resultsMu.Unlock()

	if result, exists := pm.results[name]; exists {
		result.Status = status
		result.Error = err
		if status != common.ProcessRunning {
			result.EndTime = time.Now()
		}
	} else {
		pm.results[name] = &common.ProcessResult{
			Name:      name,
			Status:    status,
			Error:     err,
			StartTime: time.Now(),
		}
	}
}

// 更新流程完整结果
func (pm *ProcessManager) updateProcessResult(name string, status common.ProcessStatus, err error, startTime, endTime time.Time) {
	pm.resultsMu.Lock()
	defer pm.resultsMu.Unlock()

	pm.results[name] = &common.ProcessResult{
		Name:      name,
		Status:    status,
		Error:     err,
		StartTime: startTime,
		EndTime:   endTime,
		CostTime:  endTime.Sub(startTime),
	}
}

type ProcessorOption func(*ProcessManager)

func WithMaxWorkers(workers int) ProcessorOption {
	return func(pm *ProcessManager) {
		if workers > 0 {
			pm.maxWorkers = workers
		}
	}
}

func WithIsDebug(isDebug bool) ProcessorOption {
	return func(pm *ProcessManager) {
		pm.isDebug = isDebug
	}
}
