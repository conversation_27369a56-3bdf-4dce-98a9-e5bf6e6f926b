package paymodemoney

import (
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/logic/dwm/customized/paymodemoney/handler"
)

func NewReportYinBaoRechargeProcessor(params common.ReportProcessBaseParams, opts ...common.ReportProcessorOption) *common.ReportProcessor {
	rp := &common.ReportProcessor{
		Extractors: map[string]common.DataExtractor{
			"YinBaoRecharge": &handler.YinBaoRechargeExtractor{},
		},
		// 巡检触发流程
		Inspector: map[string]common.DataInspector{
			"YinBaoRecharge": &handler.YinBaoRechargeInspector{},
		},
		Cleaners: map[string]common.DataCleaner{
			"YinBaoRecharge": &handler.YinBaoRechargeCleaner{},
		},
		Saver: map[string]common.DataSaver{
			"YinBaoRecharge": &handler.YinBaoRechargeSaver{},
		},
		Config: common.GetConfigFromDefault(),
		Params: params,
	}
	for _, opt := range opts {
		opt(rp)
	}
	return rp
}

type YinBaoRechargeProcessExecute struct {
	*BaseProcessExecute
}

func NewYinBaoRechargeProcessExecute() YinBaoRechargeProcessExecute {
	return YinBaoRechargeProcessExecute{
		BaseProcessExecute: NewBaseProcessExecute("pay_mode_money_report_yinbao_recharge"),
	}
}

func (p *YinBaoRechargeProcessExecute) Execute(params common.ProcessParams) error {
	return p.ExecuteWithProcessor(params, p.processExecute)
}

func (p *YinBaoRechargeProcessExecute) processExecute(params common.ProcessParams) error {
	err := p.ProcessExecute(
		params,
		func(baseParams common.ReportProcessBaseParams, opts ...common.ReportProcessorOption) *common.ReportProcessor {
			return NewReportYinBaoRechargeProcessor(baseParams, opts...)
		},
		common.WithMaxWorkers(1),
		common.WithPagination(true),
		common.WithPageSize(100),
	)
	if err != nil {
		return err
	}
	if params.Ext.IsNextProcess {
		p.nextProcess(params)
	}
	return nil
}

// 下一个处理
func (p *YinBaoRechargeProcessExecute) nextProcess(params common.ProcessParams) {
	NextProcess(params)
}
