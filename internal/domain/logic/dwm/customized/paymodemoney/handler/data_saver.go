package handler

import (
	"fmt"
	"go.uber.org/zap"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwm/customized"
	"report-service/internal/global"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

// YinBaoOrderSaver 银豹订单数据保存
type YinBaoOrderSaver struct{}

func (s *YinBaoOrderSaver) Save(params *common.ReportProcessParams, data interface{}) error {
	//fmt.Printf("银豹订单数据保存, data:%+v", data)
	return batchCreate(data)
}

// YinBaoRechargeSaver 银豹充值数据保存
type YinBaoRechargeSaver struct{}

func (s *YinBaoRechargeSaver) Save(params *common.ReportProcessParams, data interface{}) error {
	//fmt.Printf("银豹充值数据保存, data:%+v", data)
	return batchCreate(data)
}

// PFTRechargeSaver 银豹充值数据保存
type PFTRechargeSaver struct{}

func (s *PFTRechargeSaver) Save(params *common.ReportProcessParams, data interface{}) error {
	//fmt.Printf("票付通充值数据保存, data:%+v", data)
	return batchCreate(data)
}

// 批量插入
func batchCreate(data interface{}) error {
	insertItems := make([]customized.PayModeMoneyModel, 0)
	err := utils.JsonConvertor(data, &insertItems)
	if err != nil {
		global.LOG.Error("插入数据转换失败", zap.Error(err))
		return fmt.Errorf("数据转换失败: %w", err)
	}

	if len(insertItems) == 0 {
		return szerrors.NewDataNoFoundErrorWithText("没有需要插入的数据")
	}

	global.LOG.Info("开始插入清洗数据数据",
		zap.Int("recordCount", len(insertItems)),
		zap.Int("merchantId", insertItems[0].MerchantID))

	err = repository.ReportDwmPayModeMoneyRepository.Insert(insertItems)
	if err != nil {
		global.LOG.Error("插入数据失败",
			zap.Int("merchantId", insertItems[0].MerchantID),
			zap.Error(err))
		return err
	}

	global.LOG.Info("插入清洗数据数据完成",
		zap.Int("recordCount", len(insertItems)),
		zap.Int("merchantId", insertItems[0].MerchantID))
	return nil
}
