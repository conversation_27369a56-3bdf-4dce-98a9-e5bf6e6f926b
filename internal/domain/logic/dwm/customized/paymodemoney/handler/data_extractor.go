package handler

import (
	"context"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/opencooperateecologyservice/yinbao"
	"report-service/pkg/sdk/api/pftmember"
	"report-service/pkg/sdk/api/tradejournalservice"
	"report-service/pkg/szerrors"
	"time"
)

// YinBaoOrderExtractor 银豹订单数据提取
type YinBaoOrderExtractor struct{}

func (e *YinBaoOrderExtractor) Extract(params *common.ReportProcessParams) (interface{}, error) {
	//时间范围
	beginDate := carbon.Parse(params.OriginalParams.StartTime).StartOfHour()
	endDate := carbon.Parse(params.OriginalParams.EndTime).EndOfHour()
	cacheKey := "yb_order_extract_last_id:" + params.OriginalParams.UniqueKey

	global.LOG.Info("开始提取银豹订单数据",
		zap.String("cacheKey", cacheKey),
		zap.String("startTime", beginDate.ToDateTimeString()),
		zap.String("endTime", endDate.ToDateTimeString()),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	prevLastId, err := global.REDIS.Get(context.Background(), cacheKey).Result()
	lastId := 0
	if err == nil && prevLastId != "" {
		lastId = cast.ToInt(prevLastId)
	}
	var appIds []string
	if params.OriginalParams.ExternalProjectId != "" {
		appIds = append(appIds, params.OriginalParams.ExternalProjectId)
	}
	result, err := yinbao.QueryPageTicket(yinbao.QueryPageTicketRequest{
		MemberIds: []int{params.OriginalParams.MerchantId},
		PageNum:   params.PageParams.PageNum,
		PageSize:  params.PageParams.PageSize,
		StartTime: beginDate.ToDateTimeString(),
		EndTime:   endDate.ToDateTimeString(),
		LastId:    lastId,
		AppIds:    appIds,
	})
	if err != nil {
		global.LOG.Error("查询银豹订单数据失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, err
	}
	if len(result.Page.Records) == 0 {
		global.LOG.Info("未查询到银豹订单数据",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Int("pageNum", params.PageParams.PageNum),
			zap.Int("lastId", lastId))
		return nil, szerrors.NewDataNoFoundErrorWithText("未查询到银豹订单数据")
	}
	if result.LastId != nil && *result.LastId != 0 {
		err = global.REDIS.Set(context.Background(), cacheKey, *result.LastId, 180*time.Second).Err()
		if err != nil {
			global.LOG.Warn("设置Redis缓存lastId失败",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("cacheKey", cacheKey),
				zap.Int("lastId", *result.LastId),
				zap.Error(err))
		}
	}

	global.LOG.Info("成功提取银豹订单数据",
		zap.Int("recordCount", len(result.Page.Records)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	return result.Page.Records, nil
}

// YinBaoRechargeExtractor 银豹充值数据提取
type YinBaoRechargeExtractor struct{}

func (e *YinBaoRechargeExtractor) Extract(params *common.ReportProcessParams) (interface{}, error) {
	//时间范围
	beginDate := carbon.Parse(params.OriginalParams.StartTime).StartOfHour()
	endDate := carbon.Parse(params.OriginalParams.EndTime).EndOfHour()
	cacheKey := "yb_recharge_extract_last_id:" + params.OriginalParams.UniqueKey

	global.LOG.Info("开始提取银豹充值数据",
		zap.String("cacheKey", cacheKey),
		zap.String("startTime", beginDate.ToDateTimeString()),
		zap.String("endTime", endDate.ToDateTimeString()),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	prevLastId, err := global.REDIS.Get(context.Background(), cacheKey).Result()
	lastId := 0
	if err == nil && prevLastId != "" {
		lastId = cast.ToInt(prevLastId)
	}
	var appIds []string
	if params.OriginalParams.ExternalProjectId != "" {
		appIds = append(appIds, params.OriginalParams.ExternalProjectId)
	}
	result, err := yinbao.QueryPageRechargeLog(yinbao.QueryPageRechargeLogRequest{
		MemberIds: []int{params.OriginalParams.MerchantId},
		PageNum:   params.PageParams.PageNum,
		PageSize:  params.PageParams.PageSize,
		StartTime: beginDate.ToDateTimeString(),
		EndTime:   endDate.ToDateTimeString(),
		LastId:    lastId,
		AppIds:    appIds,
	})
	if err != nil {
		global.LOG.Info("未查询到银豹充值数据",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Int("pageNum", params.PageParams.PageNum),
			zap.Int("lastId", lastId))
		return nil, err
	}
	if len(result.Page.Records) == 0 {
		global.LOG.Info("未查询到银豹充值数据",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Int("pageNum", params.PageParams.PageNum),
			zap.Int("lastId", lastId))
		return nil, szerrors.NewDataNoFoundErrorWithText("未查询到银豹充值数据")
	}
	if result.LastId != nil && *result.LastId != 0 {
		err = global.REDIS.Set(context.Background(), cacheKey, *result.LastId, 180*time.Second).Err()
		if err != nil {
			global.LOG.Warn("设置Redis缓存lastId失败",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("cacheKey", cacheKey),
				zap.Int("lastId", *result.LastId),
				zap.Error(err))
		}
	}

	global.LOG.Info("成功提取银豹充值数据",
		zap.Int("recordCount", len(result.Page.Records)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	return result.Page.Records, nil
}

// PFTRechargeExtractor 票付通充值数据提取
type PFTRechargeExtractor struct{}

// Extract 票付通充值数据提取
func (e *PFTRechargeExtractor) Extract(params *common.ReportProcessParams) (interface{}, error) {
	//时间范围
	beginDate := carbon.Parse(params.OriginalParams.StartTime).StartOfHour()
	endDate := carbon.Parse(params.OriginalParams.EndTime).EndOfHour()

	global.LOG.Info("开始提取交易记录充值数据",
		zap.String("startTime", beginDate.ToDateTimeString()),
		zap.String("endTime", endDate.ToDateTimeString()),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	if params.OriginalParams.MerchantId == 0 {
		return nil, nil
	}
	member, err := pftmember.BatchSearchMember([]int{params.OriginalParams.MerchantId})
	if err != nil {
		global.LOG.Error("获取会员信息失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, err
	}
	if member == nil || len(member) == 0 {
		global.LOG.Info("未查询到会员信息",
			zap.Int("merchantId", params.OriginalParams.MerchantId))
		return nil, fmt.Errorf("未查询到会员信息")
	}
	//获取账户ID
	AccountId := member[0].AccountId
	//查询条件组装
	var apiParams = tradejournalservice.RequestParams{
		AccountIds:        []int{AccountId},
		BeginDate:         beginDate.ToDateTimeString(),
		EndDate:           endDate.ToDateTimeString(),
		RequestID:         params.OriginalParams.UniqueKey,
		SubjectCodes:      enum.OneCardCreditRechargeSubjectCode,
		TemplateItemCodes: []int{enum.PFTOneCardCreditRechargeItemCode},
		PageSize:          params.PageParams.PageSize,
		TradeScope:        enum.TradeScopeTypeTotal,
		PageNum:           params.PageParams.PageNum,
	}
	//查询交易记录明细数据
	result, err := tradejournalservice.QueryTradeJournalWithUser(apiParams)
	if err != nil {
		global.LOG.Error("查询交易记录明细数据失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, err
	}
	if len(result) == 0 {
		global.LOG.Info("未查询到交易记录明细数据",
			zap.Int("merchantId", params.OriginalParams.MerchantId))
		return nil, szerrors.NewDataNoFoundErrorWithText("未查询到交易记录明细数据")
	}

	global.LOG.Info("成功提取交易记录充值数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	return result, nil
}
