package handler

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwm/customized"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/opencooperateecologyservice/yinbao"
	"report-service/pkg/sdk/api/tradejournalservice"
	"report-service/pkg/utils"
)

// YinBaoOrderInspector 银豹订单数据检查
type YinBaoOrderInspector struct{}

func (i *YinBaoOrderInspector) Inspector(params *common.ReportProcessParams, data interface{}) (interface{}, error) {
	var result []yinbao.PageTicketResponsePageRecord
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		global.LOG.Error("银豹订单数据转换失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("数据转换失败: %w", err)
	}
	if len(result) == 0 {
		global.LOG.Info("银豹订单数据为空")
		return []customized.PayModeMoneyModel{}, nil
	}

	global.LOG.Info("开始巡检银豹订单数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	businessNos := make([]int64, 0, len(result))
	resultMap := make(map[int64]yinbao.PageTicketResponsePageRecord, 0)
	for _, res := range result {
		uid := res.Uid
		operatedAt := carbon.Parse(res.DateTime)

		projectId, exist := enum.PayModeMoneyProjectIdMap[res.AppId]
		if !exist || projectId == 0 {
			global.LOG.Debug("跳过未映射的AppId",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("appId", res.AppId),
				zap.String("uid", uid))
			continue
		}
		itemInfo := createBasePayModeMoneyModel(operatedAt, params, uid, projectId)
		businessNos = append(businessNos, itemInfo.BusinessNo)
		resultMap[itemInfo.BusinessNo] = res
	}
	if len(businessNos) == 0 {
		return nil, nil
	}
	businessNos, err = diffBusinessNosWithExisting(businessNos)
	if err != nil {
		return nil, err
	}
	list := make([]yinbao.PageTicketResponsePageRecord, 0, len(businessNos))
	for _, businessNo := range businessNos {
		if item, ok := resultMap[businessNo]; ok {
			list = append(list, item)
		}
	}
	defer func() {
		if len(list) > 0 {
			globalNotice.Warning(fmt.Sprintf("巡检补偿%d条未处理的银豹订单数据", len(list)))
		}
		global.LOG.Info("巡检银豹订单数据已完成",
			zap.Int("addCount", len(list)),
			zap.Int("recordCount", len(result)),
			zap.Int("merchantId", params.OriginalParams.MerchantId))
	}()
	return list, nil
}

// YinBaoRechargeInspector 银豹充值数据检查
type YinBaoRechargeInspector struct{}

func (i *YinBaoRechargeInspector) Inspector(params *common.ReportProcessParams, data interface{}) (interface{}, error) {
	var result []yinbao.PageRechargeLogResponsePageRecord
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		global.LOG.Error("银豹充值数据转换失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("数据转换失败: %w", err)
	}

	global.LOG.Info("开始巡检银豹充值数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	businessNos := make([]int64, 0, len(result))
	resultMap := make(map[int64]yinbao.PageRechargeLogResponsePageRecord, 0)
	for _, res := range result {
		uid := res.Uid
		operatedAt := carbon.Parse(res.DateTime)
		projectId := enum.PayModeMoneyProjectIdRechargeYb
		itemInfo := createBasePayModeMoneyModel(operatedAt, params, uid, projectId)
		businessNos = append(businessNos, itemInfo.BusinessNo)
		resultMap[itemInfo.BusinessNo] = res
	}
	if len(businessNos) == 0 {
		return nil, nil
	}
	businessNos, err = diffBusinessNosWithExisting(businessNos)
	if err != nil {
		return nil, err
	}
	list := make([]yinbao.PageRechargeLogResponsePageRecord, 0, len(businessNos))
	for _, businessNo := range businessNos {
		if item, ok := resultMap[businessNo]; ok {
			list = append(list, item)
		}
	}
	defer func() {
		if len(list) > 0 {
			globalNotice.Warning(fmt.Sprintf("巡检补偿%d条未处理的银豹充值数据", len(list)))
		}
		global.LOG.Info("巡检银豹充值数据已完成",
			zap.Int("addCount", len(list)),
			zap.Int("recordCount", len(result)),
			zap.Int("merchantId", params.OriginalParams.MerchantId))
	}()
	return list, nil
}

// PFTRechargeInspector 票付通充值数据检查
type PFTRechargeInspector struct{}

func (i *PFTRechargeInspector) Inspector(params *common.ReportProcessParams, data interface{}) (interface{}, error) {
	var result []tradejournalservice.TradeJournalResponse
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		global.LOG.Error("交易记录充值数据转换失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("数据转换失败: %w", err)
	}

	global.LOG.Info("开始巡检交易记录充值数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	businessNos := make([]int64, 0, len(result))
	resultMap := make(map[int64]tradejournalservice.TradeJournalResponse, 0)
	for _, res := range result {
		operatedAt := carbon.CreateFromTimestamp(int64(res.Rectime))
		id := cast.ToString(res.ID)
		projectId := enum.PayModeMoneyProjectIdRechargePft
		itemInfo := createBasePayModeMoneyModel(operatedAt, params, id, projectId)
		businessNos = append(businessNos, itemInfo.BusinessNo)
		resultMap[itemInfo.BusinessNo] = res
	}
	if len(businessNos) == 0 {
		return nil, nil
	}
	businessNos, err = diffBusinessNosWithExisting(businessNos)
	if err != nil {
		return nil, err
	}
	list := make([]tradejournalservice.TradeJournalResponse, 0, len(businessNos))
	for _, businessNo := range businessNos {
		if item, ok := resultMap[businessNo]; ok {
			list = append(list, item)
		}
	}
	defer func() {
		if len(list) > 0 {
			globalNotice.Warning(fmt.Sprintf("巡检补偿%d条未处理的交易记录充值数据", len(list)))
		}
		global.LOG.Info("巡检交易记录充值数据已完成",
			zap.Int("addCount", len(list)),
			zap.Int("recordCount", len(result)),
			zap.Int("merchantId", params.OriginalParams.MerchantId))
	}()
	return list, nil
}

// 巡检查询不存在的业务号
func diffBusinessNosWithExisting(businessNos []int64) ([]int64, error) {
	records, err := repository.ReportDwmPayModeMoneyRepository.GetByBusinessNos(businessNos)
	if err != nil {
		return nil, err
	}
	foundBusinessNos := make(map[int64]bool)
	for _, record := range records {
		foundBusinessNos[record.BusinessNo] = true
	}
	notFoundBusinessNos := make([]int64, 0)
	for _, businessNo := range businessNos {
		if _, ok := foundBusinessNos[businessNo]; !ok {
			notFoundBusinessNos = append(notFoundBusinessNos, businessNo)
		}
	}
	return notFoundBusinessNos, nil
}
