package handler

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"math"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/repository/dwm/customized"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/opencooperateecologyservice/yinbao"
	"report-service/pkg/sdk/api/tradejournalservice"
	"report-service/pkg/utils"
	"strings"
)

// YinBaoOrderCleaner 银豹订单数据清洗
type YinBaoOrderCleaner struct{}

func (c *YinBaoOrderCleaner) Clean(params *common.ReportProcessParams, data interface{}) (interface{}, error) {
	var result []yinbao.PageTicketResponsePageRecord
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		global.LOG.Error("银豹订单数据转换失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("数据转换失败: %w", err)
	}
	if len(result) == 0 {
		global.LOG.Info("银豹订单数据为空")
		return []customized.PayModeMoneyModel{}, nil
	}

	global.LOG.Info("开始清洗银豹订单数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	// 提取所有UID以获取折扣信息
	uidList := make([]string, 0, len(result))
	for _, res := range result {
		uidList = append(uidList, res.Uid)
	}
	discountTotalAmountMap, err := GetDiscountTotalAmount(uidList, []int{params.OriginalParams.MerchantId})
	if err != nil {
		global.LOG.Error("获取折扣总金额数据失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("获取折扣金额数据失败: %w", err)
	}
	cleanData := make([]customized.PayModeMoneyModel, 0, len(result))
	for _, res := range result {
		uid := res.Uid
		operatedAt := carbon.Parse(res.DateTime)
		totalAmount := int(math.Round(res.TotalAmount * 100))
		discountTotalAmount := 0
		if discountAmount, ok := discountTotalAmountMap[uid]; ok {
			discountTotalAmount = discountAmount
		}
		//销售或者退货金额处理
		if res.TicketType == enum.YiBaoTicketTypeReturn {
			if totalAmount != 0 {
				totalAmount = -totalAmount
			}
			if discountTotalAmount != 0 {
				discountTotalAmount = -discountTotalAmount
			}
		}
		// 后续银豹数据要通过，通过银豹appid实现不同展示不同数据的话，
		// 可以把关联appid和项目id的配置抽取到数据库customized_pay_mode_money_report_merchant_config配置里
		// 获取项目id
		projectId, exist := enum.PayModeMoneyProjectIdMap[res.AppId]
		if !exist || projectId == 0 {
			global.LOG.Debug("跳过未映射的AppId",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("appId", res.AppId),
				zap.String("uid", uid))
			continue
		}
		itemInfo := createBasePayModeMoneyModel(operatedAt, params, uid, projectId)
		if itemInfo == nil {
			global.LOG.Debug("创建基础模型数据失败",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("uid", uid),
				zap.Int("projectId", projectId))
			continue
		}
		// 映射支付方式并设置金额
		fieldsMap, ok := enum.PayModeMoneyProjectAndPayModeMap[projectId]
		if !ok {
			global.LOG.Debug("未找到支付方式映射",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.Int("projectId", projectId),
				zap.String("uid", uid))
			continue
		}
		// 设置营收和折扣金额
		itemInfo.OperatingRevenue = totalAmount
		itemInfo.DiscountAmount = discountTotalAmount
		// 映射支付方式并设置金额 可能存在多种支付方式，需要处理
		var payments []yinbao.PageTicketResponsePageRecordPayments
		err = json.Unmarshal([]byte(res.Payments), &payments)
		if err != nil {
			global.LOG.Warn("解析支付信息失败",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("uid", uid),
				zap.String("payments", res.Payments),
				zap.Error(err))
			continue
		}
		for _, payment := range payments {
			amount := int(math.Round(payment.Amount * 100))
			if res.TicketType == enum.YiBaoTicketTypeReturn {
				amount = -amount
			}
			// 映射支付方式并设置金额
			if !mapPaymentMethod(itemInfo, fieldsMap, payment.Code, amount) {
				global.LOG.Debug("支付方式未映射",
					zap.Int("merchantId", params.OriginalParams.MerchantId),
					zap.String("projectId", cast.ToString(projectId)),
					zap.String("paymentCode", payment.Code),
					zap.String("uid", uid))
				continue
			}
		}
		// 计算收入合计
		getItemTotalIncome(itemInfo, projectId)
		// 加入清洗结果
		cleanData = append(cleanData, *itemInfo)
	}

	global.LOG.Info("银豹订单数据清洗完成",
		zap.Int("totalRecords", len(result)),
		zap.Int("successRecords", len(cleanData)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	return cleanData, nil
}

// YinBaoRechargeCleaner 银豹充值数据清洗
type YinBaoRechargeCleaner struct{}

func (c *YinBaoRechargeCleaner) Clean(params *common.ReportProcessParams, data interface{}) (interface{}, error) {
	var result []yinbao.PageRechargeLogResponsePageRecord
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		global.LOG.Error("银豹充值数据转换失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("数据转换失败: %w", err)
	}

	global.LOG.Info("开始清洗银豹充值数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	cleanData := make([]customized.PayModeMoneyModel, 0, len(result))
	for _, res := range result {
		uid := res.Uid
		operatedAt := carbon.Parse(res.DateTime)
		money := int(math.Round(res.RechargeMoney * 100)) // 转换为分
		payMethod := res.PayMethod
		projectId := enum.PayModeMoneyProjectIdRechargeYb
		itemInfo := createBasePayModeMoneyModel(operatedAt, params, uid, projectId)
		if itemInfo == nil {
			global.LOG.Debug("创建基础模型数据失败",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("uid", uid),
				zap.Int("projectId", projectId))
			continue
		}
		// 映射支付方式并设置金额
		fieldsMap, ok := enum.PayModeMoneyProjectAndPayModeMap[projectId]
		if !ok {
			global.LOG.Debug("未找到支付方式映射",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.Int("projectId", projectId),
				zap.String("uid", uid))
			continue
		}
		// 映射支付方式并设置金额
		isHaveSubject := mapPaymentMethod(itemInfo, fieldsMap, payMethod, cast.ToInt(money))
		if !isHaveSubject {
			global.LOG.Debug("支付方式未映射",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("projectId", cast.ToString(projectId)),
				zap.String("paymentCode", payMethod),
				zap.String("uid", uid))
			continue
		}
		// 计算收入合计
		getItemTotalIncome(itemInfo, projectId)
		// 加入清洗结果
		cleanData = append(cleanData, *itemInfo)
	}

	global.LOG.Info("银豹充值数据清洗完成",
		zap.Int("totalRecords", len(result)),
		zap.Int("successRecords", len(cleanData)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	return cleanData, nil
}

// PFTRechargeCleaner 票付通充值数据清洗
type PFTRechargeCleaner struct{}

func (c *PFTRechargeCleaner) Clean(params *common.ReportProcessParams, data interface{}) (interface{}, error) {
	var result []tradejournalservice.TradeJournalResponse
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		global.LOG.Error("交易记录充值数据转换失败",
			zap.Int("merchantId", params.OriginalParams.MerchantId),
			zap.Error(err))
		return nil, fmt.Errorf("数据转换失败: %w", err)
	}

	global.LOG.Info("开始清洗交易记录充值数据",
		zap.Int("recordCount", len(result)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	cleanData := make([]customized.PayModeMoneyModel, 0, len(result))
	for _, res := range result {
		operatedAt := carbon.CreateFromTimestamp(int64(res.Rectime))
		money := res.Dmoney
		if res.Daction != 0 {
			money = -res.Dmoney
		}
		subjectCode := cast.ToString(res.SubjectCode)
		id := cast.ToString(res.ID)
		projectId := enum.PayModeMoneyProjectIdRechargePft
		itemInfo := createBasePayModeMoneyModel(operatedAt, params, id, projectId)
		if itemInfo == nil {
			global.LOG.Debug("创建基础模型数据失败",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.Int("projectId", projectId))
			continue
		}
		// 映射支付方式并设置金额
		fieldsMap, ok := enum.PayModeMoneyProjectAndPayModeMap[projectId]
		if !ok {
			global.LOG.Debug("未找到支付方式映射",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.Int("projectId", projectId))
			continue
		}
		// 映射支付方式并设置金额
		isHaveSubject := mapPaymentMethod(itemInfo, fieldsMap, subjectCode, money)
		if !isHaveSubject {
			global.LOG.Debug("支付方式未映射",
				zap.Int("merchantId", params.OriginalParams.MerchantId),
				zap.String("projectId", cast.ToString(projectId)),
				zap.String("paymentCode", subjectCode))
			continue
		}
		// 计算收入合计
		getItemTotalIncome(itemInfo, projectId)
		// 加入清洗结果
		cleanData = append(cleanData, *itemInfo)
	}

	global.LOG.Info("交易记录充值数据清洗完成",
		zap.Int("totalRecords", len(result)),
		zap.Int("successRecords", len(cleanData)),
		zap.Int("merchantId", params.OriginalParams.MerchantId))

	return cleanData, nil
}

// 创建基础数据
func createBasePayModeMoneyModel(
	operatedAt carbon.Carbon,
	params *common.ReportProcessParams,
	externalNo string,
	projectId int,
) *customized.PayModeMoneyModel {
	//数据来源
	dataSource, dsExists := GetDataSourceByProjectId(projectId)
	if !dsExists || dataSource == 0 {
		return nil
	}
	// 生成数据源唯一标识 交易记录Id+来源类型+项目Id
	businessNo := GenerateBusinessNo([]string{externalNo, cast.ToString(dataSource), cast.ToString(projectId)})
	// 获取时间类型
	timeType := GetTimeTypeByProjectAndTime(projectId, operatedAt)
	// 返回基础数据
	return &customized.PayModeMoneyModel{
		OperatedAt:                  operatedAt.ToDateTimeString(),
		MerchantID:                  params.OriginalParams.MerchantId,
		DataSource:                  dataSource,
		BusinessNo:                  businessNo,
		ExternalNo:                  externalNo,
		ProjectID:                   projectId,
		TimeType:                    timeType,
		OperatingRevenue:            0,
		DiscountAmount:              0,
		AccountsReceivablePayment:   0,
		EntertainmentExpensePayment: 0,
		CashPayment:                 0,
		UnionPayPayment:             0,
		StoredValueCardPayment:      0,
		YinBaoPayPayment:            0,
		RuralCommercialBankPayment:  0,
		CreditPayment:               0,
		YiBaoPayment:                0,
		AlipayPayment:               0,
		WechatPayment:               0,
		PrepaidCardPayment:          0,
		MeiTuanCouponPayment:        0,
		OtherPayment:                0,
		TotalIncome:                 0,
	}
}

// mapPaymentMethod 映射支付方式并设置金额
func mapPaymentMethod(
	itemInfo *customized.PayModeMoneyModel,
	fieldsMap map[string][]string,
	fieldVal string,
	money int) bool {
	isHaveField := false
	for key, val := range fieldsMap {
		if !utils.Container(val, fieldVal) {
			continue
		}
		// 根据支付方式字段设置对应金额
		switch key {
		case enum.PayModeMoneyDimensionAccountsReceivablePayment:
			itemInfo.AccountsReceivablePayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionEntertainmentExpensePayment:
			itemInfo.EntertainmentExpensePayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionCashPayment:
			itemInfo.CashPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionUnionPayPayment:
			itemInfo.UnionPayPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionStoredValueCardPayment:
			itemInfo.StoredValueCardPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionYinbaoPayPayment:
			itemInfo.YinBaoPayPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionRuralCommercialBankPayment:
			itemInfo.RuralCommercialBankPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionYibaoPayment:
			itemInfo.YiBaoPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionAlipayPayment:
			itemInfo.AlipayPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionWechatPayment:
			itemInfo.WechatPayment = money
			isHaveField = true
		case enum.PayModeMoneyDimensionMeituanCouponPayment:
			itemInfo.MeiTuanCouponPayment = money
			isHaveField = true
		}
	}
	return isHaveField
}

// getItemTotalIncome 获取收入合计
func getItemTotalIncome(
	itemInfo *customized.PayModeMoneyModel,
	projectId int,
) {
	switch projectId {
	case enum.PayModeMoneyProjectIdRechargePft:
		itemInfo.TotalIncome = itemInfo.CashPayment + itemInfo.YiBaoPayment + itemInfo.AlipayPayment + itemInfo.WechatPayment
	case enum.PayModeMoneyProjectIdRechargeYb:
		itemInfo.TotalIncome = itemInfo.CashPayment + itemInfo.UnionPayPayment + itemInfo.StoredValueCardPayment + itemInfo.YinBaoPayPayment +
			itemInfo.RuralCommercialBankPayment
	case enum.PayModeMoneyProjectIdSwimsuitYb, enum.PayModeMoneyProjectIdSalesCartYb, enum.PayModeMoneyProjectIdGiftShopYb:
		itemInfo.TotalIncome = itemInfo.AccountsReceivablePayment + itemInfo.EntertainmentExpensePayment + itemInfo.CashPayment +
			itemInfo.UnionPayPayment + itemInfo.StoredValueCardPayment + itemInfo.YinBaoPayPayment + itemInfo.RuralCommercialBankPayment
	case enum.PayModeMoneyProjectIdFarmerRestaurantYb, enum.PayModeMoneyProjectIdThemeRestaurantYb, enum.PayModeMoneyProjectIdSnackShopYb, enum.PayModeMoneyProjectIdPaddyCoffeeYb:
		itemInfo.TotalIncome = itemInfo.AccountsReceivablePayment + itemInfo.EntertainmentExpensePayment + itemInfo.CashPayment +
			itemInfo.UnionPayPayment + itemInfo.StoredValueCardPayment + itemInfo.YinBaoPayPayment + itemInfo.RuralCommercialBankPayment +
			itemInfo.MeiTuanCouponPayment
	}
}

// GenerateBusinessNo 生成业务唯一标识
func GenerateBusinessNo(components []string) int64 {
	// 使用分隔符连接
	combined := strings.Join(components, "#")
	// 生成MD5哈希
	hash := md5.Sum([]byte(combined))
	// 使用全部16个字节分两部分处理，降低冲突概率
	var high, low int64
	for i := 0; i < 8; i++ {
		high = (high << 8) | int64(hash[i])
	}
	for i := 8; i < 16; i++ {
		low = (low << 8) | int64(hash[i])
	}
	// 通过异或操作组合高低位，提高分布均匀性
	result := high ^ low
	// 处理符号位，确保返回正数
	if result < 0 {
		result = -result
	}
	if result == 0 {
		// 使用输入字符串长度和首尾字符确保唯一性
		if len(combined) > 1 {
			result = int64(len(combined))<<48 | int64(combined[0])<<32 | int64(combined[1])<<16 | int64(combined[len(combined)-1])
		} else {
			result = int64(len(combined))<<32 | int64(combined[0])<<16 | int64(combined[len(combined)-1])
		}
	}
	return result
}

// GetTimeTypeByProjectAndTime 根据项目ID和时间获取时间类型
// 如果该项目没有配置时间分类，则返回 PayModeMoneyTimeTypeNil
func GetTimeTypeByProjectAndTime(projectId int, time carbon.Carbon) int {
	// 检查该项目是否配置了时间分类
	timeTypeMap, exists := enum.PayModeMoneyProjectAndTimeTypeMap[projectId]
	if !exists {
		return enum.PayModeMoneyTimeTypeNil
	}
	currentDateStr := time.ToDateString()
	for timeType, timeRange := range timeTypeMap {
		if len(timeRange) < 2 {
			continue
		}
		startTime := timeRange[0]
		endTime := timeRange[1]
		// 处理不同的时间范围情况
		switch {
		case startTime == "" && endTime == "":
			// 如果开始和结束时间都为空，则匹配所有时间
			return timeType
		case startTime == "":
			// 如果开始时间为空，则判断是否在结束时间之前
			end := carbon.Parse(currentDateStr + " " + endTime)
			if time.Lte(end) {
				return timeType
			}
		case endTime == "":
			// 如果结束时间为空，则判断是否在开始时间之后
			start := carbon.Parse(currentDateStr + " " + startTime)
			if time.Gte(start) {
				return timeType
			}
		default:
			// 判断当前时间是否在开始时间和结束时间之间
			start := carbon.Parse(currentDateStr + " " + startTime)
			end := carbon.Parse(currentDateStr + " " + endTime)
			if time.Gte(start) && time.Lte(end) {
				return timeType
			}
		}
	}
	return enum.PayModeMoneyTimeTypeNil
}

// GetDataSourceByProjectId 根据项目ID获取数据来源
// 参数: projectId 项目ID
// 返回: 数据来源(dataSource)和是否找到的标识
func GetDataSourceByProjectId(projectId int) (int, bool) {
	// 遍历 PayModeMoneyProjectIdAndDataSourceMap 查找项目对应的数据来源
	for dataSource, projectIds := range enum.PayModeMoneyProjectIdAndDataSourceMap {
		if utils.Container(projectIds, projectId) {
			return dataSource, true
		}
	}
	return 0, false
}

// GetDiscountTotalAmount 获取优惠总金额
func GetDiscountTotalAmount(uidList []string, merchantIds []int) (map[string]int, error) {
	itemsResult, err := yinbao.QueryListTicketItems(uidList, merchantIds)
	if err != nil {
		return nil, err
	}
	if itemsResult == nil {
		return nil, nil
	}
	data := make(map[string]int, len(*itemsResult))
	for _, item := range *itemsResult {
		var discountDetails []yinbao.ListTicketItemsResponseItemDiscountDetail
		err = json.Unmarshal([]byte(item.DiscountDetails), &discountDetails)
		if err != nil {
			return nil, err
		}
		for _, detail := range discountDetails {
			data[item.Uid] += int(math.Round(detail.DiscountTotalAmount * 100))
		}
	}
	return data, nil
}

// CreateProjectId 生成项目id， 通常由外部id转为报表内部的id，由于前端对字段长度有限制，这边限制输出15位，所以这里限制了项目id的长度
func CreateProjectId(dateSource int, id int) int {
	var bitSize = enum.PayModeMoneyProjectIdSize - enum.PayModeMoneyProjectIdPrefix
	var projectId int
	// 使用位运算创建15位数字
	projectId = (dateSource << bitSize) | id
	// 确保是15位数字
	for projectId < 100000000000000 {
		projectId <<= 1
	}
	for projectId >= 1000000000000000 {
		projectId >>= 1
	}
	return projectId
}
