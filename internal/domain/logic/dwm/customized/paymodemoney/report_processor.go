package paymodemoney

import (
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/logic/dwm/customized/paymodemoney/handler"
)

func NewReportProcessor(params common.ReportProcessBaseParams, opts ...common.ReportProcessorOption) *common.ReportProcessor {
	rp := &common.ReportProcessor{
		Extractors: map[string]common.DataExtractor{
			"YinBaoOrder":    &handler.YinBaoOrderExtractor{},
			"YinBaoRecharge": &handler.YinBaoRechargeExtractor{},
			"PFTRecharge":    &handler.PFTRechargeExtractor{},
		},
		// 巡检触发流程
		Inspector: map[string]common.DataInspector{
			"YinBaoOrder":    &handler.YinBaoOrderInspector{},
			"YinBaoRecharge": &handler.YinBaoRechargeInspector{},
			"PFTRecharge":    &handler.PFTRechargeInspector{},
		},
		Cleaners: map[string]common.DataCleaner{
			"YinBaoOrder":    &handler.YinBaoOrderCleaner{},
			"YinBaoRecharge": &handler.YinBaoRechargeCleaner{},
			"PFTRecharge":    &handler.PFTRechargeCleaner{},
		},
		Saver: map[string]common.DataSaver{
			"YinBaoOrder":    &handler.YinBaoOrderSaver{},
			"YinBaoRecharge": &handler.YinBaoRechargeSaver{},
			"PFTRecharge":    &handler.PFTRechargeSaver{},
		},
		Config: common.GetConfigFromDefault(),
		Params: params,
	}
	for _, opt := range opts {
		opt(rp)
	}
	return rp
}

type ProcessExecute struct {
	*BaseProcessExecute
}

func NewProcessExecute() ProcessExecute {
	return ProcessExecute{
		BaseProcessExecute: NewBaseProcessExecute("pay_mode_money_report"),
	}
}

func (p *ProcessExecute) Execute(params common.ProcessParams) error {
	return p.ExecuteWithProcessor(params, p.processExecute)
}

func (p *ProcessExecute) processExecute(params common.ProcessParams) error {
	isProceed, lastTime, prevTime := p.checkExecuteTime(params)
	if !isProceed {
		return nil
	}
	processParams := params
	processParams.StartTime = lastTime
	err := p.ProcessExecute(
		processParams,
		func(baseParams common.ReportProcessBaseParams, opts ...common.ReportProcessorOption) *common.ReportProcessor {
			return NewReportProcessor(baseParams, opts...)
		},
		common.WithMaxWorkers(1),
		common.WithPagination(true),
		common.WithPageSize(100),
	)
	if err != nil {
		return err
	}
	if processParams.Ext.IsNextProcess {
		p.nextProcess(processParams)
	}
	//需要标记最后执行时间点
	p.updateSynDateMark(processParams, prevTime)
	return nil
}

// 检查执行时间是否可以执行，返回是否可以执行，以及上次成功执行时间点
func (p *ProcessExecute) checkExecuteTime(params common.ProcessParams) (bool, string, *string) {
	return CheckExecuteTime(params)
}

// 下一个处理
func (p *ProcessExecute) nextProcess(params common.ProcessParams) {
	NextProcess(params)
}

func (p *ProcessExecute) updateSynDateMark(params common.ProcessParams, prevTime *string) {
	UpdateSynDateMark(params, prevTime)
}
