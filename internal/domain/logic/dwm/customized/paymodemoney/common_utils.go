package paymodemoney

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config/customized/paymodemoneyreport"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/sdk/api/opencooperateecologyservice/yinbao"
)

// GetConfigMerchantIds 获取配置的商户id
func GetConfigMerchantIds(params common.ProcessParams) ([]int, error) {
	merchantIds := make([]int, 0)
	if params.MerchantId != 0 {
		merchantIds = []int{params.MerchantId}
		return merchantIds, nil
	}
	list, err := paymodemoneyreport.GetAllMerchantConfig()
	if err != nil {
		return nil, err
	}
	for _, item := range list {
		merchantIds = append(merchantIds, item.MerchantId)
	}
	return merchantIds, nil
}

// NextProcess 标记执行时间点
func NextProcess(params common.ProcessParams) {
	startTime := carbon.Parse(params.StartTime)
	endTime := carbon.Parse(params.EndTime)
	externalProjectId := params.Ext.ExternalProjectId
	//充值的外部项目id需要归整
	if params.Ext.ExternalMethod == enum.OpenCooperateSysDataMethodYinBaoRecharge {
		externalProjectId = enum.PayModeMoneyExternalProjectIdRechargeYb
	}
	projectId := 0
	if id, ok := enum.PayModeMoneyProjectIdMap[externalProjectId]; ok {
		projectId = id
	}
	for date := startTime; date.Lte(endTime); date = date.AddHours(1) {
		err := paymodemoneyreport.Save(params.MerchantId, projectId, date)
		if err != nil {
			global.LOG.Error("标记执行时间点异常, ",
				zap.Int("MerchantId", params.MerchantId),
				zap.Int("ProjectId", projectId),
				zap.Error(err),
			)
			globalNotice.Error(fmt.Sprintf("标记执行时间点异常, 名称: %d, 项目ID：%d, 错误：%v", params.MerchantId, projectId, err.Error()))
			continue
		}
		global.LOG.Info("标记执行时间点",
			zap.Int("MerchantId", params.MerchantId),
			zap.Int("ProjectId", projectId),
			zap.String("date", date.ToDateTimeString()),
		)
	}
}

// UpdateSynDateMark 保存最后执行时间点
func UpdateSynDateMark(params common.ProcessParams, prevTime *string) {
	var endTime carbon.Carbon
	endTime = carbon.Parse(params.EndTime)
	if prevTime != nil {
		//上次执行时间大于本次执行时间，则不需要更新
		t := carbon.Parse(*prevTime)
		if t.IsValid() && endTime.Lt(t) {
			return
		}
	}
	//需要标记最后执行时间点
	err := paymodemoneyreport.SaveSynMarkConfig(params.MerchantId, struct {
		LastTime string `json:"last_time"`
	}{LastTime: params.EndTime})
	if err != nil {
		global.LOG.Error("保存最后执行时间失败, ",
			zap.Int("MerchantId", params.MerchantId),
			zap.String("StartTime", params.StartTime),
			zap.String("EndTime", params.EndTime),
			zap.Error(err),
		)
		globalNotice.Error(fmt.Sprintf("保存最后执行时间失败, 商户ID：%d，开始时间：%s，结束时间：%s， 错误:%s", params.MerchantId, params.StartTime, params.EndTime, err.Error()))
		return
	}
	return
}

// CheckExecuteTime 检查执行时间是否可以执行，返回是否可以执行，以及上次成功执行时间点
func CheckExecuteTime(params common.ProcessParams) (bool, string, *string) {
	var orderLastTime, rechargeLastTime string
	var err error
	//查询票据
	orderLastTime, err = yinbao.QueryLastSyncSuccessTime(yinbao.QueryLastSyncSuccessTimeRequest{MemberId: params.MerchantId, Type: 1})
	if err != nil {
		return false, "", nil
	}
	rechargeLastTime, err = yinbao.QueryLastSyncSuccessTime(yinbao.QueryLastSyncSuccessTimeRequest{MemberId: params.MerchantId, Type: 2})
	if err != nil {
		return false, "", nil
	}
	if orderLastTime == "" || rechargeLastTime == "" {
		global.LOG.Error("获取最后拉取成功时间异常，请检查是否正常", zap.Int("MerchantId", params.MerchantId))
		globalNotice.Error(fmt.Sprintf("商户: %d, 开放最后拉取成功的时间查询异常，返回空，请检查是否正常", params.MerchantId))
		return false, "", nil
	}
	//取出上次执行时间
	config, err := paymodemoneyreport.GetSynMarkConfig(params.MerchantId)
	if err != nil {
		global.LOG.Error("取出上次执行时间失败", zap.Int("MerchantId", params.MerchantId), zap.Error(err))
		globalNotice.Error(fmt.Sprintf("商户: %d, 取出上次执行时间失败，请检查是否正常", params.MerchantId))
		return false, "", nil
	}
	var lastTime, prevTime carbon.Carbon
	if config != nil {
		prevTime = carbon.Parse(config.LastTime)
	}
	oLastTime := carbon.Parse(orderLastTime)
	rLastTime := carbon.Parse(rechargeLastTime)
	// 获取最小那个时间节点
	if oLastTime.Lte(rLastTime) {
		lastTime = oLastTime
	} else {
		lastTime = rLastTime
	}
	//本次执行的时间范围
	startTime := carbon.Parse(params.StartTime)
	endTime := carbon.Parse(params.EndTime)
	//脚本自动处理限制, 如果上次执行时间大于等于当前时间，则不执行
	if params.Ext.IsAutoProcess && prevTime.IsValid() && prevTime.Gte(endTime) {
		global.LOG.Info("脚本自动执行，检查执行时间, 已执行，",
			zap.Bool("isAutoProcess", params.Ext.IsAutoProcess),
			zap.String("startTime", startTime.ToDateTimeString()),
			zap.String("endTime", endTime.ToDateTimeString()),
			zap.String("prevTime", prevTime.ToDateTimeString()))
		return false, "", nil
	}
	var currentTime string
	//当前查询时间范围小于或等于最后拉取成功的时间, 可以执行脚本
	if endTime.Gt(lastTime) {
		//prevTime和lastTime相差超过3个小时
		if prevTime.IsValid() && lastTime.AddHours(3).Lt(endTime) {
			globalNotice.Warning(fmt.Sprintf("商户: %d, 开放最后拉取成功的时间: %s, 报表拉取的时段: %s ~ %s，开放数据拉取延迟已超3H，需要重点关注", params.MerchantId, lastTime.ToDateTimeString(), startTime.ToDateTimeString(), endTime.ToDateTimeString()))
		} else {
			globalNotice.Warning(fmt.Sprintf("商户: %d, 开放最后拉取成功的时间: %s, 报表拉取的时段: %s ~ %s，开放数据未完全拉取，报表数据处理失败，等待下次处理", params.MerchantId, lastTime.ToDateTimeString(), startTime.ToDateTimeString(), endTime.ToDateTimeString()))
		}
		return false, "", nil
	}
	//上次执行存在，且与本次开始时间相差1s，则只执行当前时间段，并且最大延迟不超3小时，超过3hours，则只执行当前时间段，需要人工补录
	if prevTime.IsValid() && prevTime.AddSeconds(1).Lt(startTime) {
		if prevTime.AddHours(3).Gte(endTime) {
			currentTime = prevTime.AddSeconds(1).ToDateTimeString()
			globalNotice.Warning(fmt.Sprintf("商户: %d, 开放最后拉取成功的时间: %s, 报表拉取的时段: %s ~ %s，开放数据已补全，报表已同步处理", params.MerchantId, lastTime.ToDateTimeString(), prevTime.AddSeconds(1).ToDateTimeString(), endTime.ToDateTimeString()))
		} else {
			currentTime = startTime.SubHours(3).ToDateTimeString()
			globalNotice.Error(fmt.Sprintf("商户: %d, 数据补偿范围过大, 补偿范围: %s ~ %s，仅补偿最近3h数据，其他的需要人工补偿", params.MerchantId, prevTime.AddSeconds(1).ToDateTimeString(), endTime.ToDateTimeString()))
		}
	} else {
		currentTime = startTime.ToDateTimeString()
	}
	prevTimeStr := prevTime.ToDateTimeString()
	//如果是巡检，直接是开始结束，不需要处理
	if params.Ext.IsDataInspection {
		currentTime = startTime.ToDateTimeString()
	}
	global.LOG.Info("检查执行时间,",
		zap.Bool("isProceed", true),
		zap.String("lastTime", currentTime),
		zap.String("prevTime", prevTimeStr),
	)
	return true, currentTime, &prevTimeStr
}
