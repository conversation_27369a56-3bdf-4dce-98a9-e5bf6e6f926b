// base_processor.go
package paymodemoney

import (
	"fmt"
	"github.com/spf13/cast"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/pkg/utils/snowflake"
	"strings"
)

type BaseProcessExecute struct {
	name string
}

func NewBaseProcessExecute(name string) *BaseProcessExecute {
	return &BaseProcessExecute{name: name}
}

func (b *BaseProcessExecute) Name() string {
	return b.name
}

func (b *BaseProcessExecute) ExecuteWithProcessor(params common.ProcessParams, processorFunc func(common.ProcessParams) error) error {
	merchantIds, err := GetConfigMerchantIds(params)
	if err != nil {
		return fmt.Errorf("获取配置商户id失败, 错误:%s", err)
	}
	var errorMessages []string
	for _, merchantId := range merchantIds {
		var paramsNew common.ProcessParams
		paramsNew = params
		paramsNew.MerchantId = merchantId
		err = processorFunc(paramsNew)
		if err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("merchantId %d: %v", merchantId, err))
			continue
		}
	}
	if len(errorMessages) > 0 {
		return fmt.Errorf("执行失败，共 %d 个错误:\n%s", len(errorMessages), strings.Join(errorMessages, "\n"))
	}
	return nil
}

func (b *BaseProcessExecute) ProcessExecute(params common.ProcessParams, newProcessorFunc func(common.ReportProcessBaseParams, ...common.ReportProcessorOption) *common.ReportProcessor, opts ...common.ReportProcessorOption) error {
	uniqueKey, err := snowflake.GlobalSnowflake.NextId()
	if err != nil {
		return fmt.Errorf("生成任务key失败,  错误:%s", err)
	}

	// 创建报表处理参数
	processParams := common.ReportProcessBaseParams{
		StartTime:         params.StartTime,
		EndTime:           params.EndTime,
		MerchantId:        params.MerchantId,
		ExternalProjectId: params.Ext.ExternalProjectId,
		IsDataInspection:  params.Ext.IsDataInspection,
		UniqueKey:         cast.ToString(uniqueKey),
	}
	// 创建报表处理
	processor := newProcessorFunc(
		processParams,
		opts...,
	)
	err = processor.ProcessAll()
	if err != nil {
		return fmt.Errorf("报表数据处理失败: %v", err)
	}
	return nil
}
