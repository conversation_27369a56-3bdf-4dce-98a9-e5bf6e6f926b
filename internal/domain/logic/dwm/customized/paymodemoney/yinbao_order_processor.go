package paymodemoney

import (
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/logic/dwm/customized/paymodemoney/handler"
)

func NewReportYinBaoOrderProcessor(params common.ReportProcessBaseParams, opts ...common.ReportProcessorOption) *common.ReportProcessor {
	rp := &common.ReportProcessor{
		Extractors: map[string]common.DataExtractor{
			"YinBaoOrder": &handler.YinBaoOrderExtractor{},
		},
		// 巡检触发流程
		Inspector: map[string]common.DataInspector{
			"YinBaoOrder": &handler.YinBaoOrderInspector{},
		},
		Cleaners: map[string]common.DataCleaner{
			"YinBaoOrder": &handler.YinBaoOrderCleaner{},
		},
		Saver: map[string]common.DataSaver{
			"YinBaoOrder": &handler.YinBaoOrderSaver{},
		},
		Config: common.GetConfigFromDefault(),
		Params: params,
	}
	for _, opt := range opts {
		opt(rp)
	}
	return rp
}

type YinBaoOrderProcessExecute struct {
	*BaseProcessExecute
}

func NewYinBaoOrderProcessExecute() YinBaoOrderProcessExecute {
	return YinBaoOrderProcessExecute{
		BaseProcessExecute: NewBaseProcessExecute("pay_mode_money_report_yinbao_order"),
	}
}

func (p *YinBaoOrderProcessExecute) Execute(params common.ProcessParams) error {
	return p.ExecuteWithProcessor(params, p.processExecute)
}

func (p *YinBaoOrderProcessExecute) processExecute(params common.ProcessParams) error {
	err := p.ProcessExecute(
		params,
		func(baseParams common.ReportProcessBaseParams, opts ...common.ReportProcessorOption) *common.ReportProcessor {
			return NewReportYinBaoOrderProcessor(baseParams, opts...)
		},
		common.WithMaxWorkers(1),
		common.WithPagination(true),
		common.WithPageSize(100),
	)
	if err != nil {
		return err
	}
	if params.Ext.IsNextProcess {
		p.nextProcess(params)
	}
	return nil
}

// 下一个处理
func (p *YinBaoOrderProcessExecute) nextProcess(params common.ProcessParams) {
	NextProcess(params)
}
