package common

import (
	"context"
	"fmt"
	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/semaphore"
	"report-service/pkg/szerrors"
	"sync"
)

func (p *ReportProcessor) ProcessAll() error {
	// 创建带并发限制的 errgroup
	g, ctx := errgroup.WithContext(context.Background())
	maxWorkers := p.Config.MaxWorkers
	if maxWorkers == 0 {
		maxWorkers = 1
	}
	sem := semaphore.NewWeighted(int64(maxWorkers))
	results := make(map[string]interface{}, len(p.Extractors))
	var resultsMu sync.Mutex
	for k, e := range p.Extractors {
		key := k // 创建局部变量避免闭包问题
		extractor := e
		cleaner := p.Cleaners[key]
		saver := p.Saver[key]
		inspector := p.Inspector[key]
		g.Go(func() error {
			// 获取信号量
			if err := sem.Acquire(ctx, 1); err != nil {
				return err
			}
			defer sem.Release(1)

			var result string
			var err error
			if p.Config.UsePagination {
				// 分页处理
				result, err = p.processWithPagination(key, extractor, cleaner, saver, inspector)
			} else {
				// 不分页处理
				result, err = p.processWithoutPagination(key, extractor, cleaner, saver, inspector)
			}
			if err != nil {
				return err
			}
			// 存储结果
			resultsMu.Lock()
			results[key] = result
			resultsMu.Unlock()

			return nil
		})
	}

	// 等待所有任务完成，任何一个出错都会立即返回
	if err := g.Wait(); err != nil {
		return err
	}

	fmt.Println("Results:", results)
	return nil
}

func (p *ReportProcessor) processWithPagination(key string, extractor DataExtractor,
	cleaner DataCleaner, saver DataSaver, inspector DataInspector) (string, error) {
	pageNum := 1
	pageSize := p.Config.PageSize

	for {
		// 构造分页参数
		pageParams := &ReportProcessParams{
			OriginalParams: p.Params,
			PageParams: &ReportProcessPageParams{
				PageNum:  pageNum,
				PageSize: pageSize,
			},
		}
		isOver, err := p.processExtractor(pageParams, key, extractor, cleaner, saver, inspector)
		if err != nil {
			return "", err
		}
		if isOver {
			break
		}
		pageNum++
	}

	return fmt.Sprintf("Processed %d total Pages every page size %d", pageNum, pageSize), nil
}

func (p *ReportProcessor) processWithoutPagination(key string, extractor DataExtractor,
	cleaner DataCleaner, saver DataSaver, inspector DataInspector) (string, error) {
	pageNum := 1
	pageSize := p.Config.PageSize

	// 不使用分页
	_, err := p.processExtractor(&ReportProcessParams{
		OriginalParams: p.Params,
		PageParams: &ReportProcessPageParams{
			PageNum:  pageNum,
			PageSize: pageSize,
		},
	}, key, extractor, cleaner, saver, inspector)

	if err != nil {
		return "", err
	}

	// 在非分页模式下，我们可能需要更好的方式来统计实际处理的页数
	return fmt.Sprintf("Processed data with page size %d (non-pagination mode)", pageSize), nil
}

// 保持原有单次处理逻辑的方法
func (p *ReportProcessor) processExtractor(params *ReportProcessParams, key string, extractor DataExtractor,
	cleaner DataCleaner, saver DataSaver, inspector DataInspector) (isOver bool, err error) {
	// 提取数据
	data, err := extractor.Extract(params)
	if err != nil {
		// 无数据处理，优先使用这个返回
		if szerrors.IsDataNoFoundError(err) {
			return true, nil
		}
		return true, fmt.Errorf("extract %s failed: %w", key, err)
	}
	// 提取无数据的，返回结束
	if data == nil {
		return true, nil
	}
	// 检查数据
	if params.OriginalParams.IsDataInspection {
		data, err = inspector.Inspector(params, data)
		if err != nil {
			return true, fmt.Errorf("inspect %s failed: %w", key, err)
		}
		if data == nil {
			return false, nil
		}
	}
	// 清洗数据
	cleanedData, err := cleaner.Clean(params, data)
	if err != nil {
		return true, fmt.Errorf("clean %s failed: %w", key, err)
	}
	if cleanedData == nil {
		return false, nil
	}
	// 保存数据
	err = saver.Save(params, cleanedData)
	if err != nil {
		if szerrors.IsDataNoFoundError(err) {
			return false, nil
		}
		return true, fmt.Errorf("save %s failed: %w", key, err)
	}
	return false, nil
}
