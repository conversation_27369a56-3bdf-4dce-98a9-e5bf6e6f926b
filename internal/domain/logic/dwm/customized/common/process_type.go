package common

import (
	"time"
)

// Process 流程接口定义
type Process interface {
	// Name 流程名称
	Name() string
	// Execute 执行流程
	Execute(params ProcessParams) error
}

// ProcessStatus 流程执行状态
type ProcessStatus string

const (
	ProcessPending   ProcessStatus = "pending"   // 等待执行
	ProcessRunning   ProcessStatus = "running"   // 正在执行
	ProcessCompleted ProcessStatus = "completed" // 执行完成
	ProcessFailed    ProcessStatus = "failed"    // 执行失败
)

// ProcessResult 流程执行结果
type ProcessResult struct {
	Name      string
	Status    ProcessStatus
	Error     error
	StartTime time.Time
	EndTime   time.Time
	CostTime  time.Duration
}

type ProcessParams struct {
	StartTime  string           `json:"start_time"`
	EndTime    string           `json:"end_time"`
	MerchantId int              `json:"merchant_id"`
	Ext        ProcessParamsExt `json:"ext"`
}

type ProcessParamsExt struct {
	IsNextProcess     bool   `json:"is_next_process"`
	IsDataInspection  bool   `json:"is_data_inspection"`
	IsAutoProcess     bool   `json:"is_auto_process"`
	ExternalProjectId string `json:"external_project_id"`
	ExternalMethod    string `json:"external_method"` // 外部同步数据方法
}
