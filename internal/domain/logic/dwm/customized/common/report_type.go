package common

// DataExtractor 定义统一的数据提取接口
type DataExtractor interface {
	Extract(params *ReportProcessParams) (interface{}, error)
}

// DataInspector 定义统一的数据检查接口
type DataInspector interface {
	Inspector(params *ReportProcessParams, data interface{}) (interface{}, error)
}

// DataCleaner 定义统一的数据清洗接口
type DataCleaner interface {
	Clean(params *ReportProcessParams, data interface{}) (interface{}, error)
}

// DataSaver 定义统一的数据保存接口
type DataSaver interface {
	Save(params *ReportProcessParams, data interface{}) error
}

// ReportProcessParams 统一处理报表数据参数
type ReportProcessParams struct {
	OriginalParams ReportProcessBaseParams
	// 分页参数
	PageParams *ReportProcessPageParams
}

// ReportProcessBaseParams 统一处理报表数据基础参数
type ReportProcessBaseParams struct {
	UniqueKey         string `json:"unique_key"`
	StartTime         string `json:"start_time"`
	EndTime           string `json:"end_time"`
	MerchantId        int    `json:"merchant_id"`
	IsDataInspection  bool   `json:"is_data_inspection"`
	ExternalProjectId string `json:"external_project_id"`
}

// ReportProcessPageParams 定义分页参数结构
type ReportProcessPageParams struct {
	PageNum  int `json:"page_num"`
	PageSize int `json:"page_size"`
}

// ReportProcessConfig 统一处理报表数据配置
type ReportProcessConfig struct {
	MaxWorkers    int  `json:"max_workers"`
	UsePagination bool `json:"use_pagination"`
	PageSize      int  `json:"page_size"`
}

// ReportProcessor 统一处理报表数据
type ReportProcessor struct {
	Extractors map[string]DataExtractor
	Cleaners   map[string]DataCleaner
	Saver      map[string]DataSaver
	Inspector  map[string]DataInspector
	Config     ReportProcessConfig
	Params     ReportProcessBaseParams
}
