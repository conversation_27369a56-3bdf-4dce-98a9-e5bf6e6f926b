package common

// ReportProcessorOption 添加配置选项
type ReportProcessorOption func(*ReportProcessor)

// WithMaxWorkers 设置最大工作线程数
func WithMaxWorkers(workers int) ReportProcessorOption {
	return func(rp *ReportProcessor) {
		if workers > 0 {
			rp.Config.MaxWorkers = workers
		}
	}
}

// WithPageSize 设置分页大小
func WithPageSize(pageSize int) ReportProcessorOption {
	return func(rp *ReportProcessor) {
		if pageSize > 0 {
			rp.Config.PageSize = pageSize
		}
	}
}

// WithPagination 设置是否使用分页
func WithPagination(usePagination bool) ReportProcessorOption {
	return func(rp *ReportProcessor) {
		rp.Config.UsePagination = usePagination
	}
}

// GetConfigFromDefault 获取默认配置
func GetConfigFromDefault() ReportProcessConfig {
	return ReportProcessConfig{
		MaxWorkers:    1,
		UsePagination: true,
		PageSize:      100,
	}
}
