package customized

import (
	"context"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dwm/customized/common"
	"report-service/internal/domain/logic/dwm/customized/paymodemoney"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

type ExecuteParams struct {
	StartTime  string           `json:"start_time"`
	EndTime    string           `json:"end_time"`
	MerchantId int              `json:"merchant_id"`
	Ext        ExecuteParamsExt `json:"ext"`
}

type ExecuteParamsExt struct {
	IsNextProcess     bool   `json:"is_next_process"`     // 是否是下一次执行
	IsDataInspection  bool   `json:"is_data_inspection"`  // 是否数据巡检
	IsAutoProcess     bool   `json:"is_auto_process"`     // 是否自动执行
	ExternalProjectId string `json:"external_project_id"` // 外部项目ID
	ExternalMethod    string `json:"external_method"`     // 外部同步数据方法
}

// PayModeMoneyReportYinBaoOrder 定制支付方式报表银豹订单数据
func PayModeMoneyReportYinBaoOrder(params ExecuteParams) {
	process := paymodemoney.NewYinBaoOrderProcessExecute()
	executeSingleProcess(&process, params)
}

// PayModeMoneyReportYinBaoRecharge 定制支付方式报表银豹充值数据
func PayModeMoneyReportYinBaoRecharge(params ExecuteParams) {
	process := paymodemoney.NewYinBaoRechargeProcessExecute()
	executeSingleProcess(&process, params)
}

// PayModeMoneyReport 定制支付方式报表数据
func PayModeMoneyReport(params ExecuteParams) {
	process := paymodemoney.NewProcessExecute()
	executeSingleProcess(&process, params)
}

// 自动判断是否开启调试模式， 调试下不管成功或者失败都通知
func autoDebug() bool {
	isDebug := false
	// 非生产环境开启调试模式
	if !utils.Container([]string{enum.EnvProd}, global.CONFIG.System.Env) {
		isDebug = true
	}
	return isDebug
}

// 统一的单一流程执行函数
func executeSingleProcess(process common.Process, params ExecuteParams) {
	manager := NewProcessManager(
		WithMaxWorkers(1),
		WithIsDebug(autoDebug()),
	)
	processParams := common.ProcessParams{
		StartTime:  params.StartTime,
		EndTime:    params.EndTime,
		MerchantId: params.MerchantId,
		Ext: common.ProcessParamsExt{
			IsAutoProcess:     params.Ext.IsAutoProcess,
			IsDataInspection:  params.Ext.IsDataInspection,
			IsNextProcess:     params.Ext.IsNextProcess,
			ExternalProjectId: params.Ext.ExternalProjectId,
			ExternalMethod:    params.Ext.ExternalMethod,
		},
	}
	manager.Register(process, processParams)
	ctx := context.Background()
	_ = manager.Execute(ctx, process.Name())
	manager.NotifyResultException(process.Name())
}
