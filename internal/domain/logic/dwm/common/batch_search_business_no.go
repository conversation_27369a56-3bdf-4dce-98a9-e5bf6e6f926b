package common

import "report-service/internal/domain/repository"

// DiffBusinessNosWithExisting 查询不存在的业务号
func DiffBusinessNosWithExisting(businessNos []int) ([]int, error) {
	records, err := repository.ReportDwmCommonRepository.GetByBusinessNos(businessNos)
	if err != nil {
		return nil, err
	}
	foundBusinessNos := make(map[int]bool)
	for _, record := range records {
		foundBusinessNos[record.BusinessNo] = true
	}
	notFoundBusinessNos := make([]int, 0)
	for _, businessNo := range businessNos {
		if _, ok := foundBusinessNos[businessNo]; !ok {
			notFoundBusinessNos = append(notFoundBusinessNos, businessNo)
		}
	}
	return notFoundBusinessNos, nil
}
