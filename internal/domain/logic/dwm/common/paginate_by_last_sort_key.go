package common

import (
	"report-service/internal/domain/enum"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dm"
	"report-service/internal/domain/repository/dwm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

func PaginateByLastSortKey(
	doQueryParams DoQueryParams,
	lastSortKey *string,
	pageSize int,
) (list []Model, nextSortKey string, err error) {

	// 解析、合并模版维度范围和用户维度范围
	dimensionRange := dmcommon.GetDimensionRange(doQueryParams.DimensionRange, doQueryParams.DoTemplate.SpecialDimension, doQueryParams.DataLimit)

	// 获取特殊产品规则
	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if commonSearchModelSpecialProductRule, err = doQueryParams.getSpecialProductRule(); err != nil {
		err = szerrors.NewLogicError(err)
		return
	}

	//获取集团成员id集合,需要同可查去交集
	groupMemberIds := make([]int, 0)
	if groupMemberIds, err = doQueryParams.getGroupMemberIds(); err != nil {
		err = szerrors.NewLogicError(err)
		return
	}

	filter := dwm.CommonFilterFields{
		OperateTypes:        doQueryParams.OperateTypes,
		OperatedAtStart:     &doQueryParams.StartTime,
		OperatedAtEnd:       &doQueryParams.EndTime,
		MerchantId:          doQueryParams.getMerchantId(),
		DistributorIds:      dimensionRange[enum.DimensionDistributor],
		SpuIds:              dimensionRange[enum.DimensionSpu],
		SkuIds:              dimensionRange[enum.DimensionSku],
		SaleChannels:        dimensionRange[enum.DimensionSaleChannel],
		SalePayModes:        dimensionRange[enum.DimensionPayMode],
		SellOperatorIds:     dimensionRange[enum.DimensionSellOperator],
		OperatorIds:         dimensionRange[enum.DimensionOperator],
		SellSiteIds:         dimensionRange[enum.DimensionSellSite],
		PackTypes:           commonSearchModelSpecialProductRule.PackType,
		ShowBindTypes:       commonSearchModelSpecialProductRule.ShowBindType,
		AnnualCardTypes:     commonSearchModelSpecialProductRule.AnnualCardType,
		ExchangeCouponTypes: commonSearchModelSpecialProductRule.ExchangeCouponType,
		TargetAudience:      doQueryParams.getTargetAudience(),
		NotSpuIds:           doQueryParams.getNotSpuIds(),
		GroupMemberIds:      groupMemberIds,
	}
	poList, nextSortKey, err := repository.ReportDwmCommonRepository.PaginateByLastSortKey(filter, lastSortKey, pageSize)
	if err != nil {
		return
	}
	err = utils.JsonConvertor(poList, &list)
	if err != nil {
		return
	}
	return
}
