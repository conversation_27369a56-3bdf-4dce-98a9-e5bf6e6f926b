package common

import (
	"gitee.com/golang-module/carbon/v2"
	dmcommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
)

type DoQueryParams struct {
	DoTemplate     template.DoListItem           `json:"do_template"`     // 报表模版
	OperateTypes   []int                         `json:"operate_types"`   // 操作类型
	MerchantId     int                           `json:"merchant_id"`     // 商户ID
	StartTime      carbon.Carbon                 `json:"start_time"`      // 开始时间
	EndTime        carbon.Carbon                 `json:"end_time"`        // 结束时间
	DimensionRange dmcommon.DoDimensionRange     `json:"dimension_range"` // 维度范围
	DataLimit      *datajobslimit.LimitSpuConfig `json:"data_limit"`      //数据岗位限制
}
