package common

import (
	"context"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/global"
)

func (m *Model) postToOpenTelemetry() {
	operatedAt := carbon.Parse(m.OperatedAt)
	// 6小时内的指标上报，延迟超过6小时的可能是刷历史数据，上报无意义
	if operatedAt.DiffAbsInHours() > 6 {
		return
	}
	global.OpenTelemetry.DwmDetailLatencyHistogram.Record(
		context.Background(),
		operatedAt.DiffAbsInDuration().Seconds(),
	)
}
