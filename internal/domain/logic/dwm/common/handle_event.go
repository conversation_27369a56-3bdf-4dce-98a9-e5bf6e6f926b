package common

import (
	"encoding/json"
	"fmt"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/event/dwd"
	"report-service/internal/global"
	globalkafka "report-service/internal/global/kafka"
	globalNotice "report-service/internal/global/notice"

	"gitee.com/golang-module/carbon/v2"
	"github.com/segmentio/kafka-go"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

func BatchHandle(messages ...kafka.Message) error {
	models := make([]Model, 0)
	for _, message := range messages {
		model, err := convertMessageToModel(message)
		if err != nil {
			globalNotice.Error(fmt.Sprintf("dwm common batch handle event error: %s", err.Error()))
			global.LOG.Error("handle event error", zap.Error(err))
			continue
		}
		models = append(models, model)

		// 指标上报
		model.postToOpenTelemetry()

		// 处理过时数据
		model.handleOutdatedData()
	}
	return BatchCreate(models)
}

func convertMessageToModel(message kafka.Message) (model Model, err error) {
	switch message.Topic {
	case globalkafka.GetTopicName(dwd.TopicEventReportPay):
		model, err = convertPayEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportVerify):
		model, err = convertVerifyEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportCancel):
		model, err = convertCancelEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportRevoke):
		model, err = convertRevokeEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportAfterSale):
		model, err = convertAfterSaleEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportAddTicket):
		model, err = convertAddTicketEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportFinish):
		model, err = convertFinishEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportCollect):
		model, err = convertCollectEventToModel(message.Value)
	case globalkafka.GetTopicName(dwd.TopicEventReportReprint):
		model, err = convertReprintEventToModel(message.Value)
	default:
		err = fmt.Errorf("unknown topic: %s", message.Topic)
	}
	// 单条消息处理失败，不影响其他消息的处理
	if err != nil {
		return
	}
	return
}

func convertPayEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportPay
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:           dwdEvent.PaidAt,
		BusinessNo:           dwdEvent.PayNo,
		OperateType:          enum.DWMOperateTypePay,
		PayCount:             dwdEvent.Data.Count,
		PayCostPrice:         dwdEvent.Data.CostPrice,
		PayCostDiscountPrice: dwdEvent.Data.CostDiscountPrice,
		PaySalePrice:         dwdEvent.Data.SalePrice,
		PaySaleDiscountPrice: dwdEvent.Data.SaleDiscountPrice,
		TargetAudience:       dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertVerifyEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportVerify
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:              dwdEvent.VerifiedAt,
		BusinessNo:              dwdEvent.VerifyNo,
		OperateType:             enum.DWMOperateTypeVerify,
		VerifyCount:             dwdEvent.Data.Count,
		VerifyCostPrice:         dwdEvent.Data.CostPrice,
		VerifyCostDiscountPrice: dwdEvent.Data.CostDiscountPrice,
		VerifySalePrice:         dwdEvent.Data.SalePrice,
		VerifySaleDiscountPrice: dwdEvent.Data.SaleDiscountPrice,
		TargetAudience:          dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertCancelEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportCancel
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:              dwdEvent.CancelledAt,
		BusinessNo:              dwdEvent.CancelNo,
		OperateType:             enum.DWMOperateTypeCancel,
		CancelCount:             dwdEvent.Data.Count,
		CancelCostPrice:         dwdEvent.Data.CostPrice,
		CancelCostDiscountPrice: dwdEvent.Data.CostDiscountPrice,
		CancelCostFee:           dwdEvent.Data.CostFee,
		CancelSalePrice:         dwdEvent.Data.SalePrice,
		CancelSaleDiscountPrice: dwdEvent.Data.SaleDiscountPrice,
		CancelSaleFee:           dwdEvent.Data.SaleFee,
		TargetAudience:          dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertRevokeEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportRevoke
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:              dwdEvent.RevokedAt,
		BusinessNo:              dwdEvent.RevokeNo,
		OperateType:             enum.DWMOperateTypeRevoke,
		RevokeCount:             dwdEvent.Data.Count,
		RevokeCostPrice:         dwdEvent.Data.CostPrice,
		RevokeCostDiscountPrice: dwdEvent.Data.CostDiscountPrice,
		RevokeCostFee:           dwdEvent.Data.CostFee,
		RevokeSalePrice:         dwdEvent.Data.SalePrice,
		RevokeSaleDiscountPrice: dwdEvent.Data.SaleDiscountPrice,
		RevokeSaleFee:           dwdEvent.Data.SaleFee,
		TargetAudience:          dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertAfterSaleEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportAfterSale
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:     dwdEvent.AfterSaledAt,
		BusinessNo:     dwdEvent.AfterSaleNo,
		OperateType:    enum.DWMOperateTypeAfterSale,
		AfterSaleCount: dwdEvent.Data.Count,
		AfterCostPrice: dwdEvent.Data.CostPrice,
		AfterSalePrice: dwdEvent.Data.SalePrice,
		TargetAudience: dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertAddTicketEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportAddTicket
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:     dwdEvent.AddTicketAt,
		BusinessNo:     dwdEvent.AddTicketNo,
		OperateType:    enum.DWMOperateTypePay,
		PayCount:       dwdEvent.Data.Count,
		PayCostPrice:   dwdEvent.Data.CostPrice,
		PaySalePrice:   dwdEvent.Data.SalePrice,
		TargetAudience: dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertFinishEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportFinish
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:              dwdEvent.FinishedAt,
		BusinessNo:              dwdEvent.FinishNo,
		OperateType:             enum.DWMOperateTypeVerify,
		VerifyCount:             dwdEvent.Data.Count,
		VerifyCostPrice:         dwdEvent.Data.CostPrice,
		VerifyCostDiscountPrice: dwdEvent.Data.CostDiscountPrice,
		VerifySalePrice:         dwdEvent.Data.SalePrice,
		VerifySaleDiscountPrice: dwdEvent.Data.SaleDiscountPrice,
		TargetAudience:          dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func (m *Model) fillCommonFields(
	dwdEventDataBase dwd.EventReportDataBase,
	dwdEventDataPayloadBase dwd.EventReportDataPayloadBase,
) {
	m.SortKey = carbon.Parse(m.OperatedAt).ToTimestampStruct().String() + cast.ToString(m.BusinessNo)
	m.MerchantId = dwdEventDataBase.MerchantId
	m.OrderNo = dwdEventDataBase.OrderNo
	m.TradeNo = dwdEventDataBase.TradeNo
	m.ExternalOperateNo = dwdEventDataBase.ExternalOperateNo
	m.ParentOrderNo = dwdEventDataPayloadBase.ParentOrderNo
	m.ProductType = dwdEventDataPayloadBase.ProductType
	m.SubType = dwdEventDataPayloadBase.SubType
	m.ParentMerchantId = dwdEventDataBase.ParentMerchantId
	m.DistributorId = dwdEventDataBase.DistributorId
	m.PoiId = dwdEventDataBase.PoiId
	m.SpuId = dwdEventDataBase.SpuId
	m.SkuId = dwdEventDataBase.SkuId
	m.SaleChannel = dwdEventDataBase.SaleChannel
	m.OperateChannel = dwdEventDataBase.OperateChannel
	m.CostPayMode = dwdEventDataBase.CostPayMode
	m.SalePayMode = dwdEventDataBase.SalePayMode
	m.SellOperatorId = dwdEventDataBase.SellOperatorId
	m.OperatorId = dwdEventDataBase.OperatorId
	m.SellSiteId = dwdEventDataBase.SellSiteId
	m.OperateSiteId = dwdEventDataBase.OperateSiteId
	m.CostUnitPrice = dwdEventDataBase.CostUnitPrice
	m.SaleUnitPrice = dwdEventDataBase.SaleUnitPrice
	m.PackType = dwdEventDataPayloadBase.PackType
	m.ShowBindType = dwdEventDataPayloadBase.ShowBindType
	m.AnnualCardType = dwdEventDataPayloadBase.AnnualCardType
	m.ExchangeCouponType = dwdEventDataPayloadBase.ExchangeCouponType
}

func convertCollectEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportCollect
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:     dwdEvent.CollectedAt,
		BusinessNo:     dwdEvent.CollectNo,
		OperateType:    enum.DWMOperateTypeCollect,
		CollectCount:   dwdEvent.Data.Count,
		TargetAudience: dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}

func convertReprintEventToModel(event []byte) (detail Model, err error) {
	var dwdEvent dwd.EventReportReprint
	err = json.Unmarshal(event, &dwdEvent)
	if err != nil {
		return
	}
	detail = Model{
		OperatedAt:     dwdEvent.ReprintedAt,
		BusinessNo:     dwdEvent.ReprintNo,
		OperateType:    enum.DWMOperateTypeReprint,
		ReprintCount:   dwdEvent.Data.Count,
		TargetAudience: dwdEvent.Data.Payload.GroupsTag.TargetAudience,
	}
	detail.fillCommonFields(dwdEvent.Data.EventReportDataBase, dwdEvent.Data.Payload.EventReportDataPayloadBase)
	return
}
