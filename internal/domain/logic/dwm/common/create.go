package common

import (
	"go.uber.org/zap"
	"report-service/internal/domain/repository"
	"report-service/internal/domain/repository/dwm"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

func BatchCreate(models []Model) error {
	insertItems := make([]dwm.CommonModel, 0)
	err := utils.JsonConvertor(models, &insertItems)
	if err != nil {
		return err
	}
	if len(insertItems) == 0 {
		return nil
	}
	err = repository.ReportDwmCommonRepository.Insert(insertItems)
	if err != nil {
		return err
	}
	global.LOG.Debug("Insert DWMCommon success.", zap.Int("count", len(insertItems)))
	return nil
}
