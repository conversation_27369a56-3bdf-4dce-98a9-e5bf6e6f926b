package common

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/domain/repository/dm"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
	"strconv"
)

func (d *DoQueryParams) checkNoNeedMerchantId() bool {
	return utils.Container(enum.TemplateQueryNoNeedMerchantId, d.DoTemplate.Category)
}

// 获取商户ID
func (d *DoQueryParams) getMerchantId() *int {
	if d.checkNoNeedMerchantId() {
		return nil
	}
	return &d.MerchantId
}

func (d *DoQueryParams) getGroupMemberIds() (ids []int, err error) {
	ids = make([]int, 0)
	if !utils.Container(enum.TemplateCategoryIsGroupReport, d.DoTemplate.Category) {
		return ids, nil
	}
	//获取集团成员
	ids, err = dimensionscheme.GetSubjectIdListByMerchantId(d.MerchantId, enum.DimensionToTagSourceMapKeyGroup, strconv.Itoa(d.MerchantId), strconv.Itoa(d.MerchantId))
	if err != nil {
		return ids, err
	}
	// 不填=全选
	if len(d.DimensionRange.GroupMember) == 0 {
		return ids, nil
	}
	//集团成员为空，返回不存在值
	if len(ids) == 0 {
		return []int{-2 ^ 31}, nil
	}
	// 取交集
	mergeDimensions := make([]int, 0)
	for _, dimension := range d.DimensionRange.GroupMember {
		if utils.Container(ids, dimension) {
			mergeDimensions = append(mergeDimensions, dimension)
		}
	}
	for _, dimension := range ids {
		if utils.Container(d.DimensionRange.GroupMember, dimension) {
			mergeDimensions = append(mergeDimensions, dimension)
		}
	}
	mergeDimensions = utils.RemoveDuplicate(mergeDimensions)

	// 如果没有交集，返回一个不可能存在的枚举值，使得结果为空（代码调整较小）
	if len(mergeDimensions) == 0 {
		return []int{-2 ^ 31}, nil
	}

	return mergeDimensions, nil
}

// 获取适用人群
func (d *DoQueryParams) getTargetAudience() []string {
	targetAudience := make([]string, 0)
	if d.DimensionRange.TargetAudience != nil && len(d.DimensionRange.TargetAudience) > 0 {
		targetAudience = d.DimensionRange.TargetAudience
	}
	return targetAudience
}

// 获取排除部分产品
func (d *DoQueryParams) getNotSpuIds() []int {
	notSpuIds := make([]int, 0)
	//排查部分产品
	if d.DataLimit != nil && len(d.DataLimit.NotIdList) > 0 {
		notSpuIds = d.DataLimit.NotIdList
	}
	return notSpuIds
}

// 获取特殊产品统计规则
func (d *DoQueryParams) getSpecialProductRule() (dm.CommonSearchModelSpecialProductRule, error) {
	var commonSearchModelSpecialProductRule dm.CommonSearchModelSpecialProductRule
	if err := utils.JsonConvertor(d.DoTemplate.SpecialProductRules, &commonSearchModelSpecialProductRule); err != nil {
		err = szerrors.NewLogicErrorWithText("模板特殊产品统计规则解析失败")
		return commonSearchModelSpecialProductRule, err
	}
	return commonSearchModelSpecialProductRule, nil
}
