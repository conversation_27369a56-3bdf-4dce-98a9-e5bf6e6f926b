package outdateddata

import (
	"context"
	"fmt"
	"report-service/internal/domain/logic/config"
	"report-service/internal/global"
	"report-service/pkg/szerrors"

	"gitee.com/golang-module/carbon/v2"
)

const (
	redisKey = config.CacheKeyPrefix + "outdated_data"
)

func Save(merchantId int, t carbon.Carbon) (err error) {
	// 目前时间最小颗粒度为小时，所以只需要保存小时时间戳
	t = t.StartOfHour()
	hash := fmt.Sprintf("%d_%d", merchantId, t.ToTimestampStruct().Int64())
	err = global.REDIS.HSetNX(context.Background(), redisKey, hash, 0).Err()
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}

type Item struct {
	MerchantId int
	OperatedAt carbon.Carbon
}

func Get() ([]Item, error) {
	values, err := global.REDIS.HGetAll(context.Background(), redisKey).Result()
	if err != nil {
		return nil, szerrors.NewDataPersistenceError(err)
	}
	items := make([]Item, 0, len(values))
	for hash := range values {
		var merchantId int
		var operatedAtTimeStamp int64
		_, err := fmt.Sscanf(hash, "%d_%d", &merchantId, &operatedAtTimeStamp)
		if err != nil {
			return nil, szerrors.NewDataPersistenceError(err)
		}
		items = append(items, Item{
			MerchantId: merchantId,
			OperatedAt: carbon.CreateFromTimestamp(operatedAtTimeStamp),
		})
	}
	return items, nil
}

func Del(item Item) error {
	hash := fmt.Sprintf("%d_%d", item.MerchantId, item.OperatedAt.ToTimestampStruct().Int64())
	err := global.REDIS.HDel(context.Background(), redisKey, hash).Err()
	if err != nil {
		return szerrors.NewDataPersistenceError(err)
	}
	return nil
}
