package paymodemoneyreport

import (
	"errors"
	"report-service/internal/domain/logic/config/merchantconfig"
)

const (
	merchantConfigKey = "customized_pay_mode_money_report_merchant_config"
)

type MerchantConfigPayloadItem struct {
	Key        string `json:"key"`
	MerchantId int    `json:"merchant_id"`
	MemberId   int    `json:"member_id"`
	GroupCode  string `json:"group_code"`
}

func SaveMerchantConfig(merchantId int, value interface{}) error {
	return merchantconfig.SetConfigByKeyAndMerchantId(merchantconfig.SetConfigParams{
		Key:        merchantConfigKey,
		MerchantId: merchantId,
		MemberId:   merchantId,
		Value:      value,
	})
}

func GetMerchantConfig(merchantId int) (*MerchantConfigPayloadItem, error) {
	result, err := merchantconfig.InfoByKeyAndMerchantId(merchantConfigKey, merchantId, merchantId)
	if err != nil {
		return nil, err
	}
	if result == nil {
		return nil, errors.New("商户配置不存在")
	}
	return &MerchantConfigPayloadItem{
		Key:        result.Key,
		MerchantId: result.MerchantId,
		MemberId:   result.MemberId,
		GroupCode:  result.Payload.(map[string]interface{})["group_code"].(string),
	}, nil
}

func GetAllMerchantConfig() ([]MerchantConfigPayloadItem, error) {
	result, err := merchantconfig.ListByKey(merchantConfigKey)
	if err != nil {
		return nil, err
	}
	list := make([]MerchantConfigPayloadItem, 0)
	for _, item := range result {
		list = append(list, MerchantConfigPayloadItem{
			Key:        item.Key,
			MerchantId: item.MerchantId,
			MemberId:   item.MemberId,
			GroupCode:  item.Payload.(map[string]interface{})["group_code"].(string),
		})
	}
	return list, nil
}
