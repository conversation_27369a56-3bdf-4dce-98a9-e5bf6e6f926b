package paymodemoneyreport

import (
	"report-service/internal/domain/logic/config/merchantconfig"
)

const (
	dataSynMarkKey = "customized_pay_mode_money_report_data_syn_mark"
)

type DataSynConfigPayloadItem struct {
	Key        string `json:"key"`
	MerchantId int    `json:"merchant_id"`
	MemberId   int    `json:"member_id"`
	LastTime   string `json:"last_time"`
}

func SaveSynMarkConfig(merchantId int, value interface{}) error {
	return merchantconfig.SetConfigByKeyAndMerchantId(merchantconfig.SetConfigParams{
		Key:        dataSynMarkKey,
		MerchantId: merchantId,
		MemberId:   merchantId,
		Value:      value,
	})
}

func GetSynMarkConfig(merchantId int) (*DataSynConfigPayloadItem, error) {
	result, err := merchantconfig.InfoByKeyAndMerchantId(dataSynMarkKey, merchantId, merchantId)
	if err != nil {
		return nil, err
	}
	if result == nil {
		return nil, nil
	}
	return &DataSynConfigPayloadItem{
		Key:        result.Key,
		MerchantId: result.MerchantId,
		MemberId:   result.MemberId,
		LastTime:   result.Payload.(map[string]interface{})["last_time"].(string),
	}, nil
}
