package config

import (
	"context"
	"encoding/json"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
	"time"
)

const CacheKeyPrefix = "config:"

func GetString(key string) string {
	str, err := global.REDIS.Get(context.Background(), CacheKeyPrefix+key).Result()
	if err == nil {
		return str
	}
	payload, err := repository.ConfigRepository.Get(key)
	if err != nil {
		return ""
	}
	err = utils.JsonConvertor(payload, &str)
	if err != nil {
		return ""
	}
	_ = global.REDIS.Set(context.Background(), CacheKeyPrefix+key, str, enum.CacheTimeImmediate).Err()
	return str
}

func Set(key string, value interface{}, expiration time.Duration) error {
	err := repository.ConfigRepository.Set(key, value)
	if err != nil {
		return err
	}
	err = global.REDIS.Set(context.Background(), CacheKeyPrefix+key, value, expiration).Err()
	if err != nil {
		return szerrors.NewDataPersistenceErrorWithText("failed to set cache")
	}
	return nil
}

func GetJson(key string, expiration time.Duration) string {
	str, err := global.REDIS.Get(context.Background(), CacheKeyPrefix+key).Result()
	if err != nil || str == "" {
		payload, payloadErr := repository.ConfigRepository.Get(key)
		if payloadErr != nil || payload == nil {
			return ""
		}
		payloadStr, payloadStrErr := json.Marshal(payload)
		if payloadStrErr != nil {
			global.LOG.Error("failed to config marshal payload", zap.Error(payloadStrErr))
			return ""
		}
		str = string(payloadStr)
		_ = global.REDIS.Set(context.Background(), CacheKeyPrefix+key, str, expiration).Err()
	}
	return str
}
