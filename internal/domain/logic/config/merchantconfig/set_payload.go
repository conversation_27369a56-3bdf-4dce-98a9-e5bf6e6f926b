package merchantconfig

import (
	"report-service/internal/domain/repository"
)

type SetConfigParams struct {
	Key        string      `json:"key"`
	Value      interface{} `json:"value"`
	MerchantId int         `json:"merchant_id"`
	MemberId   int         `json:"member_id"`
}

func SetConfigByKeyAndMerchantId(params SetConfigParams) error {
	err := repository.MerchantConfigRepository.Set(params.Key, params.MerchantId, params.MemberId, params.Value)
	if err != nil {
		return err
	}
	return nil
}
