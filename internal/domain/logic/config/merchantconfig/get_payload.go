package merchantconfig

import "report-service/internal/domain/repository"

type MerchantConfig struct {
	Id         int         `json:"id"`
	Key        string      `json:"key"`
	MerchantId int         `json:"merchant_id"`
	MemberId   int         `json:"member_id"`
	Payload    interface{} `json:"payload"`
}

func ListByKey(key string) ([]MerchantConfig, error) {
	list, err := repository.MerchantConfigRepository.FindManyByKey(key)
	if err != nil {
		return nil, err
	}
	var result []MerchantConfig
	for _, item := range list {
		result = append(result, MerchantConfig{
			Id:         item.Id,
			Key:        item.Key,
			MerchantId: item.MerchantId,
			MemberId:   item.MemberId,
			Payload:    item.Payload,
		})
	}
	return result, nil
}

func ListByKeyAndMerchantIds(key string, merchantIds []int) ([]MerchantConfig, error) {
	list, err := repository.MerchantConfigRepository.FindManyByKeyAndMerchantIds(key, merchantIds)
	if err != nil {
		return nil, err
	}
	var result []MerchantConfig
	for _, item := range list {
		result = append(result, MerchantConfig{
			Id:         item.Id,
			Key:        item.Key,
			MerchantId: item.MerchantId,
			MemberId:   item.MemberId,
			Payload:    item.Payload,
		})
	}
	return result, nil
}

func InfoByKeyAndMerchantId(key string, merchantId int, memberId int) (*MerchantConfig, error) {
	list, err := repository.MerchantConfigRepository.FindManyByKeyAndMerchantIdsAndMemberIds(key, []int{merchantId}, []int{memberId})
	if err != nil {
		return nil, err
	}
	var result MerchantConfig
	for _, item := range list {
		if item.MerchantId != merchantId {
			continue
		}
		result = MerchantConfig{
			Id:         item.Id,
			Key:        item.Key,
			MerchantId: item.MerchantId,
			MemberId:   item.MemberId,
			Payload:    item.Payload,
		}
	}
	if result.Id == 0 {
		return nil, nil
	}
	return &result, nil
}

func PaginateByKey(key string, pageNum int, pageSize int) ([]MerchantConfig, error) {
	list, err := repository.MerchantConfigRepository.PaginateByKey(key, pageNum, pageSize)
	if err != nil {
		return nil, err
	}
	var result []MerchantConfig
	for _, item := range list {
		result = append(result, MerchantConfig{
			Id:         item.Id,
			Key:        item.Key,
			MerchantId: item.MerchantId,
			MemberId:   item.MemberId,
			Payload:    item.Payload,
		})
	}
	return result, nil
}
