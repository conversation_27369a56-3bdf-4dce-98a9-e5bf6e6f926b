package config

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	"report-service/internal/domain/enum"
)

func UpdateLatestTime(key string, value carbon.Carbon) (err error) {
	lastStatisticTime := GetString(key)
	if lastStatisticTime == "" || carbon.Parse(lastStatisticTime).Lt(value) {
		err = Set(key, value.ToDateTimeString(), enum.CacheTimeImmediate)
		if err != nil {
			return err
		}
	}
	return nil
}

func UpdateLatestTimeByMerchant(key string, merchantId int, value carbon.Carbon) (err error) {
	cacheKey := key + "_" + cast.ToString(merchantId)
	lastStatisticTime := GetString(cacheKey)
	if lastStatisticTime == "" || carbon.Parse(lastStatisticTime).Lt(value) {
		err = Set(cacheKey, value.ToDateTimeString(), enum.CacheTimeImmediate)
		if err != nil {
			return err
		}
	}
	return nil
}
