package kafka

import (
	"errors"
	"report-service/internal/global"
	"report-service/pkg/szkafka"
)

const (
	ProducerNameDefault = "default"
)

// Send 发送一条消息
func Send(topic string, key string, value interface{}) error {
	if producer, ok := szkafka.Producers[ProducerNameDefault]; ok {
		err := producer.Send(GetTopicName(topic), szkafka.ProduceMessage{
			Key:   key,
			Value: value,
		})
		if err != nil {
			return err
		}
		return nil
	}
	return errors.New("producer not found")
}

// SendMulti 发送多条消息，支持消息发送到不同 Topic，在 ProduceMessage 中指定 Topic 即可
func SendMulti(topic string, messages ...szkafka.ProduceMessage) error {
	if producer, ok := szkafka.Producers[ProducerNameDefault]; ok {
		for index, message := range messages {
			if message.Topic != nil {
				messageTopic := GetTopicName(*message.Topic)
				messages[index].Topic = &messageTopic
			}
		}
		return producer.Send(GetTopicName(topic), messages...)
	}
	return errors.New("producer not found")
}

const (
	InternalTopicPrefix = "reportService_"
)

func GetTopicName(topic string) string {
	return InternalTopicPrefix + topic + "_" + global.CONFIG.System.Env
}
