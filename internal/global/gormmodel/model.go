package gormmodel

import (
	"time"
)

type BaseModel struct {
	Id        int       `gorm:"column:id;type:bigint unsigned;not null;primaryKey;comment:主键ID" json:"id"`
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`
}

type SoftDeleteModel struct {
	DeletedAt time.Time `gorm:"column:deleted_at;type:datetime;not null;default:'0000-00-00 00:00:00';comment:删除时间" json:"deleted_at"`
	IsDeleted int       `gorm:"column:is_deleted;type:tinyint unsigned;not null;default:0;comment:是否删除：1已删除，0未删除" json:"is_deleted"`
}
