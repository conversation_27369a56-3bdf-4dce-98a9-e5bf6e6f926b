package notice

import (
	"report-service/internal/global"
	"report-service/pkg/sdk/infrastructure/notice"
)

const (
	LevelWarning = "warning"
	LevelError   = "error"
)

func Send(level, message string) {
	if level == "" || message == "" {
		return
	}
	var levelStrategyIdMap = map[string]string{
		LevelWarning: global.CONFIG.ExternalApi.Infrastructure.NoticeStrategyId,
		LevelError:   global.CONFIG.ExternalApi.Infrastructure.StrategyId,
	}
	if _, ok := levelStrategyIdMap[level]; !ok {
		return
	}
	_ = notice.NewSend(levelStrategyIdMap[level], message)
}

func Warning(messages ...string) {
	Send(LevelWarning, format(messages...))
}

func Error(messages ...string) {
	Send(LevelWarning, format(messages...))
}
