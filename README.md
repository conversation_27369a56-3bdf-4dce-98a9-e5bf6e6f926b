# Report Service

## 项目初始化

将根目录下的 `config.example.yaml` 复制一份并重命名为 `config.yaml`，并修改其中的配置信息

```bash
cp config.example.yaml config.yaml
```

## 项目结构

```bash
.
├── build/                      # 构建输出文件
│   ├── localhost/
│   └── skywalking-server/
├── cmd/                        # 应用程序的入口点代码
│   ├── consume/                # 消费服务
│   ├── crontab/                # 定时任务服务
│   └── server/                 # Gin 驱动的 API 服务
├── internal/                   # 项目的内部实现代码
│   ├── domain/                 # 领域层代码
│   │   ├── enum/               # 枚举类型
│   │   ├── event/              # 领域事件
│   │   ├── logic/              # 业务逻辑
│   │   ├── repository/         # 仓储接口
│   │   └── service/            # 领域服务
│   ├── global/                 # 全局变量和配置
│   └── infra/                  # 基础设施层代码
│       ├── config              # 存储配置结构
│       ├── db                  # 初始化 GORM
│       ├── kafka               # 初始化 Kafka 组件
│       ├── log                 # 初始化日志组件
│       ├── persistence         # 持久化层代码
│       ├── common_init.go      # 初始化通用组件
│       ├── external_api.go     # 初始化外部 API 调用 SDK
│       ├── opentelemetry.go    # 初始化 OpenTelemetry
│       ├── pkgutil.go          # 初始化包工具
│       └── redis.go            # 初始化 Redis
└── pkg/                        # 可复用的包代码
    ├── sdk/                    # 外部服务的 SDK
    ├── szerrors/               # 自定义的 Error 类型
    ├── szkafka/                # 二次包装的 Kafka 组件
    └── utils/                  # 工具函数
```

## 仓储分层

### 业务事实表（Business Fact Table）

- 类似于大数据的ODS层。
- 业务事实表实质是对业务数据做了一定的去噪和组装。
- 其核心作用：
  - 不依赖业务系统的数据表现形式（数据结构），从报表的视角规整了业务数据格式，提升了数据复用的效率；比如目前获取订单数据，通过多个接口获取订单的相关数据，规整后的数据，将订单数据组合成一条记录；另外如果存在其他报表且依赖的字段不超出基础事实表的范围，就可以不用再抽取一次业务数据，直接复用现有业务事实表的数据，提高使用效率。
  - 业务数据和业务事实数据是一一对应的，为数据巡检提供了可行性；由于业务事实数据和业务数据是一一对应关系，就可以实现业务事实数据和业务数据的数据巡检，以及数据自动修复，可以极大的降低报表的维护成本。
  - 业务事实数据被规整到报表系统后，极大的方便了后续的数据维护和数据治理；当遇到一些异常情况需要人工排查的时候，对内业务事实数据和报表数据的比对，对外业务事实数据和业务数据的比对都非常简易。

### 报表事实表（Report Fact Table）

- 类似于大数据的DWD层，是将业务事实表针对报表形态进行了清洗过的明细数据。
- 其核心作用：
  - 作为报表的最小数据单元，其独立存在的形态，可以为后续不同场景的上层指标提供最小颗粒度的数据单元，保证数据的低耦合和复用性
  - 和业务事实表的数据是1:N的映射关系，可以实现对业务事实表的数据巡查

### 报表明细表（Report Detail Table）

- 类似于大数据的DWM层，虽然报表明细表没有做任何数据汇总，但是它将相关不同动作的明细数据针对不同业务场景进行了数据指标和维度的聚合，其核心作用：
  - 为了支撑特定业务场景的指标生成，所以针对报表事实表进行了特定数据的聚合，同时保持了数据的最小颗粒度，可以提供这一类业务场景的不同颗粒度的数据汇总
  - 由于是最小颗粒度的明细数据，和报表事实表的数据是一一对应关系，可以实现对报表事实表的数据巡查

### 报表指标表（Report Indicator Table）

- 类似于大数据的DM层，所有的报表查询都是基于此层数据。
- 其核心作用：
  - 提供不同用户查询特定场景指标数据的数据源，是报表明细数据不同维度上的汇总数据，由于数据规模较小，能够针对特定业务场景提供更加高效的查询和汇总
  - 由于指标表是明细表的汇总结果，所以无法实现数据巡检，所以指标数据的准确结果都是基于T+1的重算机制生成的，当天的实时数据无法保证绝对的准确性

## 附录

- [方案设计](https://12301-cc.feishu.cn/wiki/Gehuw5176iA1QZkV9KSco1jCnOe)
