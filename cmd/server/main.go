package main

import (
	"report-service/cmd/server/infra/gin"
	globalNotice "report-service/internal/global/notice"
	"report-service/internal/infra"
	"report-service/internal/infra/db"
	"report-service/internal/infra/kafka"

	_ "github.com/apache/skywalking-go"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod vendor
//go:generate go env -w GOPRIVATE=gitlab.12301.test
//go:generate go env -w GOINSECURE=gitlab.12301.test
//go:generate go env -w GONOPROXY=gitlab.12301.test
//go:generate go env -w GONOSUMDB=gitlab.12301.test
//go:generate git config --global url."http://ci-docker:<EMAIL>".insteadOf "http://gitlab.12301.test"

func main() {
	infra.CommonInit()
	defer infra.CommonClose()

	// 连接数据库
	db.Init()
	defer db.Close()

	// 初始化 Kafka
	kafka.Init(false)
	defer kafka.Release()

	globalNotice.Warning("Report service started.")

	// 启动gin服务
	gin.RunServer(gin.Routers())
}
