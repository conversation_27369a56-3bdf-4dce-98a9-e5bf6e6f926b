package middleware

import (
	"errors"
	"fmt"
	"net"
	"net/http"
	"net/http/httputil"
	"os"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/utils"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 捕获 panic 异常并记录日志

func Recovery() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestBody, _ := utils.GetRequestBody(c)
		defer func() {
			if err := recover(); err != nil {
				// 检查客户端是否已经关闭了连接
				var brokenPipe bool
				// 是否是网络操作错误（net.OpError）
				if ne, ok := err.(*net.OpError); ok {
					// 是否是系统调用错误（os.SyscallError）
					var se *os.SyscallError
					if errors.As(ne.Err, &se) {
						// 是否包含“broken pipe”或“connection reset by peer”
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") || strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
				}
				httpRequest, _ := httputil.DumpRequest(c.Request, false)
				if brokenPipe {
					global.LOG.Error(
						"[Recovery from panic]",
						zap.String("path", c.Request.URL.Path),
						zap.Any("error", err),
						zap.String("request", string(httpRequest)),
					)
					c.Abort()
					return
				}

				// 开发环境打印堆栈信息，方便跳转代码
				// 没有环境变量 HOSTNAME，当做开发环境
				if utils.GetHostName() == "" {
					fmt.Println(string(debug.Stack()))
					c.JSON(http.StatusInternalServerError, gin.H{
						"code": 500,
						"msg":  fmt.Sprintf("Recovery from panic, error: %v", err),
					})
					return
				}

				// 飞书告警
				globalNotice.Error(
					"[Recovery from panic]",
					"trace_id: "+utils.GetTraceIdFromGin(c),
					"path: "+c.Request.URL.Path,
					"body: "+string(requestBody),
					"error: "+fmt.Sprintf("%v", err),
					"stack: "+string(debug.Stack()),
				)

				// 记录日志
				global.LOG.Error(
					"[Recovery from panic]",
					zap.String("trace_id", utils.GetTraceIdFromGin(c)),
					zap.String("path", c.Request.URL.Path),
					zap.String("body", string(requestBody)),
					zap.Any("error", err),
					zap.String("stack", string(debug.Stack())),
				)

				c.JSON(http.StatusInternalServerError, gin.H{
					"code": 500,
					"msg":  "服务器内部错误",
				})
			}
		}()
		c.Next()
	}
}
