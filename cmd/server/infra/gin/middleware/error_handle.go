package middleware

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func ErrorHandle() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		err := c.Errors.Last()
		if err == nil {
			return
		}

		var systemError *szerrors.SystemError
		if errors.As(err, &systemError) {
			AlertHandle(c, systemError.StackError)
			response.Result(http.StatusInternalServerError, nil, "系统错误", c)
			c.Abort()
			return
		}

		var dataPersistenceError *szerrors.DataPersistenceError
		if errors.As(err, &dataPersistenceError) {
			Alert<PERSON>andle(c, dataPersistenceError.StackError)
			response.Result(http.StatusInternalServerError, nil, "数据持久化失败", c)
			c.Abort()
			return
		}

		var authenticationError *szerrors.AuthenticationError
		if errors.As(err, &authenticationError) {
			AlertHandle(c, authenticationError.StackError)
			response.Result(http.StatusUnauthorized, nil, "认证失败", c)
			c.Abort()
			return
		}

		var remoteError *szerrors.RemoteError
		if errors.As(err, &remoteError) {
			response.Result(http.StatusBadRequest, nil, remoteError.Error(), c)
			c.Abort()
			return
		}

		var invalidParamError *szerrors.InvalidParamError
		if errors.As(err, &invalidParamError) {
			response.Result(http.StatusBadRequest, invalidParamError.ErrMessages, invalidParamError.Error(), c)
			c.Abort()
			return
		}

		// 处理未知错误
		response.Result(http.StatusBadRequest, nil, err.Error(), c)
		c.Abort()
	}
}

func AlertHandle(c *gin.Context, err szerrors.StackError) {
	// 获取请求信息，解析JSON便于查看
	requestBody, _ := c.GetRawData()
	var requestBodyStruct interface{}
	jsonUnmarshalErr := json.Unmarshal(requestBody, &requestBodyStruct)
	// 将请求信息放回，否则会报错
	c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))

	sendStr := "[" + utils.GetTraceIdFromGin(c) + "]Gin Error: " + err.Error() + "\n"
	sendStr += c.Request.Method + " " + c.Request.URL.Path + " " + c.Request.URL.RawQuery + "\n"
	if jsonUnmarshalErr == nil {
		marshal, _ := json.Marshal(requestBodyStruct)
		sendStr += string(marshal) + "\n"
	}
	sendStr += err.TrimmedStack("report-service")
	globalNotice.Error(sendStr)
	fmt.Println(sendStr)

	global.LOG.Error(
		err.Error(),
		zap.String("request_method", c.Request.Method),
		zap.String("request_url", c.Request.URL.Path),
		zap.Any("request_body", requestBodyStruct),
		zap.String("raw_query", c.Request.URL.RawQuery),
		zap.String("stack", err.Stack),
	)
}
