package middleware

import (
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

const (
	ContextKeyAuthSid      = "sid"
	ContextKeyAuthMemberId = "memberId"
	ContextKeyAuthSdType   = "sdtype"
	ContextKeyAuthDType    = "dtype"
)

func PlatformAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		err := authByJavaAuthPlugin(c)
		if err != nil {
			return
		}

		c.Next()
	}
}

func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		err := authByJavaAuthPlugin(c)
		if err != nil {
			return
		}
		sid := c.GetInt("sid")
		if sid != 1 {
			c.AbortWithStatusJSON(401, gin.H{"code": 401, "msg": "非管理员用户，无权访问。"})
			return
		}

		c.Next()
	}
}

func authByJavaAuthPlugin(c *gin.Context) (err error) {
	// 检查 Header 中是否有 "pft-sid"、"pft-member-id" 设置，如果没有则返回 401
	header := c.Request.Header
	sid := cast.ToInt(header.Get("pft-sid"))
	memberId := cast.ToInt(header.Get("pft-member-id"))
	if sid == 0 || memberId == 0 {
		c.AbortWithStatusJSON(401, gin.H{"code": 401, "msg": "Authorization failed."})
		return errors.New("authorization failed")
	}
	sdType := cast.ToInt(header.Get("pft-sdtype"))
	dType := cast.ToInt(header.Get("pft-dtype"))

	//兼容处理
	if sid == memberId {
		sdType = dType
	}

	// 将 "pft-sid"、"pft-member-id" 设置到 Context 中
	c.Set(ContextKeyAuthSid, sid)
	c.Set(ContextKeyAuthMemberId, memberId)
	c.Set(ContextKeyAuthSdType, sdType)
	c.Set(ContextKeyAuthDType, dType)

	return
}
