package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"report-service/internal/global"
	"report-service/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"go.uber.org/zap"
)

func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery
		// 获取请求信息，解析JSON便于查看
		requestBody, _ := utils.GetRequestBody(c)
		var requestBodyStruct interface{}
		_ = json.Unmarshal(requestBody, &requestBodyStruct)
		// 获取响应信息
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw
		c.Next()

		duration := time.Since(start)
		level := zap.InfoLevel
		if c.Writer.Status() >= 500 {
			level = zap.ErrorLevel
		}

		var responseBody interface{}
		_ = json.Unmarshal(blw.body.Bytes(), &responseBody)
		global.LOG.Log(
			level,
			"gin_log",
			zap.String("trace_id", utils.GetTraceIdFromGin(c)),
			zap.String("path", path),
			zap.String("method", c.Request.Method),
			zap.Int("status", c.Writer.Status()),
			zap.String("query", query),
			zap.String("errors", c.Errors.ByType(gin.ErrorTypePrivate).String()),
			zap.Duration("duration", duration),
			zap.Any("request_body", requestBodyStruct),
			zap.Any("response_body", responseBody),
			zap.Any("headers", c.Request.Header),
		)

		// 指标上报
		global.OpenTelemetry.ApiLatencyHistogram.Record(context.Background(), duration.Seconds(), metric.WithAttributes(attribute.String("path", path)))
	}
}

// 自定义继承gin的ResponseWriter
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// 重写Write方法
func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
