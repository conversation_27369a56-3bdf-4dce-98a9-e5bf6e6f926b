// jsonrpc_middleware.go
package middleware

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/app"
	"report-service/pkg/utils"
)

const (
	ContextKeyAuthAppId      = "appId"
	ContextKeyJsonRpcRequest = "jsonRpcRequest"
	ContextKeyIsJsonRpc      = "isJsonRpc"
)

// JsonRpcRequest represents a JSON-RPC 2.0 request
type JsonRpcRequest struct {
	JsonRpc string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
	ID      interface{} `json:"id"`
}

// JsonRpcResponse represents a JSON-RPC 2.0 response
type JsonRpcResponse struct {
	JsonRpc string        `json:"jsonrpc"`
	Result  interface{}   `json:"result,omitempty"`
	Error   *JsonRpcError `json:"error,omitempty"`
	ID      interface{}   `json:"id"`
}

// JsonRpcResultDefault 默认返回结构体
type JsonRpcResultDefault struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

// JsonRpcError represents a JSON-RPC 2.0 error
type JsonRpcError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// JSONJsonRpcError 错误码常量
const (
	ParseError     = -32700
	InvalidRequest = -32600
	MethodNotFound = -32601
	InvalidParams  = -32602
	InternalError  = -32603
	ServerError    = -32000
)

// JsonRpcInit 处理JSON-RPC请求的中间件
func JsonRpcInit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理POST请求, gin路由已限制，可以忽略这个判断
		if c.Request.Method != http.MethodPost {
			SendJSONRPCInvalidRequestError(c, "Only POST method is allowed", nil)
			return
		}

		var req JsonRpcRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			// 按照 JSON-RPC 2.0 规范返回解析错误
			SendJSONRPCParseError(c, err.Error())
			return
		}

		// 验证JSON-RPC版本
		if req.JsonRpc != "2.0" {
			SendJSONRPCInvalidRequestError(c, "Invalid JSON-RPC version", req.ID)
			return
		}

		// 检查方法是否存在
		if req.Method == "" {
			SendJSONRPCInvalidRequestError(c, "Method is required", req.ID)
			return
		}

		// 鉴权检查
		if err := JsonRpcAuth(c, req.ID); err != nil {
			return
		}

		// 将 params 转换为 JSON 并写入 Request.Body， 覆盖掉http请求json参数
		if req.Params != nil {
			// 将 params 序列化为 JSON
			paramsJSON, err := json.Marshal(req.Params)
			if err != nil {
				SendJSONRPCInvalidParamsError(c, "Invalid params format", req.ID)
				return
			}

			// 重新设置 Request.Body
			c.Request.Body = io.NopCloser(bytes.NewBuffer(paramsJSON))
			c.Request.ContentLength = int64(len(paramsJSON))

			// 同时也将 params 注入到 Gin 上下文中，供处理函数使用
			if paramsMap, ok := req.Params.(map[string]interface{}); ok {
				for key, value := range paramsMap {
					c.Set(key, value)
				}
			}
		} else {
			// 如果 params 为空，设置空的 JSON 对象
			emptyJSON := []byte("{}")
			c.Request.Body = io.NopCloser(bytes.NewBuffer(emptyJSON))
			c.Request.ContentLength = int64(len(emptyJSON))
		}

		// 将请求信息存入上下文供后续处理使用
		c.Set(ContextKeyJsonRpcRequest, req)
		c.Set(ContextKeyIsJsonRpc, true)

		// 继续处理请求
		c.Next()
	}
}

func JsonRpcHandler(routers ...JsonRpcRouterItem) gin.HandlerFunc {
	return func(c *gin.Context) {
		isJsonRpc, exists := c.Get(ContextKeyIsJsonRpc)
		if !exists || !isJsonRpc.(bool) {
			return
		}
		// 如果是 JSON-RPC 请求
		req, _ := c.Get(ContextKeyJsonRpcRequest)
		jsonRpcReq := req.(JsonRpcRequest)
		// 路由解析
		routerMap := make(map[string]gin.HandlerFunc, len(routers))
		for _, router := range routers {
			routerMethod := utils.EnsureLeadingChar(router.Method, "/")
			routerMap[routerMethod] = router.Func
		}
		//获取执行函数
		jsonRpcReqMethod := utils.EnsureLeadingChar(jsonRpcReq.Method, "/")
		methodHandler, ok := routerMap[jsonRpcReqMethod]
		if !ok {
			SendJSONRPCMethodNotFoundError(c, jsonRpcReq.ID)
			return
		}
		// 使用 defer/recover 捕获处理函数中的 panic
		defer func() {
			if r := recover(); r != nil {
				// 返回内部错误响应
				SendJSONRPCInternalError(c, "Internal server error", jsonRpcReq.ID, map[string]interface{}{"panic": fmt.Sprintf("%v", r)})

			}
		}()
		// 调用处理函数
		methodHandler(c)
		// 检查是否已经被终止
		if c.IsAborted() {
			return
		}
		// 构造 JSON-RPC 响应
		var result, res interface{}
		var rpcError *JsonRpcError
		if res, exists = c.Get(response.ContextKeyJsonRpcResult); exists {
			result = res
		} else {
			// 没有显式设置结果，使用默认返回
			result = JsonRpcResultDefault{
				Code: http.StatusOK,
				Msg:  "success",
				Data: nil,
			}
		}
		jsonRpcResp := JsonRpcResponse{
			JsonRpc: "2.0",
			Result:  result,
			Error:   rpcError,
			ID:      jsonRpcReq.ID,
		}

		// 返回 JSON-RPC 格式的响应
		c.JSON(http.StatusOK, jsonRpcResp)
	}
}

// JsonRpcRouterItem 路由项
type JsonRpcRouterItem struct {
	Method string
	Func   gin.HandlerFunc
}

// JsonRpcAddRouter 添加路由项
func JsonRpcAddRouter(method string, fun gin.HandlerFunc) JsonRpcRouterItem {
	return JsonRpcRouterItem{
		Method: method,
		Func:   fun,
	}
}

// JsonRpcAuth 鉴权
func JsonRpcAuth(c *gin.Context, id interface{}) error {
	header := c.Request.Header
	appId := header.Get("system")
	appSecret := header.Get("auth-key")
	if appId == "" || appSecret == "" {
		SendJSONRPCInvalidRequestError(c, "Unauthorized: missing system or auth-key headers", id)
		return errors.New("unauthorized")
	}
	info := app.GetAppByAppId(appId)
	if info == nil || info.AppSecret != appSecret {
		SendJSONRPCInvalidRequestError(c, "Unauthorized: invalid system or auth-key", id)
		return errors.New("unauthorized")
	}
	c.Set(ContextKeyAuthAppId, appId)
	return nil
}

// SendJSONJsonRpcError 发送标准的 JSON-RPC 错误响应
func SendJSONJsonRpcError(c *gin.Context, code int, message string, id interface{}, data interface{}) {
	c.AbortWithStatusJSON(http.StatusOK, JsonRpcResponse{
		JsonRpc: "2.0",
		Error: &JsonRpcError{
			Code:    code,
			Message: message,
			Data:    data,
		},
		ID: id,
	})
}

// SendJSONRPCParseError 发送 JSON-RPC 解析错误响应
func SendJSONRPCParseError(c *gin.Context, message string) {
	SendJSONJsonRpcError(c, ParseError, "Parse error: "+message, nil, nil)
}

// SendJSONRPCInvalidRequestError 发送 JSON-RPC 无效请求错误响应
func SendJSONRPCInvalidRequestError(c *gin.Context, message string, id interface{}) {
	SendJSONJsonRpcError(c, InvalidRequest, message, id, nil)
}

// SendJSONRPCMethodNotFoundError 发送 JSON-RPC 方法未找到错误响应
func SendJSONRPCMethodNotFoundError(c *gin.Context, id interface{}) {
	SendJSONJsonRpcError(c, MethodNotFound, "Method not found", id, nil)
}

// SendJSONRPCInvalidParamsError 发送 JSON-RPC 无效参数错误响应
func SendJSONRPCInvalidParamsError(c *gin.Context, message string, id interface{}) {
	SendJSONJsonRpcError(c, InvalidParams, message, id, nil)
}

// SendJSONRPCInternalError 发送 JSON-RPC 内部错误响应
func SendJSONRPCInternalError(c *gin.Context, message string, id interface{}, data interface{}) {
	SendJSONJsonRpcError(c, InternalError, message, id, data)
}
