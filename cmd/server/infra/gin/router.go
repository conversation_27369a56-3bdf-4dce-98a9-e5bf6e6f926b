package gin

import (
	adminAccesswhitelist "report-service/cmd/server/api/admin/v1/accesswhitelist"
	"report-service/cmd/server/api/admin/v1/report/customized/reportvisitsstatistics"
	h5Config "report-service/cmd/server/api/h5/config"
	"report-service/cmd/server/api/h5/entryexitland"
	"report-service/cmd/server/api/h5/report"
	"report-service/cmd/server/api/internalapi/downloadcenter"
	"report-service/cmd/server/api/internalapi/jsonrpc/accesswhitelist"
	"report-service/cmd/server/api/internalapi/tool"
	"report-service/cmd/server/api/internalapi/tool/app"
	toolReport "report-service/cmd/server/api/internalapi/tool/report"
	"report-service/cmd/server/api/internalapi/tool/washtask"
	"report-service/cmd/server/api/platform/v1/dimensionscheme"
	"report-service/cmd/server/api/platform/v1/odas/tourist"
	"report-service/cmd/server/api/platform/v1/report/common"
	"report-service/cmd/server/api/platform/v1/report/config"
	"report-service/cmd/server/api/platform/v1/report/customized/businessdata"
	"report-service/cmd/server/api/platform/v1/report/customizeexportfields"
	platformReportGroupPay "report-service/cmd/server/api/platform/v1/report/grouppay"
	platformReportGroupPayDetail "report-service/cmd/server/api/platform/v1/report/grouppay/detail"
	platformReportGroupVerify "report-service/cmd/server/api/platform/v1/report/groupverify"
	platformReportGroupVerifyDetail "report-service/cmd/server/api/platform/v1/report/groupverify/detail"
	platformReportMultiplePeople "report-service/cmd/server/api/platform/v1/report/multiplepeople"
	platformReportMultiplePeopleetail "report-service/cmd/server/api/platform/v1/report/multiplepeople/detail"
	platformOperationData "report-service/cmd/server/api/platform/v1/report/operationdata"
	platformReportPay "report-service/cmd/server/api/platform/v1/report/pay"
	platformReportPayDetail "report-service/cmd/server/api/platform/v1/report/pay/detail"
	"report-service/cmd/server/api/platform/v1/report/template"
	platformReportVerify "report-service/cmd/server/api/platform/v1/report/verify"
	platformReportVerifyDetail "report-service/cmd/server/api/platform/v1/report/verify/detail"
	"report-service/cmd/server/infra/gin/middleware"

	"github.com/gin-gonic/gin"
)

// Routers 初始化总路由
func Routers() *gin.Engine {
	Router := gin.New()
	Router.Use(middleware.TraceId(), middleware.Recovery(), middleware.Logger(), middleware.ErrorHandle())

	{
		// 平台接口
		platformV1RouterGroup := Router.Group("/platform/v1", middleware.PlatformAuth())

		reportRouterGroup := platformV1RouterGroup.Group("/report")
		{
			// 营运数据报表
			operationDataRouterGroup := reportRouterGroup.Group("/operation-data")
			operationDataRouterGroup.POST("list", platformOperationData.List)
			operationDataRouterGroup.POST("statistics", platformOperationData.Statistics)
			operationDataRouterGroup.POST("create-export-task", platformOperationData.CreateExportTask)
			operationDataRouterGroup.POST("get-latest-updated-at", platformOperationData.GetLatestUpdatedAt)
		}
		{
			// 预定报表
			payRouterGroup := reportRouterGroup.Group("/pay")
			payRouterGroup.POST("list", platformReportPay.List)
			payRouterGroup.POST("statistics", platformReportPay.Statistics)
			payRouterGroup.POST("create-export-task", platformReportPay.CreateExportTask)
			payRouterGroup.POST("get-latest-updated-at", platformReportPay.GetLatestUpdatedAt)
			payRouterGroup.POST("/detail/create-export-task", platformReportPayDetail.CreateExportTask)
		}
		{
			// 验证报表
			verifyRouterGroup := reportRouterGroup.Group("/verify")
			verifyRouterGroup.POST("list", platformReportVerify.List)
			verifyRouterGroup.POST("statistics", platformReportVerify.Statistics)
			verifyRouterGroup.POST("create-export-task", platformReportVerify.CreateExportTask)
			verifyRouterGroup.POST("get-latest-updated-at", platformReportVerify.GetLatestUpdatedAt)
			verifyRouterGroup.POST("/detail/create-export-task", platformReportVerifyDetail.CreateExportTask)
		}

		{
			//多维统计
			touristRouterGroup := reportRouterGroup.Group("/multiple")
			touristRouterGroup.POST("list", platformReportMultiplePeople.List)
			touristRouterGroup.POST("summary-tree", platformReportMultiplePeople.SummaryTree)
			touristRouterGroup.POST("statistics", platformReportMultiplePeople.Statistics)
			touristRouterGroup.POST("create-export-task", platformReportMultiplePeople.CreateExportTask)
			touristRouterGroup.POST("get-latest-updated-at", platformReportMultiplePeople.GetLatestUpdatedAt)
			touristRouterGroup.POST("/detail/create-export-task", platformReportMultiplePeopleetail.CreateExportTask)
			touristRouterGroup.POST("/detail/list", platformReportMultiplePeopleetail.List)
			touristRouterGroup.POST("/detail/statistics", platformReportMultiplePeopleetail.Statistics)
			touristRouterGroup.POST("merchant-config", platformReportMultiplePeople.MerchantConfig)
			touristRouterGroup.POST("merchant-config-progress", platformReportMultiplePeople.MerchantProgress)
		}
		{
			//集团报表-集团销售
			groupPayGroup := reportRouterGroup.Group("/group-pay")
			groupPayGroup.POST("list", platformReportGroupPay.List)
			groupPayGroup.POST("statistics", platformReportGroupPay.Statistics)
			groupPayGroup.POST("create-export-task", platformReportGroupPay.CreateExportTask)
			groupPayGroup.POST("get-latest-updated-at", platformReportGroupPay.GetLatestUpdatedAt)
			groupPayGroup.POST("/detail/create-export-task", platformReportGroupPayDetail.CreateExportTask)
		}
		{
			//集团报表-集团验证
			groupVerifyGroup := reportRouterGroup.Group("/group-verify")
			groupVerifyGroup.POST("list", platformReportGroupVerify.List)
			groupVerifyGroup.POST("statistics", platformReportGroupVerify.Statistics)
			groupVerifyGroup.POST("create-export-task", platformReportGroupVerify.CreateExportTask)
			groupVerifyGroup.POST("get-latest-updated-at", platformReportGroupVerify.GetLatestUpdatedAt)
			groupVerifyGroup.POST("/detail/create-export-task", platformReportGroupVerifyDetail.CreateExportTask)
		}
		{
			//定制报表
			groupCustomizedGroup := reportRouterGroup.Group("/customized")
			{
				//定制报表-营业日报表
				groupBusinessDataGroup := groupCustomizedGroup.Group("/business-data")
				groupBusinessDataGroup.POST("list", businessdata.List)
				groupBusinessDataGroup.POST("statistics", businessdata.Statistics)
				groupBusinessDataGroup.POST("create-export-task", businessdata.CreateExportTask)
				groupBusinessDataGroup.POST("get-latest-updated-at", businessdata.GetLatestUpdatedAt)
			}
		}

		// 维度方案
		dimensionSchemeRouterGroup := platformV1RouterGroup.Group("/dimension-scheme")
		{
			dimensionSchemeRouterGroup.POST("create", dimensionscheme.SchemeCreate)
			dimensionSchemeRouterGroup.POST("modify", dimensionscheme.SchemeModify)
			dimensionSchemeRouterGroup.POST("delete", dimensionscheme.SchemeDelete)
			dimensionSchemeRouterGroup.POST("detail", dimensionscheme.SchemeDelete)
			dimensionSchemeRouterGroup.POST("pagination", dimensionscheme.SchemePagination)
		}

		// 自定义导出字段配置
		customizeExportFieldsRouterGroup := platformV1RouterGroup.Group("/customize-export-fields")
		{
			customizeExportFieldsRouterGroup.POST("enum", customizeexportfields.Enum)
			customizeExportFieldsRouterGroup.POST("save", customizeexportfields.Save)
			customizeExportFieldsRouterGroup.POST("info", customizeexportfields.Info)
			customizeExportFieldsRouterGroup.POST("header", customizeexportfields.Header)
		}

		dimensionGroupRouterGroup := platformV1RouterGroup.Group("/dimension-group")
		{
			dimensionGroupRouterGroup.POST("create", dimensionscheme.GroupCreate)
			dimensionGroupRouterGroup.POST("modify", dimensionscheme.GroupModify)
			dimensionGroupRouterGroup.POST("delete", dimensionscheme.GroupDelete)
			dimensionGroupRouterGroup.POST("pagination", dimensionscheme.GroupPagination)
		}

		dimensionGroupRelationRouterGroup := platformV1RouterGroup.Group("/dimension-group-relation")
		{
			dimensionGroupRelationRouterGroup.POST("list", dimensionscheme.DimensionGroupRelationList)
		}

		//报表模板配置
		templateConfigRouterGroup := reportRouterGroup.Group("/template")
		{
			//模板字段定义
			templateConfigRouterGroup.POST("base-config", template.BaseConfig)
			//获取列表
			templateConfigRouterGroup.POST("list", template.List)
			//获取简易列表
			templateConfigRouterGroup.POST("simple-list", template.SimpleList)
			//获取详情
			templateConfigRouterGroup.POST("detail", template.Detail)
			//新增
			templateConfigRouterGroup.POST("create", template.Create)
			//编辑
			templateConfigRouterGroup.POST("modify", template.Modify)
			//模板删除
			templateConfigRouterGroup.POST("delete", template.Delete)
		}

		//商户配置
		ConfigRouterGroup := reportRouterGroup.Group("/config")
		{
			ConfigRouterGroup.POST("base", config.BaseDefinition)
			ConfigRouterGroup.POST("info", config.GetConfig)
			ConfigRouterGroup.POST("save", config.SaveConfig)
		}

		//通用查询
		searchCommonRouterGroup := reportRouterGroup.Group("/search-common")
		{
			searchCommonRouterGroup.POST("search-dimension", common.SearchDimensionByNeed)
		}

		// ODAS 接口
		odasRouterGroup := platformV1RouterGroup.Group("/odas")
		{
			{
				// tourist 入园
				touristRouterGroup := odasRouterGroup.Group("tourist")
				touristRouterGroup.POST("group-list", tourist.GroupList)
			}
		}
	}

	{
		// 内部接口
		internalRouterGroup := Router.Group("internal")
		{
			// json-rpc
			internalRouterGroup.POST("/jsonrpc", middleware.JsonRpcInit(), middleware.JsonRpcHandler(
				//商户强制跳转查询接口
				middleware.JsonRpcAddRouter("access-white-list/check-access-redirect", accesswhitelist.IsForceRedirect),
				//同步白名单（管理端开放功能操作同步）
				middleware.JsonRpcAddRouter("access-white-list/sync-white-list", accesswhitelist.SyncAccessWhiteList),
			))
		}
		{
			// 下载中心
			internalRouterGroup.POST("download-center/export", downloadcenter.FetchPaginationData)
		}
		{
			// 工具接口
			toolRouterGroup := internalRouterGroup.Group("tool")
			{
				// 报表
				reportRouterGroup := toolRouterGroup.Group("report")
				{
					// 重算指标表
					reportRouterGroup.POST("restat", toolReport.Restat)

					// 数据验证
					reportRouterGroup.POST("data-validation", toolReport.Validate)
					reportRouterGroup.POST("data-validation-v2", toolReport.ValidateData)

					//数据巡检
					reportRouterGroup.POST("data-inspection-hour", toolReport.InspectionHour)

					//游客操作记录小时重跑
					reportRouterGroup.POST("re-tourist-operate-hour", toolReport.TouristOperateHour)
					reportRouterGroup.POST("re-tourist-restat-date", toolReport.TouristRestatDate)
					reportRouterGroup.POST("re-tourist-restat-hour", toolReport.TouristRestatHour)

					//应用失效操作清除数据
					reportRouterGroup.POST("module-tourist-source-area-clean", toolReport.TouristSourceAreaCleanUpData)

					//客源地配置同步接口
					reportRouterGroup.POST("tourist-source-area-syn", toolReport.TouristSourceAreaSyn)
					reportRouterGroup.POST("tourist-source-area-template-syn", toolReport.TouristSourceAreaTemplateSyn)
					reportRouterGroup.POST("tourist-source-area-detail-syn", toolReport.TouristSourceAreaDetailSyn)

					//定制报表内部处理接口
					reportRouterGroup.POST("customized-pay-mode-money-operate", toolReport.PayModeMoneyOperate)
					reportRouterGroup.POST("customized-pay-mode-money-restat", toolReport.PayModeMoneyRestat)
					reportRouterGroup.POST("customized-pay-mode-money-data-inspection", toolReport.PayModeMoneyDataInspection)

					// 报表工具
					reportToolRouterGroup := reportRouterGroup.Group("wash-task")
					reportToolRouterGroup.POST("business-wash-task", washtask.MerchantHistoryTask)
					reportToolRouterGroup.POST("business-wash-task-new", washtask.MerchantHistoryTaskNew)
					reportToolRouterGroup.POST("business-wash-script", washtask.MerchantHistoryScript)
				}

				// 维度方案相关工具
				toolRouterGroup.POST("dimension-to-tag-relation", tool.DimensionToTagRelation)

				// rpc应用
				appToolRouterGroup := toolRouterGroup.Group("app")
				{
					//应用配置
					appToolRouterGroup.POST("save", app.SaveApp)
				}
			}
		}
	}

	{
		// 微平台H5
		h5RouterGroup := Router.Group("/m/h5", middleware.PlatformAuth())
		{
			{
				// 报表
				reportRouterGroup := h5RouterGroup.Group("report")
				reportRouterGroup.POST("list", report.List)
				reportRouterGroup.POST("statistics", report.Statistics)
			}
			{
				// 配置
				configRouterGroup := h5RouterGroup.Group("config")
				configRouterGroup.POST("global-setting", h5Config.GlobalSetting)
				configRouterGroup.POST("sale-channel", h5Config.SaleChannel)
				configRouterGroup.POST("spu-list", h5Config.SpuList)
				configRouterGroup.POST("sku-list", h5Config.SkuList)
				configRouterGroup.POST("template", h5Config.Template)
				configRouterGroup.POST("template-group-list", h5Config.TemplateGroupList)
			}
			{
				// 出入园
				entryExitLandRouterGroup := h5RouterGroup.Group("entry-exit-land")
				entryExitLandRouterGroup.POST("group-list-by-config", entryexitland.GroupListByConfig)
				entryExitLandRouterGroup.POST("summary-inout-by-groups", entryexitland.SummaryInoutByGroups)
			}
		}
	}

	{
		// 管理端接口
		AdminV1RouterGroup := Router.Group("/admin/v1", middleware.AdminAuth())

		{
			// 访问白名单操作接口
			accessWhiteListGroup := AdminV1RouterGroup.Group("/access-white-list")
			{
				accessWhiteListGroup.POST("add", adminAccesswhitelist.ForceRedirectAdd)
				accessWhiteListGroup.POST("remove", adminAccesswhitelist.ForceRedirectRemove)
				accessWhiteListGroup.POST("list", adminAccesswhitelist.ForceRedirectList)
			}

			// 报表访问量统计
			reportVisitsStatisticsGroup := AdminV1RouterGroup.Group("/report-visits-statistics")
			{
				reportVisitsStatisticsGroup.POST("list", reportvisitsstatistics.List)
				reportVisitsStatisticsGroup.POST("create-export-task", reportvisitsstatistics.CreateExportTask)
			}
		}
	}

	return Router
}
