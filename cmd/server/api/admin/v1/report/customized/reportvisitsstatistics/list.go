package reportvisitsstatistics

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dm/customized/reportvisitsstatistics"
)

type ReqoListBase struct {
	StartTime  string `json:"start_time" validate:"required,datetime=2006-01-02 15:04:05"  label:"开始时间"`
	EndTime    string `json:"end_time" validate:"required,datetime=2006-01-02 15:04:05"  label:"结束时间"`
	MerchantId int    `json:"merchant_id" validate:"omitempty" label:"商户ID"`
	OrderBy    string `json:"order_by" validate:"omitempty,oneof='old' 'new'" label:"排序字段"`
}

type ReqoList struct {
	ReqoListBase
	PageNum  int `json:"page_num" validate:"required" label:"页码"`
	PageSize int `json:"page_size" validate:"required" label:"页大小"`
}

func List(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	startTime := carbon.Parse(req.StartTime)
	endTime := carbon.Parse(req.EndTime)
	params := reportvisitsstatistics.DoQueryParams{
		StartTime: startTime,
		EndTime:   endTime,
	}
	if req.MerchantId != 0 {
		params.MerchantId = &req.MerchantId
	}
	if req.OrderBy != "" {
		params.OrderBy = &req.OrderBy
	}
	if req.PageNum != 0 {
		params.PageNum = &req.PageNum
	}
	if req.PageSize != 0 {
		params.PageSize = &req.PageSize
	}
	paginate, total, err := reportvisitsstatistics.Paginate(params)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	data := struct {
		List  []reportvisitsstatistics.RepoListItem `json:"list"`
		Total int                                   `json:"total"`
	}{
		List:  paginate,
		Total: total,
	}

	response.OkWithData(data, c)
}
