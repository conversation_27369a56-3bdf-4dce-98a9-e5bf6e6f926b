package reportvisitsstatistics

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/customized/reportvisitsstatistics"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/szerrors"
)

func CreateExportTask(c *gin.Context) {
	var req ReqoListBase
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	startTime := carbon.Parse(req.StartTime)
	endTime := carbon.Parse(req.EndTime)
	if err = reportvisitsstatistics.CheckTimeRange(startTime, endTime); err != nil {
		return
	}
	fields := enum.ReportVisitsStatisticsFields
	heads := make([][]string, 0)
	for _, field := range fields {
		if head, ok := enum.DimensionFieldsMapReportVisitsStatistics[field]; ok {
			heads = append(heads, []string{head})
		} else if head, ok = enum.IndicatorFieldsMapReportVisitsStatistics[field]; ok {
			heads = append(heads, []string{head})
		} else {
			response.FailWithError(szerrors.NewLogicErrorWithText(fmt.Sprintf("field %s is not supported", field)), c)
			return
		}
	}
	memberId := request.GetMemberId(c)
	taskId := 0
	taskId, err = downloadcenter.AddTask("common", downloadcenter.AddTaskParams{
		Fid:          request.GetSid(c),
		OpId:         &memberId,
		TemplateCode: "new_report_service_visits_statistics",
		Request:      req,
		SelfDefinedStructure: &downloadcenter.AddTaskParamsSelfDefinedStructure{
			Head:  heads,
			Field: fields,
		},
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{"task_id": taskId}, c)
}
