package accesswhitelist

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/accesswhitelist"
)

type ReqoForceRedirectManage struct {
	MerchantId int `json:"merchant_id" validate:"required" label:"商户ID"`
}

func ForceRedirectAdd(c *gin.Context) {
	var req ReqoForceRedirectManage
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	err = accesswhitelist.ForceRedirectAdd(req.MerchantId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

func ForceRedirectRemove(c *gin.Context) {
	var req ReqoForceRedirectManage
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	err = accesswhitelist.ForceRedirectRemove(req.MerchantId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}
