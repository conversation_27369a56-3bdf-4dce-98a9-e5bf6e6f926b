package accesswhitelist

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/accesswhitelist"
)

type ReqoForceRedirectList struct {
	MerchantId int `json:"merchant_id" validate:"omitempty" label:"商户ID"`
	PageNum    int `json:"page_num" validate:"required" label:"页码"`
	PageSize   int `json:"page_size" validate:"required" label:"页大小"`
}

func ForceRedirectList(c *gin.Context) {
	var req ReqoForceRedirectList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	list, total, err := accesswhitelist.ForceRedirectList(req.MerchantId, req.PageNum, req.PageSize)
	if err != nil {
		response.JsonRpcFailWithError(err, c)
		return
	}
	data := struct {
		List  []accesswhitelist.RepoForceRedirectItem `json:"list"`
		Total int                                     `json:"total"`
	}{
		List:  list,
		Total: total,
	}

	response.OkWithData(data, c)
}
