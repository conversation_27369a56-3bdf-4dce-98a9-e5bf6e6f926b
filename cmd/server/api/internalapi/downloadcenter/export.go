package downloadcenter

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"math/big"
	"report-service/cmd/server/api/common/response"
	"report-service/cmd/server/api/internalapi/downloadcenter/exportmethod"
	downloadCenterApi "report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"
)

var methodMap = map[string]downloadCenterApi.PaginationMethod{
	"Report/OperationData":                exportmethod.OperationDataMethod{},
	"Report/Pay":                          exportmethod.PayMethod{},
	"Report/PayDetail":                    exportmethod.PayDetailMethod{},
	"Report/Verify":                       exportmethod.VerifyMethod{},
	"Report/VerifyDetail":                 exportmethod.VerifyDetailMethod{},
	"Report/multiple_tourist_source_area": exportmethod.MultiplePeopleMethod{},
	"Report/multiple_gender":              exportmethod.MultiplePeopleMethod{},
	"Report/multiple_age":                 exportmethod.MultiplePeopleMethod{},
	"Report/multiple_detail":              exportmethod.MultiplePeopleDetailMethod{},
	"Report/GroupPay":                     exportmethod.GroupPayMethod{},
	"Report/GroupPayDetail":               exportmethod.GroupPayDetailMethod{},
	"Report/GroupVerify":                  exportmethod.GroupVerifyMethod{},
	"Report/GroupVerifyDetail":            exportmethod.GroupVerifyDetailMethod{},
	"Report/ReportVisitsStatistics":       exportmethod.ReportVisitsStatisticsMethod{},
}

func FetchPaginationData(c *gin.Context) {
	var req downloadCenterApi.JsonRpcRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var params downloadCenterApi.PaginationParams
	utils.JsonConvertOrPanic(req.Params, &params)

	if _, ok := methodMap[req.Method]; !ok {
		jsonRpcResponse(c, "400", "Method not found", nil)
		return
	}
	paginationResultData, err := methodMap[req.Method].FetchPaginationData(params)
	if err != nil {
		jsonRpcResponse(c, "400", err.Error(), nil)
		return
	}
	if paginationResultData.List == nil {
		paginationResultData.List = make([]interface{}, 0)
	}
	if paginationResultData.LastSequence == nil || *paginationResultData.LastSequence == "" {
		// 最后一页的 sequence 值，默认为 2^127 - 1
		two := big.NewInt(2)
		power := new(big.Int).Exp(two, big.NewInt(127), nil)
		result := new(big.Int).Sub(power, big.NewInt(1))
		lastSequence := result.String()
		paginationResultData.LastSequence = &lastSequence
	}
	jsonRpcResponse(c, "200", "success", paginationResultData)
}

func jsonRpcResponse(c *gin.Context, code string, msg interface{}, data interface{}) {
	c.JSON(200, downloadCenterApi.JsonRpcResponse{
		JsonRpc: "2.0",
		Result: &downloadCenterApi.JsonRpcResult{
			Code: code,
			Msg:  msg,
			Data: data,
		},
		Error: nil, // 无需支持 JsonRPC 协议的错误格式，下载中心不需要 Error 字段
		Id:    carbon.Now().TimestampNano(),
	})
}
