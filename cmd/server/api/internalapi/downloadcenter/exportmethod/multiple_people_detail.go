package exportmethod

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
	reportmultipledetail "report-service/cmd/server/api/platform/v1/report/multiplepeople/detail"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	dwmTourist "report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/common/multipledetail"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"
)

type MultiplePeopleDetailMethod struct{}

func (o MultiplePeopleDetailMethod) FetchPaginationData(params downloadcenter.PaginationParams) (result downloadcenter.PaginationResultData, err error) {
	var req reportmultipledetail.ExportDetailParams
	err = utils.JsonConvertor(params.Request, &req)
	if err != nil {
		return
	}

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			return
		}
	}

	var doFieldsFilter dwmTourist.DoFieldsFilter
	err = utils.JsonConvertor(req.ReqoFieldsRange, &doFieldsFilter)
	if err != nil {
		return
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, params.OpMember.Fid, params.OpMember.OpId)
	if err != nil {
		return
	}

	doList, nextSortKey, err, _ := multipledetail.PaginateByLastSortKey(
		multipledetail.DoQueryParams{
			DoTemplate:     *doTemplate,
			MerchantId:     params.OpMember.Fid,
			StartTime:      carbon.Parse(req.TimeRange.StartTime),
			EndTime:        carbon.Parse(req.TimeRange.EndTime),
			DimensionRange: doDimensionRange,
			FieldsFilter:   doFieldsFilter,
			DataLimit:      req.LimitSpuConfig,
			Fields:         req.Fields,
			RequireCount:   false,
		},
		params.LastSequence,
		1000,
	)
	if err != nil {
		return
	}

	result.List = cast.ToSlice(doList)
	isOver := len(doList) < 1000
	result.IsOver = &isOver
	lastSequence := nextSortKey
	result.LastSequence = &lastSequence

	return
}
