package exportmethod

import (
	platformReportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/grouppay"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
)

type GroupPayMethod struct{}

func (o GroupPayMethod) FetchPaginationData(params downloadcenter.PaginationParams) (result downloadcenter.PaginationResultData, err error) {
	var req platformReportCommon.ReqoPaginationExportListParams
	err = utils.JsonConvertor(params.Request, &req)
	if err != nil {
		return
	}

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			return
		}
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryGroupPay, params.OpMember.Fid, params.OpMember.OpId)
	if err != nil {
		return
	}

	doList, total, err := grouppay.Paginate(
		params.OpMember.Fid,
		req.Type,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		req.TimeGroupType,
		doDimensionRange,
		params.PageNum,
		1000,
	)
	if err != nil {
		return
	}
	isOver := total <= params.PageNum*1000
	result.IsOver = &isOver
	var list []interface{}
	for _, item := range doList {
		var data interface{}
		data, err = o.formatData(item, *doTemplate)
		if err != nil {
			return result, err
		}
		list = append(list, data)
	}
	result.List = list
	return
}

func (o GroupPayMethod) formatData(item grouppay.DoPaginationItem, doTemplate template.DoListItem) (data map[string]interface{}, err error) {
	data = item.DoDimension.FormatByTemplate(doTemplate)
	if utils.Container(doTemplate.Indicator, enum.IndicatorPayCount) {
		data[enum.IndicatorPayCount] = item.PayCount
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorPaySalePrice) {
		data[enum.IndicatorPaySalePrice] = utils.FormatPrice(*item.PaySalePrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorPayCostPrice) {
		data[enum.IndicatorPayCostPrice] = utils.FormatPrice(*item.PayCostPrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorCancelCount) {
		data[enum.IndicatorCancelCount] = item.CancelCount
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorRevokeCount) {
		data[enum.IndicatorRevokeCount] = item.RevokeCount
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorActualSaleCount) {
		data[enum.IndicatorActualSaleCount] = item.ActualSaleCount
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorActualSalePrice) {
		data[enum.IndicatorActualSalePrice] = utils.FormatPrice(*item.ActualSalePrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorActualCostPrice) {
		data[enum.IndicatorActualCostPrice] = utils.FormatPrice(*item.ActualCostPrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorActualProfit) {
		data[enum.IndicatorActualProfit] = utils.FormatPrice(*item.ActualProfit)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorRefundFeeProfit) {
		data[enum.IndicatorRefundFeeProfit] = utils.FormatPrice(*item.RefundFeeProfit)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorAfterSaleCount) {
		data[enum.IndicatorAfterSaleCount] = item.AfterSaleCount
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorAfterSalePrice) {
		data[enum.IndicatorAfterSalePrice] = utils.FormatPrice(*item.AfterSalePrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorAfterCostPrice) {
		data[enum.IndicatorAfterCostPrice] = utils.FormatPrice(*item.AfterCostPrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorCancelSalePrice) {
		data[enum.IndicatorCancelSalePrice] = utils.FormatPrice(*item.CancelSalePrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorCancelCostPrice) {
		data[enum.IndicatorCancelCostPrice] = utils.FormatPrice(*item.CancelCostPrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorRevokeSalePrice) {
		data[enum.IndicatorRevokeSalePrice] = utils.FormatPrice(*item.RevokeSalePrice)
	}
	if utils.Container(doTemplate.Indicator, enum.IndicatorRevokeCostPrice) {
		data[enum.IndicatorRevokeCostPrice] = utils.FormatPrice(*item.RevokeCostPrice)
	}
	return
}
