package exportmethod

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/cmd/server/api/admin/v1/report/customized/reportvisitsstatistics"
	dmReportvisitsstatistics "report-service/internal/domain/logic/dm/customized/reportvisitsstatistics"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"
)

type ReportVisitsStatisticsMethod struct{}

func (o ReportVisitsStatisticsMethod) FetchPaginationData(params downloadcenter.PaginationParams) (result downloadcenter.PaginationResultData, err error) {
	var req reportvisitsstatistics.ReqoList
	err = utils.JsonConvertor(params.Request, &req)
	if err != nil {
		return
	}
	startTime := carbon.Parse(req.StartTime)
	endTime := carbon.Parse(req.EndTime)
	queryParams := dmReportvisitsstatistics.DoQueryParams{
		StartTime: startTime,
		EndTime:   endTime,
	}
	if req.MerchantId != 0 {
		queryParams.MerchantId = &req.MerchantId
	}
	if req.OrderBy != "" {
		queryParams.OrderBy = &req.OrderBy
	}
	queryParams.PageNum = &params.PageNum
	pageSize := 1000
	queryParams.PageSize = &pageSize
	paginate, total, err := dmReportvisitsstatistics.Paginate(queryParams)
	if err != nil {
		return
	}
	isOver := total <= params.PageNum*1000
	result.IsOver = &isOver
	var list []interface{}
	for _, item := range paginate {
		list = append(list, item)
	}
	result.List = list
	return
}
