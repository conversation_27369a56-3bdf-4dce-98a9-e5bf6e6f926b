package exportmethod

import (
	"gitee.com/golang-module/carbon/v2"
	"report-service/cmd/server/api/platform/v1/report/multiplepeople"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	reportmultiple "report-service/internal/domain/service/report/multiplepeople"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"
)

type MultiplePeopleMethod struct{}

func (o MultiplePeopleMethod) FetchPaginationData(params downloadcenter.PaginationParams) (result downloadcenter.PaginationResultData, err error) {
	var req multiplepeople.ExportParams
	err = utils.JsonConvertor(params.Request, &req)
	if err != nil {
		return
	}
	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			return
		}
	}

	page := req.PageInfo.Page
	pageSize := req.PageInfo.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, params.OpMember.Fid, params.OpMember.OpId)
	if err != nil {
		return
	}

	doList, total, err := reportmultiple.Paginate(
		params.OpMember.Fid,
		req.Type,
		req.SubjectType,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
		req.LimitSpuConfig,
		params.PageNum,
		1000,
	)
	if err != nil {
		return
	}
	isOver := total <= params.PageNum*1000
	result.IsOver = &isOver
	var list []interface{}
	for _, item := range doList {
		var data interface{}
		data, err = o.formatData(item, *doTemplate, req.SubjectType)
		if err != nil {
			return result, err
		}
		list = append(list, data)
	}
	result.List = list
	return
}

func (o MultiplePeopleMethod) formatData(item reportmultiple.DoPaginationItem, doTemplate template.DoListItem, subjectType int) (data map[string]interface{}, err error) {
	data = item.DoDimension.FormatByTemplate(doTemplate, subjectType)
	data[enum.IndicatorTouristCount] = item.Count
	return
}
