package exportmethod

import (
	platformReportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/common/detail"
	"report-service/internal/domain/service/report/grouppay"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
	"github.com/spf13/cast"
)

type GroupPayDetailMethod struct{}

func (o GroupPayDetailMethod) FetchPaginationData(params downloadcenter.PaginationParams) (result downloadcenter.PaginationResultData, err error) {
	var req platformReportCommon.ReqoCreateExportTaskParams
	err = utils.JsonConvertor(params.Request, &req)
	if err != nil {
		return
	}

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			return
		}
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryGroupPay, params.OpMember.Fid, params.OpMember.OpId)
	if err != nil {
		return
	}

	operateTypes := grouppay.GetOperateTypes(doTemplate.Indicator)

	doList, nextSortKey, err := detail.PaginateByLastSortKey(
		*doTemplate,
		operateTypes,
		params.OpMember.Fid,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
		req.LimitSpuConfig,
		params.LastSequence,
		1000,
	)
	if err != nil {
		return
	}

	result.List = cast.ToSlice(doList)
	isOver := len(doList) < 1000
	result.IsOver = &isOver
	lastSequence := nextSortKey
	result.LastSequence = &lastSequence

	return
}
