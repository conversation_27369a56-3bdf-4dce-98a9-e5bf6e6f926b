package accesswhitelist

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/accesswhitelist"
)

type ReqoIsForceRedirect struct {
	MerchantId int `json:"merchant_id" validate:"required" label:"商户ID"`
}
type ReqoSyncAccessWhiteList struct {
	MerchantId int  `json:"merchant_id" validate:"required" label:"商户ID"`
	Enable     bool `json:"enable" label:"开启或关闭"`
}

// IsForceRedirect 获取商户是否强制跳转
func IsForceRedirect(c *gin.Context) {
	var req ReqoIsForceRedirect
	err := request.Validated(c, &req)
	if err != nil {
		response.JsonRpcFailWithError(err, c)
		return
	}
	redirect, _ := accesswhitelist.IsForceRedirect(req.MerchantId)
	response.JsonRpcOkWithData(gin.H{
		"merchant_id": req.MerchantId,
		"redirect":    redirect,
	}, c)
}

// SyncAccessWhiteList 同步商户白名单
func SyncAccessWhiteList(c *gin.Context) {
	var req ReqoSyncAccessWhiteList
	err := request.Validated(c, &req)
	if err != nil {
		response.JsonRpcFailWithError(err, c)
		return
	}
	err = accesswhitelist.SyncAccessWhiteList(req.MerchantId, req.Enable)
	if err != nil {
		response.JsonRpcFailWithError(err, c)
		return
	}
	response.JsonRpcOk(c)
}
