package washtask

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/tool"
	"report-service/pkg/utils/snowflake"
	"time"
)

type ReqoMerchantRelationSku struct {
	MerchantIds []int    `json:"merchant_ids" validate:"omitempty" label:"商户id"`
	Begin       string   `json:"begin" validate:"omitempty" label:"开始日期"`
	End         string   `json:"end" validate:"omitempty" label:"结束日期"`
	PageSize    int      `json:"page_size" validate:"omitempty" label:"每页处理条数"`
	OrderIds    []string `json:"order_ids" validate:"omitempty" label:"订单号集合"`
	ActionIds   []int    `json:"action_ids" validate:"omitempty" label:"操作类型集合"`
	SkuIds      []int    `json:"sku_ids" validate:"omitempty" label:"门票集合"`
}

type ReqoMerchantHistoryScript struct {
	MerchantId int      `json:"merchant_id" validate:"omitempty" label:"商户id"`
	Begin      string   `json:"begin" validate:"omitempty" label:"开始日期"`
	End        string   `json:"end" validate:"omitempty" label:"结束日期"`
	PageSize   int      `json:"page_size" validate:"omitempty" label:"每页处理条数"`
	OrderIds   []string `json:"order_ids" validate:"omitempty" label:"订单号集合"`
	ActionIds  []int    `json:"action_ids" validate:"omitempty" label:"操作类型集合"`
	SkuIds     []int    `json:"sku_ids" validate:"omitempty" label:"门票集合"`
	ChainSize  int      `json:"chain_size" validate:"omitempty" label:"并发量"`
}

func MerchantHistoryTask(c *gin.Context) {
	var req ReqoMerchantRelationSku
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if ((req.OrderIds == nil || len(req.OrderIds) == 0) && (req.Begin == "" || req.End == "")) || req.PageSize == 0 {
		response.FailWithError(errors.New(fmt.Sprintf("BusinessWashTask-error 参数错误")), c)
		return
	}

	scriptId, scriptIdErr := snowflake.GlobalSnowflake.NextId()
	if scriptIdErr != nil {
		fmt.Println(fmt.Sprintf("BusinessWashTask-error end. err:%s", err))
		response.FailWithError(err, c)
		return
	}

	go func() {
		//TODO::开始时间
		startTime := time.Now()
		err = tool.BusinessWashTask(req.MerchantIds, req.Begin, req.End, req.PageSize, req.OrderIds, req.ActionIds, scriptId, req.SkuIds)
		if err != nil {
			fmt.Println(fmt.Sprintf("%d BusinessWashTask-error end. err:%s", scriptId, err))
		} else {
			fmt.Println(fmt.Sprintf("%d BusinessWashTask-info end. success", scriptId))
		}
		//TODO::结束时间
		endTime := time.Now()
		fmt.Println(fmt.Sprintf("%d BusinessWashTask-time 脚本清洗结束，花费时间：%v", scriptId, endTime.Sub(startTime)))
	}()

	response.OkWithData(scriptId, c)
}

func MerchantHistoryTaskNew(c *gin.Context) {
	var req ReqoMerchantRelationSku
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if ((req.OrderIds == nil || len(req.OrderIds) == 0) && (req.Begin == "" || req.End == "")) || req.PageSize == 0 {
		response.FailWithError(errors.New(fmt.Sprintf("BusinessWashTask-error 参数错误")), c)
		return
	}

	scriptId, scriptIdErr := snowflake.GlobalSnowflake.NextId()
	if scriptIdErr != nil {
		fmt.Println(fmt.Sprintf("BusinessWashTask-error end. err:%s", err))
		response.FailWithError(err, c)
		return
	}

	go func() {
		//TODO::开始时间
		startTime := time.Now()
		err = tool.BusinessWashTaskNew(req.MerchantIds, req.Begin, req.End, req.PageSize, req.OrderIds, req.ActionIds, scriptId, req.SkuIds)
		if err != nil {
			fmt.Println(fmt.Sprintf("%d BusinessWashTask-error end. err:%s", scriptId, err))
		} else {
			fmt.Println(fmt.Sprintf("%d BusinessWashTask-info end. success", scriptId))
		}
		//TODO::结束时间
		endTime := time.Now()
		fmt.Println(fmt.Sprintf("%d BusinessWashTask-time 脚本清洗结束，花费时间：%v", scriptId, endTime.Sub(startTime)))
	}()

	response.OkWithData(scriptId, c)
}

func MerchantHistoryScript(c *gin.Context) {
	var req ReqoMerchantHistoryScript
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if ((req.OrderIds == nil || len(req.OrderIds) == 0) && (req.Begin == "" || req.End == "")) || req.PageSize == 0 {
		response.FailWithError(errors.New(fmt.Sprintf("BusinessWashTask-error 参数错误")), c)
		return
	}

	scriptId, scriptIdErr := snowflake.GlobalSnowflake.NextId()
	if scriptIdErr != nil {
		fmt.Println(fmt.Sprintf("BusinessWashTask-error end. err:%s", err))
		response.FailWithError(err, c)
		return
	}

	go func() {
		//TODO::开始时间
		startTime := time.Now()
		script := tool.ScriptParams{
			MerchantId:  req.MerchantId,
			Begin:       req.Begin,
			End:         req.End,
			PageSize:    req.PageSize,
			OrderNumIn:  req.OrderIds,
			ActionIn:    req.ActionIds,
			SnowflakeId: scriptId,
			SkuIds:      req.SkuIds,
			ChainSize:   req.ChainSize,
		}
		if script.ChainSize == 0 {
			script.ChainSize = 2
		}
		err = script.BusinessWashScript()
		if err != nil {
			fmt.Println(fmt.Sprintf("%d BusinessWashTask-error end. err:%s", scriptId, err))
		} else {
			fmt.Println(fmt.Sprintf("%d BusinessWashTask-info end. success", scriptId))
		}
		//TODO::结束时间
		endTime := time.Now()
		fmt.Println(fmt.Sprintf("%d BusinessWashTask-time 脚本清洗结束，花费时间：%v", scriptId, endTime.Sub(startTime)))
	}()

	response.OkWithData(scriptId, c)
}
