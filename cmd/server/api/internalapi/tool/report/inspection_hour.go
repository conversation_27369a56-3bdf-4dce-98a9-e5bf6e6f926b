package report

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/ods"
)

type ReqoInspectionHour struct {
	StartDate string `json:"start_date" validate:"required,datetime=2006-01-02 15:04:05" label:"开始日期"`
	EndDate   string `json:"end_date" validate:"required,datetime=2006-01-02 15:04:05" label:"结束日期"`
}

func InspectionHour(c *gin.Context) {
	var req ReqoInspectionHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		for date := startDate; date.Lte(endDate); date = date.AddHours(1) {
			fmt.Println(date.StartOfHour(), date.EndOfHour())
			_ = ods.InspectionHour(date.StartOfHour(), date.EndOfHour())
		}
	}()
	response.Ok(c)
}
