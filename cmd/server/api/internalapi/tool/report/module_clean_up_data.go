package report

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/permission/module/touristsourceareamodule"
)

type ReqoCleanUpData struct {
	Date string `json:"date" validate:"required,datetime=2006-01-02 15:04:05" label:"清除日期"`
}

func TouristSourceAreaCleanUpData(c *gin.Context) {
	var req ReqoCleanUpData
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		date := carbon.Parse(req.Date)
		_ = touristsourceareamodule.CleanUpData(date)

	}()
	response.Ok(c)
}
