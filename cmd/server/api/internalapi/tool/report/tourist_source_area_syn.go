package report

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/tool"
)

type ReqoTouristSourceAreaTemplateSyn struct {
	Type  int         `json:"type"  validate:"required"`
	Fid   int         `json:"fid"  validate:"omitempty"`
	IdMap map[int]int `json:"id_map"  validate:"omitempty"`
}

type ReqoTouristSourceAreaDetailSyn struct {
	Fid       int    `json:"fid"  validate:"omitempty"`
	StartDate string `json:"start_date" validate:"required,datetime=2006-01-02 15:04:05" label:"开始日期"`
	EndDate   string `json:"end_date" validate:"required,datetime=2006-01-02 15:04:05" label:"结束日期"`
	PageSize  int    `json:"page_size"  validate:"omitempty"`
}

func TouristSourceAreaSyn(c *gin.Context) {
	go func() {
		tool.SynSupplierConfig()
	}()
	response.Ok(c)
}

func TouristSourceAreaTemplateSyn(c *gin.Context) {
	var req ReqoTouristSourceAreaTemplateSyn
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		tool.TemplateSyn(req.Type, req.Fid, req.IdMap)
	}()
	response.Ok(c)
}

func TouristSourceAreaDetailSyn(c *gin.Context) {
	var req ReqoTouristSourceAreaDetailSyn
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		tool.TouristSourceAreaDetailSyn(req.StartDate, req.EndDate, req.Fid, req.PageSize)
	}()
	response.Ok(c)
}
