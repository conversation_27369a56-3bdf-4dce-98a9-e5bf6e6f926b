package report

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dm/common"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
)

type ReqoRestat struct {
	StartDate  string `json:"start_date" validate:"required,datetime=2006-01-02" label:"开始日期"`
	EndDate    string `json:"end_date" validate:"required,datetime=2006-01-02" label:"结束日期"`
	Categories []int  `json:"categories" validate:"omitempty,dive,gt=0" label:"分类ID"`
}

const (
	DmCommonDay  = 1
	DmCommonHour = 2
)

func Restat(c *gin.Context) {
	var req ReqoRestat
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	startTime := carbon.Parse(req.StartDate).StartOfDay()
	endTime := carbon.Parse(req.EndDate).EndOfDay()
	if len(req.Categories) == 0 {
		req.Categories = []int{DmCommonDay, DmCommonHour}
	}
	if utils.Container(req.Categories, DmCommonDay) {
		err = common.RestatCommonDay(startTime, endTime)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}
	if utils.Container(req.Categories, DmCommonHour) {
		err = common.RestatCommonHour(startTime, endTime)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}
	response.Ok(c)
}
