package report

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dm/datavalidation"
)

type ReqoValidate struct {
	StartDate  string `json:"start_date" validate:"required,datetime=2006-01-02" label:"开始日期"`
	EndDate    string `json:"end_date" validate:"required,datetime=2006-01-02" label:"结束日期"`
	MerchantId int    `json:"merchant_id" validate:"omitempty" label:"商户ID"`
	SkuId      int    `json:"sku_id" validate:"omitempty" label:"SkuID"`
}

func Validate(c *gin.Context) {
	var req ReqoValidate
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		for date := startDate; date.Lte(endDate); date = date.AddDays(1) {
			datavalidation.Validation(date, req.MerchantId, req.SkuId)
		}
	}()
	response.Ok(c)
}

func ValidateData(c *gin.Context) {
	var req ReqoValidate
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		for date := startDate; date.Lte(endDate); date = date.AddDays(1) {
			datavalidation.ValidationData(date, req.MerchantId, req.SkuId)
		}
	}()
	response.Ok(c)
}
