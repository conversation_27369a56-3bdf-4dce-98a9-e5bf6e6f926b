package report

import (
	"errors"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dm/customized/paymodemoney"
	"report-service/internal/domain/logic/dwm/customized"
	"report-service/internal/global"
)

type ReqoExecuteTaskOperateHour struct {
	StartDate  string `json:"start_date" validate:"required,datetime=2006-01-02 15:04:05" label:"开始日期"`
	EndDate    string `json:"end_date" validate:"required,datetime=2006-01-02 15:04:05" label:"结束日期"`
	MerchantId int    `json:"merchant_id" validate:"omitempty" label:"商户ID"`
	IsReStat   bool   `json:"is_re_stat" validate:"omitempty" label:"是否重算小时"`
}

// PayModeMoneyOperate 支付方式金额报表重算明细数据
func PayModeMoneyOperate(c *gin.Context) {
	var req ReqoExecuteTaskOperateHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if req.StartDate == "" || req.EndDate == "" {
		response.FailWithError(errors.New("参数错误"), c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		//结束时间小于等于当前时间
		currentTime := carbon.Now().EndOfHour()
		if endDate.Gt(currentTime) {
			// 不能处理当前小时数据
			endDate = currentTime
		}
		for date := startDate; date.Lte(endDate); date = date.AddHours(1) {
			customized.PayModeMoneyReport(customized.ExecuteParams{
				StartTime:  date.StartOfHour().ToDateTimeString(),
				EndTime:    date.EndOfHour().ToDateTimeString(),
				MerchantId: req.MerchantId,
				Ext:        customized.ExecuteParamsExt{IsNextProcess: req.IsReStat},
			})
		}
	}()
	response.Ok(c)
}

func PayModeMoneyDataInspection(c *gin.Context) {
	var req ReqoExecuteTaskOperateHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if req.StartDate == "" || req.EndDate == "" {
		response.FailWithError(errors.New("参数错误"), c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		currentTime := carbon.Now().EndOfHour().SubHours(1)
		if endDate.Gt(currentTime) {
			// 不能处理当前小时数据
			endDate = currentTime
		}
		//不能处理当前小时数据
		for date := startDate; date.Lt(endDate); date = date.AddHours(1) {
			customized.PayModeMoneyReport(customized.ExecuteParams{
				StartTime:  date.StartOfHour().ToDateTimeString(),
				EndTime:    date.EndOfHour().ToDateTimeString(),
				MerchantId: req.MerchantId,
				Ext: customized.ExecuteParamsExt{
					IsNextProcess:    req.IsReStat,
					IsDataInspection: true,
				},
			})
		}
	}()
	response.Ok(c)
}

// PayModeMoneyRestat 统计支付方式金额报表指标重算
func PayModeMoneyRestat(c *gin.Context) {
	var req ReqoExecuteTaskOperateHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startTime := carbon.Parse(req.StartDate)
		endTime := carbon.Parse(req.EndDate)
		for date := startTime; date.Lte(endTime); date = date.AddHours(1) {
			paymodemoney.RestatOutdatedItemHour(date.StartOfHour(), date.EndOfHour(), req.MerchantId, 0)
		}
		messages := []string{
			"Start Time: " + startTime.ToDateTimeString(),
			"End Time: " + endTime.ToDateTimeString(),
			"Merchant Id: " + cast.ToString(req.MerchantId),
		}
		global.LOG.Info("statistic pay mode money hour outdated data success", zap.Strings("messages", messages))
		return
	}()
	response.Ok(c)
}
