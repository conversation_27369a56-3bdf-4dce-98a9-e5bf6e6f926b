package report

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	dmtourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/dwm/tourist"
)

type ReqoTouristOperateHour struct {
	StartDate    string `json:"start_date" validate:"required,datetime=2006-01-02 15:04:05" label:"开始日期"`
	EndDate      string `json:"end_date" validate:"required,datetime=2006-01-02 15:04:05" label:"结束日期"`
	MerchantId   int    `json:"merchant_id" validate:"omitempty" label:"商户ID"`
	IsReStatHour bool   `json:"is_re_stat" validate:"omitempty" label:"是否重算小时"`
}

func TouristOperateHour(c *gin.Context) {
	var req ReqoTouristOperateHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		_ = tourist.OperateTask(startDate, endDate, req.MerchantId, req.IsReStatHour)

	}()
	response.Ok(c)
}

func TouristRestatDate(c *gin.Context) {
	var req ReqoTouristOperateHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		_ = dmtourist.RestatTask(startDate, endDate, req.MerchantId)

	}()
	response.Ok(c)
}

func TouristRestatHour(c *gin.Context) {
	var req ReqoTouristOperateHour
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	go func() {
		startDate := carbon.Parse(req.StartDate)
		endDate := carbon.Parse(req.EndDate)
		_ = dmtourist.RestatHourTask(startDate, endDate, req.MerchantId)

	}()
	response.Ok(c)
}
