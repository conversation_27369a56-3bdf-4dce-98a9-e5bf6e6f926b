package tool

import (
	"fmt"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/dimensionscheme"
	"report-service/internal/global"
	"report-service/internal/global/notice"
	"time"

	"github.com/gin-gonic/gin"

	"go.uber.org/zap"
)

// DimensionToTagRelation 将维度方案转换为标签关系
// @Summary 将维度方案转换为标签关系
// @Description 将MySQL的方案、分组、维度值迁移到SelectDB的维度标签关系表
// @Tags 工具
// @Accept json
// @Produce json
// @Success 200 {object} response.Response
// @Router /internal-api/tool/dimension-to-tag-relation [post]
func DimensionToTagRelation(c *gin.Context) {
	go func() {
		startTime := time.Now()
		global.LOG.Info("开始执行维度方案转换为标签关系任务")

		err := dimensionscheme.MigrateScheme()

		duration := time.Since(startTime)

		if err != nil {
			errMsg := fmt.Sprintf("维度方案转换为标签关系失败，耗时：%v，错误：%v", duration, err)
			global.LOG.Error(errMsg, zap.Error(err))
			notice.Error(errMsg)
		} else {
			successMsg := fmt.Sprintf("维度方案转换为标签关系成功，耗时：%v", duration)
			global.LOG.Info(successMsg)
			notice.Warning(successMsg)
		}
	}()

	response.Ok(c)
}
