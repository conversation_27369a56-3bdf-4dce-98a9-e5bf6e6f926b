package app

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/app"
)

type ReqoSaveApp struct {
	AppId     string `json:"app_id" validate:"required" label:"应用ID"`
	AppName   string `json:"app_name" validate:"required" label:"应用名称"`
	AppSecret string `json:"app_secret" validate:"required" label:"应用密钥"`
}

// SaveApp 保存应用
func SaveApp(c *gin.Context) {
	var req ReqoSaveApp
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	err = app.SaveApp(req.AppId, req.AppSecret, req.AppName)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}
