package entryexitland

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/pkg/sdk/odas/grpc/tourist"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
	"strings"

	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ReqoSummaryInoutByGroups struct {
	GroupIds  []int  `json:"group_ids" validate:"required,min=1" label:"分组ID集合"`
	StartTime string `json:"start_time" validate:"required,datetime=2006-01-02 15:04:05" label:"开始时间"`
	EndTime   string `json:"end_time" validate:"required,datetime=2006-01-02 15:04:05" label:"结束时间"`
}

type ResoSummaryInoutByGroups struct {
	EntryCount int `json:"entry_count"`
	ExitCount  int `json:"exit_count"`
}

func SummaryInoutByGroups(c *gin.Context) {
	var req ReqoSummaryInoutByGroups
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	// 校验分组ID是否有效
	gIds := make([]int, 0)
	groupListRes, err := tourist.GroupList(int64(request.GetSid(c)))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	for _, group := range groupListRes.List {
		if utils.Container(req.GroupIds, int(group.Id)) {
			gIds = append(gIds, int(group.Id))
		}
	}
	if len(gIds) == 0 {
		response.FailWithError(szerrors.NewInvalidParamErrorWithText("无有效分组ID"), c)
		return
	}
	// 查询出入园人数
	res, err := tourist.SummaryInoutByGroups(
		strings.Join(cast.ToStringSlice(gIds), ","),
		carbon.Parse(req.StartTime).StdTime(),
		carbon.Parse(req.EndTime).StdTime(),
		true, // 客流优化就是假数据，不需要假数据
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(ResoSummaryInoutByGroups{
		EntryCount: int(res.In),
		ExitCount:  int(res.Out),
	}, c)
}
