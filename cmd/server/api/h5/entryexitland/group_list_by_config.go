package entryexitland

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/config"
	"report-service/pkg/sdk/odas/grpc/tourist"
	touristpb "report-service/pkg/sdk/odas/grpc/tourist/pb"
	"report-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

func GroupListByConfig(c *gin.Context) {
	sid := request.GetSid(c)
	res, err := tourist.GroupList(int64(sid))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	parkPeopleSettingData, err := config.ParkPeopleSetting(sid)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	configGroupIds := parkPeopleSettingData.DataRange
	groupList := make([]*touristpb.GroupListResponseDTO, 0)
	for _, dto := range res.List {
		if utils.Container(configGroupIds, int(dto.Id)) {
			groupList = append(groupList, dto)
		}
	}
	response.OkWithData(groupList, c)
}
