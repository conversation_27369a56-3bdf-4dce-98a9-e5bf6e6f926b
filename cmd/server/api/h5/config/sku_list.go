package config

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/sdk/api/distributioncenter/platformsalelist"
	"report-service/pkg/sdk/api/ticketservice"
	"report-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

type ReqoSkuList struct {
	Category int    `json:"category" validate:"required" label:"模板分类"`
	Name     string `json:"name" validate:"omitempty,max=255" label:"名称"`
	request.PageInfo
}

type ResoSkuItem struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

func SkuList(c *gin.Context) {
	var req ReqoSkuList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	doTemplate, err := template.Detail(0, req.Category, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	tidList := make([]int, 0, len(doTemplate.SpecialDimension.Sku))
	if len(doTemplate.SpecialDimension.Sku) > 0 {
		for _, v := range doTemplate.SpecialDimension.Sku {
			tidList = append(tidList, v.Id)
		}
	}
	if len(tidList) == 0 {
		//支持数据岗位权限
		params := make(map[string]interface{})
		params["fid"] = request.GetSid(c)
		params["ticketStatusList"] = []int{1, 2}
		params["notPTypes"] = []string{"Q"}
		params["pageNum"] = req.Page
		params["pageSize"] = req.PageSize
		if req.Name != "" {
			params["ticketName"] = req.Name
		}

		lidList := make([]int, 0)
		if len(doTemplate.SpecialDimension.Spu) > 0 {
			for _, v := range doTemplate.SpecialDimension.Spu {
				lidList = append(lidList, v.Id)
			}
			lidList = dataLimit.GetIdList(lidList)
		} else {
			lidList = dataLimit.IdList
		}
		if len(lidList) > 0 {
			params["lidList"] = lidList
		}
		//排查部分产品
		if len(dataLimit.NotIdList) > 0 {
			params["notLidList"] = dataLimit.NotIdList
		}

		rows, total, rowsErr := platformsalelist.QueryTicketNameListByPaging(params)
		if rowsErr != nil {
			response.FailWithError(rowsErr, c)
			return
		}

		idList := make([]int, 0)
		list := make([]ResoSkuItem, 0, len(rows))
		for _, row := range rows {
			if utils.Container(idList, *row.Tid) {
				continue
			}
			idList = append(idList, *row.Tid)
			list = append(list, ResoSkuItem{
				Id:   *row.Tid,
				Name: *row.TicketName,
			})
		}
		response.OkWithData(response.PageResultData(list, total, req.Page, req.PageSize), c)
		return
	} else {
		//已配置过的
		chunkList := utils.ChunkSlice(tidList, req.PageSize)
		if len(chunkList) == 0 || req.Page > len(chunkList) {
			response.OkWithData(response.PageResultData(make([]ResoSkuItem, 0), 0, req.Page, req.PageSize), c)
			return
		}
		chunkTids := []int{}
		for k, v := range chunkList {
			if k+1 == req.Page {
				chunkTids = v
			}
		}
		if len(chunkTids) == 0 {
			response.OkWithData(response.PageResultData(make([]ResoSkuItem, 0), 0, req.Page, req.PageSize), c)
		}
		skuIdKeyNameMap, skuErr := ticketservice.QueryTicketTitleByIds(chunkTids)
		if skuErr != nil {
			response.FailWithError(skuErr, c)
			return
		}

		idList := make([]int, 0)
		list := make([]ResoSkuItem, 0, len(skuIdKeyNameMap))
		for tid, name := range skuIdKeyNameMap {
			if utils.Container(idList, tid) {
				continue
			}
			idList = append(idList, tid)
			list = append(list, ResoSkuItem{
				Id:   tid,
				Name: name,
			})
		}

		response.OkWithData(response.PageResultData(list, len(tidList), req.Page, req.PageSize), c)
		return
	}

}
