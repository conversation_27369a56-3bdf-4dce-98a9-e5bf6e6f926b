package config

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template"

	"github.com/gin-gonic/gin"
)

type ReqoSaleChannel struct {
	Category int `json:"category" validate:"required" label:"模板分类"`
}

func SaleChannel(c *gin.Context) {
	var req ReqoSaleChannel
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	doTemplate, err := template.Detail(0, req.Category, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if len(doTemplate.SpecialDimension.SaleChannel) > 0 {
		resp := make(map[int]string, len(doTemplate.SpecialDimension.SaleChannel))
		for _, v := range doTemplate.SpecialDimension.SaleChannel {
			resp[v.Id] = v.Name
		}
		response.OkWithData(resp, c)
		return
	}
	response.OkWithData(enum.OrderModeMap, c)
}
