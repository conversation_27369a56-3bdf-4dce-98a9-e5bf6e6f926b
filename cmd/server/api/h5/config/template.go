package config

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/template"

	"github.com/gin-gonic/gin"
)

type ReqoTemplate struct {
	Category int `json:"category" validate:"required" label:"模板分类"`
}

func Template(c *gin.Context) {
	var req ReqoTemplate
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	doTemplate, err := template.Detail(0, req.Category, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(doTemplate, c)
}
