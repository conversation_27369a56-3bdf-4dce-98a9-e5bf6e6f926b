package config

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ReqoTemplateGroupList struct {
	Category int `json:"category" validate:"required" label:"模板分类"`
	request.PageInfo
}

func TemplateGroupList(c *gin.Context) {
	var req ReqoTemplateGroupList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	doTemplate, err := template.Detail(0, req.Category, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if !cast.ToBool(doTemplate.CanQueryDimension.Enable) ||
		doTemplate.CanQueryDimension.Option == "" ||
		!utils.Container(doTemplate.CanQueryDimension.ExtOption, enum.TemplateExtOptionUseDimensionSchemeGroup) {
		response.FailWithError(szerrors.NewLogicErrorWithText("当前模板未配置“使用维度分组方案“，暂不支持查看方案分组"), c)
		return
	}
	var mapDimensionScheme map[string]common.ChildItem
	err = utils.JsonConvertor(doTemplate.DimensionScheme, &mapDimensionScheme)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	childItem, ok := mapDimensionScheme[doTemplate.CanQueryDimension.Option]
	if !ok {
		response.FailWithError(szerrors.NewLogicErrorWithText("当前模板未配置对应维度方案，无法查询方案分组"), c)
		return
	}
	list, total, err := dimensionscheme.GroupPagination(request.GetSid(c), childItem.Id, req.Page, req.PageSize)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(response.PageResultData(list, total, req.Page, req.PageSize), c)
}
