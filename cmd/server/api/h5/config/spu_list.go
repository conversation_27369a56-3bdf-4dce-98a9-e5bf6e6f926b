package config

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/sdk/api/distributioncenter/platformsalelist"
	"report-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

type ReqoSpuList struct {
	Category int    `json:"category" validate:"required" label:"模板分类"`
	Name     string `json:"name" validate:"omitempty,max=255" label:"名称"`
	request.PageInfo
}

type ResoSpuItem struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

func SpuList(c *gin.Context) {
	var req ReqoSpuList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	doTemplate, err := template.Detail(0, req.Category, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	lidList := make([]int, 0)
	if len(doTemplate.SpecialDimension.Spu) > 0 {
		for _, v := range doTemplate.SpecialDimension.Spu {
			lidList = append(lidList, v.Id)
		}
		lidList = dataLimit.GetIdList(lidList)
	} else {
		lidList = dataLimit.IdList
	}
	notLidIds := make([]int, 0)
	//排查部分产品
	if len(dataLimit.NotIdList) > 0 {
		notLidIds = dataLimit.NotIdList
	}

	queryExpiration := true
	rows, total, err := platformsalelist.QueryPlatformAuthorityLandByPaging(platformsalelist.PlatformAuthorityLandQuery{
		Fid:             request.GetSid(c),
		LandTitleLike:   &req.Name,
		PageNum:         req.Page,
		PageSize:        req.PageSize,
		QueryExpiration: &queryExpiration,
		QueryTotal:      2,
		LidList:         lidList,
		NotLidList:      notLidIds,
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	idList := make([]int, 0)
	list := make([]ResoSpuItem, 0, len(rows))
	for _, row := range rows {
		if utils.Container(idList, *row.Lid) {
			continue
		}
		idList = append(idList, *row.Lid)
		list = append(list, ResoSpuItem{
			Id:   *row.Lid,
			Name: *row.LandTitle,
		})
	}
	response.OkWithData(response.PageResultData(list, total, req.Page, req.PageSize), c)
}
