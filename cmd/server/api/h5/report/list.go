package report

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	apiPlatformCommon "report-service/cmd/server/api/platform/v1/report/common"
	logicDmCommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/service/report/h5"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
)

type ReqoList struct {
	Category       int                                   `json:"category" validate:"required" label:"报表类型"`
	TimeRange      apiPlatformCommon.ReqoTimeRange       `json:"time_range" validate:"required" label:"查询周期"`
	DimensionRange *apiPlatformCommon.ReqoDimensionRange `json:"dimension_range" validate:"omitempty" label:"维度数据范围"`
	TagCodes       []string                              `json:"tag_codes" validate:"omitempty" label:"标签编码集合"`
	request.PageInfo
}

type ResoPaginationItem struct {
	SpuId           *int    `json:"spu_id,omitempty"`            // SpuID
	SpuName         *string `json:"spu_name,omitempty"`          // Spu名称
	SkuId           *int    `json:"sku_id,omitempty"`            // SkuID
	SkuName         *string `json:"sku_name,omitempty"`          // Sku名称
	SaleChannel     *int    `json:"sale_channel,omitempty"`      // 销售渠道
	SaleChannelName *string `json:"sale_channel_name,omitempty"` // 销售渠道名称
	SaleUnitPrice   *int    `json:"sale_unit_price,omitempty"`   // 销售单价
	// 标签维度
	SpuTagCode         *string `json:"spu_tag_code,omitempty"`          // SPU标签编码
	SpuTagName         *string `json:"spu_tag_name,omitempty"`          // SPU标签名称
	SkuTagCode         *string `json:"sku_tag_code,omitempty"`          // SKU标签编码
	SkuTagName         *string `json:"sku_tag_name,omitempty"`          // SKU标签名称
	SaleChannelTagCode *string `json:"sale_channel_tag_code,omitempty"` // 销售渠道标签编码
	SaleChannelTagName *string `json:"sale_channel_tag_name,omitempty"` // 销售渠道标签名称
	ResoIndicator
}

type ResoIndicator struct {
	ActualSaleCount       *int `json:"actual_sale_count,omitempty"`        // 实售数量
	ActualSalePrice       *int `json:"actual_sale_price,omitempty"`        // 实售金额
	ActualVerifyCount     *int `json:"actual_verify_count,omitempty"`      // 净验证数量
	ActualVerifySalePrice *int `json:"actual_verify_sale_price,omitempty"` // 净验证销售金额
}

func List(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	page := req.PageInfo.Page
	pageSize := req.PageInfo.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	var doDimensionRange logicDmCommon.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	doList, total, err := h5.Paginate(
		request.GetSid(c),
		request.GetMemberId(c),
		req.Category,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		req.TagCodes,
		doDimensionRange,
		dataLimit,
		page,
		pageSize,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var list []ResoPaginationItem
	err = utils.JsonConvertor(doList, &list)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(response.PageResultData(list, total, req.PageInfo.Page, req.PageInfo.PageSize), c)
}
