package report

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	logicDmCommon "report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/service/report/h5"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
)

func Statistics(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	var doDimensionRange logicDmCommon.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	doStatistics, err := h5.Statistics(
		request.GetSid(c),
		request.GetMemberId(c),
		req.Category,
		req.TagCodes,
		doDimensionRange,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		dataLimit,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var reso ResoIndicator
	err = utils.JsonConvertor(doStatistics, &reso)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(reso, c)
}
