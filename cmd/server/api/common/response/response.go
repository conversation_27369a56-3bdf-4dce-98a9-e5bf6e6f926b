package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

func Result(code int, data interface{}, msg string, c *gin.Context) {
	// 开始时间
	c.JSO<PERSON>(http.StatusOK, Response{
		code,
		data,
		msg,
	})
}

func Ok(c *gin.Context) {
	Result(http.StatusOK, nil, "success.", c)
}

func OkWithData(data interface{}, c *gin.Context) {
	Result(http.StatusOK, data, "success", c)
}

func FailWithError(err error, c *gin.Context) {
	_ = c.<PERSON>rror(err)
}
