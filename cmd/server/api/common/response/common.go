package response

type PageResult struct {
	Items       interface{} `json:"items"`
	Total       int         `json:"total"`
	CurrentPage int         `json:"current_page"`
	LastPage    int         `json:"last_page"`
	PerPage     int         `json:"per_page"`
}

type Member struct {
	Id      int    `json:"id"`
	Name    string `json:"name"`
	Account string `json:"account"`
}

func PageResultData(items interface{}, total int, currentPage int, perPage int) PageResult {
	if perPage == 0 {
		perPage = 20
	}
	if currentPage == 0 {
		currentPage = 1
	}
	return PageResult{
		Items:       items,
		Total:       total,
		CurrentPage: currentPage,
		LastPage:    total/perPage + 1,
		PerPage:     perPage,
	}
}
