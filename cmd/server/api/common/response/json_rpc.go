package response

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	ContextKeyJsonRpcResult = "__jsonRpcResult__"
)

func JsonRpcResult(code int, data interface{}, msg string, c *gin.Context) {
	c.Set(ContextKeyJsonRpcResult, Response{
		code,
		data,
		msg,
	})
}

func JsonRpcOk(c *gin.Context) {
	JsonRpcResult(http.StatusOK, nil, "success.", c)
}

func JsonRpcOkWithData(data interface{}, c *gin.Context) {
	JsonRpcResult(http.StatusOK, data, "success", c)
}

func JsonRpcFailWithError(err error, c *gin.Context) {
	JsonRpcResult(http.StatusBadRequest, nil, err.Error(), c)
}
