package request

import (
	"errors"
	"reflect"
	"report-service/pkg/szerrors"
	utilValidator "report-service/pkg/utils/validator"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func Validated(c *gin.Context, v interface{}) (err error) {
	value := reflect.ValueOf(v)
	if value.Kind() != reflect.Ptr {
		err = szerrors.NewSystemErrorWithText("被赋值的变量必须是指针类型")
		return
	}

	// 如果是 GET 请求则使用 ShouldBindQuery 函数来绑定参数
	// 注意：GET 请求结构体中的字段必须使用 form 标签来进行绑定
	if c.Request.Method == "GET" {
		err = c.BindQuery(v)
		if err != nil {
			err = szerrors.NewInvalidParamErrorWithText("Query 中获取参数失败")
			return
		}
	} else {
		err = c.ShouldBindJSON(v)
		if err != nil {
			err = szerrors.NewInvalidParamErrorWithText("JSON 解析失败，" + err.Error())
			return
		}
	}

	// 如果是切片类型，则遍历进行验证
	elem := value.Elem()
	if elem.Kind() == reflect.Slice {
		for i := 0; i < elem.Len(); i++ {
			err = utilValidator.Validator.Struct(elem.Index(i).Interface())
			if err != nil {
				errMsg, errs := handleValidatorErr(err)
				errMsg = "第" + strconv.Itoa(i+1) + "个元素验证失败：" + errMsg
				err = szerrors.NewInvalidParamErrorWithText(errMsg, errs...)
				return
			}
		}
		return
	}

	err = utilValidator.Validator.Struct(v)
	if err != nil {
		errMsg, errs := handleValidatorErr(err)
		err = szerrors.NewInvalidParamErrorWithText(errMsg, errs...)
		return
	}

	return
}

func handleValidatorErr(err error) (errMsg string, errData []string) {
	var invalidValidationError *validator.InvalidValidationError
	if errors.As(err, &invalidValidationError) {
		return "非法验证：" + err.Error(), nil
	}
	var errs []string
	for _, vErr := range err.(validator.ValidationErrors) {
		errs = append(errs, vErr.Translate(utilValidator.Trans))
	}
	return errs[0], errs
}
