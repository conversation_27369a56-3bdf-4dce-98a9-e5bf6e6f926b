package request

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/infra/gin/middleware"
)

func GetSid(c *gin.Context) int {
	return c.GetInt(middleware.ContextKeyAuthSid)
}

func GetMemberId(c *gin.Context) int {
	return c.GetInt(middleware.ContextKeyAuthMemberId)
}

func GetSdType(c *gin.Context) int {
	return c.GetInt(middleware.ContextKeyAuthSdType)
}

func GetDType(c *gin.Context) int {
	return c.GetInt(middleware.ContextKeyAuthDType)
}
