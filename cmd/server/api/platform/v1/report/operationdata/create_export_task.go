package operationdata

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/szerrors"
)

func CreateExportTask(c *gin.Context) {
	var req reportCommon.ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	err = common.CheckReportTypeAndTimeRange(req.Type, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryOperationData, sid, memberId)
	if err != nil {
		return
	}

	fields := append(doTemplate.Dimension, doTemplate.Indicator...)
	heads := make([][]string, 0)
	for _, field := range fields {
		if head, ok := enum.TemplateDimensionFieldsMapCommon[field]; ok {
			heads = append(heads, []string{head})
		} else if head, ok = enum.TemplateIndicatorFieldsMapCommon[field]; ok {
			heads = append(heads, []string{head})
		} else {
			response.FailWithError(szerrors.NewLogicErrorWithText(fmt.Sprintf("field %s is not supported", field)), c)
			return
		}
	}

	var params = reportCommon.ReqoPaginationExportListParams{
		ReqoList: req,
	}

	taskId, err := downloadcenter.AddTask("common", downloadcenter.AddTaskParams{
		Fid:          sid,
		OpId:         &memberId,
		TemplateCode: "new_report_service_operation_data",
		Request:      params,
		SelfDefinedStructure: &downloadcenter.AddTaskParamsSelfDefinedStructure{
			Head:  heads,
			Field: fields,
		},
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{"task_id": taskId}, c)
}
