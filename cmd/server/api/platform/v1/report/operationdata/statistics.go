package operationdata

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/service/report/operationdata"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
)

type ReqoStatistics struct {
	Type       int `json:"type" validate:"required,oneof=1 2 4" label:"类型"` // 1-小时 2-天 4-实时报表
	TemplateId int `json:"template_id" validate:"required" label:"模版ID"`
	TimeRange  struct {
		StartTime string `json:"start_time" validate:"required,datetime=2006-01-02 15:04:05" label:"开始时间"`
		EndTime   string `json:"end_time" validate:"required,datetime=2006-01-02 15:04:05" label:"结束时间"`
	} `json:"time_range" validate:"required" label:"查询周期"`
	DimensionRange *struct {
		SaleChannel []int `json:"sale_channel" validate:"omitempty,gt=0" label:"订单渠道"`
		Distributor []int `json:"distributor" validate:"omitempty,gt=0" label:"分销商"`
		Spu         []int `json:"spu" validate:"omitempty,gt=0" label:"产品"`
		Sku         []int `json:"sku" validate:"omitempty,gt=0" label:"票种"`
		PayMode     []int `json:"pay_mode" validate:"omitempty,gt=0" label:"支付方式"`
	} `json:"dimension_range" validate:"omitempty" label:"维度数据范围"`
}

func Statistics(c *gin.Context) {
	var req ReqoStatistics
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		doDimensionRange = common.DoDimensionRange{
			SaleChannel: req.DimensionRange.SaleChannel,
			Distributor: req.DimensionRange.Distributor,
			Spu:         req.DimensionRange.Spu,
			Sku:         req.DimensionRange.Sku,
			PayMode:     req.DimensionRange.PayMode,
		}
	}

	doStatistics, err := operationdata.Statistics(
		request.GetSid(c),
		request.GetMemberId(c),
		req.Type,
		req.TemplateId,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var reso ResoIndicator
	err = utils.JsonConvertor(doStatistics, &reso)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(reso, c)
}
