package groupverify

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/szerrors"
)

const (
	DownloadCenterTemplateCode = "new_report_service_group_verify" // 下载中心模板代码
)

func CreateExportTask(c *gin.Context) {
	var req reportCommon.ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	err = common.CheckReportTypeAndTimeRange(req.Type, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var params = reportCommon.ReqoPaginationExportListParams{
		ReqoList: req,
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryGroupVerify, sid, memberId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	fields := append(doTemplate.Dimension, doTemplate.Indicator...)
	heads := make([][]string, 0)
	headsFields := make([]string, 0)
	for _, field := range fields {
		if head, ok := enum.TemplateDimensionFieldsMapVerify[field]; ok {
			heads = append(heads, []string{head})
			headsFields = append(headsFields, field)
		} else if head, ok = enum.TemplateIndicatorFieldsMapVerify[field]; ok {
			heads = append(heads, []string{head})
			headsFields = append(headsFields, field)
		} else {
			//部分维度字段无需验证
			if _, ok = enum.TemplateDimensionFieldsMapNoValidate[field]; !ok {
				response.FailWithError(szerrors.NewLogicErrorWithText(fmt.Sprintf("field %s is not supported", field)), c)
				return
			}
		}
	}
	taskId, err := downloadcenter.AddTask("common", downloadcenter.AddTaskParams{
		Fid:          sid,
		OpId:         &memberId,
		TemplateCode: DownloadCenterTemplateCode,
		Request:      params,
		SelfDefinedStructure: &downloadcenter.AddTaskParamsSelfDefinedStructure{
			Head:  heads,
			Field: headsFields,
		},
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{"task_id": taskId}, c)
}
