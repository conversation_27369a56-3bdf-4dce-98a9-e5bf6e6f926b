package groupverify

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/groupverify"
	"report-service/pkg/utils"

	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
)

type ReqoStatistics struct {
	Type       int `json:"type" validate:"required,oneof=1 2 4" label:"类型"` // 1-小时 2-天 4-实时报表
	TemplateId int `json:"template_id" validate:"required" label:"模版ID"`
	TimeRange  struct {
		StartTime string `json:"start_time" validate:"required,datetime=2006-01-02 15:04:05" label:"开始时间"`
		EndTime   string `json:"end_time" validate:"required,datetime=2006-01-02 15:04:05" label:"结束时间"`
	} `json:"time_range" validate:"required" label:"查询周期"`
	DimensionRange *reportCommon.ReqoDimensionRange `json:"dimension_range" validate:"omitempty" label:"维度数据范围"`
}

func Statistics(c *gin.Context) {
	var req ReqoStatistics
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryGroupVerify, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	doStatistics, err := groupverify.Statistics(
		request.GetSid(c),
		req.Type,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var reso ResoIndicator
	err = utils.JsonConvertor(doStatistics, &reso)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(reso, c)
}
