package template

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/template"
)

type ReqoCreate struct {
	Data     string `json:"data" validate:"required" label:"配置数据"`
	Category int    `json:"category" validate:"required" label:"模板分类"`
}

type ReqoModify struct {
	TemplateId int    `json:"id"  validate:"required" label:"模板ID"`
	Data       string `json:"data" validate:"required" label:"配置数据"`
}

type ReqoDelete struct {
	TemplateId int `json:"id"  validate:"required" label:"模板ID"`
}

type ResoCreateId struct {
	CreateId int `json:"id"`
}

func Create(c *gin.Context) {
	var req ReqoCreate
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var createParams template.DoCreate
	err = json.Unmarshal([]byte(req.Data), &createParams)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	createParams.Category = req.Category
	//用户信息
	createParams.MerchantId = request.GetSid(c)
	createParams.MemberId = request.GetMemberId(c)
	createParams.SdType = request.GetSdType(c)

	var templateId int
	templateId, err = template.Create(createParams)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	responseData := ResoCreateId{
		CreateId: templateId,
	}
	response.OkWithData(responseData, c)
}

func Modify(c *gin.Context) {
	var req ReqoModify
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var modifyParams template.DoModify
	err = json.Unmarshal([]byte(req.Data), &modifyParams)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	modifyParams.TemplateId = req.TemplateId
	//用户信息
	modifyParams.MerchantId = request.GetSid(c)
	modifyParams.MemberId = request.GetMemberId(c)
	modifyParams.SdType = request.GetSdType(c)
	err = template.Modify(modifyParams)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData([]interface{}{}, c)
}

func Delete(c *gin.Context) {
	var req ReqoDelete
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	//处理删除
	err = template.Delete(req.TemplateId, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData([]interface{}{}, c)
}
