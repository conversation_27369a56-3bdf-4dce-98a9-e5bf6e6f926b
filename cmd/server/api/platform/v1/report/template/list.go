package template

import (
	"errors"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/template"
)

type ReqoList struct {
	Category int `json:"category" validate:"required" label:"报表类型"`
}

type ReqoDetail struct {
	Id       int `json:"id" validate:"omitempty" label:"报表id"`
	Category int `json:"category" validate:"omitempty" label:"报表类型"`
}

// 模板列表
func List(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	listRes, resErr := template.List(req.Category, request.GetSid(c), request.GetMemberId(c))
	if resErr != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(listRes, c)
}

// 简易列表
func SimpleList(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	listRes, listErr := template.SimpleList(req.Category, request.GetSid(c), request.GetMemberId(c))
	if listErr != nil {
		response.FailWithError(listErr, c)
		return
	}

	response.OkWithData(listRes, c)
}

// 模板详情
func Detail(c *gin.Context) {
	var req ReqoDetail
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	infoRes, infoErr := template.Detail(req.Id, req.Category, request.GetSid(c), request.GetMemberId(c), false)
	if infoErr != nil {
		response.FailWithError(infoErr, c)
		return
	}
	if infoRes == nil {
		response.FailWithError(errors.New("无数据"), c)
		return
	}

	response.OkWithData(infoRes, c)
}
