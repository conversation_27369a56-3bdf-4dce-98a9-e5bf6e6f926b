package template

import (
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/template"

	"github.com/gin-gonic/gin"
)

type ReqoConfig struct {
	Category int `json:"category" validate:"omitempty" label:"报表类型"`
}

func BaseConfig(c *gin.Context) {
	var req ReqoConfig
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	doConfigs, doConfigsErr := template.GetConfig(req.Category)
	if doConfigsErr != nil {
		response.FailWithError(doConfigsErr, c)
		return
	}
	response.OkWithData(doConfigs, c)
}
