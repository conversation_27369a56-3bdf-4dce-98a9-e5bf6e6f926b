package multiplepeople

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/multiplepeople"
	"report-service/pkg/utils"
)

type ReqoList struct {
	reportCommon.ReqoList
	SubjectType int `json:"subject_type" validate:"required,oneof=1 2 3 4 5 6 7 8 9 10" label:"查询地域"` //1-地域 2-国家 3-省 4-市 5-区县 6-性别 7-年龄 8-客源地图表 9-性别图表 10-年龄图表
}
type ResoPaginationItem struct {
	reportCommon.ResoTouristDimension
	ResoIndicator
}

type ResoIndicator struct {
	Count int `json:"count"` //游客人数
}

func List(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	page := req.PageInfo.Page
	pageSize := req.PageInfo.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	doList, total, err := multiplepeople.Paginate(
		request.GetSid(c),
		req.Type,
		req.SubjectType,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
		dataLimit,
		page,
		pageSize,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var list []ResoPaginationItem
	err = utils.JsonConvertor(doList, &list)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(response.PageResultData(list, total, req.PageInfo.Page, req.PageInfo.PageSize), c)
}
