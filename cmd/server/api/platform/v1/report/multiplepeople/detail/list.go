package detail

import (
	"errors"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	dmTourist "report-service/internal/domain/logic/dm/tourist"
	dwmTourist "report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	customizeExport "report-service/internal/domain/service/report/common/customizeexportfields"
	"report-service/internal/domain/service/report/common/multipledetail"
	"report-service/pkg/utils"
)

type ReqoDetailList struct {
	ReqoCreateDetailExportTask
	LastSequence *string `json:"lastSequence"`
	PrevSequence *string `json:"prevSequence"`
	request.PageInfo
}

type PageResult struct {
	Items        interface{} `json:"items"`
	Total        int         `json:"total"`
	LastSequence *string     `json:"lastSequence,omitempty"`
	PrevSequence *string     `json:"prevSequence,omitempty"`
}

func List(c *gin.Context) {
	var req ReqoDetailList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//限制页数范围
	if req.PageSize <= 0 || req.PageSize > 500 {
		req.PageSize = 10
	}

	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	//时间段校验
	err = dmTourist.CheckReportTypeAndTimeRange(enum.ReportTypeDetail, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//维度范围
	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			return
		}
	}

	//字段范围
	var doFieldsFilter dwmTourist.DoFieldsFilter
	err = utils.JsonConvertor(req.ReqoFieldsRange, &doFieldsFilter)
	if err != nil {
		return
	}

	//模板
	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, sid, memberId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//自定义导出字段
	fields, err := customizeExport.FieldsInfo(request.GetSid(c), request.GetMemberId(c), enum.CustomizeExportDetailTouristMultiple)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if len(fields) == 0 {
		response.FailWithError(errors.New("导出字段不能为空"), c)
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	//是否需要上一页标识
	requirePrevSortKey := false
	if !utils.Container([]int{1, 2}, req.Page) {
		requirePrevSortKey = true
	}

	//明细分页
	doList, nextSortKey, err, ext := multipledetail.PaginateByLastSortKey(
		multipledetail.DoQueryParams{
			DoTemplate:         *doTemplate,
			MerchantId:         sid,
			StartTime:          carbon.Parse(req.TimeRange.StartTime),
			EndTime:            carbon.Parse(req.TimeRange.EndTime),
			DimensionRange:     doDimensionRange,
			FieldsFilter:       doFieldsFilter,
			DataLimit:          dataLimit,
			Fields:             fields,
			RequireCount:       true,
			RequirePrevSortKey: requirePrevSortKey,
		},
		req.LastSequence,
		req.PageSize,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	total := 0
	if ext != nil && ext.Total != nil {
		total = *ext.Total
	}

	prevSequence := ""
	if ext != nil && ext.PrevSortKey != nil {
		prevSequence = *ext.PrevSortKey
	}

	if doList == nil {
		doList = make([]map[string]interface{}, 0)
	}

	response.OkWithData(PageResult{
		Items:        doList,
		Total:        total,
		LastSequence: &nextSortKey,
		PrevSequence: &prevSequence,
	}, c)
}
