package detail

import (
	"errors"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	dmTourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	customizeExport "report-service/internal/domain/service/report/common/customizeexportfields"
	"report-service/pkg/sdk/api/downloadcenter"
)

const (
	DownloadCenterTemplateCode = "new_report_service_multiple_detail" // 下载中心模板代码
)

type ReqoCreateDetailExportTask struct {
	reportCommon.ReqoCreateExportTask
	reportCommon.ReqoFieldsRange
}

type ExportDetailParams struct {
	ReqoCreateDetailExportTask
	Fields         []string                     `json:"fields"`
	LimitSpuConfig datajobslimit.LimitSpuConfig `json:"limit_spu_config"`
}

func CreateExportTask(c *gin.Context) {
	var req ReqoCreateDetailExportTask
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	err = dmTourist.CheckReportTypeAndTimeRange(enum.ReportTypeDetail, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//自定义导出字段
	fieldsRes, err := customizeExport.FieldsKeyVal(request.GetSid(c), request.GetMemberId(c), enum.CustomizeExportDetailTouristMultiple)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if len(fieldsRes) == 0 {
		response.FailWithError(errors.New("导出字段不能为空"), c)
	}

	fields := make([]string, 0)
	heads := make([][]string, 0)
	//支持下导出自定义字段排序
	for _, re := range fieldsRes {
		fields = append(fields, re.Key)
		heads = append(heads, []string{re.Title})
	}

	//请求参数加上导出字段
	var reqNew ExportDetailParams

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}
	reqNew.LimitSpuConfig = dataLimit

	reqNew.ReqoCreateDetailExportTask = req
	reqNew.Fields = fields
	taskId, err := downloadcenter.AddTask("common", downloadcenter.AddTaskParams{
		Fid:          sid,
		OpId:         &memberId,
		TemplateCode: DownloadCenterTemplateCode,
		Request:      reqNew,
		SelfDefinedStructure: &downloadcenter.AddTaskParamsSelfDefinedStructure{
			Head:  heads,
			Field: fields,
		},
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{"task_id": taskId}, c)
}
