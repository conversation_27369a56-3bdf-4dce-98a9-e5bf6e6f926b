package detail

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/cmd/server/api/platform/v1/report/multiplepeople"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	dmTourist "report-service/internal/domain/logic/dm/tourist"
	dwmTourist "report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/common/multipledetail"
	"report-service/pkg/utils"
)

func Statistics(c *gin.Context) {
	var req ReqoCreateDetailExportTask
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	//时间段校验
	err = dmTourist.CheckReportTypeAndTimeRange(enum.ReportTypeDetail, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//维度范围
	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			return
		}
	}

	//字段范围
	var doFieldsFilter dwmTourist.DoFieldsFilter
	err = utils.JsonConvertor(req.ReqoFieldsRange, &doFieldsFilter)
	if err != nil {
		return
	}

	//模板
	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, sid, memberId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}
	doStatistics, err := multipledetail.Statistics(
		multipledetail.DoQueryParams{
			DoTemplate:     *doTemplate,
			MerchantId:     sid,
			StartTime:      carbon.Parse(req.TimeRange.StartTime),
			EndTime:        carbon.Parse(req.TimeRange.EndTime),
			DimensionRange: doDimensionRange,
			FieldsFilter:   doFieldsFilter,
			DataLimit:      dataLimit,
			RequireCount:   true,
		},
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var reso multiplepeople.ResoIndicator
	err = utils.JsonConvertor(doStatistics, &reso)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(reso, c)
}
