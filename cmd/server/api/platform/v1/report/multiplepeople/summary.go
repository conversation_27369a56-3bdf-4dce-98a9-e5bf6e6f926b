package multiplepeople

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/multiplepeople"
	"report-service/pkg/utils"
)

type ResoSummaryTreeItem struct {
	Region   string                 `json:"region"`
	Name     string                 `json:"name"`
	Count    int                    `json:"count"`
	Children []*ResoSummaryTreeItem `json:"children,omitempty"`
}

func SummaryTree(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	doList, err := multiplepeople.SummaryTree(
		request.GetSid(c),
		req.Type,
		req.SubjectType,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
		dataLimit,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var list []ResoSummaryTreeItem
	err = utils.JsonConvertor(doList, &list)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(list, c)
}
