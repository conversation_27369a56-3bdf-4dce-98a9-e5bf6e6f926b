package multiplepeople

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/permission/module"
	"report-service/internal/domain/logic/permission/module/touristsourceareamodule"
)

type ResoMerchantConfig struct {
	IsEnabled    bool   `json:"is_enable"`
	StartTime    string `json:"start_time"`
	IsProcessing bool   `json:"is_processing"`
}

type ResoMerchantProgress struct {
	Progress     int  `json:"progress"`
	IsProcessing bool `json:"is_processing"`
}

func MerchantConfig(c *gin.Context) {
	var req = module.OperateData{
		MerchantId: request.GetSid(c),
		ModuleTag:  enum.ModuleTagTouristSourceArea,
	}
	info := touristsourceareamodule.GetTouristSourceAreaConfig(req)
	if info == nil {
		response.OkWithData(ResoMerchantConfig{}, c)
		return
	}
	var data = ResoMerchantConfig{
		IsEnabled:    info.IsEnable,
		StartTime:    info.StartTime,
		IsProcessing: info.IsProcessing,
	}
	response.OkWithData(data, c)
}

func MerchantProgress(c *gin.Context) {
	var req = module.OperateData{
		MerchantId: request.GetSid(c),
		ModuleTag:  enum.ModuleTagTouristSourceArea,
	}
	info := touristsourceareamodule.GetTouristSourceAreaConfig(req)
	if info == nil {
		response.OkWithData(ResoMerchantProgress{}, c)
		return
	}
	var data = ResoMerchantProgress{
		Progress:     info.Progress.Percent,
		IsProcessing: info.IsProcessing,
	}
	response.OkWithData(data, c)
}
