package multiplepeople

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
)

func GetLatestUpdatedAt(c *gin.Context) {
	response.OkWithData(map[string]interface{}{
		"hour": config.GetString(enum.ConfigKeyDmTouristHourLastStatisticTime),
		"day":  config.GetString(enum.ConfigKeyDmTouristDayLastStatisticTime),
	}, c)
}
