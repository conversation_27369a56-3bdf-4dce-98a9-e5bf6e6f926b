package multiplepeople

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	dmTourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/multiplepeople"
	"report-service/pkg/sdk/api/downloadcenter"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
	"strconv"
)

type ReqoExport struct {
	reportCommon.ReqoList
	Mode string `json:"mode" validate:"required,oneof='source_area' 'gender' 'age'" label:"导出类型"`
}

type ExportParams struct {
	reportCommon.ReqoList
	SubjectType    int                          `json:"subject_type" validate:"omitempty" label:"查询对象"`
	LimitSpuConfig datajobslimit.LimitSpuConfig `json:"limit_spu_config"`
}

func CreateExportTask(c *gin.Context) {
	var req ReqoExport
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	err = dmTourist.CheckReportTypeAndTimeRange(req.Type, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryTouristSourceArea, sid, memberId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var params = ExportParams{
		ReqoList: req.ReqoList,
	}

	var templateCode string
	switch req.Mode {
	case enum.TemplateMultipleModeSourceArea:
		templateCode = "new_report_service_multiple_tourist_source_area"
		params.SubjectType = enum.TouristSubjectTypeRegionAll
	case enum.TemplateMultipleModeGender:
		templateCode = "new_report_service_multiple_gender"
		params.SubjectType = enum.TouristSubjectTypeGender
	case enum.TemplateMultipleModeAge:
		templateCode = "new_report_service_multiple_age"
		params.SubjectType = enum.TouristSubjectTypeAge
	}
	dimension, err := dmTourist.GetSubjectTypeDimension(*doTemplate, []int{params.SubjectType})
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	headTitle := "时间："
	headTitle += carbon.Parse(req.TimeRange.StartTime).StartOfHour().ToDateTimeString()
	headTitle += "至"
	headTitle += carbon.Parse(req.TimeRange.EndTime).EndOfHour().ToDateTimeString()

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}
	//获取总人数
	doStatistics, err := multiplepeople.Statistics(
		request.GetSid(c),
		request.GetMemberId(c),
		req.Type,
		req.TemplateId,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		doDimensionRange,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	count := doStatistics.Count
	headTitle += "               总计人数：" + strconv.Itoa(*count)

	fields := append(dimension, doTemplate.Indicator...)
	heads := make([][]string, 0)
	for _, field := range fields {
		if head, ok := enum.TemplateDimensionFieldsMapTouristSourceArea[field]; ok {
			heads = append(heads, []string{headTitle, head})
		} else if head, ok = enum.TemplateDimensionFieldsMapTouristGender[field]; ok {
			heads = append(heads, []string{headTitle, head})
		} else if head, ok = enum.TemplateDimensionFieldsMapTouristAge[field]; ok {
			heads = append(heads, []string{headTitle, head})
		} else if head, ok = enum.TemplateIndicatorFieldsMapTourist[field]; ok {
			heads = append(heads, []string{headTitle, head})
		} else {
			response.FailWithError(szerrors.NewLogicErrorWithText(fmt.Sprintf("field %s is not supported", field)), c)
			return
		}
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}
	params.LimitSpuConfig = dataLimit

	taskId, err := downloadcenter.AddTask("common", downloadcenter.AddTaskParams{
		Fid:          sid,
		OpId:         &memberId,
		TemplateCode: templateCode,
		Request:      params,
		SelfDefinedStructure: &downloadcenter.AddTaskParamsSelfDefinedStructure{
			Head:  heads,
			Field: fields,
		},
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{"task_id": taskId}, c)
}
