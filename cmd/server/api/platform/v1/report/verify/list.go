package verify

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/verify"
	"report-service/pkg/utils"
)

type ResoPaginationItem struct {
	reportCommon.ResoDimension
	ResoIndicator
}

type ResoIndicator struct {
	VerifyCount           *int `json:"verify_count,omitempty"`             // 验证数量
	VerifySalePrice       *int `json:"verify_sale_price,omitempty"`        // 验证金额
	VerifyCostPrice       *int `json:"verify_cost_price,omitempty"`        // 验证采购金额
	ActualVerifyCount     *int `json:"actual_verify_count,omitempty"`      // 净验证数量
	ActualVerifySalePrice *int `json:"actual_verify_sale_price,omitempty"` // 净验证金额
	ActualVerifyCostPrice *int `json:"actual_verify_cost_price,omitempty"` // 净验证采购金额
	ActualVerifyProfit    *int `json:"actual_verify_profit,omitempty"`     // 净验证利润
	RevokeCount           *int `json:"revoke_count,omitempty"`             // 撤销数量
	RevokeSalePrice       *int `json:"revoke_sale_price,omitempty"`        // 撤销销售金额
	RevokeCostPrice       *int `json:"revoke_cost_price,omitempty"`        // 撤销采购金额
	RefundFeeProfit       *int `json:"refund_fee_profit,omitempty"`        // 退款手续费利润
	AfterSaleCount        *int `json:"after_sale_count,omitempty"`         // 售后数量
	AfterSalePrice        *int `json:"after_sale_price,omitempty"`         // 售后销售金额
	AfterCostPrice        *int `json:"after_cost_price,omitempty"`         // 售后采购金额
}

func List(c *gin.Context) {
	var req reportCommon.ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	page := req.PageInfo.Page
	pageSize := req.PageInfo.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryVerify, request.GetSid(c), request.GetMemberId(c))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	doList, total, err := verify.Paginate(
		request.GetSid(c),
		req.Type,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		req.TimeGroupType,
		doDimensionRange,
		dataLimit,
		page,
		pageSize,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var list []ResoPaginationItem
	err = utils.JsonConvertor(doList, &list)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(response.PageResultData(list, total, req.PageInfo.Page, req.PageInfo.PageSize), c)
}
