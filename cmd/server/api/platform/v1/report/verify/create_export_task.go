package verify

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/service/report/verify"
	"report-service/pkg/utils"
	"report-service/pkg/utils/snowflake"
)

func CreateExportTask(c *gin.Context) {
	var req reportCommon.ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	err = common.CheckReportTypeAndTimeRange(req.Type, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(request.GetSid(c), request.GetMemberId(c))
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}

	var doDimensionRange common.DoDimensionRange
	if req.DimensionRange != nil {
		err = utils.JsonConvertor(req.DimensionRange, &doDimensionRange)
		if err != nil {
			response.FailWithError(err, c)
			return
		}
	}

	doTemplate, err := template.Detail(req.TemplateId, enum.TemplateCategoryVerify, sid, memberId)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var uniqueKey int
	uniqueKey, err = snowflake.GlobalSnowflake.NextId()
	if err != nil {
		response.FailWithError(fmt.Errorf("生成任务key失败,  错误:%s", err), c)
		return
	}
	var fileKey string
	var taskId int
	fileKey, taskId, err = verify.CreateExcelData(
		sid,
		memberId,
		req.Type,
		*doTemplate,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
		req.TimeGroupType,
		doDimensionRange,
		dataLimit,
		uniqueKey,
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if fileKey != "" {
		response.OkWithData(gin.H{"task_id": taskId, "file": fileKey}, c)
		return
	}
	response.OkWithData([]int{}, c)
}
