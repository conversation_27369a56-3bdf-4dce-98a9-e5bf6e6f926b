package customizeexportfields

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	customizeExport "report-service/internal/domain/service/report/common/customizeexportfields"
	customizeExportTypes "report-service/internal/domain/service/report/common/customizeexportfields/types"
)

type ReqoFields struct {
	Tag string `json:"tag" validate:"required" label:"配置标识"`
}

type ReqoFieldsSave struct {
	ReqoFields
	Data []string `json:"data" validate:"required" label:"配置数据"`
}

func Enum(c *gin.Context) {
	var req ReqoFields
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	enum, err := customizeExport.FieldsEnum(req.Tag)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(enum, c)
}

func Save(c *gin.Context) {
	var req ReqoFieldsSave
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var doExportConfig customizeExportTypes.DoExportConfig
	doExportConfig.Config = req.Data
	err = customizeExport.FieldsSave(request.GetSid(c), request.GetMemberId(c), req.Tag, doExportConfig)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

func Info(c *gin.Context) {
	var req ReqoFields
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	info, err := customizeExport.FieldsInfo(request.GetSid(c), request.GetMemberId(c), req.Tag)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(info, c)
}

func Header(c *gin.Context) {
	var req ReqoFields
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	info, err := customizeExport.FieldsKeyVal(request.GetSid(c), request.GetMemberId(c), req.Tag)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(info, c)
}
