package common

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportSearch "report-service/internal/domain/logic/report/search"
	searchCommon "report-service/internal/domain/logic/report/search/common"
)

type ReqoSearchDimensionByNeed struct {
	FieldType string `json:"type" validate:"required" label:"查询类型"`
	KeyWord   string `json:"key_word" validate:"omitempty" label:"查询关键词"`
	PageSize  int    `json:"page_size" validate:"omitempty" label:"分页大小"`
	PageNum   int    `json:"page_num" validate:"omitempty" label:"当前页"`
}

func SearchDimensionByNeed(c *gin.Context) {
	var req ReqoSearchDimensionByNeed
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	sdType := request.GetSdType(c)
	doQueryParams := searchCommon.DoQueryParams{
		KeyWord:      req.Key<PERSON>ord,
		MerchantId:   request.GetSid(c),
		MemberId:     request.GetMemberId(c),
		MerchantType: &sdType,
		PageNum:      req.PageNum,
		PageSize:     req.PageSize,
	}

	result, resultErr := reportSearch.SearchDimensionByNeed(req.FieldType, doQueryParams)
	if resultErr != nil {
		response.FailWithError(resultErr, c)
		return
	}
	if result != nil {
		response.OkWithData(result, c)
		return
	}

	response.OkWithData([]interface{}{}, c)
}
