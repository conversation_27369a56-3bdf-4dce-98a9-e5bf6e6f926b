package common

import (
	"report-service/cmd/server/api/common/request"
	"report-service/internal/domain/logic/permission/datajobslimit"
)

type ReqoList struct {
	Type           int                 `json:"type" validate:"required,oneof=1 2 4" label:"类型"` // 1-小时 2-天 4-实时报表
	TemplateId     int                 `json:"template_id" validate:"required" label:"模版ID"`
	TimeRange      ReqoTimeRange       `json:"time_range" validate:"required" label:"查询周期"`
	TimeGroupType  *int                `json:"time_group_type" validate:"omitempty,oneof=1 2 3 4" label:"时间分组类型"` // 1-小时 2-天 3-月 4-年
	DimensionRange *ReqoDimensionRange `json:"dimension_range" validate:"omitempty" label:"维度数据范围"`
	request.PageInfo
}

type ReqoCreateExportTask struct {
	TemplateId     int                 `json:"template_id" validate:"required" label:"模版ID"`
	TimeRange      ReqoTimeRange       `json:"time_range" validate:"required" label:"查询周期"`
	DimensionRange *ReqoDimensionRange `json:"dimension_range" validate:"omitempty" label:"维度数据范围"`
}

type ReqoTimeRange struct {
	StartTime string `json:"start_time" validate:"required,datetime=2006-01-02 15:04:05" label:"开始时间"`
	EndTime   string `json:"end_time" validate:"required,datetime=2006-01-02 15:04:05" label:"结束时间"`
}

type ReqoDimensionRange struct {
	SaleChannel    []int    `json:"sale_channel" validate:"omitempty" label:"订单渠道"`
	Distributor    []int    `json:"distributor" validate:"omitempty" label:"分销商"`
	Spu            []int    `json:"spu" validate:"omitempty" label:"产品"`
	Sku            []int    `json:"sku" validate:"omitempty" label:"票种"`
	PayMode        []int    `json:"pay_mode" validate:"omitempty" label:"支付方式"`
	Operator       []int    `json:"operator" validate:"omitempty" label:"操作员"`
	SellOperator   []int    `json:"sell_operator" validate:"omitempty" label:"售票员"`
	SellSite       []int    `json:"sell_site" validate:"omitempty" label:"售票站点"`
	Region         []string `json:"region" validate:"omitempty" label:"地域编号"`
	Country        []string `json:"country" validate:"omitempty" label:"国家编号"`
	Province       []string `json:"province" validate:"omitempty" label:"省编号"`
	City           []string `json:"city" validate:"omitempty" label:"市编号"`
	District       []string `json:"district" validate:"omitempty" label:"区县编号"`
	TargetAudience []string `json:"target_audience" validate:"omitempty" label:"适用人群"`
	GroupMember    []int    `json:"group_member" validate:"omitempty" label:"集团成员"`
}

type ReqoFieldsRange struct {
	Region            *string        `json:"region" validate:"omitempty" label:"地域编号"`
	Country           *string        `json:"country" validate:"omitempty" label:"国家编号"`
	Province          *string        `json:"province" validate:"omitempty" label:"省编号"`
	City              *string        `json:"city" validate:"omitempty" label:"市编号"`
	District          *string        `json:"district" validate:"omitempty" label:"区县编号"`
	Gender            *int           `json:"gender" validate:"omitempty" label:"性别类型"`
	Age               *int           `json:"age" validate:"omitempty" label:"年龄段分组"`
	AgeGroupId        *int           `json:"age_group_id" validate:"omitempty" label:"年龄段分组"`
	OperatedTimeRange *ReqoTimeRange `json:"operated_time_range" validate:"omitempty" label:"操作时间"`
	Operator          *int           `json:"operator" validate:"omitempty" label:"操作人员"`
	OperateType       *int           `json:"operate_type" validate:"omitempty" label:"操作类型"`
	OperateSite       *int           `json:"operate_site" validate:"omitempty" label:"操作站点"`
	OrderNo           *string        `json:"order_no" validate:"omitempty" label:"订单号"`
	BusinessCode      *string        `json:"business_code" validate:"omitempty" label:"门票码"`
	Spu               *int           `json:"spu" validate:"omitempty" label:"产品"`
	Sku               *int           `json:"sku" validate:"omitempty" label:"票种"`
	SaleChannel       *int           `json:"sale_channel" validate:"omitempty" label:"订单渠道"`
	Nickname          *string        `json:"nickname" validate:"omitempty" label:"游客姓名"`
	Mobile            *string        `json:"mobile" validate:"omitempty" label:"手机号"`
	IdType            *int           `json:"id_type" validate:"omitempty" label:"证件类型"`
	IdNumber          *string        `json:"id_number" validate:"omitempty" label:"证件号"`
}

type ReqoPaginationExportListParams struct {
	ReqoList
	LimitSpuConfig datajobslimit.LimitSpuConfig `json:"limit_spu_config"`
}

type ReqoCreateExportTaskParams struct {
	ReqoCreateExportTask
	LimitSpuConfig datajobslimit.LimitSpuConfig `json:"limit_spu_config"`
}
