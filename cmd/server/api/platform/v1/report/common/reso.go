package common

type ResoDimension struct {
	Date               *string `json:"date,omitempty"`
	CostUnitPrice      *int    `json:"cost_unit_price,omitempty"`      // 采购单价
	SaleUnitPrice      *int    `json:"sale_unit_price,omitempty"`      // 销售单价
	MerchantId         *int    `json:"merchant_id,omitempty"`          // 商户ID
	MerchantName       *string `json:"merchant_name,omitempty"`        // 商户名称
	ParentMerchantId   *int    `json:"parent_merchant_id,omitempty"`   // 上级商户ID
	ParentMerchantName *string `json:"parent_merchant_name,omitempty"` // 上级商户名称
	DistributorId      *int    `json:"distributor_id,omitempty"`       // 分销商户ID
	DistributorName    *string `json:"distributor_name,omitempty"`     // 分销商名称
	PoiId              *int    `json:"poi_id,omitempty"`               // PoiID
	PoiName            *string `json:"poi_name,omitempty"`             // Poi名称
	SpuId              *int    `json:"spu_id,omitempty"`               // SpuID
	SpuName            *string `json:"spu_name,omitempty"`             // Spu名称
	SkuId              *int    `json:"sku_id,omitempty"`               // SkuID
	SkuName            *string `json:"sku_name,omitempty"`             // Sku名称
	SaleChannel        *int    `json:"sale_channel,omitempty"`         // 销售渠道
	SaleChannelName    *string `json:"sale_channel_name,omitempty"`    // 销售渠道名称
	CostPayMode        *int    `json:"cost_pay_mode,omitempty"`        // 采购支付方式
	CostPayModeName    *string `json:"cost_pay_mode_name,omitempty"`   // 采购支付方式名称
	SalePayMode        *int    `json:"sale_pay_mode,omitempty"`        // 销售支付方式
	SalePayModeName    *string `json:"sale_pay_mode_name,omitempty"`   // 销售支付方式名称
	SellOperatorId     *int    `json:"sell_operator_id,omitempty"`     // 售票员ID
	SellOperatorName   *string `json:"sell_operator_name,omitempty"`   // 售票员名称
	OperatorId         *int    `json:"operator_id,omitempty"`          // 操作人ID
	OperatorName       *string `json:"operator_name,omitempty"`        // 操作人名称
	SellSiteId         *int    `json:"sell_site_id,omitempty"`         // 售票站点ID
	SellSiteName       *string `json:"sell_site_name,omitempty"`       // 售票站点名称
	TargetAudience     *string `json:"target_audience,omitempty"`      //适用人群
	TargetAudienceName *string `json:"target_audience_name,omitempty"` //适用人群名称
	GroupMember        *int    `json:"group_member,omitempty"`         //集团成员
	GroupMemberName    *string `json:"group_member_name,omitempty"`    //集团成员名称
	// 标签维度
	SpuTagCode         *string `json:"spu_tag_code,omitempty"`          // SPU标签编码
	SpuTagName         *string `json:"spu_tag_name,omitempty"`          // SPU标签名称
	SkuTagCode         *string `json:"sku_tag_code,omitempty"`          // SKU标签编码
	SkuTagName         *string `json:"sku_tag_name,omitempty"`          // SKU标签名称
	PayModeTagCode     *string `json:"pay_mode_tag_code,omitempty"`     // 支付方式标签编码
	PayModeTagName     *string `json:"pay_mode_tag_name,omitempty"`     // 支付方式标签名称
	SaleChannelTagCode *string `json:"sale_channel_tag_code,omitempty"` // 销售渠道标签编码
	SaleChannelTagName *string `json:"sale_channel_tag_name,omitempty"` // 销售渠道标签名称
}

type ResoTouristDimension struct {
	Region       *string `json:"region,omitempty"` //地域
	RegionName   *string `json:"region_name,omitempty"`
	Country      *string `json:"country,omitempty"` //国家
	CountryName  *string `json:"country_name,omitempty"`
	Province     *string `json:"province,omitempty"` //省
	ProvinceName *string `json:"province_name,omitempty"`
	City         *string `json:"city,omitempty"` //市
	CityName     *string `json:"city_name,omitempty"`
	District     *string `json:"district,omitempty"` //区
	DistrictName *string `json:"district_name,omitempty"`
	Age          *int    `json:"age,omitempty"`
	AgeName      *string `json:"age_name,omitempty"`
	Gender       *int    `json:"gender,omitempty"`
	GenderName   *string `json:"genderName,omitempty"`
	// 分组维度
	AgeGroupId   *int    `json:"age_group_id,omitempty"`   //年龄段分组
	AgeGroupName *string `json:"age_group_name,omitempty"` //年龄段分组名称
}
