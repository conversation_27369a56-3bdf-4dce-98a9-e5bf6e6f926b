package detail

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	reportCommon "report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/permission/datajobslimit"
	"report-service/internal/domain/service/report/pay/detail"
	"report-service/pkg/sdk/api/downloadcenter"
)

func CreateExportTask(c *gin.Context) {
	var req reportCommon.ReqoCreateExportTask
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	sid := request.GetSid(c)
	memberId := request.GetMemberId(c)

	//请求参数加上导出字段
	var params = reportCommon.ReqoCreateExportTaskParams{
		ReqoCreateExportTask: req,
	}

	//数据岗位权限
	dataLimit := datajobslimit.QueryBusinessData(sid, memberId)
	if check, checkErr := dataLimit.AllLimit(); check {
		response.FailWithError(checkErr, c)
		return
	}
	params.LimitSpuConfig = dataLimit

	err = common.CheckReportTypeAndTimeRange(enum.ReportTypeDetail, carbon.Parse(req.TimeRange.StartTime), carbon.Parse(req.TimeRange.EndTime))
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	fields := make([]string, 0)
	heads := make([][]string, 0)
	for _, item := range detail.FieldNameSlice {
		fields = append(fields, item.Field)
		heads = append(heads, []string{item.Name})
	}

	taskId, err := downloadcenter.AddTask("common", downloadcenter.AddTaskParams{
		Fid:          sid,
		OpId:         &memberId,
		TemplateCode: "new_report_service_pay_detail",
		Request:      params,
		SelfDefinedStructure: &downloadcenter.AddTaskParamsSelfDefinedStructure{
			Head:  heads,
			Field: fields,
		},
	})
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{"task_id": taskId}, c)
}
