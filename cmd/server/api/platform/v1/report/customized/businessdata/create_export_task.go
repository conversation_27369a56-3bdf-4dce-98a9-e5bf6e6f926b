package businessdata

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/report/customized/businessdata"
	"report-service/pkg/utils/snowflake"
)

func CreateExportTask(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var uniqueKey int
	uniqueKey, err = snowflake.GlobalSnowflake.NextId()
	if err != nil {
		response.FailWithError(fmt.Errorf("生成任务key失败,  错误:%s", err), c)
		return
	}
	var fileKey string
	var taskId int
	fileKey, taskId, err = businessdata.CreateExcelData(
		request.GetSid(c),
		uniqueKey,
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if fileKey != "" {
		response.OkWithData(gin.H{"task_id": taskId, "file": fileKey}, c)
		return
	}
	response.OkWithData([]int{}, c)
}
