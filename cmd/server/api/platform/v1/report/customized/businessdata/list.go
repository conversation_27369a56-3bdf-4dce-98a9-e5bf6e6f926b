package businessdata

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/cmd/server/api/platform/v1/report/common"
	"report-service/internal/domain/service/report/customized/businessdata"
)

type ReqoList struct {
	TimeRange common.ReqoTimeRange `json:"time_range" validate:"required" label:"查询周期"`
}

func List(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	doList, err := businessdata.Paginate(
		request.GetSid(c),
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if doList != nil && len(doList) > 0 {
		response.OkWithData(doList, c)
		return
	}

	response.OkWithData([]int{}, c)
}
