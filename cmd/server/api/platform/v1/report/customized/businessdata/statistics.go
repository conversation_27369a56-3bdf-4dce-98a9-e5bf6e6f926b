package businessdata

import (
	"gitee.com/golang-module/carbon/v2"
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/service/report/customized/businessdata"
)

func Statistics(c *gin.Context) {
	var req ReqoList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	totalIndicator, err := businessdata.Statistics(
		request.GetSid(c),
		carbon.Parse(req.TimeRange.StartTime),
		carbon.Parse(req.TimeRange.EndTime),
	)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if totalIndicator != nil {
		response.OkWithData(totalIndicator, c)
		return
	}

	response.OkWithData([]int{}, c)
}
