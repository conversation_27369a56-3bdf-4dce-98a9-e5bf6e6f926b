package businessdata

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
)

func GetLatestUpdatedAt(c *gin.Context) {
	response.OkWithData(map[string]interface{}{
		"hour": config.GetString(enum.ConfigKeyDmPayModeMoneyLastStatisticTime + "_" + cast.ToString(request.GetSid(c))),
	}, c)
}
