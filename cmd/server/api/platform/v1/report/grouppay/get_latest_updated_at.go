package grouppay

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/config"
)

func GetLatestUpdatedAt(c *gin.Context) {
	response.OkWithData(map[string]interface{}{
		"day":  config.GetString(enum.ConfigKeyDmCommonDayLastStatisticTime),
		"hour": config.GetString(enum.ConfigKeyDmCommonHourLastStatisticTime),
	}, c)
}
