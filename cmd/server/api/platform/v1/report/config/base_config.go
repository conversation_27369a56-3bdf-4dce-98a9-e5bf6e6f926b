package config

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/config"
)

type reqoBaseDefinition struct {
	Key string `json:"key" validate:"required" label:"配置标识"`
}

// BaseDefinition 获取商户配置基本定义
func BaseDefinition(c *gin.Context) {
	var req reqoBaseDefinition
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	var definition interface{}
	definition, err = config.BaseDefinition(req.Key)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(definition, c)
}
