package config

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/config"
)

type reqoSaveConfig struct {
	Key  string `json:"key" validate:"required" label:"配置标识"`
	Data string `json:"data" validate:"required" label:"配置数据"`
}

func SaveConfig(c *gin.Context) {
	var req reqoSaveConfig
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	//用户信息
	merchantId := request.GetSid(c)
	err = config.SaveConfig(merchantId, req.Key, req.Data)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}
