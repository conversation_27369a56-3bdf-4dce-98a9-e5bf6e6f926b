package config

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/report/config"
)

type reqoGetConfig struct {
	Key string `json:"key" validate:"required" label:"配置标识"`
}

func GetConfig(c *gin.Context) {
	var req reqoGetConfig
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	//用户信息
	merchantId := request.GetSid(c)
	var info interface{}
	info, err = config.GetConfig(merchantId, req.Key)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(info, c)
}
