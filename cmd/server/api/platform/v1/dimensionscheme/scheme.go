package dimensionscheme

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	dimensionSchemeLogic "report-service/internal/domain/logic/dimensionscheme"
	dimensionSchemeService "report-service/internal/domain/service/dimensionscheme"
	"report-service/pkg/utils"
)

type ReqoSchemeCreate struct {
	Name                   string                             `json:"name" validate:"required,max=30" label:"名称"`
	DimensionType          int                                `json:"dimension_type" validate:"required,gt=0,oneof=1 2 3 4" label:"维度类型：1spu，2sku，3支付方式，4订单渠道"`
	Groups                 []ReqoGroup                        `json:"groups" validate:"required,min=1,dive" label:"分组列表"`
	DimensionGroupRelation []ReqoDimensionGroupRelationCreate `json:"dimension_group_relation" validate:"dive" label:"维度分组关系"`
}

type ReqoGroup struct {
	Name  string `json:"name" validate:"required,max=30"`
	Index *int   `json:"index" validate:"required"`
}

type ReqoDimensionGroupRelationCreate struct {
	DimensionValue *int `json:"dimension_value" validate:"required"`
	GroupIndex     *int `json:"group_index" validate:"required"`
}

func SchemeCreate(c *gin.Context) {
	var req ReqoSchemeCreate
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var params dimensionSchemeLogic.SchemeCreateParams
	err = utils.JsonConvertor(req, &params)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	params.MerchantId = request.GetSid(c)
	_, err = dimensionSchemeLogic.SchemeCreate(&params)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

type ReqoSchemeModify struct {
	Id                     int                                `json:"id" validate:"required" label:"ID"`
	Name                   string                             `json:"name" validate:"required" label:"名称"`
	DimensionGroupRelation []ReqoDimensionGroupRelationModify `json:"dimension_group_relation" validate:"dive"`
}

type ReqoDimensionGroupRelationModify struct {
	DimensionValue *int `json:"dimension_value" validate:"required"`
	GroupId        int  `json:"group_id" validate:"required"`
}

func SchemeModify(c *gin.Context) {
	var req ReqoSchemeModify
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	var params dimensionSchemeLogic.SchemeModifyParams
	err = utils.JsonConvertor(req, &params)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	params.MerchantId = request.GetSid(c)
	err = dimensionSchemeLogic.SchemeModify(&params)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

type ReqoSchemeDelete struct {
	Id int `json:"id" validate:"required" label:"ID"`
}

func SchemeDelete(c *gin.Context) {
	var req ReqoSchemeDelete
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	err = dimensionSchemeService.DeleteWithCleanTemplateSchemeId(request.GetSid(c), req.Id)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

type ReqoSchemePagination struct {
	DimensionType int `json:"dimension_type" validate:"required,gt=0,oneof=1 2 3 4" label:"维度类型：1spu，2sku，3支付方式，4订单渠道"`
	request.PageInfo
}

func SchemePagination(c *gin.Context) {
	var req ReqoSchemePagination
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	page := utils.ParsePage(req.Page)
	pageSize := utils.ParsePageSize(req.PageSize)

	items, total, err := dimensionSchemeLogic.Pagination(request.GetSid(c), req.DimensionType, page, pageSize)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(response.PageResultData(items, total, page, pageSize), c)
}
