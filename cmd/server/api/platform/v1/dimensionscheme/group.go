package dimensionscheme

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/pkg/utils"
)

type ReqoGroupCreate struct {
	SchemeId int    `json:"scheme_id" validate:"required" label:"方案ID"`
	Name     string `json:"name" validate:"required,max=30" label:"名称"`
}

func GroupCreate(c *gin.Context) {
	var req ReqoGroupCreate
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	err = dimensionscheme.GroupCreate(request.GetSid(c), req.SchemeId, req.Name)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

type ReqoGroupModify struct {
	Id   int    `json:"id" validate:"required" label:"分组ID"`
	Name string `json:"name" validate:"required" label:"名称"`
}

func GroupModify(c *gin.Context) {
	var req ReqoGroupModify
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	err = dimensionscheme.GroupModify(request.GetSid(c), req.Id, req.Name)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

type ReqoGroupDelete struct {
	Id int `json:"id" validate:"required" label:"分组ID"`
}

func GroupDelete(c *gin.Context) {
	var req ReqoGroupDelete
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	err = dimensionscheme.GroupDelete(request.GetSid(c), req.Id)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.Ok(c)
}

type ReqoGroupPagination struct {
	SchemeId int `json:"scheme_id" validate:"required" label:"方案ID"`
	request.PageInfo
}

func GroupPagination(c *gin.Context) {
	var req ReqoGroupPagination
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	page := utils.ParsePage(req.Page)
	pageSize := utils.ParsePageSize(req.PageSize)

	items, total, err := dimensionscheme.GroupPagination(request.GetSid(c), req.SchemeId, page, pageSize)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(response.PageResultData(items, total, page, pageSize), c)
}
