package dimensionscheme

import (
	"github.com/gin-gonic/gin"
	"report-service/cmd/server/api/common/request"
	"report-service/cmd/server/api/common/response"
	"report-service/internal/domain/logic/dimensionscheme"
)

type ReqoDimensionGroupRelationList struct {
	SchemeId        int   `json:"scheme_id" validate:"required,gt=0" label:"方案ID"`
	DimensionValues []int `json:"dimension_values" validate:"omitempty" label:"维度值列表"`
}

func DimensionGroupRelationList(c *gin.Context) {
	var req ReqoDimensionGroupRelationList
	err := request.Validated(c, &req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	items, err := dimensionscheme.GroupRelationList(request.GetSid(c), req.SchemeId, req.DimensionValues)
	if err != nil {
		response.FailWithError(err, c)
		return
	}

	response.OkWithData(items, c)
}
