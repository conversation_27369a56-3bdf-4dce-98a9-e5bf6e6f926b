package paymodemoney

import (
	"fmt"
	"report-service/internal/domain/logic/config/customized/paymodemoneyreport"
	"report-service/internal/domain/logic/dm/customized/paymodemoney"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
)

func DmHandleTask() {
	global.LOG.Info("开始处理数据指标汇总任务")
	defer global.LOG.Info("数据指标汇总任务处理完毕")
	outdatedData, err := paymodemoneyreport.Get()
	if err != nil {
		globalNotice.Error("statistic pay mode money report error, " + err.Error())
		return
	}
	if len(outdatedData) == 0 {
		return
	}
	for _, item := range outdatedData {
		// 如果数据时间间隔小于5分钟，则不处理，避免数据写入延迟，导致指标汇总缺失
		if item.OperatedAt.DiffAbsInMinutes() < 5 {
			continue
		}
		paymodemoney.RestatOutdatedItemHour(item.OperatedAt.StartOfHour(), item.OperatedAt.EndOfHour(), item.MerchantId, item.ProjectId)
		err = paymodemoneyreport.Del(item)
		if err != nil {
			globalNotice.Error(fmt.Sprintf("statistic pay mode money report del cache key error, merchant_id: %d, operated_at: %s, %s", item.MerchantId, item.OperatedAt.ToDateTimeString(), err.Error()))
			continue
		}
		global.LOG.Info(fmt.Sprintf("数据指标汇总成功， 商户ID: %d， 操作时间: %s", item.MerchantId, item.OperatedAt.ToDateTimeString()))
	}
}
