package paymodemoney

import (
	"gitee.com/golang-module/carbon/v2"
	"go.uber.org/zap"
	"report-service/internal/domain/logic/dwm/customized"
	"report-service/internal/global"
)

func DwmHandleTask() {
	startTime := carbon.Now().SubHours(1).StartOfHour()
	endTime := carbon.Now().SubHours(1).EndOfHour()
	global.LOG.Info("开始执行数据明细任务...",
		zap.String("start_time", startTime.ToDateTimeString()),
		zap.String("end_time", endTime.ToDateTimeString()),
	)
	defer global.LOG.Info("数据明细任务执行完毕...",
		zap.String("start_time", startTime.ToDateTimeString()),
		zap.String("end_time", endTime.ToDateTimeString()),
	)
	customized.PayModeMoneyReport(
		customized.ExecuteParams{
			StartTime: startTime.ToDateTimeString(),
			EndTime:   endTime.ToDateTimeString(),
			Ext: customized.ExecuteParamsExt{
				IsNextProcess: true,
				IsAutoProcess: true,
			},
		},
	)
}

func CronDataInspection() {
	startTime := carbon.Now().SubHours(4).StartOfHour()
	endTime := carbon.Now().SubHours(4).EndOfHour()
	global.LOG.Info("开始巡检数据明细任务...",
		zap.String("start_time", startTime.ToDateTimeString()),
		zap.String("end_time", endTime.ToDateTimeString()),
	)
	defer global.LOG.Info("数据巡检任务执行完毕...",
		zap.String("start_time", startTime.ToDateTimeString()),
		zap.String("end_time", endTime.ToDateTimeString()),
	)
	customized.PayModeMoneyReport(
		customized.ExecuteParams{
			StartTime: startTime.ToDateTimeString(),
			EndTime:   endTime.ToDateTimeString(),
			Ext: customized.ExecuteParamsExt{
				IsNextProcess:    true,
				IsDataInspection: true,
			},
		},
	)
}
