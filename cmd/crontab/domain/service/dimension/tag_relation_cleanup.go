package dimension

import (
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/report/template"
	"report-service/internal/domain/logic/report/template/common"
	"report-service/internal/domain/repository"
	"report-service/internal/global"
	"report-service/pkg/utils"

	"go.uber.org/zap"
)

// CleanupUnusedTagGroups 清理废弃的标签组
// 主要功能是删除标签关联表中废弃的标签组数据
func CleanupUnusedTagGroups() {
	global.LOG.Info("开始清理废弃的标签组数据")

	// 1. 获取所有添加了模板的商户ID
	merchantIds, err := template.GetAllMerchantWithTemplate()
	if err != nil {
		global.LOG.Error("获取商户列表失败", zap.Error(err))
		return
	}

	if len(merchantIds) == 0 {
		global.LOG.Info("没有找到配置了模板的商户，跳过清理")
		return
	}

	global.LOG.Info("找到的商户数量", zap.Int("count", len(merchantIds)))

	// 2. 遍历商户ID，处理每个商户的标签组
	for _, merchantId := range merchantIds {
		err := cleanupMerchantTagGroups(merchantId)
		if err != nil {
			global.LOG.Error("清理商户标签组失败",
				zap.Int("merchant_id", merchantId),
				zap.Error(err))
			continue
		}
	}

	global.LOG.Info("废弃标签组清理完成")
}

// cleanupMerchantTagGroups 清理单个商户的废弃标签组
func cleanupMerchantTagGroups(merchantId int) error {
	// 1. 获取该商户所有的模板
	templates, err := repository.ReportTemplateConfigRepository.MerchantFindAll(merchantId)
	if err != nil {
		return err
	}

	if len(templates) == 0 {
		global.LOG.Info("商户没有配置模板，跳过", zap.Int("merchant_id", merchantId))
		return nil
	}

	// 2. 解析出所有模板中使用的标签组
	usedTagGroups := make(map[string]bool)
	for _, tmpl := range templates {
		var payloadData common.TemplatePayload
		err := utils.JsonConvertor(tmpl.Payload, &payloadData)
		if err != nil {
			global.LOG.Warn("模板数据解析失败，跳过",
				zap.Int("template_id", tmpl.Id),
				zap.Error(err))
			continue
		}

		// 收集所有使用的标签组
		for _, tagGroupCode := range payloadData.DimensionTagGroup {
			if tagGroupCode != "" {
				usedTagGroups[tagGroupCode] = true
			}
		}
	}

	// 如果没有找到任何标签组，跳过
	if len(usedTagGroups) == 0 {
		global.LOG.Info("商户模板中没有使用标签组，跳过", zap.Int("merchant_id", merchantId))
		return nil
	}

	// 3. 查询该商户在标签关联表中的所有标签组
	existingTagGroups, err := repository.TagRelationRepository.FindTagGroupsByMerchantId(merchantId, enum.DimensionToTagSourceMapKeyTagCenter)
	if err != nil {
		global.LOG.Error("查询标签关联表失败",
			zap.Int("merchant_id", merchantId),
			zap.Error(err))
		return err
	}

	if len(existingTagGroups) == 0 {
		global.LOG.Info("商户没有标签组数据，跳过", zap.Int("merchant_id", merchantId))
		return nil
	}

	// 4. 找出需要删除的标签组
	toDeleteTagGroups := make([]string, 0)
	for _, tagGroupCode := range existingTagGroups {
		// 如果标签组不在使用的标签组中，则需要删除
		if !usedTagGroups[tagGroupCode] {
			toDeleteTagGroups = append(toDeleteTagGroups, tagGroupCode)
		}
	}

	if len(toDeleteTagGroups) == 0 {
		global.LOG.Info("商户没有需要清理的标签组", zap.Int("merchant_id", merchantId))
		return nil
	}

	global.LOG.Info("将要清理的标签组",
		zap.Int("merchant_id", merchantId),
		zap.Strings("tag_groups", toDeleteTagGroups))

	// 5. 批量删除废弃的标签组数据
	err = repository.TagRelationRepository.DeleteByMerchantIdAndTagGroupCodes(merchantId, toDeleteTagGroups, enum.DimensionToTagSourceMapKeyTagCenter)
	if err != nil {
		global.LOG.Error("批量删除标签关联表数据失败",
			zap.Int("merchant_id", merchantId),
			zap.Strings("tag_groups", toDeleteTagGroups),
			zap.Error(err))
		return err
	}

	global.LOG.Info("成功清理商户废弃标签组",
		zap.Int("merchant_id", merchantId),
		zap.Int("deleted_count", len(toDeleteTagGroups)))

	return nil
}
