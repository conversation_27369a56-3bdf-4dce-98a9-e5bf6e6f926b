package infra

import (
	"encoding/json"
	"fmt"
	dimensionService "report-service/cmd/crontab/domain/service/dimension"
	"report-service/cmd/crontab/domain/service/report/customized/paymodemoney"
	"report-service/internal/domain/logic/dm/common"
	"report-service/internal/domain/logic/dm/datavalidation"
	dmtourist "report-service/internal/domain/logic/dm/tourist"
	"report-service/internal/domain/logic/dwm/tourist"
	"report-service/internal/domain/service/ods"
	"report-service/internal/domain/service/permission/module/touristsourceareamodule"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/internal/infra/config/types"

	"github.com/robfig/cron/v3"
)

var cronMap = map[string]func(){
	"statistic_dm_common_day":              common.RestatCommonDayYesterday,
	"statistic_dm_common_hour":             common.RestatCommonHourOneHourAgo,
	"validate_data_for_previous_day":       datavalidation.ValidationYesterday,
	"data_inspection_hour":                 ods.CronDataInspectionHour,
	"restat_outdated_data":                 common.RestatOutdatedData,
	"tourist_dwm_hour":                     tourist.HandleTask,
	"statistic_dm_tourist_day":             dmtourist.RestatCommonDayYesterday,
	"tourist_clean_up_data":                touristsourceareamodule.CleanUpDataYesterday,
	"tourist_statistics_history":           touristsourceareamodule.StatisticsHistory,
	"cleanup_unused_tag_groups":            dimensionService.CleanupUnusedTagGroups,
	"customized_pay_mode_money_dwm":        paymodemoney.DwmHandleTask,
	"customized_pay_mode_money_inspection": paymodemoney.CronDataInspection,
	"customized_pay_mode_money_dm":         paymodemoney.DmHandleTask,
}

func InitTimer() {
	if !global.CONFIG.Timer.Start {
		return
	}
	for _, detail := range global.CONFIG.Timer.Detail {
		go addTask(detail)
	}
}

type cronLogger struct {
	TaskName string
}

func (c cronLogger) Info(msg string, keysAndValues ...interface{}) {
	if keysAndValues == nil {
		global.LOG.Info(fmt.Sprintf("cron[%s] info: %s", c.TaskName, msg))
		return
	}
	b, _ := json.Marshal(keysAndValues)
	global.LOG.Info(fmt.Sprintf("cron[%s] info: %s, %s", c.TaskName, msg, string(b)))
}

func (c cronLogger) Error(err error, msg string, keysAndValues ...interface{}) {
	if keysAndValues == nil {
		global.LOG.Error(fmt.Sprintf("cron[%s] error: %s, %s", c.TaskName, err, msg))
		return
	}
	b, _ := json.Marshal(keysAndValues)
	errMsg := fmt.Sprintf("cron[%s] error: %s, %s, %s", c.TaskName, err, msg, string(b))
	global.LOG.Error(errMsg)
	globalNotice.Error(errMsg)
}

func addTask(detail types.Detail) {
	var option []cron.Option
	if global.CONFIG.Timer.WithSeconds {
		option = append(option, cron.WithSeconds())
	}
	logger := cronLogger{}
	logger.TaskName = detail.TaskName
	option = append(option, cron.WithLogger(logger))
	handle, ok := cronMap[detail.TaskName]
	if !ok {
		global.LOG.Error(fmt.Sprintf("add timer error: no such func %s", detail.TaskName))
		return
	}
	_, err := global.Timer.AddTaskByFunc(detail.TaskName, detail.Interval, handle, option...)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("add timer error: %s", err))
	}
	global.LOG.Info(fmt.Sprintf("add timer success: %s", detail.TaskName))
}
