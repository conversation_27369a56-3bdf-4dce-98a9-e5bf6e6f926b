package dimension

import (
	"encoding/json"
	"report-service/internal/domain/logic/dimensionscheme"
	"report-service/internal/global"

	"github.com/segmentio/kafka-go"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

// SubjectTaggedMessage 定义了 Kafka 消息的结构
type SubjectTaggedMessage struct {
	Subject     string     `json:"subject"`
	SubjectID   string     `json:"subjectId"`
	SubjectName string     `json:"subjectName"`
	TagGroup    TagGroup   `json:"tagGroup"`
	MatchTags   []MatchTag `json:"matchTags"`
}

// TagGroup 定义了消息内标签组的结构
type TagGroup struct {
	ID             string        `json:"id"`
	CreateBy       int           `json:"createBy"`
	UpdateBy       int           `json:"updateBy"`
	CreateAt       string        `json:"createAt"`
	UpdateAt       string        `json:"updateAt"`
	Name           string        `json:"name"`
	Code           string        `json:"code"`
	Type           int           `json:"type"`
	Status         int           `json:"status"`
	Domain         string        `json:"domain"`
	DomainID       string        `json:"domainId"`
	TaggedMultiple bool          `json:"taggedMultiple"`
	ExtensionData  ExtensionData `json:"extensionData"`
}

// ExtensionData 定义了标签组内扩展数据的结构
type ExtensionData struct {
	Scene string `json:"scene"`
}

// MatchTag 定义了消息内匹配标签的结构
type MatchTag struct {
	Tag  Tag         `json:"tag"`
	Data interface{} `json:"data"` // 使用 interface{} 因为 "data" 的结构不完全确定
}

// Tag 定义了匹配标签内标签的结构
type Tag struct {
	ID          string `json:"id"`
	CreateBy    int    `json:"createBy"`
	UpdateBy    int    `json:"updateBy"`
	CreateAt    string `json:"createAt"`
	UpdateAt    string `json:"updateAt"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	GroupCode   string `json:"groupCode"`
	Version     string `json:"version"`
	Description string `json:"description"`
	Status      int    `json:"status"`
	Sid         int    `json:"sid"`
}

// HandleRefreshTagRelation 消费 Kafka 消息并触发维度方案刷新
func HandleRefreshTagRelation(messages ...kafka.Message) error {
	for _, msg := range messages {
		var message SubjectTaggedMessage
		if err := json.Unmarshal(msg.Value, &message); err != nil {
			global.LOG.Error("无法解析刷新标签关系消息", zap.Error(err), zap.ByteString("message", msg.Value))
			continue // 跳过此消息，继续处理其他消息
		}

		// 只处理 domain 为 "member_scene" 的消息
		if message.TagGroup.Domain != "member_scene" {
			continue
		}

		scene := message.TagGroup.ExtensionData.Scene
		tagGroupCode := message.TagGroup.Code
		merchantId, err := cast.ToIntE(message.TagGroup.DomainID)
		if err != nil {
			global.LOG.Error("刷新标签关系消息中缺少必需字段",
				zap.String("scene", scene),
				zap.String("tagGroupCode", tagGroupCode),
				zap.ByteString("message", msg.Value))
			continue
		}

		if scene == "" || tagGroupCode == "" {
			global.LOG.Error("刷新标签关系消息中缺少必需字段",
				zap.String("scene", scene),
				zap.String("tagGroupCode", tagGroupCode),
				zap.ByteString("message", msg.Value))
			continue // 跳过此消息，继续处理其他消息
		}

		// 调用实际的领域逻辑
		err = dimensionscheme.RefreshTagRelationByTagGroupCode(scene, tagGroupCode, merchantId)
		if err != nil {
			global.LOG.Error("刷新标签关系失败",
				zap.Error(err),
				zap.String("scene", scene),
				zap.String("tagGroupCode", tagGroupCode))
			// 记录错误但继续处理其他消息
			continue
		}

		global.LOG.Info("成功刷新标签关系",
			zap.String("scene", scene),
			zap.String("tagGroupCode", tagGroupCode))
	}

	return nil // 表示所有处理完成
}
