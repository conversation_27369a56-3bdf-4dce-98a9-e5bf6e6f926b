package dimension

import (
	"encoding/json"
	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"
	"report-service/internal/domain/logic/dimensionscheme"
	templateLogic "report-service/internal/domain/logic/report/template"
	"report-service/internal/global"
	"report-service/pkg/utils"
)

const (
	RelationSonIdType = 3 // 关联的子账号类型 0分销商 1:资源方 2:员工 3:供应方（父ID为集团帐号情况下）
)

// MemberRelationMessage 商户关联消息结构体
type MemberRelationMessage struct {
	AfterList  MemberRelationDataItem  `json:"afterList"`
	BeforeList *MemberRelationDataItem `json:"beforeList"`
	EventType  string                  `json:"eventType"`
	Offset     int                     `json:"offset"`
	SchemaName string                  `json:"schemaName"`
	TableName  string                  `json:"tableName"`
}

type MemberRelationDataItem struct {
	CutTime     string `json:"cuttime"`
	Id          int    `json:"id"`
	ParentId    int    `json:"parent_id"`
	PriceGroup  int    `json:"price_group_id"`
	RecTime     string `json:"rectime"`
	Remark      string `json:"remark"`
	ShipType    int    `json:"ship_type"`
	SonId       int    `json:"son_id"`
	SonIdType   int    `json:"son_id_type"`
	Status      int    `json:"status"`
	RestoreTime string `json:"restoretime"`
}

func HandleRefreshMemberRelation(messages ...kafka.Message) error {
	for _, msg := range messages {
		var kafkaMsgData MemberRelationMessage
		if err := json.Unmarshal(msg.Value, &kafkaMsgData); err != nil {
			global.LOG.Error("无法解析商户关联消息", zap.Error(err), zap.ByteString("messageValue", msg.Value))
			continue
		}
		// 非供应方（父ID为集团帐号情况下）不处理
		if kafkaMsgData.AfterList.SonIdType != RelationSonIdType {
			continue
		}
		if kafkaMsgData.AfterList.ParentId == 0 || kafkaMsgData.AfterList.SonId == 0 {
			global.LOG.Error("接收到非集团帐号关联消息，跳过处理", zap.ByteString("messageValue", msg.Value))
			continue
		}

		//实际业务逻辑
		groupMemberIds := make([]int, 0)
		var err error
		switch kafkaMsgData.EventType {
		case "INSERT":
			//单条写入
			err = dimensionscheme.AddMemberTagRelationByGroupAccountIdAndSonId(kafkaMsgData.AfterList.ParentId, kafkaMsgData.AfterList.SonId)
		case "UPDATE", "DELETE":
			//直接刷新，删除全部，重新生成
			groupMemberIds, err = dimensionscheme.RefreshMemberTagRelationByGroupAccountId(kafkaMsgData.AfterList.ParentId)
		default:
			global.LOG.Warn("接收到非INSERT、UPDATE、DELETE商户关联消息，跳过处理", zap.ByteString("messageValue", msg.Value))
			continue
		}
		if err != nil {
			global.LOG.Error("处理商户关联消息失败", zap.Error(err), zap.ByteString("messageValue", msg.Value))
			continue
		}

		//删除或则更新需要同步处理模板
		if utils.Container([]string{"UPDATE", "DELETE"}, kafkaMsgData.EventType) {
			//模板更新下
			templates, errLogic := templateLogic.GetAllTemplatesByMerchantId(kafkaMsgData.AfterList.ParentId)
			if errLogic != nil {
				global.LOG.Error("查询商户模板列表失败", zap.Error(errLogic), zap.ByteString("messageValue", msg.Value))
				continue
			}
			for _, tpl := range templates {
				if tpl.Payload.SpecialDimension.GroupMember == nil || len(tpl.Payload.SpecialDimension.GroupMember) == 0 {
					continue // 模板没有配置模板内集团成员的过滤
				}
				//标记模板是否需要更新
				modified := false
				groupMemberIdsNew := make([]int, 0)
				for _, groupMemberId := range tpl.Payload.SpecialDimension.GroupMember {
					if utils.Container(groupMemberIds, groupMemberId) {
						groupMemberIdsNew = append(groupMemberIdsNew, groupMemberId)
					} else {
						modified = true
					}
				}
				// 需要更新，联动模板更新
				if modified {
					// 更新模板内集团成员过滤
					tpl.Payload.SpecialDimension.GroupMember = groupMemberIdsNew
					errUpdate := templateLogic.UpdateTemplate(tpl)
					if errUpdate != nil {
						global.LOG.Error("更新模板失败", zap.Int("templateId", tpl.Id), zap.Error(errUpdate))
					}
				}
			}
		}
		global.LOG.Info("处理商户关联消息完成", zap.Int("groupAccountId", kafkaMsgData.AfterList.ParentId),
			zap.Int("sonId", kafkaMsgData.AfterList.SonId), zap.String("eventType", kafkaMsgData.EventType))
	}
	return nil
}
