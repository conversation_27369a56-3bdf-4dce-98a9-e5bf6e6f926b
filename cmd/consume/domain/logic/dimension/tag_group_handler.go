package dimension

import (
	"encoding/json"
	"report-service/internal/domain/enum"
	dsLogic "report-service/internal/domain/logic/dimensionscheme"
	templateLogic "report-service/internal/domain/logic/report/template"

	"report-service/internal/global"

	"github.com/segmentio/kafka-go"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

// TagGroupDeleteMessage 定义了标签组删除 Kafka 消息的结构
type TagGroupDeleteMessage struct {
	Code     string `json:"code"`     // 标签组编码
	Domain   string `json:"domain"`   // 标签组所属域
	DomainId string `json:"domainId"` // 标签组所属域ID
}

// HandleTagGroupDelete 处理标签组删除的 Kafka 消息
// - 删除标签组相关的标签关联表记录
// - 商户下的模板如果使用了该标签组，也需要置空
func HandleTagGroupDelete(messages ...kafka.Message) error {
	for _, msg := range messages {
		var kafkaMsgData TagGroupDeleteMessage
		if err := json.Unmarshal(msg.Value, &kafkaMsgData); err != nil {
			global.LOG.Error("无法解析标签组删除消息", zap.Error(err), zap.ByteString("messageValue", msg.Value))
			continue // 跳过此消息，处理下一条
		}

		if kafkaMsgData.Domain != "member_scene" {
			global.LOG.Warn("接收到非member_scene域的标签组删除消息，跳过处理", zap.ByteString("messageValue", msg.Value))
			continue
		}

		if kafkaMsgData.Code == "" {
			global.LOG.Warn("接收到空的标签组编码，跳过处理", zap.ByteString("messageValue", msg.Value))
			continue
		}

		global.LOG.Info("开始处理标签组删除事件", zap.String("groupCode", kafkaMsgData.Code))

		// 1. 删除标签组相关的标签关联表记录 (r_dimension_tag_relation)
		// 调用 dimensionscheme logic 层的方法
		err := dsLogic.DeleteTagRelationsByGroupCode(cast.ToInt(kafkaMsgData.DomainId), kafkaMsgData.Code, enum.DimensionToTagSourceMapKeyTagCenter)
		if err != nil {
			global.LOG.Error("删除标签关联记录失败",
				zap.String("groupCode", kafkaMsgData.Code),
				zap.Error(err))
			// 根据业务需求决定是否继续或返回错误。这里选择继续处理其他消息。
			continue
		}
		global.LOG.Info("成功删除标签关联记录", zap.String("groupCode", kafkaMsgData.Code))

		// 2. 商户下的模板 (r_template) 如果使用了该标签组，也需要置空其 payload 中的引用
		templates, errLogic := templateLogic.GetAllTemplatesByMerchantId(cast.ToInt(kafkaMsgData.DomainId))
		if errLogic != nil {
			global.LOG.Error("获取商户模板失败", zap.Int("merchantId", cast.ToInt(kafkaMsgData.DomainId)), zap.Error(errLogic))
			continue
		}

		for _, tpl := range templates {
			if tpl.Payload.DimensionTagGroup == nil {
				continue // 模板没有配置维度标签组
			}

			modified := false
			for scene, groupCodeInTpl := range tpl.Payload.DimensionTagGroup {
				if groupCodeInTpl == kafkaMsgData.Code {
					delete(tpl.Payload.DimensionTagGroup, scene) // 从 map 中移除
					modified = true
				}
			}

			if modified {
				errUpdate := templateLogic.UpdateTemplate(tpl)
				if errUpdate != nil {
					global.LOG.Error("更新模板失败", zap.Int("templateId", tpl.Id), zap.String("groupCode", kafkaMsgData.Code), zap.Error(errUpdate))
				}
			}
		}

		global.LOG.Info("标签组删除事件处理完成", zap.String("groupCode", kafkaMsgData.Code))
	}
	return nil
}
