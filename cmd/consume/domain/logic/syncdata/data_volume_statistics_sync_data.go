package syncdata

import (
	"encoding/json"
	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"
	"report-service/internal/domain/logic/dm/customized/reportvisitsstatistics"
	"report-service/internal/global"
)

type EventType int

const (
	EventTypeAdd    EventType = 1 // 新增
	EventTypeUpdate EventType = 2 // 修改
	EventTypeDelete EventType = 9 // 删除
)

type EventData struct {
	SerialNo   int         `json:"serial_no"`   // 流水号
	BusinessNo int         `json:"business_no"` // 业务编号
	MerchantID int         `json:"merchant_id"` // 商户ID
	MemberID   int         `json:"member_id"`   // 用户ID
	ModuleKey  string      `json:"module_key"`  // 模块标识
	IncrCnt    int         `json:"incr_cnt"`    // 增量数
	OperatedAt string      `json:"operated_at"` // 操作时间
	Extra      interface{} `json:"extra"`       // 增量数据
}

type EventMessage struct {
	SerialNo   int       `json:"serial_no"`   // 消息流水号
	Type       EventType `json:"type"`        // 事件类型
	Data       EventData `json:"data"`        // 事件数据
	OperatedAt string    `json:"operated_at"` // 事件时间
}

func DataVolumeStatistics(messages ...kafka.Message) error {
	list := make([]reportvisitsstatistics.HandleDataItem, 0)
	for _, msg := range messages {
		var kafkaMsgData EventMessage
		if err := json.Unmarshal(msg.Value, &kafkaMsgData); err != nil {
			global.LOG.Error("无法解析数据量统计消息", zap.Error(err), zap.ByteString("messageValue", msg.Value))
			continue
		}
		//只处理新增数据
		if kafkaMsgData.Type != EventTypeAdd {
			continue
		}
		list = append(list, reportvisitsstatistics.HandleDataItem{
			SerialNo:   kafkaMsgData.Data.SerialNo,
			BusinessNo: kafkaMsgData.Data.BusinessNo,
			MerchantID: kafkaMsgData.Data.MerchantID,
			MemberID:   kafkaMsgData.Data.MemberID,
			ModuleKey:  kafkaMsgData.Data.ModuleKey,
			IncrCnt:    kafkaMsgData.Data.IncrCnt,
			OperatedAt: kafkaMsgData.Data.OperatedAt,
			Extra:      kafkaMsgData.Data.Extra,
		})
	}
	if len(list) <= 0 {
		return nil
	}
	err := reportvisitsstatistics.HandleDataCleaner(list)
	if err != nil {
		global.LOG.Error("数据量统计处理失败", zap.Error(err))
		return err
	}
	global.LOG.Info("数据量统计处理成功", zap.Int("count", len(list)))
	return nil
}
