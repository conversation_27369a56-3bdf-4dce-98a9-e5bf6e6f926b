package syncdata

import (
	"encoding/json"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"
	"report-service/internal/domain/enum"
	"report-service/internal/domain/logic/dwm/customized"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
)

// OpenYttCooperateSyncDataInfo 开放生态服务通知数据更新事件信息
type OpenYttCooperateSyncDataInfo struct {
	MemberId  int    `json:"memberId"`
	Method    string `json:"method"`
	AppId     string `json:"appId"`
	StateTime string `json:"stateTime"`
	EndTime   string `json:"endTime"`
}

// OpenYttCooperateSyncDataMessage 开放生态服务通知数据更新事件
type OpenYttCooperateSyncDataMessage struct {
	SysCode  string                       `json:"sysCode"`
	IsManual bool                         `json:"isManual"`
	SyncData OpenYttCooperateSyncDataInfo `json:"syncData"`
}

func HandleOpenYttCooperateSyncData(messages ...kafka.Message) error {
	for _, msg := range messages {
		var message OpenYttCooperateSyncDataMessage
		if err := json.Unmarshal(msg.Value, &message); err != nil {
			global.LOG.Error("无法解析开放生态服务通知数据更新消息", zap.Error(err), zap.ByteString("message", msg.Value))
			continue
		}

		// 同步银豹数据处理
		if message.SysCode == enum.OpenCooperateSysCodeYinBao && message.IsManual {
			if err := HandleOpenCooperateManualSysDataYinBao(message); err != nil {
				global.LOG.Error("处理开放生态服务通知数据更新消息失败", zap.Error(err), zap.Any("message", message))
				continue
			}
			globalNotice.Warning("开放生态服务通知数据手动更新消息，处理完成",
				fmt.Sprintf("商户ID：%d", message.SyncData.MemberId),
				fmt.Sprintf("日期：%s ~ %s", message.SyncData.StateTime, message.SyncData.EndTime),
				fmt.Sprintf("AppId：%s", message.SyncData.AppId),
				fmt.Sprintf("Method：%s", message.SyncData.Method),
			)
		}
	}
	return nil
}

func HandleOpenCooperateManualSysDataYinBao(message OpenYttCooperateSyncDataMessage) error {
	syncRecordAddLog(message)
	startTime := carbon.Parse(message.SyncData.StateTime)
	endTime := carbon.Parse(message.SyncData.EndTime)
	//结束时间不能大于等于今天00:00:00, 如果是否，则设置为昨天23:59:59
	if endTime.Gte(carbon.Now().StartOfDay()) {
		endTime = carbon.Now().SubDays(1).EndOfDay()
	}
	//结束时间不能小于开始时间
	if endTime.Lt(startTime) {
		return nil
	}
	for date := startTime; date.Lte(endTime); date = date.AddHours(1) {
		switch message.SyncData.Method {
		case enum.OpenCooperateSysDataMethodYinBaoTicket:
			customized.PayModeMoneyReportYinBaoOrder(customized.ExecuteParams{
				StartTime:  date.StartOfHour().ToDateTimeString(),
				EndTime:    date.EndOfHour().ToDateTimeString(),
				MerchantId: message.SyncData.MemberId,
				Ext: customized.ExecuteParamsExt{
					IsNextProcess:     true,
					ExternalProjectId: message.SyncData.AppId,
					ExternalMethod:    message.SyncData.Method,
				},
			})
		case enum.OpenCooperateSysDataMethodYinBaoRecharge:
			customized.PayModeMoneyReportYinBaoRecharge(customized.ExecuteParams{
				StartTime:  date.StartOfHour().ToDateTimeString(),
				EndTime:    date.EndOfHour().ToDateTimeString(),
				MerchantId: message.SyncData.MemberId,
				Ext: customized.ExecuteParamsExt{
					IsNextProcess:     true,
					ExternalProjectId: message.SyncData.AppId,
					ExternalMethod:    message.SyncData.Method,
				},
			})
		}
	}
	return nil
}

func syncRecordAddLog(message OpenYttCooperateSyncDataMessage) {
	switch message.SyncData.Method {
	case enum.OpenCooperateSysDataMethodYinBaoTicket:
		global.LOG.Info("手动同步银豹票据数据，报表联动同步", zap.Any("sync_data", message.SyncData))
	case enum.OpenCooperateSysDataMethodYinBaoRecharge:
		global.LOG.Info("手动同步银豹充值数据，报表联动同步", zap.Any("sync_data", message.SyncData))
	}
}
