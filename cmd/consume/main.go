package main

import (
	"os"
	"os/signal"
	"report-service/internal/global"
	globalNotice "report-service/internal/global/notice"
	"report-service/internal/infra"
	"report-service/internal/infra/db"
	"report-service/internal/infra/kafka"
	"syscall"

	_ "github.com/apache/skywalking-go"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod vendor
//go:generate go env -w GOPRIVATE=gitlab.12301.test
//go:generate go env -w GOINSECURE=gitlab.12301.test
//go:generate go env -w GONOPROXY=gitlab.12301.test
//go:generate go env -w GONOSUMDB=gitlab.12301.test
//go:generate git config --global url."http://ci-docker:<EMAIL>".insteadOf "http://gitlab.12301.test"

func main() {
	infra.CommonInit()
	defer infra.CommonClose()

	// 连接数据库
	db.Init()
	defer db.Close()

	// 初始化 Kafka
	kafka.Init(true)
	defer kafka.Release()

	globalNotice.Warning("Report service started.")

	// 设置信号处理
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	<-signalChan

	// 收到信号后进行清理
	global.LOG.Info("shutting down gracefully...")
}
