package szerrors

import "errors"

type DataNoFoundError struct {
	StackError
}

// NewDataNoFoundError 创建无数据错误
func NewDataNoFoundError(err error) error {
	stackError := NewStackError(err)
	return &DataNoFoundError{
		StackError: *stackError,
	}
}

// NewDataNoFoundErrorWithText 创建带自定义文本的无数据错误
func NewDataNoFoundErrorWithText(text string) error {
	stackError := NewStackError(errors.New(text))
	return &DataNoFoundError{
		StackError: *stackError,
	}
}

// IsDataNoFoundError 判断是否是无数据错误
func IsDataNoFoundError(err error) bool {
	var dataNoFoundError *DataNoFoundError
	return errors.As(err, &dataNoFoundError)
}
