package szerrors

import "errors"

// DataPersistenceError 数据持久化错误
type DataPersistenceError struct {
	StackError
}

func NewDataPersistenceError(err error) error {
	stackError := NewStackError(err)
	return &DataPersistenceError{
		StackError: *stackError,
	}
}

func NewDataPersistenceErrorWithText(text string) error {
	stackError := NewStackError(errors.New(text))
	return &DataPersistenceError{
		StackError: *stackError,
	}
}
