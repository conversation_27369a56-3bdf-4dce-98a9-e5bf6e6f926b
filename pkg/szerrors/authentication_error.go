package szerrors

import "errors"

// AuthenticationError 认证错误
type AuthenticationError struct {
	StackError
}

func NewAuthenticationError(err error) error {
	stackError := NewStackError(err)
	return &AuthenticationError{
		StackError: *stackError,
	}
}

func NewAuthenticationErrorWithText(text string) error {
	stackError := NewStackError(errors.New(text))
	return &AuthenticationError{
		StackError: *stackError,
	}
}
