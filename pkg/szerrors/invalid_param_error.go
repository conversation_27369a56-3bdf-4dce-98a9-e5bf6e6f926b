package szerrors

import "errors"

// InvalidParamError 验证错误
type InvalidParamError struct {
	StackError
	ErrMessages []string
}

func NewInvalidParamError(err error) error {
	stackError := NewStackError(err)
	return &InvalidParamError{
		StackError: *stackError,
	}
}

func NewInvalidParamErrorWithText(text string, errMessages ...string) error {
	stackError := NewStackError(errors.New(text))
	return &InvalidParamError{
		StackError:  *stackError,
		ErrMessages: errMessages,
	}
}
