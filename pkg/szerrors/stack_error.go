package szerrors

import (
	"regexp"
	"runtime/debug"
	"strings"
)

type StackError struct {
	Err   error
	Stack string
}

func (e StackError) Error() string {
	return e.Err.Error()
}

func (e StackError) TrimmedStack(projectModule string) string {
	lines := strings.Split(e.Stack, "\n")
	var trimmedLines []string
	for i, line := range lines {
		if i == 0 {
			trimmedLines = append(trimmedLines, line)
			continue
		}
		// 只输出项目实现的包，不打印第三方包
		if !strings.Contains(line, projectModule) {
			continue
		}
		// 过滤掉必经的路径
		if strings.Contains(line, "szerrors.TrimmedStack") || strings.Contains(line, "szerrors.NewStackError") {
			continue
		}
		// 加点缩进
		line = "  " + line
		// 去除参数，都是内存地址，没有意义
		re := regexp.MustCompile(`(\w+)\(.+\)`)
		line = re.ReplaceAllString(line, "$1()")
		trimmedLines = append(trimmedLines, line)
	}
	return strings.Join(trimmedLines, "\n")
}

func NewStackError(err error) *StackError {
	return &StackError{
		Err:   err,
		Stack: string(debug.Stack()),
	}
}
