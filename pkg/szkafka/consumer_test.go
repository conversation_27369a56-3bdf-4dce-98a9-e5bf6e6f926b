package szkafka

import (
	"github.com/segmentio/kafka-go"
	"testing"
	"time"
)

// 测试消费性能
func BenchmarkConsumer(b *testing.B) {
	consume := NewConsumer(&ConsumerOptions{
		Brokers:        []string{"192.168.20.211:9092"},
		Topic:          "reportService_test_local",
		GroupId:        "reportService",
		CommitInterval: time.Second,
		MessageHandler: func(messages ...kafka.Message) error {
			return nil
		},
	})
	defer func() { _ = consume.Close() }()
	b.ResetTimer()
	b.Run("BenchmarkConsumer", func(b *testing.B) {
		for i := 0; i < 30000; i++ {
			err := consume.Consume()
			if err != nil {
				b.Error(err)
				return
			}
		}
	})
}

// 测试批量消费
func TestConsumeBatch(t *testing.T) {
	consume := NewConsumer(&ConsumerOptions{
		Brokers:        []string{"192.168.20.211:9092"},
		Topic:          "reportService_test_local",
		GroupId:        "reportService",
		CommitInterval: time.Second,
		BatchSize:      20000,
		MessageHandler: func(messages ...kafka.Message) error {
			t.Log("收到消息", len(messages), messages[0].Offset)
			time.Sleep(time.Second)
			return nil
		},
	})
	defer consume.Close()

	for i := 0; i < 999; i++ {
		err := consume.Consume()
		if err != nil {
			t.Error(err)
			return
		}
	}

}
