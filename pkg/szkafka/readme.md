# Kafka 包

## 介绍

本包基于 [segmentio/kafka-go](https://github.com/segmentio/kafka-go) 包提供了对 Kafka 的简单封装，包括生产者、消费者、消息和主题等。

## 配置

### ConsumerOptions 配置结构体

`ConsumerOptions` 结构体用于定义消费者的配置选项，提供了消息消费的灵活设置。以下是各个字段的详细说明。

#### 字段说明

- **Brokers** `[]string`  
  Kafka Broker 地址列表。

- **Topic** `string`  
  订阅的主题名称。

- **GroupId** `string`  
  消费组 ID，用于标识该消费者的组。

- **GroupTopics** `[]string`  
  订阅多个分区，与 Topic 互斥

- **BatchSize** `int`  
  批量消费的消息数量。

- **MessageHandler** `*MessageHandler`  
  批量消费消息的回调函数。

- **PoolSize** `int`  
  消费者协程池大小。`0` 表示不启用协程池。

## 高级配置

- **MinBytes** `int`  
  单个消息的最小字节数。当消息大小小于 `MinBytes` 时，`Consume` 不会立即返回消息，而是等待更多消息达到条件后才进行消费。  
  **应用场景**：适用于批量消息消费，当消息积累到指定大小时才开始处理。例如，对于消息大小约 500 字节的 `myuu_pft_order_track`，设置为 `10e4` 时，消费约 200 条消息后开始处理。  
  **默认值**：1

- **MaxBytes** `int`  
  单次读取的最大字节数。通过限制单次读取消息的大小，可以避免因过大消息导致的内存不足问题。  
  **应用场景**：用于控制单次拉取的消息量，防止内存溢出等问题。  
  **默认值**：1MB，且不能小于 `MinBytes`。

- **MaxWait** `time.Duration`  
  最大等待时间。如果在等待时未达到最小字节数 `MinBytes`，在达到 `MaxWait` 时间时也会返回消息。  
  **默认值**：10 秒

- **CommitInterval** `time.Duration`  
  偏移量提交的时间间隔。设置为 `0` 时会同步提交偏移量。定期提交有助于提高性能。

- ** StartTime** `time.Time`  
  起始时间，默认从最早的位置消费。

## 用法示例

### 生产者

```go
produce := NewProducer(&ProducerOptions{
    Brokers: []string{"192.168.20.211:9092"},
})
for i := 0; i < b.N; i++ {
    err := produce.Send("test_topic", ProduceMessage{
        Key:   "test_key",
        Value: "test message",
    })
    if err != nil {
        return
    }
}
err := produce.Close()
if err != nil {
    return
}
```

### 消费者

```go
consume := NewConsumer(&ConsumerOptions{
    Brokers: []string{"192.168.20.211:9092"},
    Topic:          "test_topic",
    GroupId:        "test_group",
    PoolSize:       3,
    CommitInterval: time.Second,
    BatchSize:      200,
    MessageHandler: func(messages ...kafka.Message) error {
        // 处理批量消息
        return nil
    },
})
defer consume.Close()
```

## 依赖

1. kafka-go 客户端 - [segmentio/kafka-go](https://github.com/segmentio/kafka-go)
2. 协程池，管理 Consume 和 ConsumeHandler 执行逻辑 - [ants/pool](https://github.com/panjf2000/ants)
3. 日志库 - [zap](https://github.com/uber-go/zap)