package szkafka

import (
	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"sync"
	"time"
)

var (
	Producers     map[string]*Producer
	consumers     []*Consumer
	consumersPool *ants.Pool
)

type KafkaProducerConfig struct {
	Name string
	*ProducerOptions
}

func New(kafkaProducerConfigs []KafkaProducerConfig, consumersOptions ...*ConsumerOptions) {
	if len(kafkaProducerConfigs) > 0 {
		Producers = make(map[string]*Producer)
		for _, producerConfig := range kafkaProducerConfigs {
			Producers[producerConfig.Name] = NewProducer(producerConfig.ProducerOptions)
			Logger.Info("producer init", zap.Any("options", producerConfig.ProducerOptions))
		}
	}
	if len(consumersOptions) <= 0 {
		return
	}

	var err error
	consumersPool, err = ants.NewPool(len(consumersOptions))
	if err != nil {
		panic(err)
	}
	for _, consumerOptions := range consumersOptions {
		consumer := NewConsumer(consumerOptions)
		consumers = append(consumers, consumer)
		Logger.Info(
			"consumer init",
			zap.Any("Brokers", consumerOptions.Brokers),
			zap.Any("Topic", consumerOptions.Topic),
			zap.Any("GroupTopics", consumerOptions.GroupTopics),
			zap.Any("GroupId", consumerOptions.GroupId),
			zap.Any("BatchSize", consumerOptions.BatchSize),
			zap.Any("PoolSize", consumerOptions.PoolSize),
			zap.Any("MinBytes", consumerOptions.MinBytes),
			zap.Any("MaxBytes", consumerOptions.MaxBytes),
			zap.Any("MaxWait", consumerOptions.MaxWait),
			zap.Any("CommitInterval", consumerOptions.CommitInterval),
			zap.Any("StartOffset", consumerOptions.StartOffset),
		)
		err = consumersPool.Submit(func() {
			for {
				if consumersPool.IsClosed() {
					return
				}
				_ = consumer.Consume()
			}
		})
		if err != nil {
			panic(err)
		}
	}
}

func Release() {
	Logger.Info("Closing consumers")
	wg := sync.WaitGroup{}
	for _, consumer := range consumers {
		wg.Add(1)
		go func() {
			_ = consumer.Close()
			wg.Done()
		}()
	}
	if consumersPool != nil {
		wg.Add(1)
		go func() {
			err := consumersPool.ReleaseTimeout(time.Minute)
			wg.Done()
			if err != nil {
				Logger.Error("Failed to close consumers pool", zap.Error(err))
			}
		}()
	}
	wg.Wait()
	Logger.Info("Closed consumers")

	Logger.Info("Closing producers")
	for _, producer := range Producers {
		_ = producer.Close()
	}
	Logger.Info("Closed producers")
}
