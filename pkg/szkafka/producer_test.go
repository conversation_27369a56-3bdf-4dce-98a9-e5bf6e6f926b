package szkafka

import (
	"gitee.com/golang-module/carbon/v2"
	"testing"
)

// 测试发送消息性能
// 内网kafka、开发机器，大概为 34249 ns/op
func BenchmarkProduce(b *testing.B) {
	produce := NewProducer(&ProducerOptions{
		Brokers: []string{"192.168.20.211:9092"},
	})
	b.<PERSON>set<PERSON>imer()
	for i := 0; i < b.N; i++ {
		s := carbon.Now().ToDateTimeMicroString()
		err := produce.Send("reportService_test_local", ProduceMessage{
			Key:   s,
			Value: s,
		})
		if err != nil {
			b.<PERSON>rror(err)
			return
		}
	}
	err := produce.Close()
	if err != nil {
		b.<PERSON><PERSON>r(err)
		return
	}
}
