package szkafka

import (
	"context"
	"encoding/json"
	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"
)

type Producer struct {
	writer  *kafka.Writer
	options *ProducerOptions
}

type ProducerOptions struct {
	Brokers []string
}

type ProduceMessage struct {
	Topic *string // 如果为空，则使用默认topic
	Key   string
	Value interface{}
}

func NewProducer(opt *ProducerOptions) *Producer {
	return &Producer{
		writer: &kafka.Writer{
			Addr:     kafka.TCP(opt.Brokers...),
			Balancer: &kafka.LeastBytes{},
			Async:    true, // 设定为异步发送消息，比同步发送快非常多
		},
		options: opt,
	}
}
func (p *Producer) Send(topic string, messages ...ProduceMessage) (err error) {
	kafkaMessages := make([]kafka.Message, 0)
	for _, message := range messages {
		t := topic
		if message.Topic != nil {
			t = *message.Topic
		}
		var value []byte
		if v, ok := message.Value.(string); ok {
			value = []byte(v)
		} else {
			value, err = json.Marshal(message.Value)
			if err != nil {
				return err
			}
		}
		kafkaMessages = append(kafkaMessages, kafka.Message{
			Topic: t,
			Key:   []byte(message.Key),
			Value: value,
		})
		Logger.Debug("Send message", zap.String("topic", t), zap.String("key", message.Key), zap.String("value", string(value)))
	}
	err = p.writer.WriteMessages(context.Background(), kafkaMessages...)
	if err != nil {
		Logger.Error("producer send message error", zap.Error(err))
		return err
	}
	return nil
}

func (p *Producer) Close() error {
	err := p.writer.Close()
	if err != nil {
		Logger.Error("producer close error", zap.Error(err))
		return err
	}
	return nil
}
