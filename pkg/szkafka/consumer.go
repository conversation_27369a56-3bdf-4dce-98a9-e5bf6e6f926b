package szkafka

import (
	"context"
	"github.com/panjf2000/ants/v2"
	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"
	"time"
)

type Consumer struct {
	reader         *kafka.Reader
	batchSize      int
	messageHandler MessageHandler
	pool           *ants.Pool
	options        *ConsumerOptions
}

type ConsumerOptions struct {
	Brokers        []string
	Topic          string
	GroupTopics    []string // 订阅多个分区，与 Topic 互斥
	GroupId        string
	BatchSize      int            // 批量消费消息的大小
	MessageHandler MessageHandler // 批量消费消息的回调函数。
	PoolSize       int            // 消费者协程池的大小，0 表示不启用池。

	// MinBytes 表示单个消息的最小字节数。
	// 当消息大小小于 MinBytes 时，Consume 不会返回该消息，而是等待更多消息。
	// [该参数暂时无用] 目前设置了 BatchSize 后，MinBytes 没什么意义，因为 kafka-go 只有获取单条消息的方法，无法批量获取。
	// 默认 1
	MinBytes int

	// MaxBytes 表示单次读取的最大字节数。
	// [该参数暂时无用] 同上。
	// 默认 1MB，不得小于 MinBytes。
	MaxBytes int

	// MaxWait 表示最大等待时间，即便未达到最小字节数时，也会返回该消息。
	// 默认 10s
	MaxWait time.Duration

	// CommitInterval 参数指示偏移量提交给代理的时间间隔。如果设为 0，则提交将以同步方式进行。
	// 定期提交可以提升性能。
	// 默认 1s
	CommitInterval time.Duration

	StartOffset int64 // 起始偏移量，默认从最早的位置消费。
}

type MessageHandler func(messages ...kafka.Message) error

func NewConsumer(opt *ConsumerOptions) *Consumer {
	var pool *ants.Pool
	var err error
	if opt.PoolSize > 1 {
		pool, err = ants.NewPool(opt.PoolSize)
		if err != nil {
			panic(err)
		}
	}
	if opt.BatchSize <= 0 {
		opt.BatchSize = 1
	}
	if opt.MaxWait == 0 {
		opt.MaxWait = 10 * time.Second
	}
	if opt.CommitInterval == 0 {
		opt.CommitInterval = 1 * time.Second
	}
	consumer := &Consumer{
		reader: kafka.NewReader(kafka.ReaderConfig{
			Brokers:        opt.Brokers,
			Topic:          opt.Topic,
			GroupTopics:    opt.GroupTopics,
			GroupID:        opt.GroupId,
			MinBytes:       opt.MinBytes,
			MaxBytes:       opt.MaxBytes,
			MaxWait:        opt.MaxWait,
			CommitInterval: opt.CommitInterval,
			StartOffset:    opt.StartOffset,
		}),
		batchSize:      opt.BatchSize,
		messageHandler: opt.MessageHandler,
		pool:           pool,
		options:        opt,
	}

	return consumer
}

func (c *Consumer) Consume() error {
	// 带有超时的上下文，防止达不到 BatchSize 导致持续阻塞
	ctx, cancel := context.WithTimeout(context.Background(), c.options.MaxWait)
	defer cancel()

	messages := make([]kafka.Message, 0)
	for i := 0; i < c.batchSize; i++ {
		m, err := c.reader.FetchMessage(ctx)
		if err != nil {
			// 超时或其他错误，跳出循环
			break
		}
		messages = append(messages, m)
		Logger.Debug("Received message", zap.String("topic", m.Topic), zap.String("key", string(m.Key)),
			zap.String("value", string(m.Value)), zap.Int("offset", int(m.Offset)), zap.Int("partition", m.Partition))
	}

	// 同步处理消息
	if c.pool == nil {
		return c.commitMessages(messages...)
	}
	// 异步并发处理消息
	return c.pool.Submit(func() { _ = c.commitMessages(messages...) })
}

func (c *Consumer) commitMessages(messages ...kafka.Message) error {
	if len(messages) == 0 {
		return nil
	}
	defer func() {
		if r := recover(); r != nil {
			Logger.Error("Handle message panic", zap.Any("panic", r), zap.String("topic", c.options.Topic),
				zap.String("groupId", c.options.GroupId))
		}
	}()
	err := c.messageHandler(messages...)
	if err != nil {
		Logger.Error("Handle message error", zap.Error(err), zap.String("topic", c.options.Topic),
			zap.String("groupId", c.options.GroupId))
		return err
	}
	_ = c.reader.CommitMessages(context.Background(), messages...)
	return nil
}

func (c *Consumer) Close() error {
	var err error

	// 等待协程池释放资源
	if c.pool != nil {
		err = c.pool.ReleaseTimeout(time.Minute)
		if err != nil {
			Logger.Error("Pool release error", zap.Error(err))
			return err
		}
	}

	// 关闭 kafka reader
	err = c.reader.Close()
	if err != nil {
		Logger.Error("Close reader error", zap.Error(err))
		return err
	}

	return nil
}
