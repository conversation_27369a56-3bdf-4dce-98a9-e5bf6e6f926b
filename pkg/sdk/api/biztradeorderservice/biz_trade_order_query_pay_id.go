package biztradeorderservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

const (
	QueryPayIdBatchSize = 200
)

type PayIdInfo struct {
	OrderId     string `json:"orderId"`
	PaySerialNo string `json:"paySerialNo"`
}

func BatchQueryPayId(orderIds []string) ([]PayIdInfo, error) {
	orderIds = utils.RemoveDuplicate(orderIds)
	splitList := utils.ChunkSlice(orderIds, QueryPayIdBatchSize)
	var results []PayIdInfo
	for _, splitIds := range splitList {
		response, resErr := java.NewApiRequest("/order-center/feign-client/biz/trade-order/query-pay-id", map[string]interface{}{
			"orderIdList": splitIds,
		}, true)
		if resErr != nil {
			return nil, resErr
		}
		var data []PayIdInfo
		err := utils.JsonConvertor(response, &data)
		if err != nil {
			return nil, err
		}
		results = append(results, data...)
	}
	return results, nil
}

func BatchQueryPayIdMapByOrderIds(orderIds []string) (map[string]string, error) {
	res, err := BatchQueryPayId(orderIds)
	if err != nil {
		return nil, err
	}
	if res == nil || len(res) == 0 {
		return nil, nil
	}
	results := make(map[string]string)
	for _, info := range res {
		results[info.OrderId] = info.PaySerialNo
	}
	return results, nil
}
