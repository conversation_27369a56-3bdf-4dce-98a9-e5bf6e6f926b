package merchantdatascopeservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type BusinessData struct {
	IdList    []int `json:"idList"`
	NotIdList []int `json:"notIdList"`
}

func QueryBusinessData(userId int, tag string) (*BusinessData, error) {
	data, err := java.Post("/web/v1/authority/merchantDataScopeService/queryBusinessData", map[string]interface{}{
		"userId": userId,
		"tag":    tag,
	})
	if err != nil {
		return nil, err
	}
	var result BusinessData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
