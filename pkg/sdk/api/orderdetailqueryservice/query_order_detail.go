package orderdetailqueryservice

import (
	"encoding/json"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type FxDetailsCommon struct {
	Aids       string `json:"aids"`
	AidsMoney  string `json:"aidsMoney"`
	AidsPrice  string `json:"aidsPrice"`
	ApplyDid   int    `json:"applyDid"`
	Assembly   int    `json:"assembly"`
	ConcatId   string `json:"concatId"`
	ID         int    `json:"id"`
	Memo       string `json:"memo"`
	OrderMonth int    `json:"orderMonth"`
	OrderTime  int64  `json:"orderTime"`
	OrderId    string `json:"orderid"`
	Origin     string `json:"origin"`
	PayStatus  int    `json:"payStatus"`
	Series     string `json:"series"`
	SyncState  int    `json:"syncState"`
	UpdateTime int64  `json:"updateTime"`
	NoticeExt  string `json:"noticeExt"`  // 作为字符串存储嵌套的JSON
	OfflineExt string `json:"offlineExt"` // 作为字符串存储嵌套的JSON
	ProductExt string `json:"productExt"` // 作为字符串存储嵌套的JSON
	PartnerExt string `json:"partnerExt"` // 作为字符串存储嵌套的JSON
}
type FxDetailsResponse struct {
	FxDetailsCommon
	ExtContent string `json:"extContent"` // 作为字符串存储嵌套的JSON
}
type FxDetailsData struct {
	FxDetailsCommon
	ExtContent FxDetailsExtContent `json:"extContent"` // 作为字符串存储嵌套的JSON
}
type FxDetailsExtContent struct {
	SectionTimeStr string `json:"sectionTimeStr"`
	FirstDTime     string `json:"firstDtime"`
	PSubType       int    `json:"pSubType"`
}

func QueryOrderDetailsByOrderNos(orderNos []string) ([]FxDetailsData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.BatchPost("/web/v1/orderquery/orderDetailQueryService/queryOrderDetailsByOrdernums", map[string]interface{}{
		"ordernums": orderNos,
	}, "ordernums", 200, true)
	if err != nil {
		return nil, err
	}
	var fxDetailsResponse []FxDetailsResponse
	err = utils.JsonConvertor(data, &fxDetailsResponse)
	if err != nil {
		return nil, err
	}
	results, resErr := handleResponseData(fxDetailsResponse)
	if resErr != nil {
		return nil, resErr
	}
	return results, nil
}

// QueryOrderDetailsMapByOrderNos 订单详情获取
func QueryOrderDetailsMapByOrderNos(orderNos []string) (map[string]FxDetailsData, error) {
	data, err := QueryOrderDetailsByOrderNos(orderNos)
	if err != nil {
		return nil, err
	}
	results := make(map[string]FxDetailsData)
	for _, item := range data {
		results[item.OrderId] = item
	}
	return results, nil
}

func handleResponseData(fxDetailsResponse []FxDetailsResponse) ([]FxDetailsData, error) {
	var results []FxDetailsData
	for _, v := range fxDetailsResponse {
		var common FxDetailsCommon
		err := utils.JsonConvertor(v, &common)
		if err != nil {
			return nil, err
		}
		result := FxDetailsData{
			FxDetailsCommon: common,
		}
		//扩展信息json解析
		if v.ExtContent != "" {
			var extContent FxDetailsExtContent
			jsonErr := json.Unmarshal([]byte(v.ExtContent), &extContent)
			if jsonErr != nil {
				continue
			}
			result.ExtContent = extContent
		}
		results = append(results, result)
	}
	return results, nil
}
