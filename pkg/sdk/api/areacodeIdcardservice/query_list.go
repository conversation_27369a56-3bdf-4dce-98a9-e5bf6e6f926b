package areacodeIdcardservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type ResponseData struct {
	PageNum  int                    `json:"pageNum"`
	PageSize int                    `json:"pageSize"`
	Pages    int                    `json:"pages"`
	Total    int                    `json:"total"`
	List     []ResponseDataListItem `json:"list"`
}

type ResponseDataListItem struct {
	AreaCode     int    `json:"areaCode"`
	AreaFullName string `json:"areaFullName"`
	AreaName     string `json:"areaName"`
	AreaState    int    `json:"areaState"`
	AreaType     int    `json:"areaType"`
	CreateTime   int    `json:"createTime"`
	CreateUserId int    `json:"createUserId"`
	Id           int    `json:"id"`
	ParentCode   int    `json:"parentCode"`
	Remark       string `json:"remark"`
	UpdateDname  string `json:"updateDname"`
	UpdateTime   int    `json:"updateTime"`
	UpdateUserId int    `json:"updateUserId"`
	IdCardItems  []struct {
		Id     int `json:"id"`
		IdCard int `json:"idCard"`
	} `json:"idCardItems"`
}

func QueryList(areaName string, areaType int, parentCode int, areaCode int, areaCodeList []int, areaState int, pageNum int, pageSize int) (*ResponseData, error) {
	javaParams := make(map[string]interface{})
	if areaName != "" {
		javaParams["areaName"] = areaName
	}
	if areaType != 0 {
		javaParams["areaType"] = areaType
	}
	if parentCode > 0 {
		javaParams["parentCode"] = parentCode
	}
	if areaCode > 0 {
		javaParams["areaCode"] = areaCode
	}
	if len(areaCodeList) > 0 {
		javaParams["areaCodeList"] = areaCodeList
	}
	if areaState != 0 {
		javaParams["areaState"] = areaState
	}
	if pageNum > 0 {
		javaParams["page"] = pageNum
	}
	if pageSize > 0 {
		javaParams["size"] = pageSize
	}
	data, err := java.Post("/web/v1/server/areaCodeIdCardService/find", javaParams)
	if err != nil {
		return nil, err
	}
	var result *ResponseData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
