package areacodeIdcardservice

import (
	"github.com/spf13/cast"
	"report-service/pkg/utils"
)

func QueryListByAreaCodes(areaCodes []int, params ...interface{}) (map[int]string, error) {
	areaCodes = utils.RemoveDuplicate(areaCodes)
	areaState := 86
	if len(params) > 0 {
		areaState = cast.ToInt(params[0])
	}
	pageSize := 100
	if len(params) > 1 {
		pageSize = cast.ToInt(params[1])
	}
	chunks := utils.ChunkSlice(areaCodes, pageSize)
	result := make(map[int]string, len(areaCodes))
	for _, chunk := range chunks {
		responseData, err := QueryList("", 0, 0, 0, chunk, areaState, 1, pageSize)
		if err != nil {
			return nil, err
		}
		if len(responseData.List) == 0 {
			continue
		}
		for _, item := range responseData.List {
			result[item.AreaCode] = item.AreaName
		}
	}
	return result, nil
}
