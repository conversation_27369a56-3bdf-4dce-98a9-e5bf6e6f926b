package tag

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type QueryTagPaginateParams struct {
	Code      string `json:"code,omitempty"`   //标签（精确查询）
	EqName    string `json:"eqName,omitempty"` //标签名称（精确匹配）
	GroupCode string `json:"groupCode"`        //标签组标识
	Name      string `json:"name,omitempty"`   // 标签名称（模糊匹配）
	PageNum   int    `json:"pageNum"`          // 页数
	PageSize  int    `json:"pageSize"`         // 每页数量
	Sids      []int  `json:"sids,omitempty"`   // 供应商id
	Status    int    `json:"status,omitempty"` // 状态
	Type      int    `json:"type,omitempty"`   // 标签类型 1系统标签 2自定义标签
}

type ResponseTagPaginateCommon struct {
	Code      string      `json:"code"`
	Extra     interface{} `json:"extra"`
	Message   string      `json:"message"`
	Status    int         `json:"status"`
	Success   bool        `json:"success"`
	Timestamp int         `json:"timestamp"`
	Total     int         `json:"total"`
}

type ResponseTagPaginateData struct {
	ResponseTagPaginateCommon
	Rows []ResponseTagInfoItem `json:"rows"`
}

type ResponseTagInfoItem struct {
	Code        string `json:"code"`        // 标签（精确查询）
	CreateAt    string `json:"createAt"`    // 创建时间
	CreateBy    int    `json:"createBy"`    // 创建者
	Description string `json:"description"` // 备注
	GroupCode   string `json:"groupCode"`   // 标签组标识
	Id          string `json:"id"`          // ID
	Name        string `json:"name"`        // 名称
	OwnerId     int    `json:"ownerId"`     // 拥有者id
	OwnerName   string `json:"ownerName"`   // 拥有者名称
	Sid         int    `json:"sid"`         // 供应商id
	Status      int    `json:"status"`      // 状态：1：启用，0：禁用
	UpdateAt    string `json:"updateAt"`    // 更新时间
	UpdateBy    int    `json:"updateBy"`    // 修改者
	Version     string `json:"version"`     // 版本
}

func TagPaginate(params QueryTagPaginateParams) ([]ResponseTagInfoItem, error) {
	response, err := java.OriginalPost("/tag-center/tag/page", params)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}
	var res ResponseTagPaginateData
	err = utils.JsonConvertor(response, &res)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}

	return res.Rows, nil
}

func TagPaginateNameAndCode(params QueryTagPaginateParams) (map[string]string, error) {
	tagNameMap := make(map[string]string)
	response, err := TagPaginate(params)
	if err != nil {
		return nil, err
	}
	for _, item := range response {
		tagNameMap[item.Code] = item.Name
	}
	return tagNameMap, nil
}
