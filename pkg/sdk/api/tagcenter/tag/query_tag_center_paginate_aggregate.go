package tag

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type QueryTagPaginateAggregateParams struct {
	Codes                 []string               `json:"codes,omitempty"`                 //标签（精确查询）
	DomainIds             []string               `json:"domainIds,omitempty"`             // 标签组域值
	Domains               []string               `json:"domains,omitempty"`               // 标签组域
	ExtensionDataMatchers []ExtensionDataMatcher `json:"extensionDataMatchers,omitempty"` //扩展属性匹配器(暂时不支持超过3条)，以及目前只支持EQ
	GroupCodes            []string               `json:"groupCodes,omitempty"`            // 标签组标识
	GroupName             string                 `json:"groupName,omitempty"`             // 标签组名称（模糊匹配）
	GroupStatus           int                    `json:"groupStatus,omitempty"`           // 标签组状态：1：启用，0：禁用
	Name                  string                 `json:"name,omitempty"`                  // 标签名称（模糊匹配）
	PageNum               int                    `json:"pageNum"`                         // 页数
	PageSize              int                    `json:"pageSize"`                        // 每页数量
	Status                int                    `json:"status,omitempty"`                //  标签状态
	TaggedMultiple        bool                   `json:"taggedMultiple,omitempty"`        // 标签组多标签
}

type ExtensionDataMatcher struct {
	Property string      `json:"property"` // 属性字段
	Type     string      `json:"type"`     //  查询类型
	Value    interface{} `json:"value"`    // 查询值
}

type ResponseTagPaginateAggregateData struct {
	ResponseTagPaginateCommon
	Rows []ResponseTagAggregateItem `json:"rows"`
}

type ResponseTagAggregateItem struct {
	Code        string           `json:"code"`
	CreateAt    string           `json:"createAt"`
	CreateBy    int              `json:"createBy"`
	Description string           `json:"description"`
	GroupCode   string           `json:"groupCode"`
	Id          string           `json:"id"`
	Name        string           `json:"name"`
	OwnerId     int              `json:"ownerId"`
	OwnerName   string           `json:"ownerName"`
	Sid         int              `json:"sid"`
	Status      int              `json:"status"`
	TagGroup    ResponseTagGroup `json:"tagGroup"`
	UpdateAt    string           `json:"updateAt"`
	UpdateBy    int              `json:"updateBy"`
	Version     string           `json:"version"`
}

type ResponseTagGroup struct {
	Code          string      `json:"code"`
	CreateAt      string      `json:"createAt"`
	CreateBy      int         `json:"createBy"`
	Description   string      `json:"description"`
	Domain        string      `json:"domain"`
	DomainId      string      `json:"domainId"`
	ExtensionData interface{} `json:"extensionData"`
	Id            string      `json:"id"`
	Name          string      `json:"name"`
	OwnerId       int         `json:"ownerId"`
	OwnerName     string      `json:"ownerName"`
	Sid           int         `json:"sid"`
	Status        int         `json:"status"`
	UpdateAt      string      `json:"updateAt"`
	UpdateBy      int         `json:"updateBy"`
}

func TagPaginateAggregate(params QueryTagPaginateAggregateParams) ([]ResponseTagAggregateItem, error) {
	response, err := java.OriginalPost("/tag-center/tag/page/aggregate", params)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}
	var res ResponseTagPaginateAggregateData
	err = utils.JsonConvertor(response, &res)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}

	return res.Rows, nil
}

func TagPaginateAggregateCodeAndNameMap(params QueryTagPaginateAggregateParams) (map[string]string, error) {
	codeAndNameMap := make(map[string]string)
	res, err := TagPaginateAggregate(params)
	if err != nil {
		return nil, err
	}
	for _, item := range res {
		codeAndNameMap[item.Code] = item.Name
	}
	return codeAndNameMap, nil
}
