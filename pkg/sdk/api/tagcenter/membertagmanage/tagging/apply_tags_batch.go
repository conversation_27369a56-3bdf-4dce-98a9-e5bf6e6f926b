package tagging

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggroup"
	"report-service/pkg/utils"
)

// TaggingApplyTagsRequestVO corresponds to the OpenAPI schema TaggingApplyTagsRequestVO
type TaggingApplyTagsRequestVO struct {
	// 主体
	Subject string `json:"subject"`
	// 主体ID
	SubjectID string `json:"subjectId"`
	// 主体名称
	SubjectName string `json:"subjectName"`
	// 要应用的标签列表
	Tags []ApplyTag `json:"tags"`
}

type ApplyTag struct {
	// 标识
	Code string `json:"code"`
	// 过程数据
	Data *MapObject `json:"data,omitempty"`
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 版本
	Version string `json:"version"`
}

// 过程数据
type MapObject struct {
	Key map[string]interface{} `json:"key,omitempty"`
}

// TaggingApplyTagsBatch calls the POST /feign-client/app/member-tag-manage/tagging/apply/tags/batch endpoint
func TaggingApplyTagsBatch(req []TaggingApplyTagsRequestVO) (*taggroup.ResultBooleanVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tagging/apply/tags/batch", req)
	if err != nil {
		return nil, err
	}

	var resVO taggroup.ResultBooleanVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}
