package tagging

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggroup"
	"report-service/pkg/utils"
)

// TaggingRemoveTagsRequestVO corresponds to the OpenAPI schema TaggingRemoveTagsRequestVO
type TaggingRemoveTagsRequestVO struct {
	// 主体
	Subject string `json:"subject"`
	// 主体ID
	SubjectID string `json:"subjectId"`
	// 要移除的标签列表
	Tags []RemoveTag `json:"tags"`
}

// RemoveTag corresponds to the OpenAPI schema RemoveTag
type RemoveTag struct {
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 标识
	Code string `json:"code"`
}

// TaggingRemoveTag calls the POST /feign-client/app/member-tag-manage/tagging/remove-tag endpoint
func TaggingRemoveTag(req *TaggingRemoveTagsRequestVO) (*taggroup.ResultBooleanVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tagging/remove-tag", req)
	if err != nil {
		return nil, err
	}

	var resVO taggroup.ResultBooleanVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}