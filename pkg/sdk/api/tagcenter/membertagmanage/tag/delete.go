package tag

import (
	"fmt"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggroup" // Import taggroup package
	"report-service/pkg/utils"
)

// TagDelete calls the POST /feign-client/app/member-tag-manage/tag/delete/{groupCode} endpoint
// reqBody is a slice of tag codes (strings) to delete.
func TagDelete(groupCode string, reqBody []string) (*taggroup.ResultBooleanVO, error) { // Qualify the type
	// Construct the dynamic path
	path := fmt.Sprintf("/app-nest-core/feign-client/app/member-tag-manage/tag/delete/%s", groupCode)

	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", path, reqBody)
	if err != nil {
		return nil, err
	}

	// Reuse ResultBooleanVO from taggroup/delete.go
	var resVO taggroup.ResultBooleanVO // Qualify the type
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}