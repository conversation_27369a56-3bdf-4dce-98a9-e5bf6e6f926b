package tag

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TagUpdateRequestVO corresponds to the OpenAPI schema TagUpdateRequestVO
type TagUpdateRequestVO struct {
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 标签标识 (长度不超过200)
	Code string `json:"code"`
	// 名称 (不传递时，不进行更新, maxLength: 30, pattern: ^[\u4E00-\u9FA5a-zA-Z0-9\s]*$)
	Name *string `json:"name,omitempty"`
	// 状态 (不传递时，不进行更新)
	Status *int `json:"status,omitempty"`
	// 备注
	Description *string `json:"description,omitempty"`
	// 商家ID (Assuming MemberID is needed based on other requests, though not explicitly in this spec's requestBody)
	// MemberID int64 `json:"memberId"` // Add if required by the actual API implementation
}

// TagUpdate calls the POST /feign-client/app/member-tag-manage/tag/update endpoint
func TagUpdate(req *TagUpdateRequestVO) (*ResultTagResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag/update", req)
	if err != nil {
		return nil, err
	}

	// Reuse ResultTagResponseVO from create.go
	var resVO ResultTagResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}