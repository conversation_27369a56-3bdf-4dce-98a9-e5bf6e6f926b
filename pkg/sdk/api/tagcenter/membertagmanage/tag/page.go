package tag

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TagPageRequestVO corresponds to the OpenAPI schema TagPageRequestVO
type TagPageRequestVO struct {
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 名称（模糊匹配）
	Name *string `json:"name,omitempty"`
	// 标签（精确查询）
	Code *string `json:"code,omitempty"`
	// 状态
	Status *int `json:"status,omitempty"`
	// 页数 从1开始
	PageNum int `json:"pageNum"`
	// 每页数量
	PageSize int `json:"pageSize"`
	// 供应商id列表
	Sids []int64 `json:"sids,omitempty"` // Assuming int64 for IDs
	// 商家ID
	MemberId int64 `json:"memberId"`
	// 标签类型 1系统标签 2自定义标签
	Type *int `json:"type,omitempty"`
	// 名称（精确匹配）
	EqName *string `json:"eqName,omitempty"`
	// 商家ID (Assuming MemberID is needed based on other requests, though not explicitly in this spec's requestBody)
	// MemberID int64 `json:"memberId"` // Add if required by the actual API implementation
}

// PageResultTagResponseVO corresponds to the OpenAPI schema PageResultTagResponseVO
type PageResultTagResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据 (Uses TagResponseVO from create.go)
	Rows []TagResponseVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
	// 总数
	Total *int64 `json:"total,omitempty"`
}

// TagPage calls the POST /feign-client/app/member-tag-manage/tag/page endpoint
func TagPage(req *TagPageRequestVO) (*PageResultTagResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag/page", req)
	if err != nil {
		return nil, err
	}

	var resVO PageResultTagResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}