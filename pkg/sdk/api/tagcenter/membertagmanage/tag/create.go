package tag

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// SceneTagCreateRequestVO corresponds to the OpenAPI schema SceneTagCreateRequestVO
type SceneTagCreateRequestVO struct {
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 名称 (maxLength: 30, pattern: ^[\u4E00-\u9FA5a-zA-Z0-9\s]*$)
	Name string `json:"name"`
	// 状态 (默认禁用)
	Status *int `json:"status,omitempty"`
	// 备注
	Description *string `json:"description,omitempty"`
	// 商家
	MemberID int64 `json:"memberId"`
}

// ResultTagResponseVO corresponds to the OpenAPI schema ResultTagResponseVO
type ResultTagResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据
	Rows []TagResponseVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
}

// TagResponseVO corresponds to the OpenAPI schema TagResponseVO
type TagResponseVO struct {
	// ID
	ID *string `json:"id,omitempty"`
	// 创建者
	CreateBy *int64 `json:"createBy,omitempty"`
	// 修改者
	UpdateBy *int64 `json:"updateBy,omitempty"`
	// 创建时间
	CreateAt *string `json:"createAt,omitempty"`
	// 更新时间
	UpdateAt *string `json:"updateAt,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 标识
	Code *string `json:"code,omitempty"`
	// 标签组标识
	GroupCode *string `json:"groupCode,omitempty"`
	// 备注
	Description *string `json:"description,omitempty"`
	// 状态：1：启用，0：禁用
	Status *int `json:"status,omitempty"`
	// 版本
	Version *string `json:"version,omitempty"`
	// 供应商id
	Sid *int64 `json:"sid,omitempty"`
	// 拥有者id
	OwnerID *int64 `json:"ownerId,omitempty"`
	// 拥有者名称
	OwnerName *string `json:"ownerName,omitempty"`
}

// TagCreate calls the POST /feign-client/app/member-tag-manage/tag/create endpoint
func TagCreate(req *SceneTagCreateRequestVO) (*ResultTagResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag/create", req)
	if err != nil {
		return nil, err
	}

	var resVO ResultTagResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}