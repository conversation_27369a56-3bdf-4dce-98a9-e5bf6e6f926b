package tag

import (
	"net/url"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/sdk/api/tagcenter/membertagmanage/taggroup"
	"report-service/pkg/utils"
)

// ResultTagDetailResponseVO corresponds to the OpenAPI schema ResultTagDetailResponseVO
type ResultTagDetailResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据
	Rows []TagDetailResponseVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
}

// TagDetailResponseVO corresponds to the OpenAPI schema TagDetailResponseVO
type TagDetailResponseVO struct {
	// ID
	ID *string `json:"id,omitempty"`
	// 创建者
	CreateBy *int64 `json:"createBy,omitempty"`
	// 修改者
	UpdateBy *int64 `json:"updateBy,omitempty"`
	// 创建时间
	CreateAt *string `json:"createAt,omitempty"`
	// 更新时间
	UpdateAt *string `json:"updateAt,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 标识
	Code *string `json:"code,omitempty"`
	// 标签组标识
	GroupCode *string `json:"groupCode,omitempty"`
	// 备注
	Description *string `json:"description,omitempty"`
	// 状态：1：启用，0：禁用
	Status *int `json:"status,omitempty"`
	// 版本
	Version *string `json:"version,omitempty"`
	// 供应商id
	Sid *int64 `json:"sid,omitempty"`
	// 拥有者id
	OwnerID *int64 `json:"ownerId,omitempty"`
	// 拥有者名称
	OwnerName *string `json:"ownerName,omitempty"`
	// 标签组
	TagGroup *taggroup.TagGroupResponseVO `json:"tagGroup,omitempty"`
}

// TagDetail calls the GET /feign-client/app/member-tag-manage/tag/detail endpoint
func TagDetail(groupCode string, code string) (*ResultTagDetailResponseVO, error) {
	// Construct the URL with query parameters
	u, err := url.Parse("/app-nest-core/feign-client/app/member-tag-manage/tag/detail")
	if err != nil {
		return nil, err
	}
	q := u.Query()
	q.Set("groupCode", groupCode)
	q.Set("code", code)
	u.RawQuery = q.Encode()

	// Use the java.OriginalRequest function with GET method
	res, err := java.OriginalRequest("GET", u.String(), nil)
	if err != nil {
		return nil, err
	}

	var resVO ResultTagDetailResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}