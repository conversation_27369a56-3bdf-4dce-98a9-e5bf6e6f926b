package taggroup

import (
	"errors"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// MemberTagGroupCreateRequest 创建标签组请求
type MemberTagGroupCreateRequest struct {
	// 名称
	Name string `json:"name"`
	// 分组状态，默认启用
	Status *int64 `json:"status,omitempty"`
	// 场景，product:产品,ticket:门票,saleChannel:销售渠道,payMode:支付方式
	Scene string `json:"scene"`
	// 类型，false: 单标签,true: 多标签
	TaggedMultiple bool `json:"taggedMultiple"`
	// 商家
	MemberId int64 `json:"memberId"`
}

type TagGroupResponseVO struct {
	Code      string                   `json:"code"`
	Rows      []TagGroupResponseItemVO `json:"rows"`
	Message   string                   `json:"message"`
	Status    int                      `json:"status"`
	Success   bool                     `json:"success"`
	Timestamp int64                    `json:"timestamp"`
}

type TagGroupResponseItemVO struct {
	// ID
	ID *string `json:"id,omitempty"`
	// 创建者
	CreateBy *int64 `json:"createBy,omitempty"`
	// 修改者
	UpdateBy *int64 `json:"updateBy,omitempty"`
	// 创建时间
	CreateAt *string `json:"createAt,omitempty"`
	// 更新时间
	UpdateAt *string `json:"updateAt,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 标识
	Code *string `json:"code,omitempty"`
	// 类型：1：常量标签，2：动态标签
	Type *int64 `json:"type,omitempty"`
	// 标签组含义
	Description *string `json:"description,omitempty"`
	// 状态：1：启用，0：禁用
	Status *int64 `json:"status,omitempty"`
	// 域
	Domain *string `json:"domain,omitempty"`
	// 域ID
	DomainId *string `json:"domainId,omitempty"`
	// 多标签，false: 单标签,true: 多标签
	TaggedMultiple *bool `json:"taggedMultiple,omitempty"`
	// 扩展数据(用来存储分组的业务属性,查询分组时，不支持过滤)
	ExtensionData map[string]interface{} `json:"extensionData,omitempty"`
}

// MemberTagGroupCreate 创建标签组
func MemberTagGroupCreate(req *MemberTagGroupCreateRequest) (*TagGroupResponseVO, error) {
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag-group/create", req)
	if err != nil {
		return nil, err
	}
	var resVO TagGroupResponseVO
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}
	if !resVO.Success {
		return nil, errors.New(resVO.Message)
	}
	return &resVO, nil
}
