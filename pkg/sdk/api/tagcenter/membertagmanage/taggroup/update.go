package taggroup

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TagGroupUpdateRequestVO corresponds to the OpenAPI schema MemberRequest for the update endpoint
type TagGroupUpdateRequestVO struct {
	// 名称 (不传递时，不进行更新)
	Name *string `json:"name,omitempty"`
	// 标识 (长度不超过200)
	Code string `json:"code"`
	// 分组状态 (不传递时，不进行更新)
	Status *int `json:"status,omitempty"`
	// 备注
	Description *string `json:"description,omitempty"`
	// 商家
	MemberID int64 `json:"memberId"`
}

// ResultTagGroupResponseVO corresponds to the OpenAPI schema ResultTagGroupResponseVO
// Note: This structure wraps the TagGroupResponseVO defined in create.go
type ResultTagGroupResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据 (API spec indicates an array, even for update)
	Rows []TagGroupResponseVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
}


// TagGroupUpdate calls the POST /feign-client/app/member-tag-manage/tag-group/update endpoint
func TagGroupUpdate(req *TagGroupUpdateRequestVO) (*ResultTagGroupResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag-group/update", req)
	if err != nil {
		return nil, err
	}

	var resVO ResultTagGroupResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}