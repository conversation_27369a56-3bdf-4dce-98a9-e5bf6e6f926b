package taggroup

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// MemberRequest corresponds to the OpenAPI schema MemberRequest
type MemberRequest struct {
	// 名称（模糊匹配）
	Name *string `json:"name,omitempty"`
	// 标签（精确查询）
	Code *string `json:"code,omitempty"`
	// 状态
	Status *int `json:"status,omitempty"`
	// 页数 从1开始
	PageNum int `json:"pageNum"`
	// 每页数量
	PageSize int `json:"pageSize"`
	// 商家
	MemberID int64 `json:"memberId"` // Changed to int64 assuming it might be large
}

// PageResultTagGroupResponseVO corresponds to the OpenAPI schema PageResultTagGroupResponseVO
type PageResultTagGroupResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据
	Rows []TagGroupPageItemVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
	// 总数
	Total *int64 `json:"total,omitempty"`
}

// TagGroupPageItemVO corresponds to the TagGroupResponseVO schema within PageResultTagGroupResponseVO
// Renamed from TagGroupResponseVO to avoid conflict with tag_group_create.go
type TagGroupPageItemVO struct {
	// ID
	ID *string `json:"id,omitempty"`
	// 创建者
	CreateBy *int64 `json:"createBy,omitempty"`
	// 修改者
	UpdateBy *int64 `json:"updateBy,omitempty"`
	// 创建时间
	CreateAt *string `json:"createAt,omitempty"`
	// 更新时间
	UpdateAt *string `json:"updateAt,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 标识
	Code *string `json:"code,omitempty"`
	// 类型：1：常量标签，2：动态标签
	Type *int `json:"type,omitempty"`
	// 标签组含义
	Description *string `json:"description,omitempty"`
	// 状态：1：启用，0：禁用
	Status *int `json:"status,omitempty"`
	// 域
	Domain *string `json:"domain,omitempty"`
	// 域ID
	DomainID *string `json:"domainId,omitempty"`
	// 多标签 false: 单标签,true: 多标签
	TaggedMultiple *bool `json:"taggedMultiple,omitempty"` // Spec says required, using pointer for consistency
	// 扩展数据(用来存储分组的业务属性,查询分组时，不支持过滤)
	ExtensionData map[string]interface{} `json:"extensionData,omitempty"`
}

// TagGroupPage calls the POST /feign-client/app/member-tag-manage/tag-group/page endpoint
func TagGroupPage(req *MemberRequest) (*PageResultTagGroupResponseVO, error) {
	// Use the java.OriginalRequest function, assuming it handles the base URL and common logic
	// The path should match the OpenAPI spec
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag-group/page", req)
	if err != nil {
		return nil, err
	}

	var resVO PageResultTagGroupResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}