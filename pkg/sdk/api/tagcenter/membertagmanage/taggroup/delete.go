package taggroup

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TagGroupDeleteRequestVO corresponds to the OpenAPI schema MemberRequest for the delete endpoint
type TagGroupDeleteRequestVO struct {
	// 标签组标识列表
	Codes []string `json:"codes"`
	// 商家
	MemberID int64 `json:"memberId"`
}

// ResultBooleanVO corresponds to the OpenAPI schema ResultBoolean
type ResultBooleanVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据 (API spec indicates an array of booleans)
	Rows []bool `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
}

// TagGroupDelete calls the POST /feign-client/app/member-tag-manage/tag-group/delete endpoint
func TagGroupDelete(req *TagGroupDeleteRequestVO) (*ResultBooleanVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tag-group/delete", req)
	if err != nil {
		return nil, err
	}

	var resVO ResultBooleanVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}