package taggeddata

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TaggedDataByTagRequestVO corresponds to the OpenAPI schema TaggedDataByTagRequestVO
type TaggedDataByTagRequestVO struct {
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 标签标识列表
	Codes []string `json:"codes"`
	// 标签标识列表查询方式;and|or;不区分大小写
	CodeQueryMethod string `json:"codeQueryMethod"`
	// 主体 不指定则不限制类型
	Subject *string `json:"subject,omitempty"`
	// 主体名称（模糊匹配）
	SubjectName *string `json:"subjectName,omitempty"`
	// 标签（精确查询）
	SubjectID *string `json:"subjectId,omitempty"`
	// 是否过滤已删除标签 true：过滤已删除标签;默认true
	FilterDeleted *bool `json:"filterDeleted,omitempty"`
	// 页数 从1开始
	PageNum int `json:"pageNum"`
	// 每页数量
	PageSize int `json:"pageSize"`
	// 供应商id
	Sid int64 `json:"sid"`
	// 标签(正则表达式)
	SubjectIDMatch *string `json:"subjectIdMatch,omitempty"`
}

// PageResultTaggedDataResponseVO corresponds to the OpenAPI schema PageResultTaggedDataResponseVO
type PageResultTaggedDataResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据
	Rows []TaggedDataResponseVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
	// 总数
	Total *int64 `json:"total,omitempty"`
}

// TaggedDataResponseVO corresponds to the OpenAPI schema TaggedDataResponseVO
type TaggedDataResponseVO struct {
	// 打标数据主键
	ID string `json:"id"`
	// 主体
	Subject string `json:"subject"`
	// 主体ID
	SubjectID string `json:"subjectId"`
	// 主体名称
	SubjectName *string `json:"subjectName,omitempty"`
	// 数据源Id
	DataSourceID string `json:"dataSourceId"`
	// 已打标签
	Tags []Tagged `json:"tags"`
	// 创建者
	CreateBy int64 `json:"createBy"`
	// 修改者
	UpdateBy int64 `json:"updateBy"`
	// 创建时间
	CreateAt string `json:"createAt"`
	// 更新时间
	UpdateAt string `json:"updateAt"`
}

// Tagged corresponds to the OpenAPI schema Tagged
type Tagged struct {
	// 标签组编码
	GroupCode string `json:"groupCode"`
	// 标签名称
	TagName string `json:"tagName"`
	// 标签编码
	TagCode string `json:"tagCode"`
	// 版本
	Version string `json:"version"`
	//
	Data map[string]interface{} `json:"data,omitempty"`
	// 是否已删除 true:删除，false：未删除
	Deleted bool `json:"deleted"`
	// 创建者
	CreateBy int64 `json:"createBy"`
	// 修改者
	UpdateBy int64 `json:"updateBy"`
	// 创建时间
	CreateAt string `json:"createAt"`
	// 更新时间
	UpdateAt string `json:"updateAt"`
	// 状态 1启用  0禁用
	Status int `json:"status"`
}

// TaggedDataSubjectTag calls the POST /feign-client/app/member-tag-manage/tagged-data/subject/tag endpoint
func TaggedDataSubjectTag(req *TaggedDataByTagRequestVO) (*PageResultTaggedDataResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tagged-data/subject/tag", req)
	if err != nil {
		return nil, err
	}

	var resVO PageResultTaggedDataResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}
