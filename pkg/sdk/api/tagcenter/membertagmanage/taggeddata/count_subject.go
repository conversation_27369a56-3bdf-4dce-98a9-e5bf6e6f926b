package taggeddata

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// CountSubjectRequestVO corresponds to the OpenAPI schema CountSubjectRequestVO
type CountSubjectRequestVO struct {
	// 标签组标识
	GroupCode string `json:"groupCode"`
	// 标识 长度不超过200
	Codes []string `json:"codes"`
	// 主体
	Subjects []string `json:"subjects,omitempty"`
}

// ResultMapLong corresponds to the OpenAPI schema ResultMapLong
type ResultMapLong struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据
	Rows []MapLong `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
}

// MapLong corresponds to the OpenAPI schema MapLong
type MapLong struct {
	Key int64 `json:"key"`
}

// TaggedDataCountSubject calls the POST /feign-client/app/member-tag-manage/tagged-data/count/subject endpoint
func TaggedDataCountSubject(req *CountSubjectRequestVO) (*ResultMapLong, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tagged-data/count/subject", req)
	if err != nil {
		return nil, err
	}

	var resVO ResultMapLong
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}
