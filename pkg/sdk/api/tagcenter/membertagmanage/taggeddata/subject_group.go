package taggeddata

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TaggedDataByGroupRequestVO corresponds to the OpenAPI schema TaggedDataByGroupRequestVO
type TaggedDataByGroupRequestVO struct {
	// 主体
	Subject string `json:"subject"`
	// 主体ID (maxItems: 100)
	SubjectIDs []string `json:"subjectIds,omitempty"`
	// 标签组编码 (maxItems: 10)
	GroupCodes []string `json:"groupCodes"`
	// 是否过滤已删除标签 true：过滤已删除标签;默认true
	FilterDeleted *bool `json:"filterDeleted,omitempty"`
	// 页数 从1开始
	PageNum int `json:"pageNum"`
	// 每页数量
	PageSize int `json:"pageSize"`
	// 供应商id
	Sid *int64 `json:"sid,omitempty"`
}

// TaggedDataSubjectGroup calls the POST /feign-client/app/member-tag-manage/tagged-data/subject/group endpoint
// Summary: 通过标签组获取主体打标数据
func TaggedDataSubjectGroup(req *TaggedDataByGroupRequestVO) (*PageResultTaggedDataResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tagged-data/subject/group", req)
	if err != nil {
		return nil, err
	}

	// Reuse PageResultTaggedDataResponseVO from subject_tag.go (defined in the same package)
	var resVO PageResultTaggedDataResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}