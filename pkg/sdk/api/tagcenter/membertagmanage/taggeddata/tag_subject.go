package taggeddata

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// TaggedDataBySubjectRequestVO corresponds to the OpenAPI schema TaggedDataBySubjectRequestVO
type TaggedDataBySubjectRequestVO struct {
	// 主体
	Subject string `json:"subject"`
	// 主体ID
	SubjectIDS []string `json:"subjectIds"`
	// 标签组标识 不指定则不限制标签组
	GroupCode *string `json:"groupCode,omitempty"`
	// 是否过滤已删除标签 true：过滤已删除标签;默认true
	FilterDeleted *bool `json:"filterDeleted,omitempty"`
}

// ResultTaggedDataResponseVO corresponds to the OpenAPI schema ResultTaggedDataResponseVO
type ResultTaggedDataResponseVO struct {
	// 状态
	Status *int `json:"status,omitempty"`
	// 代码
	Code *string `json:"code,omitempty"`
	// 消息
	Message *string `json:"message,omitempty"`
	// 返回数据
	Rows []TaggedDataResponseVO `json:"rows,omitempty"`
	// 附加数据
	Extra map[string]interface{} `json:"extra,omitempty"`
	// 服务器时间
	Timestamp *int64 `json:"timestamp,omitempty"`
}

// TaggedDataBySubject calls the POST /feign-client/app/member-tag-manage/tagged-data/tag/subject endpoint
func TaggedDataBySubject(req *TaggedDataBySubjectRequestVO) (*ResultTaggedDataResponseVO, error) {
	// Use the java.OriginalRequest function
	res, err := java.OriginalRequest("POST", "/app-nest-core/feign-client/app/member-tag-manage/tagged-data/tag/subject", req)
	if err != nil {
		return nil, err
	}

	var resVO ResultTaggedDataResponseVO
	// Use utils.JsonConvertor to parse the response
	err = utils.JsonConvertor(res, &resVO)
	if err != nil {
		return nil, err
	}

	return &resVO, nil
}