package orderticketservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

//	{
//	   "code": 200,
//	   "msg": "获取成功",
//	   "data": [
//	       {
//	           "discountAmount": 330,
//	           "discountItems": [
//	               {
//	                   "discountAmount": 260,
//	                   "discountCode": "65",
//	                   "discountDesc": "{\"teamGroupName\":\"zyq\",\"teamGroupId\":1,\"teamPriceTagId\":8,\"price\":35,\"tag\":\"北京优惠\"}",
//	                   "discountType": 4,
//	                   "refundDiscountAmount": 0
//	               }
//	           ],
//	           "orderNum": "69443050615063",
//	           "refundDiscountAmount": 0
//	       }
//	   ]
//	}
type OrderDiscountSummaryData struct {
	DiscountAmount       int                                `json:"discountAmount"`
	DiscountItems        []OrderDiscountSummaryDiscountItem `json:"discountItems"`
	OrderNum             string                             `json:"orderNum"`
	RefundDiscountAmount int                                `json:"refundDiscountAmount"`
}

type OrderDiscountSummaryDiscountItem struct {
	DiscountAmount       int    `json:"discountAmount"`
	DiscountCode         string `json:"discountCode"`
	DiscountDesc         string `json:"discountDesc"`
	DiscountType         int    `json:"discountType"`
	RefundDiscountAmount int    `json:"refundDiscountAmount"`
}

func QueryOrderDiscountSummaryByOrderNos(orderNos []string) ([]OrderDiscountSummaryData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.BatchPost("/web/v1/transaction/orderTicketService/summaryDiscountByOrderNums", map[string]interface{}{
		"orderNums": orderNos,
	}, "orderNums", 100, true)
	if err != nil {
		return nil, err
	}

	var result []OrderDiscountSummaryData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
