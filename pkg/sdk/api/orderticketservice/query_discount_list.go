package orderticketservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

//{
//    "code": 200,
//    "msg": "",
//    "data": [
//        {
//            "couponPrice": 65,
//            "couponType": 4,
//            "couponValue": "65",
//            "description": "{\"teamGroupName\":\"zyq\",\"teamGroupId\":1,\"teamPriceTagId\":8,\"price\":35,\"tag\":\"北京优惠\"}",
//            "orderNum": "69443050615063",
//            "payPrice": 35,
//            "serialNum": 9,
//            "status": 1
//        }
//    ]
//}

type OrderDiscountInfoData struct {
	CouponPrice int    `json:"couponPrice"`
	CouponType  int    `json:"couponType"`
	CouponValue string `json:"couponValue"`
	Description string `json:"description"`
	OrderNum    string `json:"orderNum"`
	PayPrice    int    `json:"payPrice"`
	SerialNum   int    `json:"serialNum"`
	Status      int    `json:"status"`
}

func QueryOrderDiscountList(orderNo string, status int, serialNums []int) ([]OrderDiscountInfoData, error) {
	data, err := java.Post("/web/v1/transaction/orderTicketService/list", map[string]interface{}{
		"orderNum":   orderNo,
		"status":     status,
		"serialNums": serialNums,
	})
	if err != nil {
		return nil, err
	}

	var result []OrderDiscountInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
