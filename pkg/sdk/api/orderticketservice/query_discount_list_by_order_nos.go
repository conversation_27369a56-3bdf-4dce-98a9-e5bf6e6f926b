package orderticketservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

//{
//    "code": 200,
//    "msg": "获取成功",
//    "data": [
//        {
//            "couponPrice": 0,
//            "couponType": 0,
//            "couponValue": "",
//            "description": "{\"tag\":\"原价\",\"price\":100,\"teamPriceTagId\":null}",
//            "orderNum": "69443050615063",
//            "payPrice": 100,
//            "serialNum": 1,
//            "status": 2
//        }
//    ]
//}

func QueryOrderDiscountInfoByOrderNos(orderNos []string) ([]OrderDiscountInfoData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.BatchPost("/web/v1/transaction/orderTicketService/listByOrderNums", map[string]interface{}{
		"orderNums": orderNos,
	}, "orderNums", 100, true)
	if err != nil {
		return nil, err
	}

	var result []OrderDiscountInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
