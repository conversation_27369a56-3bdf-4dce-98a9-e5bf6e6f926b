package api

var (
	GateWayUri           string
	BaseUri              string
	DownloadCenter       DownloadCenterConfig
	PlatformApi          PlatformApiConfig
	OpenCooperateEcology OpenCooperateEcologyConfig
	DownloadCenterApi    DownloadCenterApiConfig
)

type DownloadCenterConfig struct {
	BaseUri string
	AuthKey string
	System  string
}

type PlatformApiConfig struct {
	RpcApi PlatformApiConfigRpcApi
}

type PlatformApiConfigRpcApi struct {
	Uri     string
	AuthKey string
	System  string
}

type OpenCooperateEcologyConfig struct {
	Addr string
}

type DownloadCenterApiConfig struct {
	BaseUri string
}
