package bizregionarea

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type ResponsePaginate struct {
	Code    string               `json:"code"`
	Message string               `json:"message"`
	Data    ResponsePaginateData `json:"data"`
}

type ResponseInfoList struct {
	Code    string             `json:"code"`
	Message string             `json:"message"`
	Data    []ResponseDataItem `json:"data"`
}

type ResponsePaginateData struct {
	Total int                `json:"total"`
	Rows  []ResponseDataItem `json:"rows"`
}

type ResponseDataItem struct {
	AreaCode   string              `json:"areaCode"`   // 行政区代码
	AreaName   string              `json:"areaName"`   // 中文名称
	Children   []*ResponseDataItem `json:"children"`   // 下级区域
	CityCode   string              `json:"cityCode"`   // 区号
	CountryId  int                 `json:"countryId"`  // 国家ID
	JoinName   string              `json:"joinName"`   // 级联名称
	Lat        float64             `json:"lat"`        // 纬度
	Level      int                 `json:"level"`      // 区域级别
	Lng        float64             `json:"lng"`        // 经度
	ParentCode string              `json:"parentCode"` // 上级编码
	Remark     string              `json:"remark"`     // 备注
	ShortName  string              `json:"shortName"`  // 简称
	ZipCode    int                 `json:"zipCode"`    // 邮政编码
}

func QueryRegionAreaPaginate(areaName string, countryId, level, pageNum, pageSize int) (*ResponsePaginateData, error) {
	params := make(map[string]interface{})
	if areaName != "" {
		params["areaName"] = areaName
	}
	if countryId != 0 {
		params["countryId"] = countryId
	}
	if level != 0 {
		params["level"] = level
	}
	params["page"] = 1
	if pageNum != 0 {
		params["page"] = pageNum
	}
	params["size"] = 10
	if pageSize != 0 {
		params["size"] = pageSize
	}
	response, err := java.OriginalGet("/base-server/biz/region-area/page", params)
	if err != nil {
		return nil, err
	}
	var result ResponsePaginate
	err = utils.JsonConvertor(response, &result)
	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

func BatchRegionAreaInfo(areaCodes []string) ([]ResponseDataItem, error) {
	areaCodes = utils.RemoveDuplicate(areaCodes)
	response, err := java.OriginalPost("/base-server/biz/region-area/batch-info", areaCodes)
	if err != nil {
		return nil, err
	}
	var result ResponseInfoList
	err = utils.JsonConvertor(response, &result)
	if err != nil {
		return nil, err
	}

	return result.Data, nil
}
