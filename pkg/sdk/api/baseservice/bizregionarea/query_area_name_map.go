package bizregionarea

import (
	"report-service/pkg/utils"
)

func QueryAreaNameMapByAreaCode(areaCodes []string) (map[string]string, error) {
	areaCodes = utils.RemoveDuplicate(areaCodes)
	pageSize := 100
	chunks := utils.ChunkSlice(areaCodes, pageSize)
	result := make(map[string]string, len(areaCodes))
	for _, chunk := range chunks {
		responseData, err := BatchRegionAreaInfo(chunk)
		if err != nil {
			return nil, err
		}
		if len(responseData) == 0 {
			continue
		}
		for _, item := range responseData {
			result[item.AreaCode] = item.AreaName
		}
	}
	return result, nil
}
