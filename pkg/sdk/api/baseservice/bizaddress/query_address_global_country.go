package bizaddress

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type ResponseCountryInfoList struct {
	Code    string                        `json:"code"`
	Message string                        `json:"message"`
	Data    []ResponseDataCountryInfoItem `json:"data"`
}

type ResponseDataCountryInfoItem struct {
	Alpha2Code   string `json:"alpha2Code"`   // ISO 3166-1标准中的二位字母代码
	Alpha3Code   string `json:"alpha3Code"`   // ISO 3166-1标准中的三位字母代码
	Id           int    `json:"id"`           // 主键ID
	NameEn       string `json:"nameEn"`       // 英文名称
	NameZh       string `json:"nameZh"`       // 中文名称
	Numeric3Code string `json:"numeric3Code"` // ISO 3166-1标准中的三位数字代码
}

func BatchRegionAreaInfo(countryId []string) ([]ResponseDataCountryInfoItem, error) {
	countryId = utils.RemoveDuplicate(countryId)
	response, err := java.OriginalPost("/base-server/biz/address/global-country/batch-info", countryId)
	if err != nil {
		return nil, err
	}
	var result ResponseCountryInfoList
	err = utils.JsonConvertor(response, &result)
	if err != nil {
		return nil, err
	}

	return result.Data, nil
}

func QueryAddressGlobalCountryList() ([]ResponseDataCountryInfoItem, error) {
	response, err := java.OriginalGet("/base-server/biz/address/global-country/list", nil)
	if err != nil {
		return nil, err
	}
	var result ResponseCountryInfoList
	err = utils.JsonConvertor(response, &result)
	if err != nil {
		return nil, err
	}

	return result.Data, nil
}
