package bizaddress

import (
	"github.com/spf13/cast"
	"report-service/pkg/utils"
)

func QueryCountryNameMapByCountryCode(countryId []string) (map[string]string, error) {
	countryId = utils.RemoveDuplicate(countryId)
	pageSize := 100
	chunks := utils.ChunkSlice(countryId, pageSize)
	result := make(map[string]string, len(countryId))
	for _, chunk := range chunks {
		responseData, err := BatchRegionAreaInfo(chunk)
		if err != nil {
			return nil, err
		}
		if len(responseData) == 0 {
			continue
		}
		for _, item := range responseData {
			result[cast.ToString(item.Id)] = item.NameZh
		}
	}
	return result, nil
}
