package ordercouponservice

import (
	"errors"
	"reflect"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
	"strings"
)

//{
//    "code": 200,
//    "msg": "success",
//    "data": [
//        {
//            "aid": 10221184,
//            "emoney": 10,
//            "fid": 3267451,
//            "id": 11906,
//            "lid": 347732,
//            "nmoney": 990,
//            "ordernum": "70971603714885",
//            "pid": 399593,
//            "pmoney": 1000,
//            "rectime": 1709716038,
//            "tid": 110035973
//        }
//    ],
//    "inside_error": ""
//}

type OrderCouponData struct {
	Aid      int    `json:"aid"`
	Fid      int    `json:"fid"`
	Id       int    `json:"id"`
	Spu      int    `json:"lid"`
	Sku      int    `json:"tid"`
	EMoney   int    `json:"emoney"`
	NMoney   int    `json:"nmoney"`
	PMoney   int    `json:"pmoney"`
	OrderNum string `json:"ordernum"`
	Pid      int    `json:"pid"`
	RecTime  int    `json:"rectime"`
}

func QueryOrderCouponByOrderNo(orderNo string) (interface{}, error) {
	data, err := java.Post("/web/v1/transaction/orderCouponService/queryByOrderNum", map[string]interface{}{
		"orderNum": orderNo,
	})
	if err != nil {
		return nil, err
	}

	var result []OrderCouponData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	if len(result) > 0 {
		return result[0], nil
	}

	return nil, nil
}

func QueryOrderCouponByOrderNos(orderNos []string) ([]OrderCouponData, error) {
	//按订单号分批处理，这里要注意的是，多个订单号需要逗号隔开
	chunkSize := 50 // 每个子切片的元素个数
	numChunks := len(orderNos) / chunkSize
	if len(orderNos)%chunkSize != 0 {
		numChunks += 1
	}
	orderNoSplit := make([]string, 0, numChunks)

	// 遍历原始数据并处理
	for i := 0; i < len(orderNos); i += chunkSize {
		// 计算当前子切片的结束索引，避免越界
		end := i + chunkSize
		if end > len(orderNos) {
			end = len(orderNos)
		}
		// 将子切片中的元素用逗号连接成一个字符串
		chunk := strings.Join(orderNos[i:end], ",")
		orderNoSplit = append(orderNoSplit, chunk)
	}

	var resultData []interface{}
	for _, splitItem := range orderNoSplit {
		data, err := java.BatchPost("/web/v1/transaction/orderCouponService/queryByOrderNum", map[string]interface{}{
			"orderNum": splitItem,
		}, "orderNum", 1, true)
		if err != nil {
			return nil, err
		}

		resultData, err = mergeSlices(resultData, data)
		if err != nil {
			return nil, err
		}
	}

	var result []OrderCouponData
	err := utils.JsonConvertor(resultData, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func mergeSlices(a, b interface{}) ([]interface{}, error) {
	av := reflect.ValueOf(a)
	bv := reflect.ValueOf(b)
	if av.Kind() != reflect.Slice || bv.Kind() != reflect.Slice {
		return nil, errors.New("both arguments must be slices")
	}
	mergedSlice := make([]interface{}, 0, av.Len()+bv.Len())
	for i := 0; i < av.Len(); i++ {
		mergedSlice = append(mergedSlice, av.Index(i).Interface())
	}
	for i := 0; i < bv.Len(); i++ {
		mergedSlice = append(mergedSlice, bv.Index(i).Interface())
	}
	return mergedSlice, nil
}
