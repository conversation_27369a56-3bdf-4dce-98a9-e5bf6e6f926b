package uussorderservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type OrderInfoData struct {
	Aid           int    `json:"aid"`
	ApplyDid      int    `json:"applyDid"`
	BeginTime     int    `json:"begintime"`
	EndTime       int    `json:"endtime"`
	Code          int    `json:"code"`
	ContactTel    string `json:"contacttel"`
	Id            int    `json:"id"`
	SpuId         int    `json:"lid"`
	Member        int    `json:"member"`
	OrderTimeDate int    `json:"ordertime"`
	OrderTime     int    `json:"orderTime"`
	OrderMode     int    `json:"ordermode"`
	OrderName     string `json:"ordername"`
	OrderNum      string `json:"ordernum"`
	OrderTel      string `json:"ordertel"`
	PayStatus     int    `json:"payStatus"`
	PayTime       int    `json:"payTime"`
	PayMode       int    `json:"paymode"`
	PersonId      string `json:"personid"`
	Pid           int    `json:"pid"`
	Playtime      int    `json:"playtime"`
	ProductType   string `json:"productType"`
	RemoteNum     string `json:"remotenum"`
	ReMsg         int    `json:"remsg"`
	SalerId       int    `json:"salerid"`
	Status        int    `json:"status"`
	SkuId         int    `json:"tid"`
	TNum          int    `json:"tnum"`
	TotalMoney    int    `json:"totalmoney"`
	TPrice        int    `json:"tprice"`
	TradeOrderId  string `json:"tradeOrderId"`
}

///web/v1/transaction/uuSsOrderService/queryOrderInfoByOrdernumList 接口不支持历史库订单数据查询

func InBatchesQueryOrderByOrderNos(orderNos []string) ([]OrderInfoData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.BatchPost("/web/v1/transaction/uuSsOrderService/queryOrderInfoByOrdernumList", map[string]interface{}{
		"ordernums": orderNos,
	}, "ordernums", 500, true)
	if err != nil {
		return nil, err
	}
	var result []OrderInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func QueryOrderByOrderNos(orderNos []string) ([]OrderInfoData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.Post("/web/v1/transaction/uuSsOrderService/queryOrderInfoByOrdernumList", map[string]interface{}{
		"ordernums": orderNos,
	})
	if err != nil {
		return nil, err
	}
	var result []OrderInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
