package java

import (
	"errors"
	"fmt"
	"net/url"
	"reflect"
	"report-service/pkg/sdk/api"
	"report-service/pkg/utils"
	"report-service/pkg/utils/httputil"
	"report-service/pkg/utils/snowflake"
	"strconv"
	"strings"
	"time"
)

type ResponseCommonPaginateData struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type ResponseBody struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Msg     string      `json:"msg"`
	SubMsg  string      `json:"sub_msg"`
	Data    interface{} `json:"data"`
}

type RequestBody struct {
	RequestTime   string      `json:"requestTime"`
	TransactionId string      `json:"transactionId"`
	Content       interface{} `json:"content"`
}

func Post(uri string, requestBody interface{}) (data interface{}, err error) {
	return Request("POST", uri, requestBody)
}

func Request(method string, uri string, requestBody interface{}) (data interface{}, err error) {
	traceIdStr, err := getTraceId()
	if err != nil {
		return nil, err
	}
	uri = api.BaseUri + uri
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-TRACE-ID":   traceIdStr,
	}
	requestBody = RequestBody{
		RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
		TransactionId: traceIdStr,
		Content:       requestBody,
	}
	var responseBody ResponseBody
	err = httputil.Request(method, uri, headers, requestBody, &responseBody)
	if err != nil {
		return nil, err
	}
	if responseBody.Code == 0 {
		return data, errors.New("response code is empty")
	}
	if responseBody.Code != 200 {
		return nil, errors.New(returnNotEmptyString(responseBody.Message, responseBody.Msg, responseBody.SubMsg))
	}
	return responseBody.Data, nil
}

func RequestResponseBody(method string, uri string, requestBody interface{}) (data interface{}, err error) {
	traceIdStr, err := getTraceId()
	if err != nil {
		return nil, err
	}
	uri = api.BaseUri + uri
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-TRACE-ID":   traceIdStr,
	}
	requestBody = RequestBody{
		RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
		TransactionId: traceIdStr,
		Content:       requestBody,
	}
	var responseBody ResponseBody
	err = httputil.Request(method, uri, headers, requestBody, &responseBody)
	if err != nil {
		return nil, err
	}
	if responseBody.Code == 0 {
		return data, errors.New("response code is empty")
	}

	return responseBody, nil
}

func getTraceId() (string, error) {
	traceId, err := snowflake.GlobalSnowflake.NextId()
	if err != nil {
		return "", err
	}
	return strconv.Itoa(traceId), nil
}

type SpecialResponseBody struct {
	Status    int         `json:"status"`
	Code      string      `json:"code"`
	Timestamp int         `json:"timestamp"`
	Extra     interface{} `json:"extra"`
	Message   string      `json:"message"`
	Success   bool        `json:"success"`
	Total     int         `json:"total"`
	Rows      interface{} `json:"rows"`
}

func SpecialPost(uri string, requestBody interface{}) (interface{}, int, error) {
	return SpecialRequest("POST", uri, requestBody)
}

func SpecialRequest(method string, uri string, requestBody interface{}) (interface{}, int, error) {
	traceIdStr, err := getTraceId()
	if err != nil {
		return nil, 0, err
	}
	uri = api.GateWayUri + uri
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-TRACE-ID":   traceIdStr,
	}
	var responseBody SpecialResponseBody
	err = httputil.Request(method, uri, headers, requestBody, &responseBody)
	if err != nil {
		return nil, 0, err
	}
	if responseBody.Code != "200" {
		return nil, 0, errors.New(fmt.Sprintf("请求服务失败，错误信息：%s", responseBody.Message))
	}
	return responseBody.Rows, responseBody.Total, nil
}

func OriginalPost(uri string, requestBody interface{}) (data interface{}, err error) {
	return OriginalRequest("POST", uri, requestBody)
}

func OriginalGet(uri string, params map[string]interface{}) (data interface{}, err error) {
	if len(params) > 0 {
		var queryPrams []string
		for k, v := range params {
			var tmp string
			//类型转换
			switch val := v.(type) {
			case string:
				tmp = url.QueryEscape(val)
			case int:
				tmp = strconv.Itoa(val)
			case float64:
				tmp = strconv.FormatFloat(val, 'f', -1, 64)
			case bool:
				tmp = strconv.FormatBool(val)
			default:
				return "", fmt.Errorf("unsupported type: %T", v)
			}
			queryPrams = append(queryPrams, fmt.Sprintf("%s=%s", k, tmp))
		}
		uri = uri + "?" + strings.Join(queryPrams, "&")
	}
	return OriginalRequest("GET", uri, map[string]interface{}{})
}

func OriginalRequest(method string, uri string, requestBody interface{}) (data interface{}, err error) {
	traceIdStr, err := getTraceId()
	if err != nil {
		return "", err
	}
	uri = api.GateWayUri + uri
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-TRACE-ID":   traceIdStr,
	}
	var responseBody interface{}
	err = httputil.RequestClient{
		Method:         method,
		Url:            uri,
		Headers:        headers,
		Body:           requestBody,
		Response:       &responseBody,
		LogRawResponse: true,
	}.Request()
	if err != nil {
		return nil, err
	}
	return responseBody, nil
}

func returnNotEmptyString(strings ...string) string {
	for _, s := range strings {
		if s != "" {
			return s
		}
	}
	return ""
}

func returnNotEmptySlice(sliceList ...[]interface{}) []interface{} {
	for _, s := range sliceList {
		if len(s) > 0 {
			return s
		}
	}
	return []interface{}{}
}

// 分批查询处理
func BatchPost(uri string, requestBody interface{}, splitKey string, limit int, isNeedRetry bool) (interface{}, error) {
	rv := reflect.ValueOf(requestBody)
	//验证是否分批查询，无需分批处理
	if (rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array && rv.Kind() != reflect.Map) || !rv.MapIndex(reflect.ValueOf(splitKey)).IsValid() {
		return PostRetry("POST", uri, requestBody, isNeedRetry)
	}

	//需要分批处理
	splitElem := reflect.ValueOf(rv.MapIndex(reflect.ValueOf(splitKey)).Interface())

	if splitElem.Kind() != reflect.Slice && splitElem.Kind() != reflect.Array && splitElem.Kind() != reflect.Map {
		return PostRetry("POST", uri, requestBody, isNeedRetry)
	}
	if splitElem.Len() <= limit {
		return PostRetry("POST", uri, requestBody, isNeedRetry)
	}

	splitValue := splitElem.Interface()
	splitList, err := chunkSlice(splitValue, limit)
	if err != nil {
		return nil, err
	}

	var resultData []interface{}
	for _, splitItem := range splitList {
		rv.SetMapIndex(reflect.ValueOf(splitKey), reflect.ValueOf(splitItem))
		var result interface{}
		result, err = PostRetry("POST", uri, requestBody, isNeedRetry)
		if err != nil {
			return nil, errors.New("batch query is error")
		}
		resultData, err = mergeSlices(resultData, result)
	}

	return resultData, nil
}

func PostRetry(method string, uri string, requestBody interface{}, isNeedRetry bool) (interface{}, error) {
	var responseBody ResponseBody
	result, err := RequestResponseBody(method, uri, requestBody)
	if err != nil {
		//补全重试机制
		if isNeedRetry {
			result, err = RequestResponseBody(method, uri, requestBody)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	err = utils.JsonConvertor(result, &responseBody)
	if err != nil {
		return nil, err
	}
	if responseBody.Code != 200 && isNeedRetry {
		result, err = RequestResponseBody(method, uri, requestBody)
		if err != nil {
			return nil, err
		}
		err = utils.JsonConvertor(result, &responseBody)
		if err != nil {
			return nil, err
		}
		if responseBody.Code != 200 {
			return nil, errors.New(returnNotEmptyString(responseBody.Message, responseBody.Msg, responseBody.SubMsg))
		}
		return responseBody.Data, nil
	}

	if responseBody.Code != 200 {
		return nil, errors.New(returnNotEmptyString(responseBody.Message, responseBody.Msg, responseBody.SubMsg))
	}

	return responseBody.Data, nil
}

// chunkSlice 将给定的切片按照指定的大小分批
func chunkSlice(slice interface{}, chunkSize int) ([][]interface{}, error) {
	rv := reflect.ValueOf(slice)

	if rv.Kind() != reflect.Slice {
		return nil, errors.New("input is not a slice")
	}
	var chunks [][]interface{}
	for i := 0; i < rv.Len(); i += chunkSize {
		end := i + chunkSize
		if end > rv.Len() {
			end = rv.Len()
		}
		chunk := rv.Slice(i, end).Interface()
		var chunkItem []interface{}
		rv2 := reflect.ValueOf(chunk)
		if rv2.Kind() != reflect.Slice {
			return nil, errors.New("input item is not a slice")
		}
		for s := 0; s < rv2.Len(); s++ {
			chunkItemTmp := rv2.Index(s).Interface()
			chunkItem = append(chunkItem, chunkItemTmp)
		}
		chunks = append(chunks, chunkItem)
	}

	return chunks, nil
}

func mergeSlices(a, b interface{}) ([]interface{}, error) {
	av := reflect.ValueOf(a)
	bv := reflect.ValueOf(b)
	if av.Kind() != reflect.Slice || bv.Kind() != reflect.Slice {
		return nil, errors.New("both arguments must be slices")
	}
	mergedSlice := make([]interface{}, 0, av.Len()+bv.Len())
	for i := 0; i < av.Len(); i++ {
		mergedSlice = append(mergedSlice, av.Index(i).Interface())
	}
	for i := 0; i < bv.Len(); i++ {
		mergedSlice = append(mergedSlice, bv.Index(i).Interface())
	}
	return mergedSlice, nil
}

type NewApiResponse struct {
	Status    int           `json:"status"`
	Code      string        `json:"code"`
	Message   string        `json:"message"`
	Rows      []interface{} `json:"rows"`
	Timestamp int           `json:"timestamp"`
	Success   bool          `json:"success"`
	Data      []interface{} `json:"data"`
}

// NewApiRequest 新中台接口调用优化
func NewApiRequest(uri string, requestBody interface{}, isNeedRetry bool) (data interface{}, err error) {
	var responseBody NewApiResponse
	result, resErr := OriginalPost(uri, requestBody)
	if resErr != nil {
		//补全重试机制
		if isNeedRetry {
			result, resErr = OriginalPost(uri, requestBody)
			if resErr != nil {
				return nil, resErr
			}
		} else {
			return nil, resErr
		}
	}
	err = utils.JsonConvertor(result, &responseBody)
	if err != nil {
		return nil, err
	}
	if responseBody.Code != "200" && isNeedRetry {
		result, err = OriginalPost(uri, requestBody)
		if err != nil {
			return nil, err
		}
		err = utils.JsonConvertor(result, &responseBody)
		if err != nil {
			return nil, err
		}
		if responseBody.Code != "200" {
			return nil, errors.New(returnNotEmptyString(responseBody.Message, "接口错误"))
		}
		return returnNotEmptySlice(responseBody.Rows, responseBody.Data), nil
	}
	if responseBody.Code != "200" {
		return nil, errors.New(returnNotEmptyString(responseBody.Message, "接口错误"))
	}
	return returnNotEmptySlice(responseBody.Rows, responseBody.Data), nil
}
