package landservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

func QueryLandByIds(landIds []int) ([]LandData, error) {
	landIds = utils.RemoveDuplicate(landIds)
	data, err := java.BatchPost("/web/v1/product/landService/queryLandByIds", map[string]interface{}{
		"landIds": landIds,
	}, "landIds", 200, true)
	if err != nil {
		return nil, err
	}
	var result []LandData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
