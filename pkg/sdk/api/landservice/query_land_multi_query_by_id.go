package landservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

func QueryLandMultiQueryById(landIds []int) ([]LandData, error) {
	landIds = utils.RemoveDuplicate(landIds)
	data, err := java.BatchPost("/web/v1/product/landService/queryLandMultiQueryById", map[string]interface{}{
		"landIdList": landIds,
	}, "landIdList", 200, true)
	if err != nil {
		return nil, err
	}

	var result []LandData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
