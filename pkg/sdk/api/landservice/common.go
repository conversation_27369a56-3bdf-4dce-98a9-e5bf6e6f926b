package landservice

type LandData struct {
	Id       int    `json:"id"`
	Title    string `json:"title"`
	Ptype    string `json:"pType"`
	ApplyDid int    `json:"applyDid"`
}

func QueryLandTitleByIds(landIds []int) (map[int]string, error) {
	var result []LandData
	var err error
	result, err = QueryLandByIds(landIds)
	if err != nil {
		return nil, err
	}
	var data = make(map[int]string)
	for _, item := range result {
		data[item.Id] = item.Title
	}
	return data, nil
}
