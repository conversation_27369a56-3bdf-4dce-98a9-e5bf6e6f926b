package evolutegroupservice

// 通过分销商Id获取分组名称
func QueryGroupNameBySidFids(sid int, fids []int) (map[int]string, error) {
	//分销商Id获取分组Id
	groupList, groupErr := QueryGroupDistributorList(sid, fids)
	if groupErr != nil {
		return nil, groupErr
	}
	fidGroupIdMap := make(map[int]int)
	groupIds := []int{}
	for _, group := range groupList {
		//特殊分组时存在ParentGroupId，特殊分组场景去这个字段
		groupId := group.GroupId
		if group.ParentGroupId > 0 {
			groupId = group.ParentGroupId
		}
		fidGroupIdMap[group.Fid] = groupId
		groupIds = append(groupIds, group.GroupId)
	}
	//分组Id获取分组名称
	groupIdNameMap := make(map[int]string)
	if len(groupIds) > 0 {
		groupInfo, groupInfoErr := QueryGroupInfoByGroupIds(groupIds)
		if groupInfoErr != nil {
			return nil, groupInfoErr
		}
		for _, group := range groupInfo {
			groupIdNameMap[group.Id] = group.GroupName
		}
	}
	//分销商Id关联分组名称
	fidGroupNameMap := make(map[int]string)
	for _, fid := range fids {
		fidGroupNameMap[fid] = ""
		if groupId, ok := fidGroupIdMap[fid]; ok {
			if groupName, yes := groupIdNameMap[groupId]; yes {
				fidGroupNameMap[fid] = groupName
			}
		}
	}
	return fidGroupNameMap, nil
}
