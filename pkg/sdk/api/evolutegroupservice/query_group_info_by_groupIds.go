package evolutegroupservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type GroupInfo struct {
	GroupDefault  int    `json:"groupDefault"`
	GroupName     string `json:"groupName"`
	GroupType     int    `json:"groupType"`
	Id            int    `json:"id"`
	OperatorId    int    `json:"operatorId"`
	OrderBy       int    `json:"orderBy"`
	ParentGroupId int    `json:"parentGroupId"`
	RecTime       int    `json:"rectime"`
	Sid           int    `json:"sid"`
}

func QueryGroupInfoByGroupIds(groupIds []int) ([]GroupInfo, error) {
	groupIds = utils.RemoveDuplicate(groupIds)
	data, err := java.BatchPost("/web/v1/product/evoluteGroupService/queryByGroupIds", map[string]interface{}{
		"groupIds": groupIds,
	}, "groupIds", 100, true)
	if err != nil {
		return nil, err
	}
	var results []GroupInfo
	err = utils.JsonConvertor(data, &results)
	if err != nil {
		return nil, err
	}
	return results, nil
}
