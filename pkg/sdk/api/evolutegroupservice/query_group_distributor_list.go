package evolutegroupservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type GroupDistributorData struct {
	Fid           int `json:"fid"`
	GroupId       int `json:"groupId"`
	Id            int `json:"id"`
	OperatorId    int `json:"operatorId"`
	ParentGroupId int `json:"parentGroupId"`
	RecTime       int `json:"rectime"`
	Sid           int `json:"sid"`
}

// QueryGroupDistributorList 批量获取分销商分组信息
func QueryGroupDistributorList(sid int, fids []int) ([]GroupDistributorData, error) {
	fids = utils.RemoveDuplicate(fids)
	data, err := java.BatchPost("/web/v1/product/evoluteGroupFidService/queryGroupDistributorList", map[string]interface{}{
		"sid":  sid,
		"fids": fids,
	}, "fids", 200, true)
	if err != nil {
		return nil, err
	}
	var results []GroupDistributorData
	err = utils.JsonConvertor(data, &results)
	if err != nil {
		return nil, err
	}
	return results, nil
}
