package orderaidssplitqueryservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

//web/v1/orderquery/orderAidsSplitQueryService/getOrderDistributionChain
//{
//    "code": 200,
//    "msg": "success",
//    "data": [
//        {
//            "applyDid": 3385,
//            "buyerid": 3438,
//            "costMoney": 0,
//            "id": 237036337,
//            "level": 0,
//            "memberRelationship": 0,
//            "orderMonth": 9,
//            "orderTime": 1694430507,
//            "orderid": "69443050615063",
//            "pmode": 2,
//            "saleMoney": 100,
//            "sellerid": 3385,
//            "syncState": 1,
//            "updateTime": 1694430509
//        }
//    ],
//    "inside_error": ""
//}

type OrderDistributionChainInfoData struct {
	ApplyDid           int    `json:"apply_did"`
	BuyerId            int    `json:"buyerid"`
	CostMoney          int    `json:"costMoney"`
	Id                 int    `json:"id"`
	Level              int    `json:"level"`
	MemberRelationship int    `json:"memberRelationship"`
	OrderMonth         int    `json:"orderMonth"`
	OrderTime          int    `json:"orderTime"`
	OrderId            string `json:"orderid"`
	PMode              int    `json:"pmode"`
	SaleMoney          int    `json:"saleMoney"`
	SellerId           int    `json:"sellerid"`
}

func QueryOrderDistributionChainByOrderNos(orderNos []string) ([]OrderDistributionChainInfoData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.BatchPost("/web/v1/orderquery/orderAidsSplitQueryService/getOrderDistributionChain", map[string]interface{}{
		"ordernums":          orderNos,
		"memberRelationship": nil,
		"sellerId":           nil,
		"withLevel":          false,
		"desc":               false,
	}, "ordernums", 100, true)
	if err != nil {
		return nil, err
	}

	var result []OrderDistributionChainInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
