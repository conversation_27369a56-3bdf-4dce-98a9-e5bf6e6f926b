package admin

import (
	"github.com/pkg/errors"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

// MemberQueryParams 定义会员查询参数
type DoQuerySubRelationParams struct {
	Account            string `json:"account,omitempty"`            // 用户账号
	ComNameLike        string `json:"comNameLike,omitempty"`        // 企业名称 (模糊 最左匹配)
	LettersLike        string `json:"lettersLike,omitempty"`        // 姓名首字母 (模糊 最左匹配)
	MemberNameLike     string `json:"memberNameLike,omitempty"`     // 会员名称 (模糊 最左匹配)
	MemberStatusList   []int  `json:"memberStatusList,omitempty"`   // 用户状态列表：0=正常 1=禁用 2=删除 3=未审核
	MemberTypeList     []int  `json:"memberTypeList,omitempty"`     // 会员类型列表：0=供应商 1=分销商 2=直接供应方 3=合并终端后的资源方 5=普通用户 6=员工 7=集团帐号 9=平台帐号
	Mobile             string `json:"mobile,omitempty"`             // 手机号
	PageNum            int    `json:"pageNum,omitempty"`            // 页码，必填项，最小值为 1
	PageSize           int    `json:"pageSize,omitempty"`           // 分页大小，最大值为 100
	ParentIdList       []int  `json:"parentIdList,omitempty"`       // 上级用户 ID 列表，必须字段
	RelationStatusList []int  `json:"relationStatusList,omitempty"` // 关系状态列表：0=有效 1=无效
	Self               int    `json:"self,omitempty"`               // 是否查询自己：1=是 2=否
	ShipTypeList       []int  `json:"shipTypeList,omitempty"`       // 关系类型列表：0=供销关系 1=从属关系 2=平级关系 3=推荐关系 4=资源中心关系
	SonIdList          []int  `json:"sonIdList,omitempty"`          // 下级用户 ID 列表
	SonIdTypeList      []int  `json:"sonIdTypeList,omitempty"`      // 子账号类型列表：0=分销商 1=资源方 2=员工 3=供应方（父ID为集团帐号情况下）
}

// SubRelationListResponseItem 下级关系列表
type SubRelationListResponseItem struct {
	Account    string `json:"account,omitempty"`    // 用户账号
	MemberId   int    `json:"memberId,omitempty"`   // 会员ID，格式为 int32
	MemberName string `json:"memberName,omitempty"` // 会员名称
	Mobile     string `json:"mobile,omitempty"`     // 手机号
}

type ResponseSubRelationPaginate struct {
	java.ResponseCommonPaginateData
	Data ResponseSubRelationPaginateData `json:"data"`
}

type ResponseSubRelationPaginateData struct {
	Records []SubRelationListResponseItem `json:"records"`
	Total   int                           `json:"total"`
	Size    int                           `json:"size"`
	Current int                           `json:"current"`
	Pages   int                           `json:"pages"`
}

// QuerySubRelationListByMoreParamsPaging 管理端通用下级关系查询 接口文档：http://yapi.12301.test/project/229/interface/api/38918
func QuerySubRelationListByMoreParamsPaging(params DoQuerySubRelationParams) (list []SubRelationListResponseItem, total int, err error) {
	list = make([]SubRelationListResponseItem, 0)
	total = 0
	if params.PageSize > 100 {
		err = szerrors.NewLogicErrorWithText("分页大小超过限制[100]")
		return
	}
	if params.PageSize < 1 {
		params.PageSize = 10
	}
	if params.PageNum < 1 {
		params.PageNum = 1
	}
	response, err := java.OriginalPost("/user-center/admin/relationQuery/queryCommonSubRelationByPaging", params)
	if err != nil {
		err = szerrors.NewRemoteError(err)
		return
	}
	var res ResponseSubRelationPaginate
	err = utils.JsonConvertor(response, &res)
	if err != nil {
		err = szerrors.NewRemoteError(err)
		return
	}
	if res.Code != "200" {
		err = szerrors.NewRemoteError(errors.New(res.Message))
		return
	}
	if len(res.Data.Records) == 0 {
		return
	}
	list = res.Data.Records
	total = res.Data.Total

	return
}
