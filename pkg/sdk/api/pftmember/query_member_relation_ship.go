package pftmember

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type memberRelation struct {
	Id           int    `json:"id"`
	ParentId     int    `json:"parentId"`
	SonId        int    `json:"sonId"`
	PriceGroupId int    `json:"priceGroupId"`
	RecTime      string `json:"rectime"`
	Remark       string `json:"remark"`
	ShipType     int    `json:"shipType"`  //1是从属关系
	SonIdType    int    `json:"sonIdType"` //2是员工
	Status       int    `json:"status"`
}

func QueryMemberStaffRelationList(sonIds []int) ([]memberRelation, error) {
	sonIds = utils.RemoveDuplicate(sonIds)
	data, err := java.BatchPost("/web/v1/member/memberRelationshipQueryService/queryStaffRelation", map[string]interface{}{
		"sonIds":    sonIds,
		"parentIds": []int{},
	}, "sonIds", 100, true)
	if err != nil {
		return nil, err
	}
	var results []memberRelation
	err = utils.JsonConvertor(data, &results)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// QueryMemberStaffRelationMapByIds 通过员工ID批量获取主账号ID
func QueryMemberStaffRelationMapByIds(sonIds []int) (map[int]int, error) {
	res, err := QueryMemberStaffRelationList(sonIds)
	if err != nil {
		return nil, err
	}
	if res == nil || len(res) == 0 {
		return nil, nil
	}
	results := make(map[int]int)
	for _, info := range res {
		results[info.SonId] = info.ParentId
	}
	return results, nil
}
