package pftmember

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type MemberExt struct {
	AddSalerType      int    `json:"addSalerType"`
	Birthday          int    `json:"birthday"`
	Business          string `json:"business"`
	Cinfos            string `json:"cinfos"`
	City              int    `json:"city"`
	ComName           string `json:"comName"`
	ContractDepart    int    `json:"contractDepart"`
	ContractModel     int    `json:"contractModel"`
	ContractNum       string `json:"contractNum"`
	Coop              string `json:"coop"`
	CooperateType     int    `json:"cooperateType"`
	CorpKind          int    `json:"corpKind"`
	Country           int    `json:"country"`
	DistributionCheck int    `json:"distributionCheck"`
	EarlyWarning      int64  `json:"earlyWarning"`
	Ext               string `json:"ext"`
	Fax               string `json:"fax"`
	Fid               int    `json:"fid"`
	FinanceName       string `json:"financeName"`
	FormatType        int    `json:"formatType"`
	Hotline           string `json:"hotline"`
	Id                int    `json:"id"`
	IsPay             int    `json:"isPay"`
	JiutianAuth       int    `json:"jiutianAuth"`
	OtherLes          string `json:"otherLes"`
	Position          string `json:"position"`
	ProtocolEnd       string `json:"protocolEnd"`
	ProtocolMain      string `json:"protocolMain"`
	ProtocolMeal      string `json:"protocolMeal"`
	ProtocolStart     string `json:"protocolStart"`
	Province          int    `json:"province"`
	RefundBack        int    `json:"refundBack"`
	ShortCompanyName  string `json:"shortCompanyName"`
	TerminalType      int    `json:"terminalType"`
	Types             int    `json:"types"`
	Website           string `json:"website"`
}

func GetFidKeyMemberExtMap(fidList []int) (fidKeyMemberExtMap map[int]MemberExt, err error) {
	fidKeyMemberExtMap = make(map[int]MemberExt, 0)
	fidList = utils.RemoveDuplicate(fidList)
	chunkedFidList := utils.ChunkSlice(fidList, 500)
	for _, chunk := range chunkedFidList {
		var data interface{}
		data, err = java.Post("/web/v1/member/memberExtInfoQueryService/queryMemberExtByMemberIds", map[string]interface{}{
			"fidList": chunk,
		})
		if err != nil {
			return
		}
		var memberExtListTemp []MemberExt
		err = utils.JsonConvertor(data, &memberExtListTemp)
		if err != nil {
			return
		}
		for _, memberExt := range memberExtListTemp {
			fidKeyMemberExtMap[memberExt.Fid] = memberExt
		}
	}
	return
}
