package pftmember

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type MemberRelationDataItem struct {
	Cuttime    string `json:"cuttime"`
	Id         int    `json:"id"`
	ParentId   int    `json:"parentId"`
	PriceGroup int    `json:"priceGroupId"`
	Rectime    string `json:"rectime"`
	Remark     string `json:"remark"`
	ShipType   int    `json:"shipType"`
	SonId      int    `json:"sonId"`
	SonIdType  int    `json:"sonIdType"`
	Status     int    `json:"status"`
}

// QueryRelationByGroupId 获取集团成员信息
func QueryRelationByGroupId(parentId int, status int) ([]MemberRelationDataItem, error) {
	data, err := java.PostRetry("POST", "/web/v1/member/memberRelationshipQueryService/queryRelationByGroupId", map[string]interface{}{
		"parentId": parentId,
		"status":   status, //-1.查询全部 0.正常 1.失效
	}, false)
	if err != nil {
		return nil, err
	}

	var result []MemberRelationDataItem
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
