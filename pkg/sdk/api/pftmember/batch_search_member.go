package pftmember

import (
	"encoding/json"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
	"strconv"
	"time"
)

type Member struct {
	Account      string  `json:"account"`
	AccountId    int     `json:"accountId"`
	Alipay       string  `json:"alipay"`
	BankAccount1 string  `json:"bankAccount1"`
	BankAccount2 string  `json:"bankAccount2"`
	Cname        string  `json:"cname"`
	Creattime    string  `json:"creattime"`
	Ctel         string  `json:"ctel"`
	CustomerId   int     `json:"customerId"`
	Dcode        string  `json:"dcode"`
	Dcodeurl     string  `json:"dcodeurl"`
	Dname        string  `json:"dname"`
	Dtype        int     `json:"dtype"`
	FeeBank      float64 `json:"feeBank"`
	FeeCode      int     `json:"feeCode"`
	FeePayWay    int     `json:"feePayWay"`
	FeePlatform  int     `json:"feePlatform"`
	FeeSms       int     `json:"feeSms"`
	GroupId      int     `json:"groupId"`
	GroupingId   int     `json:"groupingId"`
	Headphoto    string  `json:"headphoto"`
	Id           int     `json:"id"`
	Kefuid       int     `json:"kefuid"`
	Lasttime     string  `json:"lasttime"`
	Letters      string  `json:"letters"`
	MemberAuth   string  `json:"memberAuth"`
	Mobile       string  `json:"mobile"`
	RegSource    int     `json:"regSource"`
	Salesid      int     `json:"salesid"`
	Status       int     `json:"status"`
}

type MemberBaseInfo struct {
	Id      int    `json:"id"`
	Cname   string `json:"cname"`
	Dname   string `json:"dname"`
	Dtype   int    `json:"dtype"`
	Account string `json:"account"`
}

func BatchSearchMember(memberIds []int) (list []Member, err error) {
	memberIds = utils.RemoveDuplicate(memberIds)
	// 删除 memberIds 中小于 1 的元素
	for i := 0; i < len(memberIds); i++ {
		if memberIds[i] < 1 {
			memberIds = append(memberIds[:i], memberIds[i+1:]...)
			i--
		}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return
	}
	data, err := java.Post("/web/v1/member/memberQueryService/batchMemberInfoByIds", map[string]interface{}{
		"memberIds": memberIds,
	})
	if err != nil {
		return
	}
	err = utils.JsonConvertor(data, &list)
	if err != nil {
		return nil, err
	}
	return
}

// BatchSearchMemberAndCache 批量查询会员信息并缓存每个会员信息
func BatchSearchMemberAndCache(memberIds []int) (list []Member, err error) {
	if memberIds == nil || len(memberIds) == 0 {
		return
	}
	// 从缓存中获取会员信息
	list, err = getMemberListFromCache(memberIds)
	if err != nil {
		return nil, err
	}
	// 如果缓存中缺失会员信息，则从接口中获取缺失的会员信息
	if len(list) != len(memberIds) {
		// 获取缺失的会员信息
		var missingMemberIds []int
		for _, memberId := range memberIds {
			var exist bool
			for _, member := range list {
				if member.Id == memberId {
					exist = true
					break
				}
			}
			if !exist {
				missingMemberIds = append(missingMemberIds, memberId)
			}
		}
		// 从接口中获取缺失的会员信息
		var missingMemberList []Member
		missingMemberList, err = BatchSearchMember(missingMemberIds)
		if err != nil {
			return nil, err
		}
		// 将缺失的会员信息添加到 list 中
		for _, missingMember := range missingMemberList {
			list = append(list, missingMember)
		}
		// 将缺失的会员信息添加到缓存中
		err = SetMemberListToCache(missingMemberList)
		if err != nil {
			return nil, err
		}
	}
	return list, nil
}

func getCacheKey(memberId int) string {
	return "member:" + strconv.Itoa(memberId)
}

// 从缓存中获取会员信息
func getMemberListFromCache(memberIds []int) (list []Member, err error) {
	var keys []string
	for _, memberId := range memberIds {
		keys = append(keys, getCacheKey(memberId))
	}
	ctx := global.REDIS.Context()
	data, err := global.REDIS.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}
	// 将缓存中的数据转换成 Member 列表
	for _, item := range data {
		if item == nil {
			continue
		}
		var member Member
		err = json.Unmarshal([]byte(item.(string)), &member)
		if err != nil {
			return nil, err
		}
		list = append(list, member)
	}
	return list, nil
}

func SetMemberListToCache(list []Member) (err error) {
	ctx := global.REDIS.Context()
	for _, member := range list {
		jsonData, err := json.Marshal(member)
		if err != nil {
			return err
		}
		//缓存半个小时
		err = global.REDIS.Set(ctx, getCacheKey(member.Id), jsonData, 1800*time.Second).Err()
		if err != nil {
			return err
		}
	}
	return nil
}

func GetMemberIdKeyDNameMap(memberIds []int) (map[int]string, error) {
	memberIdKeyNameMap := make(map[int]string)
	var members []Member
	var err error
	//这个接口批量查询人数超过500人会报错, 有最大500查询限制
	if len(memberIds) >= 500 {
		members, err = InBatchesSearchMember(memberIds)
	} else {
		members, err = BatchSearchMember(memberIds)
	}
	if err != nil {
		return nil, err
	}
	for _, member := range members {
		memberIdKeyNameMap[member.Id] = member.Dname
	}
	return memberIdKeyNameMap, nil
}

// GetMemberBaseInfoMapByIds 用户基础信息获取
func GetMemberBaseInfoMapByIds(memberIds []int) (map[int]MemberBaseInfo, error) {
	memberInfoMap := make(map[int]MemberBaseInfo)
	var members []Member
	var err error
	//这个接口批量查询人数超过500人会报错, 有最大500查询限制
	if len(memberIds) >= 500 {
		members, err = InBatchesSearchMember(memberIds)
	} else {
		members, err = BatchSearchMember(memberIds)
	}
	if err != nil {
		return nil, err
	}
	for _, member := range members {
		memberInfoMap[member.Id] = MemberBaseInfo{
			Id:      member.Id,
			Cname:   member.Cname,
			Dname:   member.Dname,
			Dtype:   member.Dtype,
			Account: member.Account,
		}
	}
	return memberInfoMap, nil
}

func GetMemberNameAndIdMapWithIdKey(memberIds []int) (map[int]string, error) {
	memberIds = utils.RemoveDuplicate(memberIds)
	memberList, err := BatchSearchMemberAndCache(memberIds)
	if err != nil {
		return nil, err
	}
	nameMap := make(map[int]string)
	for _, member := range memberList {
		nameMap[member.Id] = member.Dname
	}
	return nameMap, nil
}

// InBatchesSearchMember 分批查询
func InBatchesSearchMember(memberIds []int) ([]Member, error) {
	memberIds = utils.RemoveDuplicate(memberIds)
	// 删除 memberIds 中小于 1 的元素
	for i := 0; i < len(memberIds); i++ {
		if memberIds[i] < 1 {
			memberIds = append(memberIds[:i], memberIds[i+1:]...)
			i--
		}
	}
	if memberIds == nil || len(memberIds) == 0 {
		return nil, nil
	}
	data, err := java.BatchPost("/web/v1/member/memberQueryService/batchMemberInfoByIds", map[string]interface{}{
		"memberIds": memberIds,
	}, "memberIds", 200, true)
	if err != nil {
		return nil, err
	}
	var list []Member
	err = utils.JsonConvertor(data, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}
