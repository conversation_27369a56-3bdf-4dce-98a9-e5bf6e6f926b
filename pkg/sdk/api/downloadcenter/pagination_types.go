package downloadcenter

type PaginationParams struct {
	Request  interface{} `json:"request"` // 新增任务时携带的request
	OpMember struct {
		Fid  int `json:"fid"`  // 商户id
		OpId int `json:"opId"` // 操作员memberId
	} `json:"opMember"` // 商户和操作人
	PageNum      int     `json:"pageNum"`      // 页码
	LastSequence *string `json:"lastSequence"` // 最后一条记录ID，用于根据ID翻页。获取分页数据时业务方提供，请求下一页时携带过去
}

type PaginationResultData struct {
	IsOver       *bool         `json:"isOver,omitempty"` // 是否完结 true 完结
	List         []interface{} `json:"list"`             // 分页数据
	LastSequence *string       `json:"lastSequence"`     // 最后一条记录ID，用于根据ID翻页。获取分页数据时业务方提供，请求下一页时携带过去
}

type PaginationMethod interface {
	FetchPaginationData(params PaginationParams) (result PaginationResultData, err error)
}
