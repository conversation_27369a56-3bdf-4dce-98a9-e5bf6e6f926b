package downloadcenter

import "report-service/pkg/utils"

type AddTaskParams struct {
	Fid                  int                                `json:"fid"`                            // 商户id
	OpId                 *int                               `json:"opId,omitempty"`                 // 操作员memberId
	TemplateCode         string                             `json:"templateCode"`                   // 模板编码
	PageCount            *int                               `json:"pageCount,omitempty"`            // 总页数 不传就是3页没取到数据结束，或最多拉取10000页
	FileName             *string                            `json:"fileName,omitempty"`             // 自定义文件名
	FirstHead            *string                            `json:"firstHead,omitempty"`            // 自定义大标题 替换配置表中标题
	Request              interface{}                        `json:"request"`                        // 导出查询字段，下载中心请求业务方的分页数据时携带的参数
	SelfDefinedStructure *AddTaskParamsSelfDefinedStructure `json:"selfDefinedStructure,omitempty"` // 自定义结构
	Send                 *int                               `json:"send,omitempty"`                 // 默认不发，是否发送邮件：1=不发，2=发
	Email                *string                            `json:"email,omitempty"`                // 邮箱
}

type AddTaskParamsSelfDefinedStructure struct {
	Head  [][]string `json:"head"`  // 自定义表头
	Field []string   `json:"field"` // 自定义字段
}

type AddTaskResult struct {
	TaskId int `json:"taskId"` // 任务ID
}

func AddTask(module string, params AddTaskParams) (taskId int, err error) {
	data, err := Request(module, "addTask", params)
	if err != nil {
		return
	}
	var result AddTaskResult
	utils.JsonConvertOrPanic(data, &result)
	return result.TaskId, nil
}
