package downloadcenter

import (
	"errors"
	"report-service/pkg/sdk/api"
	"report-service/pkg/utils/httputil"
	"report-service/pkg/utils/snowflake"
	"strconv"
)

type JsonRpcRequest struct {
	JsonRpc string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	Id      int         `json:"id"`
}

type JsonRpcResponse struct {
	JsonRpc string         `json:"jsonrpc"`
	Result  *JsonRpcResult `json:"result"`
	Error   *JsonRpcError  `json:"error,omitempty"`
	Id      interface{}    `json:"id"` // int or string
}

type JsonRpcResult struct {
	Code string      `json:"code"`
	Msg  interface{} `json:"msg"` // string or []string
	Data interface{} `json:"data"`
}

type JsonRpcError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func Request(module, method string, params interface{}) (data interface{}, err error) {
	uri := api.DownloadCenter.BaseUri
	system := api.DownloadCenter.System
	authKey := api.DownloadCenter.AuthKey
	id, err := snowflake.GlobalSnowflake.NextId()
	if err != nil {
		return nil, err
	}
	headers := map[string]string{
		"Content-Type": "application/json",
		"module":       module,
		"system":       system,
		"auth-key":     authKey,
		"request-id":   strconv.Itoa(id),
	}
	requestBody := JsonRpcRequest{
		JsonRpc: "2.0",
		Method:  method,
		Params:  []interface{}{params}, // 特殊处理，这里要转成数组传，对方框架弊端
		Id:      id,
	}
	var response JsonRpcResponse
	err = httputil.Request("POST", uri, headers, requestBody, &response)
	if err != nil {
		return nil, err
	}
	if response.Error != nil {
		return nil, errors.New(response.Error.Message)
	}
	if response.Result.Code != "200" {
		return nil, errors.New(response.Result.Msg.(string))
	}
	return response.Result.Data, nil
}
