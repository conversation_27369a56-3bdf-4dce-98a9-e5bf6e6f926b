package downloadcenter

import (
	"errors"
	"report-service/pkg/sdk/api"
	"report-service/pkg/utils/httputil"
	"time"
)

type SubmitResult struct {
	Code string      `json:"code"`
	Msg  interface{} `json:"msg"` // string or []string
	Data interface{} `json:"data"`
}

type SubmitFormFilesItem struct {
	FilePath  string
	FileField string
}

// SubmitFormWithFile 提交表单并支持文件上传
func SubmitFormWithFile(uri string, params map[string]interface{}, files []SubmitFormFilesItem) (data interface{}, err error) {
	BaseUri := api.DownloadCenterApi.BaseUri
	uri = BaseUri + uri
	// 设置基础headers
	headers := map[string]string{}
	// 构建multipart表单请求
	var response SubmitResult
	// 添加文件
	var requestFiles []httputil.RequestSubmitFormFilesItem
	for _, file := range files {
		requestFiles = append(requestFiles, httputil.RequestSubmitFormFilesItem{
			FileField: file.FileField,
			FilePath:  file.FilePath,
		})
	}
	err = httputil.RequestSubmitForm("POST", uri, headers, params, &response,
		httputil.WithFiles(requestFiles...),  //多文件请求
		httputil.WithTimeout(10*time.Second), //超时限制
	)
	if err != nil {
		return nil, err
	}
	if response.Code != "200" {
		return nil, errors.New(response.Msg.(string))
	}
	return response.Data, nil
}
