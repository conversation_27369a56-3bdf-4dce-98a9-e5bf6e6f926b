package downloadcenter

import "github.com/spf13/cast"

func AddFileTask(merchantId, memberId int, fileName, realZipPath string) (taskId int, err error) {
	apiParams := make(map[string]interface{})
	apiParams["fid"] = merchantId
	apiParams["memberId"] = memberId
	apiParams["fileName"] = fileName
	var files []SubmitFormFilesItem
	files = append(files, SubmitFormFilesItem{
		FileField: "file",
		FilePath:  realZipPath,
	})
	//提交表单请求，上传文件
	res, err := SubmitFormWithFile("/download-center/excel-task/uploadExportExcelFile", apiParams, files)
	if err != nil {
		return
	}
	taskId = cast.ToInt(res)
	return
}
