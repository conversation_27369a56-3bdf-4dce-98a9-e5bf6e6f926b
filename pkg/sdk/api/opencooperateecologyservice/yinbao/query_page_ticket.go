package yinbao

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/opencooperateecologyservice"
	"report-service/pkg/utils"
)

// QueryPageTicketRequest 票据请求结构体
type QueryPageTicketRequest struct {
	PageNum   int      `json:"pageNum"`
	PageSize  int      `json:"pageSize"`
	StartTime string   `json:"startTime"`
	EndTime   string   `json:"endTime"`
	LastId    int      `json:"lastId"`
	MemberIds []int    `json:"memberIds"`
	AppIds    []string `json:"appIds"`
}

// PageTicketResponsePageRecord 票据记录结构体
type PageTicketResponsePageRecord struct {
	ID                        int     `json:"id"`
	ConSysBind                int     `json:"conSysBind"`
	MemberId                  int     `json:"memberId"`
	SyncCreateTime            string  `json:"syncCreateTime"`
	SyncUpdateTime            string  `json:"syncUpdateTime"`
	AppId                     string  `json:"appId"`
	Sn                        string  `json:"sn"`
	Uid                       string  `json:"uid"`
	TicketType                string  `json:"ticketType"`
	CashierUid                int     `json:"cashierUid"`
	Cashier                   string  `json:"cashier"`
	CustomerUid               int     `json:"customerUid"`
	DateTime                  string  `json:"dateTime"`
	SysUpdateTime             string  `json:"sysUpdateTime"`
	TotalAmount               float64 `json:"totalAmount"`
	TotalProfit               float64 `json:"totalProfit"`
	Discount                  float64 `json:"discount"`
	ExternalOrderNo           string  `json:"externalOrderNo"`
	Remark                    string  `json:"remark"`
	ServiceFee                float64 `json:"serviceFee"`
	Gratuity                  float64 `json:"gratuity"`
	CoverCharge               float64 `json:"coverCharge"`
	BoxFee                    float64 `json:"boxFee"`
	Rounding                  float64 `json:"rounding"`
	SellTicketUid             int     `json:"sellTicketUid"`
	Invalid                   int     `json:"invalid"`
	WebOrderNo                string  `json:"webOrderNo"`
	Payments                  string  `json:"payments"`
	TicketSpendDetail         string  `json:"ticketSpendDetail"`
	TicketOnTable             string  `json:"ticketOnTable"`
	TicketStoreAppidOrAccount string  `json:"ticketStoreAppidOrAccount"`
}

// PageTicketResponsePage 票据分页结构体
type PageTicketResponsePage struct {
	Records []PageTicketResponsePageRecord `json:"records"`
	Total   int                            `json:"total"`
	Size    int                            `json:"size"`
	Current int                            `json:"current"`
	Pages   int                            `json:"pages"`
}

// QueryPageTicketResponse 票据查询返回结构体
type QueryPageTicketResponse struct {
	LastId *int                   `json:"lastId"`
	Page   PageTicketResponsePage `json:"page"`
}

// PageTicketResponsePageRecordPayments 票据分页结构体中的支付信息结构体
type PageTicketResponsePageRecordPayments struct {
	Code   string  `json:"code"`
	Amount float64 `json:"amount"`
}

// QueryPageTicket 查询票据分页数据  http://open-cooperate-ecology-internal.12301dev.com:9080/doc.html#/default/%E9%9B%B6%E5%94%AE%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2%20-%20%E9%93%B6%E8%B1%B9/pageTicket
func QueryPageTicket(params QueryPageTicketRequest) (result *QueryPageTicketResponse, err error) {
	// 参数校验
	if params.PageNum <= 0 {
		params.PageNum = 1
	}
	if params.PageSize <= 0 || params.PageSize > 1000 {
		params.PageSize = 20
	}

	// 解析时间参数进行验证
	if params.StartTime == "" {
		return nil, fmt.Errorf("startTime is required")
	}
	if params.EndTime == "" {
		return nil, fmt.Errorf("endTime is required")
	}
	if !carbon.Parse(params.StartTime).IsValid() {
		return nil, fmt.Errorf("startTime format error, should be yyyy-MM-dd HH:ii:ss")
	}
	if !carbon.Parse(params.EndTime).IsValid() {
		return nil, fmt.Errorf("endTime format error, should be yyyy-MM-dd HH:ii:ss")
	}

	// 构造API参数
	apiParams := make(map[string]interface{})
	apiParams["pageNum"] = params.PageNum
	apiParams["pageSize"] = params.PageSize
	apiParams["startTime"] = params.StartTime
	apiParams["endTime"] = params.EndTime
	if params.LastId != 0 {
		apiParams["lastId"] = params.LastId
	}
	if len(params.MemberIds) > 0 {
		apiParams["memberIds"] = params.MemberIds
		if utils.Container([]string{enum.EnvDevelop, enum.EnvTest}, global.CONFIG.System.Env) {
			apiParams["memberIds"] = []int{enum.PayModeMoneyTestMemberId}
		}
	}
	if len(params.AppIds) > 0 {
		apiParams["appIds"] = params.AppIds
	}

	data, err := opencooperateecologyservice.Post("/cooperate-ecology-data-internal/retail/yinbao/pageTicket", apiParams)
	if err != nil {
		return nil, err
	}

	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
