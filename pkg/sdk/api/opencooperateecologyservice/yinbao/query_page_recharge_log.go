package yinbao

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/domain/enum"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/opencooperateecologyservice"
	"report-service/pkg/utils"
)

// QueryPageRechargeLogRequest 充值记录请求结构体
type QueryPageRechargeLogRequest struct {
	PageNum   int      `json:"pageNum"`
	PageSize  int      `json:"pageSize"`
	StartTime string   `json:"startTime"`
	EndTime   string   `json:"endTime"`
	LastId    int      `json:"lastId"`
	MemberIds []int    `json:"memberIds"`
	AppIds    []string `json:"appIds"`
}

// PageRechargeLogResponsePageRecord 充值记录结构体
type PageRechargeLogResponsePageRecord struct {
	ID                         int     `json:"id"`
	ConSysBind                 int     `json:"conSysBind"`
	MemberId                   int     `json:"memberId"`
	SyncCreateTime             string  `json:"syncCreateTime"`
	SyncUpdateTime             string  `json:"syncUpdateTime"`
	Uid                        string  `json:"uid"`
	RechargeStoreAppId         string  `json:"rechargeStoreAppId"`
	RechargeStoreAccount       string  `json:"rechargeStoreAccount"`
	CustomerUid                int     `json:"customerUid"`
	CashierUid                 int     `json:"cashierUid"`
	RechargeMoney              float64 `json:"rechargeMoney"`
	GiftMoney                  float64 `json:"giftMoney"`
	DateTime                   string  `json:"dateTime"`
	PayMethod                  string  `json:"payMethod"`
	PayMethodCode              string  `json:"payMethodCode"`
	CustomerMoneyAfterRecharge float64 `json:"customerMoneyAfterRecharge"`
}

// PageRechargeLogResponsePage 充值记录分页结构体
type PageRechargeLogResponsePage struct {
	Records []PageRechargeLogResponsePageRecord `json:"records"`
	Total   int                                 `json:"total"`
	Size    int                                 `json:"size"`
	Current int                                 `json:"current"`
	Pages   int                                 `json:"pages"`
}

// QueryPageRechargeLogResponse 充值记录查询返回结构体
type QueryPageRechargeLogResponse struct {
	LastId *int                        `json:"lastId"`
	Page   PageRechargeLogResponsePage `json:"page"`
}

// QueryPageRechargeLog 分页查询充值记录 http://open-cooperate-ecology-internal.12301dev.com:9080/doc.html#/default/%E9%9B%B6%E5%94%AE%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2%20-%20%E9%93%B6%E8%B1%B9/pageRechargeLog
func QueryPageRechargeLog(params QueryPageRechargeLogRequest) (result *QueryPageRechargeLogResponse, err error) {
	// 参数校验
	if params.PageNum <= 0 {
		params.PageNum = 1
	}
	if params.PageSize <= 0 || params.PageSize > 1000 {
		params.PageSize = 20
	}

	// 解析时间参数进行验证
	if params.StartTime == "" {
		return nil, fmt.Errorf("startTime is required")
	}
	if params.EndTime == "" {
		return nil, fmt.Errorf("endTime is required")
	}
	if !carbon.Parse(params.StartTime).IsValid() {
		return nil, fmt.Errorf("startTime format error, should be yyyy-MM-dd HH:ii:ss")
	}
	if !carbon.Parse(params.EndTime).IsValid() {
		return nil, fmt.Errorf("endTime format error, should be yyyy-MM-dd HH:ii:ss")
	}

	// 构造API参数
	apiParams := make(map[string]interface{})
	apiParams["pageNum"] = params.PageNum
	apiParams["pageSize"] = params.PageSize
	apiParams["startTime"] = params.StartTime
	apiParams["endTime"] = params.EndTime
	if params.LastId != 0 {
		apiParams["lastId"] = params.LastId
	}
	if len(params.MemberIds) > 0 {
		apiParams["memberIds"] = params.MemberIds
		if utils.Container([]string{enum.EnvDevelop, enum.EnvTest}, global.CONFIG.System.Env) {
			apiParams["memberIds"] = []int{enum.PayModeMoneyTestMemberId}
		}
	}
	if len(params.AppIds) > 0 {
		apiParams["appIds"] = params.AppIds
	}
	data, err := opencooperateecologyservice.Post("/cooperate-ecology-data-internal/retail/yinbao/pageRechargeLog", apiParams)
	if err != nil {
		return nil, err
	}

	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
