package yinbao

import (
	"report-service/pkg/sdk/api/opencooperateecologyservice"
	"report-service/pkg/utils"
)

// ListTicketItemsResponseItem 票据项目结构体
type ListTicketItemsResponseItem struct {
	ID                   int     `json:"id"`
	ConSysBind           int     `json:"conSysBind"`
	MemberId             int     `json:"memberId"`
	SyncCreateTime       string  `json:"syncCreateTime"`
	SyncUpdateTime       string  `json:"syncUpdateTime"`
	Sn                   string  `json:"sn"`
	Uid                  string  `json:"uid"`
	ProductUid           int     `json:"productUid"`
	Name                 string  `json:"name"`
	BuyPrice             float64 `json:"buyPrice"`
	SellPrice            float64 `json:"sellPrice"`
	CustomerPrice        float64 `json:"customerPrice"`
	Quantity             float64 `json:"quantity"`
	Discount             float64 `json:"discount"`
	CustomerDiscount     float64 `json:"customerDiscount"`
	TotalAmount          float64 `json:"totalAmount"`
	TotalProfit          float64 `json:"totalProfit"`
	IsCustomerDiscount   int     `json:"isCustomerDiscount"`
	ProductBarcode       string  `json:"productBarcode"`
	TicketItemAttributes string  `json:"ticketItemAttributes"`
	DiscountDetails      string  `json:"discountDetails"`
	SaleGuiderList       string  `json:"saleGuiderList"`
}

// ListTicketItemsResponseItemDiscountDetail 折扣详情结构体
type ListTicketItemsResponseItemDiscountDetail struct {
	ProductUid          int     `json:"productUid"`          // 产品UID
	DiscountType        int     `json:"discountType"`        // 折扣类型
	DiscountRate        float64 `json:"discountRate"`        // 折扣率
	DiscountAmount      float64 `json:"discountAmount"`      // 折扣金额
	DiscountRuleUid     int     `json:"discountRuleUid"`     // 折扣规则UID
	DiscountTotalAmount float64 `json:"discountTotalAmount"` // 折扣总金额
	DateTime            string  `json:"datetime"`            // 时间
}

// QueryListTicketItemsResponse 票据项目列表查询返回结构体
type QueryListTicketItemsResponse []ListTicketItemsResponseItem

func QueryListTicketItems(uIds []string, memberIds []int) (result *QueryListTicketItemsResponse, err error) {
	if len(uIds) == 0 {
		return nil, nil
	}
	uIds = utils.RemoveDuplicate(uIds)
	// 构造API参数
	apiParams := make(map[string]interface{})
	apiParams["uids"] = uIds
	if len(memberIds) > 0 {
		apiParams["memberIds"] = memberIds
	}
	data, err := opencooperateecologyservice.Post("/cooperate-ecology-data-internal/retail/yinbao/listTicketItems", apiParams)
	if err != nil {
		return nil, err
	}

	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
