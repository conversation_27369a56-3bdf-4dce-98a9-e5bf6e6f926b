package yinbao

import (
	"fmt"
	"net/url"
	"report-service/internal/domain/enum"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/opencooperateecologyservice"
	"report-service/pkg/utils"
)

// QueryLastSyncSuccessTimeRequest 查询最后同步成功时间请求参数
type QueryLastSyncSuccessTimeRequest struct {
	MemberId int    `json:"memberId"`
	Type     int    `json:"type"`            // Type 类型: 1-单据, 2-充值记录
	AppId    string `json:"appId,omitempty"` // AppId 应用ID（可选）
}

// QueryLastSyncSuccessTime 最后拉取成功时间  接口文档：http://open-cooperate-ecology-internal.12301dev.com:9080/doc.html#/default/%E9%9B%B6%E5%94%AE%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2%20-%20%E9%93%B6%E8%B1%B9/lastSyncSuccessTime
func QueryLastSyncSuccessTime(params QueryLastSyncSuccessTimeRequest) (string, error) {
	// 构建查询参数
	apiParams := url.Values{}
	if utils.Container([]string{enum.EnvDevelop, enum.EnvTest}, global.CONFIG.System.Env) {
		apiParams.Add("memberId", fmt.Sprintf("%d", enum.PayModeMoneyTestMemberId))
	} else {
		apiParams.Add("memberId", fmt.Sprintf("%d", params.MemberId))
	}
	apiParams.Add("type", fmt.Sprintf("%d", params.Type))
	if params.AppId != "" {
		apiParams.Add("appId", params.AppId)
	}
	// 拼接URL和查询参数
	path := "/cooperate-ecology-data-internal/retail/yinbao/lastSyncSuccessTime"
	if len(apiParams) > 0 {
		path = fmt.Sprintf("%s?%s", path, apiParams.Encode())
	}
	result, err := opencooperateecologyservice.GET(path)
	if err != nil {
		return "", err
	}
	lastTime := result.(string)
	return lastTime, nil
}
