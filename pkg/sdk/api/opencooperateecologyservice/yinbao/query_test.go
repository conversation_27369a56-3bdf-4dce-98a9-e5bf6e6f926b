package yinbao

import (
	"fmt"
	"report-service/pkg/sdk/api"
	"testing"
)

// 获取银豹票据记录
func TestQueryPageTicket(t *testing.T) {
	api.OpenCooperateEcology.Addr = "http://open-cooperate-ecology-internal.12301dev.com:9080"
	gotResult, err := QueryPageTicket(QueryPageTicketRequest{
		PageNum:   1,
		PageSize:  10,
		StartTime: "2025-07-23 11:00:00",
		EndTime:   "2025-07-23 11:59:59",
		LastId:    0,
	})

	fmt.Println(gotResult, err)
}

// 获取银豹充值记录记录
func TestQueryPageRechargeLog(t *testing.T) {
	api.OpenCooperateEcology.Addr = "http://open-cooperate-ecology-internal.12301dev.com:9080"
	gotResult, err := QueryPageRechargeLog(QueryPageRechargeLogRequest{
		PageNum:   1,
		PageSize:  10,
		StartTime: "2025-07-02 09:00:00",
		EndTime:   "2025-07-02 09:59:59",
		LastId:    0,
	})

	fmt.Println(gotResult, err)
}

// 查询银豹票据商品明细
func TestQueryListTicketItems(t *testing.T) {
	api.OpenCooperateEcology.Addr = "http://open-cooperate-ecology-internal.12301dev.com:9080"
	gotResult, err := QueryListTicketItems([]string{
		"130917098058189000",
	}, []int{6970})

	fmt.Println(gotResult, err)

}
