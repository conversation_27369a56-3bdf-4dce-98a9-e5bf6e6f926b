package opencooperateecologyservice

import (
	"errors"
	"report-service/pkg/sdk/api"
	"report-service/pkg/utils/httputil"
)

type ResponseBody struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func Post(uri string, requestBody interface{}) (data interface{}, err error) {
	return Request("POST", uri, requestBody)
}

func GET(uri string) (data interface{}, err error) {
	return Request("GET", uri, nil)
}

func Request(method string, uri string, requestBody interface{}) (data interface{}, err error) {
	uri = api.OpenCooperateEcology.Addr + uri
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	var responseBody ResponseBody
	err = httputil.Request(method, uri, headers, requestBody, &responseBody)
	if err != nil {
		return nil, err
	}
	if responseBody.Code == 0 {
		return data, errors.New("response code is empty")
	}
	if responseBody.Code != 200 {
		return nil, errors.New(responseBody.Msg)
	}
	return responseBody.Data, nil
}
