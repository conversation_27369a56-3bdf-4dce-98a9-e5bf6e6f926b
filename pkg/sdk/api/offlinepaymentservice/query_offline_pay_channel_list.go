package offlinepaymentservice

import (
	"errors"
	"fmt"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type OfflinePayChannelResponse struct {
	Code    string        `json:"code"`
	Message string        `json:"message"`
	Data    []interface{} `json:"data"`
}

type OfflinePayChannelItem struct {
	Id          int    `json:"id"`
	MemberId    int    `json:"memberId"`
	Code        int    `json:"code"`
	SubjectCode int    `json:"subjectCode"`
	Name        string `json:"name"`
	Status      int    `json:"status"`
	UpdateAt    string `json:"updateAt"`
	ReadOnly    bool   `json:"readOnly"`
	PayMode     int    `json:"payMode"`
}

func QueryOfflinePayChannelList(memberId int, linkPayMode bool) ([]OfflinePayChannelItem, error) {
	if memberId == 0 {
		return nil, errors.New("memberId can not be empty")
	}
	response, err := java.OriginalGet("/user-center/feign-client/offline-pay-channel/list", map[string]interface{}{
		"memberId":    memberId,
		"linkPayMode": linkPayMode,
	})
	if err != nil {
		return nil, err
	}
	var res OfflinePayChannelResponse
	err = utils.JsonConvertor(response, &res)
	if err != nil {
		return nil, err
	}
	if res.Code != "200" {
		return nil, errors.New(fmt.Sprintf("请求服务失败，错误信息：%s", res.Message))
	}
	var result []OfflinePayChannelItem
	err = utils.JsonConvertor(res.Data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
