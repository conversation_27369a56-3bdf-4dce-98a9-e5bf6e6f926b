package tradejournalservice

import (
	"fmt"
	"report-service/pkg/sdk/api"
	"testing"
)

func TestQueryTradeJournalWithUser(t *testing.T) {
	api.BaseUri = "http://java-api.12301dev.com:8080"
	user, err := QueryTradeJournalWithUser(RequestParams{
		AccountIds:        []int{4531462},
		BeginDate:         "2025-07-01 00:00:00",
		EndDate:           "2025-07-25 23:59:59",
		RequestID:         "68833d41d4a489006",
		SubjectCodes:      []int{1101, 2601, 1201, 1202},
		TemplateItemCodes: []int{1028},
		PageSize:          1000,
		TradeScope:        "total",
		PageNum:           1,
	})
	fmt.Printf("%+v  %+v\n", user, err)
}
