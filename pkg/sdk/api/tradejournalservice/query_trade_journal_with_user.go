package tradejournalservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type TradeJournalResponse struct {
	AccountAdjust        int    `json:"accountAdjust"`
	AccountBookId        int64  `json:"accountBookId"`
	AccountId            int    `json:"accountId"`
	AccountType          int    `json:"accountType"`
	Aid                  int    `json:"aid"`
	AvailableMoney       int    `json:"availableMoney"`
	AvailableRemainMoney int    `json:"availableRemainMoney"`
	AvailableTrade       int    `json:"availableTrade"`
	AvailableTradeType   int    `json:"availableTradeType"`
	Channel              int    `json:"channel"`
	CustomerId           int    `json:"customerId"`
	Daction              int    `json:"daction"`
	Dmoney               int    `json:"dmoney"`
	Dtype                int    `json:"dtype"`
	Fid                  int    `json:"fid"`
	FrozenMoney          int    `json:"frozenMoney"`
	FrozenRemainMoney    int    `json:"frozenRemainMoney"`
	FrozenTrade          int    `json:"frozenTrade"`
	FrozenTradeType      int    `json:"frozenTradeType"`
	FunctionCode         int    `json:"functionCode"`
	ID                   int    `json:"id"`
	LastTradeId          int    `json:"lastTradeId"`
	Lmoney               int    `json:"lmoney"`
	Memo                 string `json:"memo"`
	Opid                 int    `json:"opid"`
	Orderid              string `json:"orderid"`
	Ptype                int    `json:"ptype"`
	Recmonth             int    `json:"recmonth"`
	Rectime              int    `json:"rectime"`
	ReversalTradeId      int    `json:"reversalTradeId"`
	Sequence             int    `json:"sequence"`
	SettleMark           int    `json:"settleMark"`
	Status               int    `json:"status"`
	SubjectCode          int    `json:"subjectCode"`
	SubjectType          int    `json:"subjectType"`
	TemplateCode         int    `json:"templateCode"`
	TemplateItemCode     int    `json:"templateItemCode"`
	TotalTrade           int    `json:"totalTrade"`
	TradeAccountId       int    `json:"tradeAccountId"`
	TradeLimitMoney      int    `json:"tradeLimitMoney"`
	TradeLimitTotal      int    `json:"tradeLimitTotal"`
	TradeLimitType       int    `json:"tradeLimitType"`
	TradeNo              string `json:"tradeNo"`
	TradeSubjectCode     int    `json:"tradeSubjectCode"`
	TradeTime            int    `json:"tradeTime"`
}

type RequestParams struct {
	BeginDate         string `json:"beginDate"`
	EndDate           string `json:"endDate"`
	RequestID         string `json:"requestId"`
	SubjectCodes      []int  `json:"subjectCodes"`
	AccountIds        []int  `json:"accountIds"`
	TemplateItemCodes []int  `json:"templateItemCodes"`
	PageSize          int    `json:"pageSize"`
	TradeScope        string `json:"tradeScope"`
	PageNum           int    `json:"pageNum"`
}

func QueryTradeJournalWithUser(params RequestParams) (result []TradeJournalResponse, err error) {
	javaParams := make(map[string]interface{})
	javaParams["beginDate"] = params.BeginDate
	javaParams["endDate"] = params.EndDate
	javaParams["requestId"] = params.RequestID
	javaParams["subjectCodes"] = params.SubjectCodes
	javaParams["accountIds"] = params.AccountIds
	javaParams["templateItemCodes"] = params.TemplateItemCodes
	javaParams["pageSize"] = params.PageSize
	javaParams["tradeScope"] = params.TradeScope
	javaParams["pageNum"] = params.PageNum

	data, err := java.Post("/web/v1/member/tradeJournal/pageQuery", javaParams)
	if err != nil {
		return nil, err
	}
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
