package platformrpcapi

import (
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type DoQuerySiteListParams struct {
	Sids     []int  `json:"sids"`
	KeyWords string `json:"key_words"`
	PageNum  int    `json:"page"`
	PageSize int    `json:"size"`
	Fields   string `json:"fields"`
	IsTotal  bool   `json:"is_total"`
}

// QuerySiteListBySids 根据商户ID数组获取站点列表
func QuerySiteListBySids(params DoQuerySiteListParams) ([]SiteInfoData, error) {
	data, resErr := Request("Terminal/Site/getSiteListBySids", []DoQuerySiteListParams{params})
	if resErr != nil {
		return nil, szerrors.NewRemoteError(resErr)
	}
	var result []SiteInfoData
	err := utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
