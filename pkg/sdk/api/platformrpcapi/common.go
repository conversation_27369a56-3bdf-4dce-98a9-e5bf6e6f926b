package platformrpcapi

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"report-service/internal/global"
	"time"
)

type CacheListEachOneSetUp struct {
	KeyFormat    string        `json:"key_format"`     //key字串格式 例如"cache:%s"
	KeyFieldName string        `json:"key_field_name"` //key字段名称  需要替换%s字串的list里面的元素字段名
	Expired      time.Duration `json:"expired"`        //过期时间（s）
}

func (config *CacheListEachOneSetUp) GetListEachOneFromCache(keyIds interface{}) ([]interface{}, error) {
	if config.KeyFormat == "" || config.KeyFieldName == "" {
		return nil, errors.New("config is error")
	}
	var keys []string
	switch v := keyIds.(type) {
	case []int:
		for _, id := range v {
			keys = append(keys, fmt.Sprintf(config.KeyFormat, id))
		}
	case []string:
		for _, id := range v {
			keys = append(keys, fmt.Sprintf(config.KeyFormat, id))
		}
	default:
		return nil, errors.New("params type is error")
	}
	if len(keys) == 0 {
		return nil, nil
	}
	ctx := global.REDIS.Context()
	data, err := global.REDIS.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}
	var results []interface{}
	for _, item := range data {
		if item == nil {
			continue
		}
		var info interface{}
		err = json.Unmarshal([]byte(item.(string)), &info)
		if err != nil {
			return nil, err
		}
		results = append(results, info)
	}
	return results, nil
}

func (config *CacheListEachOneSetUp) SetListEachOneToCache(list []interface{}) error {
	if config.KeyFormat == "" || config.KeyFieldName == "" {
		return errors.New("config is error")
	}
	ctx := global.REDIS.Context()
	for _, info := range list {
		keyVal := reflect.ValueOf(info).FieldByName(config.KeyFieldName)
		if !keyVal.IsValid() {
			return errors.New(fmt.Sprintf("field %s not exists", config.KeyFieldName))
		}
		jsonData, err := json.Marshal(info)
		if err != nil {
			return err
		}
		err = global.REDIS.Set(ctx, fmt.Sprintf(config.KeyFormat, keyVal.Interface()), jsonData, config.Expired*time.Second).Err()
		if err != nil {
			return err
		}
	}
	return nil
}
