package platformrpcapi

import (
	"errors"
	"report-service/pkg/utils"
)

type OrderCancelInfoData struct {
	TrackId     string `json:"track_id"`
	OrderId     string `json:"orderid"`
	ServiceInfo string `json:"service_info"`
}

type ServiceInfoItem struct {
	Fid   int `json:"fid"`
	Aid   int `json:"aid"`
	Money int `json:"money"`
}

func BatchOrderCancelInfoAndCache(trackIds []string) ([]OrderCancelInfoData, error) {
	if trackIds == nil || len(trackIds) == 0 {
		return nil, errors.New("param trackIds is nil")
	}
	cacheListEachOne := &CacheListEachOneSetUp{
		KeyFormat:    "order_cancel_info:%s",
		KeyFieldName: "TrackId",
		Expired:      60,
	}
	listRes, err := cacheListEachOne.GetListEachOneFromCache(trackIds)
	if err != nil {
		return nil, err
	}
	var list []OrderCancelInfoData
	err = utils.JsonConvertor(listRes, &list)
	if err != nil {
		return nil, err
	}
	if len(list) != len(trackIds) {
		var missingTrackIds []string
		for _, trackId := range trackIds {
			var exist bool
			for _, item := range list {
				if item.TrackId == trackId {
					exist = true
					break
				}
			}
			if !exist {
				missingTrackIds = append(missingTrackIds, trackId)
			}
		}
		var missingList []OrderCancelInfoData
		missingList, err = InBatchesQueryOrderCancelInfoList(missingTrackIds)
		if err != nil {
			return nil, err
		}
		var cacheList []interface{}
		for _, missingItem := range missingList {
			list = append(list, missingItem)
			cacheList = append(cacheList, missingItem)
		}
		err = cacheListEachOne.SetListEachOneToCache(cacheList)
		if err != nil {
			return nil, err
		}
	}
	return list, nil
}

// InBatchesQueryOrderCancelInfoList 分批获取退票手续费
func InBatchesQueryOrderCancelInfoList(trackIds []string) ([]OrderCancelInfoData, error) {
	trackIds = utils.RemoveDuplicate(trackIds)
	splitList := utils.ChunkSlice(trackIds, 300)
	var results []OrderCancelInfoData
	for _, splitIds := range splitList {
		data, resErr := Request("/Order/OrderCancelInfo/getListByTrackIds", []map[string]interface{}{{
			"track_ids": splitIds,
		}})
		if resErr != nil {
			return nil, resErr
		}
		var result []OrderCancelInfoData
		err := utils.JsonConvertor(data, &result)
		if err != nil {
			return nil, err
		}
		for _, infoData := range result {
			results = append(results, infoData)
		}
	}

	return results, nil
}
