package platformrpcapi

import (
	"errors"
	"report-service/pkg/sdk/api"
	"report-service/pkg/utils/httputil"
	"report-service/pkg/utils/snowflake"
	"strconv"
)

type JsonRpcRequest struct {
	JsonRpc string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	Id      int         `json:"id"`
}

type JsonRpcResponse struct {
	JsonRpc string         `json:"jsonrpc"`
	Result  *JsonRpcResult `json:"result"`
	Error   *JsonRpcError  `json:"error"`
	Id      int            `json:"id"`
}

type JsonRpcResult struct {
	Code int         `json:"code"`
	Msg  interface{} `json:"msg"` // string or []string
	Data interface{} `json:"data"`
}

type JsonRpcError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func Request(method string, params interface{}) (data interface{}, err error) {
	uri := api.PlatformApi.RpcApi.Uri
	system := api.PlatformApi.RpcApi.System
	authKey := api.PlatformApi.RpcApi.AuthKey
	id, err := snowflake.GlobalSnowflake.NextId()
	if err != nil {
		return nil, err
	}
	headers := map[string]string{
		"Content-Type": "application/json",
		"request-id":   strconv.Itoa(id),
		"module":       "report-service",
		"system":       system,
		"auth-key":     authKey,
	}
	requestBody := JsonRpcRequest{
		JsonRpc: "2.0",
		Method:  method,
		Params:  params,
		Id:      id,
	}
	var response JsonRpcResponse
	err = httputil.Request("POST", uri, headers, requestBody, &response)
	if err != nil {
		return nil, err
	}
	if response.Error != nil {
		return nil, errors.New(response.Error.Message)
	}
	if response.Result.Code != 200 {
		return nil, errors.New(response.Result.Msg.(string))
	}
	return response.Result.Data, nil
}
