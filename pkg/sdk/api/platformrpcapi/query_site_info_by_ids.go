package platformrpcapi

import (
	"errors"
	"report-service/pkg/utils"
	"strconv"
)

type SiteInfoData struct {
	SiteId string `json:"id"`
	Sid    string `json:"sid"`
	SName  string `json:"sname"`
}
type SiteInfo struct {
	SiteId string `json:"id"`
	Sid    string `json:"sid"`
	SName  string `json:"sname"`
}

func QuerySiteInfoByIds(siteIds []int) ([]SiteInfoData, error) {
	//转一层格式兼容下
	var siteIdsNew []string
	for _, siteId := range siteIds {
		siteIdsNew = append(siteIdsNew, strconv.Itoa(siteId))
	}
	siteIdsNew = utils.RemoveDuplicate(siteIdsNew)
	splitList := utils.ChunkSlice(siteIdsNew, 200)
	var results []SiteInfoData
	for _, splitIds := range splitList {
		data, resErr := Request("Terminal/Site/getSiteInfoByIds", []map[string]interface{}{{
			"site_ids": splitIds,
		}})
		if resErr != nil {
			return nil, resErr
		}
		var result []SiteInfoData
		err := utils.JsonConvertor(data, &result)
		if err != nil {
			return nil, err
		}
		for _, infoData := range result {
			results = append(results, infoData)
		}
	}

	return results, nil
}

// QuerySiteNameMapByIds 获取站点名称map
func QuerySiteNameMapByIds(siteIds []int) (map[int]string, error) {
	res, err := QuerySiteInfoByIds(siteIds)
	if err != nil {
		return nil, err
	}
	if res == nil || len(res) == 0 {
		return nil, nil
	}
	results := make(map[int]string)
	for _, info := range res {
		siteId, siteErr := strconv.Atoi(info.SiteId)
		if siteErr != nil {
			continue
		}
		results[siteId] = info.SName
	}
	return results, nil
}

// QuerySiteSidMapByIds 获取站点对应的商户ID
func QuerySiteSidMapByIds(siteIds []int) (map[int]int, error) {
	if siteIds == nil || len(siteIds) == 0 {
		return nil, errors.New("param siteIds is nil")
	}
	//站点缓存设置
	cacheListEachOne := &CacheListEachOneSetUp{
		KeyFormat:    "merchant_site_info:%s",
		KeyFieldName: "SiteId",
		Expired:      1800,
	}
	var siteIdsNew []string
	for _, siteId := range siteIds {
		siteIdsNew = append(siteIdsNew, strconv.Itoa(siteId))
	}
	listRes, err := cacheListEachOne.GetListEachOneFromCache(siteIdsNew)
	if err != nil {
		return nil, err
	}
	var list []SiteInfoData
	err = utils.JsonConvertor(listRes, &list)
	if err != nil {
		return nil, err
	}
	if len(list) != len(siteIds) {
		var missingIds []int
		for _, siteId := range siteIds {
			var exist bool
			for _, item := range list {
				if item.SiteId == strconv.Itoa(siteId) {
					exist = true
					break
				}
			}
			if !exist {
				missingIds = append(missingIds, siteId)
			}
		}
		var missingList []SiteInfoData
		missingList, err = QuerySiteInfoByIds(missingIds)
		if err != nil {
			return nil, err
		}
		var cacheList []interface{}
		for _, missingItem := range missingList {
			tmp := SiteInfoData{
				SiteId: missingItem.SiteId,
				Sid:    missingItem.Sid,
				SName:  "",
			}
			list = append(list, tmp)
			cacheList = append(cacheList, tmp)
		}
		err = cacheListEachOne.SetListEachOneToCache(cacheList)
		if err != nil {
			return nil, err
		}
	}
	results := make(map[int]int)
	if len(list) > 0 {
		for _, info := range list {
			if info.SiteId == "" || info.Sid == "" {
				continue
			}
			siteId, siteErr := strconv.Atoi(info.SiteId)
			if siteErr != nil {
				continue
			}
			sid, sidErr := strconv.Atoi(info.Sid)
			if sidErr != nil {
				continue
			}
			results[siteId] = sid
		}
	}
	return results, nil
}
