package platformsalelist

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type PlatformTicketAndInvalidQuery struct {
	Fid             int      `json:"fid" validate:"required,min=1"`                          // 分销商ID, 必填
	LandTitleLike   *string  `json:"landTitleLike,omitempty" validate:"omitempty,max=255"`   // 景区名称(模糊), 最多255个字符
	NotTypeList     []string `json:"notTypeList,omitempty" validate:"omitempty,max=10"`      // 需要排除的产品类型, 最多10个
	OnlyTransfer    *int     `json:"onlyTransfer,omitempty" validate:"omitempty,oneof=0 1"`  // 是否仅查询转分销 0:查全部 1:仅查转分销
	PageNum         int      `json:"pageNum" validate:"required,min=1"`                      // 页码, 必填, 大于等于1
	PageSize        int      `json:"pageSize" validate:"required,min=1,max=200"`             // 条数, 必填, 1到200之间
	ProductTypeList []string `json:"productTypeList,omitempty" validate:"omitempty,max=10"`  // 产品类型, 最多10个
	QueryExpiration *bool    `json:"queryExpiration,omitempty"`                              // 显示过期门票 false:不显示 true:显示
	QueryTotal      int      `json:"queryTotal" validate:"required,oneof=0 1 2"`             // 分页总数查询类型
	Sid             *int     `json:"sid" validate:"required,min=1"`                          // 供应商ID
	TicketTitleLike *string  `json:"ticketTitleLike,omitempty" validate:"omitempty,max=255"` // 门票名称(模糊), 最多255个字符
	TidList		 []int    `json:"tidList,omitempty" validate:"omitempty,dive,min=1"`       // 门票id, 最多200个
}

type TicketAndInvalidVO struct {
	Aids         *string `json:"aids,omitempty"`         // 分销用户链
	ChainChannel *string `json:"chainChannel,omitempty"` // 分销链上的销售渠道配置
	ChainId      *int64  `json:"chainId,omitempty"`      // 分销链ID
	Fid          *int    `json:"fid,omitempty"`          // 分销商ID
	GroupIds     *string `json:"groupIds,omitempty"`     // 分销分组链
	LandTitle    *string `json:"landTitle,omitempty"`    // 景区名称
	Lid          *int    `json:"lid,omitempty"`          // 景区ID
	Lvl          *int    `json:"lvl,omitempty"`          // 分销层级
	Pid          *int    `json:"pid,omitempty"`          // 商品ID
	ProductType  *string `json:"productType,omitempty"`  // 产品类型
	Sid          *int    `json:"sid,omitempty"`          // 供应商ID
	SourceId     *int    `json:"sourceId,omitempty"`     // 顶级供应商ID
	TicketStatus *int    `json:"ticketStatus,omitempty"` // 门票状态
	TicketTitle  *string `json:"ticketTitle,omitempty"`  // 门票名称
	Tid          *int    `json:"tid,omitempty"`          // 门票ID
}

func QueryPlatformTicketAndInvalidByPaging(reqo PlatformTicketAndInvalidQuery) ([]TicketAndInvalidVO, int, error) {
	rows, total, err := java.SpecialPost("/distribution-center/platformSaleList/queryPlatformTicketAndInvalidByPaging", reqo)
	if err != nil {
		return nil, 0, szerrors.NewRemoteError(err)
	}
	var data []TicketAndInvalidVO
	err = utils.JsonConvertor(rows, &data)
	if err != nil {
		return nil, 0, szerrors.NewRemoteError(err)
	}
	return data, total, nil
}
