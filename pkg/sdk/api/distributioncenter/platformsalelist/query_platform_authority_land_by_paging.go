package platformsalelist

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

type PlatformAuthorityLandQuery struct {
	Fid             int      `json:"fid" validate:"required,min=1"`                         // 分销商ID, 必填
	LandTitleLike   *string  `json:"landTitleLike,omitempty" validate:"omitempty,max=255"`  // 景区名称(模糊), 最多255个字符
	LidList         []int    `json:"lidList,omitempty" validate:"omitempty,dive,min=1"`     // 景区id, 最多200个
	NotLidList      []int    `json:"notLidList,omitempty" validate:"omitempty,dive,min=1"`  // 需要排除的景区id, 最多200个
	NotTypeList     []string `json:"notTypeList,omitempty" validate:"omitempty,max=10"`     // 需要排除的产品类型, 最多10个
	OnlyTransfer    *int     `json:"onlyTransfer,omitempty" validate:"omitempty,oneof=0 1"` // 是否仅查询转分销 0:查全部 1:仅查转分销
	PageNum         int      `json:"pageNum" validate:"required,min=1"`                     // 页码, 必填, 大于等于1
	PageSize        int      `json:"pageSize" validate:"required,min=1,max=200"`            // 条数, 必填, 1到200之间
	ProductType     *string  `json:"productType,omitempty" validate:"omitempty,max=1"`      // 产品类型, 最多1个字符
	QueryExpiration *bool    `json:"queryExpiration,omitempty"`                             // 显示过期门票 false:不显示 true:显示
	QueryTotal      int      `json:"queryTotal" validate:"required,oneof=0 1 2"`            // 分页总数查询类型
	Sid             *int     `json:"sid" validate:"required,min=1"`                         // 供应商ID
}

type SaleDistinctLandVO struct {
	Fid         *int    `json:"fid,omitempty"`         // 分销商ID
	LandTitle   *string `json:"landTitle,omitempty"`   // 景区名称
	Lid         *int    `json:"lid,omitempty"`         // 景区ID
	ProductType *string `json:"productType,omitempty"` // 产品类型
	SidArr      *string `json:"sidArr,omitempty"`      // 供应商id, 多个用逗号隔开（只返回部分供应商）
	SourceId    *int    `json:"sourceId,omitempty"`    // 顶级供应商id
}

func QueryPlatformAuthorityLandByPaging(reqo PlatformAuthorityLandQuery) ([]SaleDistinctLandVO, int, error) {
	rows, total, err := java.SpecialPost("/distribution-center/platformSaleList/queryPlatformAuthorityLandByPaging", reqo)
	if err != nil {
		return nil, 0, szerrors.NewRemoteError(err)
	}
	var data []SaleDistinctLandVO
	err = utils.JsonConvertor(rows, &data)
	if err != nil {
		return nil, 0, szerrors.NewRemoteError(err)
	}
	return data, total, nil
}
