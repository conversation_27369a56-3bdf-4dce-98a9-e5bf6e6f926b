package platformsalelist

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

//type PlatformTicketQuery struct {
//	//ExpirationType   int      `json:"expirationType,omitempty"`                                   // 是否显示过期门票 1-已过期 2-未过期
//	Fid        int      `json:"fid" validate:"required,min=1"`                        // 商户ID, 必填
//	LidList    []int    `json:"lidList,omitempty" validate:"omitempty,dive,min=1"`    // 景区id, 最多200个
//	NotLidList []int    `json:"notLidList,omitempty" validate:"omitempty,dive,min=1"` // 需要排除的景区id, 最多200个
//	NotTypes   []string `json:"notPTypes,omitempty" validate:"omitempty,max=10"`      // 需要排除的产品类型, 最多10个
//	PageNum    int      `json:"pageNum" validate:"required,min=1"`                    // 页码, 必填, 大于等于1
//	PageSize   int      `json:"pageSize" validate:"required,min=1,max=200"`           // 条数, 必填, 1到200之间
//	//SupplyType       int      `json:"supplyType,omitempty" validate:"omitempty,max=10"`           // 1-自供应  2-转分销
//	TicketName       *string `json:"ticketName,omitempty" validate:"omitempty,max=255"`          // 门票名称(模糊), 最多255个字符
//	TicketStatusList []int   `json:"ticketStatusList,omitempty" validate:"omitempty,dive,min=1"` // 门票状态 1=在售的门票 2=下架的门票 6=已删除的门票 -1=不限
//}

type TicketRows struct {
	Current *int                    `json:"current,omitempty"` // 当前页
	Pages   *int                    `json:"pages,omitempty"`   // 总页
	Records []TicketRowsRecordsItem `json:"records,omitempty"` // 门票数据
	Size    *int                    `json:"size,omitempty"`    // 页数
	Total   *int                    `json:"total,omitempty"`   // 总数
}

type TicketRowsRecordsItem struct {
	TicketName *string `json:"ticketName,omitempty"` // 门票名称
	Tid        *int    `json:"tid,omitempty"`        // 门票ID
}

func QueryTicketNameListByPaging(reqo map[string]interface{}) ([]TicketRowsRecordsItem, int, error) {
	rows, _, err := java.SpecialPost("/distribution-center/feign-client/distributionQuery/queryTicketNameList", reqo)
	if err != nil {
		return nil, 0, szerrors.NewRemoteError(err)
	}
	//这里要注意，rows 是嵌套的，page和total在嵌套里面，解完取第一个子元素
	var data []TicketRows
	err = utils.JsonConvertor(rows, &data)
	if err != nil {
		return nil, 0, szerrors.NewRemoteError(err)
	}
	if len(data) == 0 {
		return nil, 0, nil
	}
	return data[0].Records, *data[0].Total, nil
}
