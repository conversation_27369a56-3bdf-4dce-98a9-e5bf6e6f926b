package admin

import (
	"errors"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

// DoQuerySaleSkuParams 定义了销售列表查询的所有参数
type DoQuerySaleSkuParams struct {
	Active                 int      `json:"active,omitempty"`                 // 分销链的向下转分销开关 0:关闭 1:开启
	Channel                int      `json:"channel,omitempty"`                // 销售渠道，取值范围 1-50
	ChannelList            []int    `json:"channelList,omitempty"`            // 销售渠道列表，最多包含 5 个整数
	CityCode               int      `json:"cityCode,omitempty"`               // 城市编码，最小值为 1
	FidList                []int    `json:"fidList,omitempty"`                // 分销商 ID 列表，非必须字段
	LandTitleLike          string   `json:"landTitleLike,omitempty"`          // 景区名称模糊搜索，最大长度 255 字符
	Lid                    int      `json:"lid,omitempty"`                    // 景区ID，必填，最小值为 1
	LidList                []int    `json:"lidList,omitempty"`                // 景区 ID 列表，最多包含 50 个整数
	NotLidList             []int    `json:"notLidList,omitempty"`             // 排除的景区 ID 列表，最多包含 50 个整数
	NotTidList             []int    `json:"notTidList,omitempty"`             // 排除的门票 ID 列表，最多包含 1000 个整数
	NotTypeList            []string `json:"notTypeList,omitempty"`            // 排除的产品类型列表，最多包含 10 个字符串
	OnlyTransfer           int      `json:"onlyTransfer,omitempty"`           // 是否仅查询转分销 0:查全部 1:仅查转分销
	OrderFlag              int      `json:"orderFlag,omitempty"`              // 报团标识 0:非报团 1:报团
	Oversea                int      `json:"oversea,omitempty"`                // 境内外标识 0:境内 1:境外
	PageNum                int      `json:"pageNum,omitempty"`                // 页码，必填项，最小值为 1
	PageSize               int      `json:"pageSize,omitempty"`               // 每页条数，必填项，取值范围 1-200
	Pid                    int      `json:"pid,omitempty"`                    // 商品 ID，最小值为 1
	PidList                []int    `json:"pidList,omitempty"`                // 商品 ID 列表，最多包含 100 个整数
	PlayDate               string   `json:"playDate,omitempty"`               // 游玩日期，格式为 yyyy-MM-dd
	ProductNameLike        string   `json:"productNameLike,omitempty"`        // 商品名称模糊搜索，最大长度 512 字符
	ProductType            string   `json:"productType,omitempty"`            // 产品类型，最多一个字符 A=景点，B=线路等
	ProductTypeList        []string `json:"productTypeList,omitempty"`        // 产品类型列表，最多包含 10 个字符串
	ProvinceCode           int      `json:"provinceCode,omitempty"`           // 省份编码，最小值为 1
	QueryBasePrice         int      `json:"queryBasePrice,omitempty"`         // 查询基础价格 0:不查询 1:查询今天价格 2:查询最近价格
	QueryDistributionPrice int      `json:"queryDistributionPrice,omitempty"` // 查分销差价 0:不查询 1:本级差价 2:总差价
	QueryExpiration        bool     `json:"queryExpiration,omitempty"`        // 是否显示过期门票 false:不显示 true:显示
	QueryTotal             int      `json:"queryTotal"`                       // 分页总数查询类型 0:不返回总数 1:只返回第一页的总数 2:每一页都返回总数
	Sid                    int      `json:"sid,omitempty"`                    // 供应商ID，必填，最小值为 1
	SidList                []int    `json:"sidList,omitempty"`                // 分销商ID列表，非必须，最小值为 1
	SourceId               int      `json:"sourceId,omitempty"`               // 顶级供应商ID，最小值为 1
	TicketActive           int      `json:"ticketActive,omitempty"`           // 是否只显示在线支付产品 0:不允许 1:允许
	TicketChannel          int      `json:"ticketChannel,omitempty"`          // 门票上的销售渠道，取值范围 1-50
	TicketStatus           int      `json:"ticketStatus,omitempty"`           // 门票状态 1:上架 2:下架 6:删除
	TicketTitleLike        string   `json:"ticketTitleLike,omitempty"`        // 门票名称模糊搜索，最大长度 255 字符
	TidList                []int    `json:"tidList,omitempty"`                // 门票 ID 列表，最多包含 100 个整数
}

// SaleSkuListResponseItem 表示有效和无效门票的具体信息
type SaleSkuListResponseItem struct {
	Aids         string `json:"aids,omitempty"`         // 分销用户链
	ChainChannel string `json:"chainChannel,omitempty"` // 分销链上的销售渠道配置
	ChainId      int64  `json:"chainId,omitempty"`      // 分销链ID
	Fid          int    `json:"fid,omitempty"`          // 分销商ID
	GroupIds     string `json:"groupIds,omitempty"`     // 分销分组链
	LandTitle    string `json:"landTitle,omitempty"`    // 景区名称
	Lid          int    `json:"lid,omitempty"`          // 景区ID
	Lvl          int    `json:"lvl,omitempty"`          // 分销层级
	Pid          int    `json:"pid,omitempty"`          // 商品ID
	ProductType  string `json:"productType,omitempty"`  // 产品类型 A=景点，B=线路，C=酒店等
	Sid          int    `json:"sid,omitempty"`          // 供应商ID
	SourceId     int    `json:"sourceId,omitempty"`     // 顶级供应商ID
	TicketStatus int    `json:"ticketStatus,omitempty"` // 门票状态
	TicketTitle  string `json:"ticketTitle,omitempty"`  // 门票名称
	Tid          int    `json:"tid,omitempty"`          // 门票ID
}

type ResponseSaleSkuPaginate struct {
	java.ResponseCommonPaginateData
	Data ResponseSaleSkuPaginateData `json:"data"`
}

type ResponseSaleSkuPaginateData struct {
	Records []SaleSkuListResponseItem `json:"records"`
	Total   int                       `json:"total"`
	Size    int                       `json:"size"`
	Current int                       `json:"current"`
	Pages   int                       `json:"pages"`
}

// QuerySaleSkuListByMoreParamsPaging 多商户门票列表分页查询（包含下架产品）接口文档：http://yapi.12301.test/project/245/interface/api/38890
func QuerySaleSkuListByMoreParamsPaging(params DoQuerySaleSkuParams) (list []SaleSkuListResponseItem, total int, err error) {
	list = make([]SaleSkuListResponseItem, 0)
	total = 0
	if params.PageSize > 200 {
		err = errors.New("分页大小超过限制[200]")
		return
	}
	if params.PageSize < 1 {
		params.PageSize = 10
	}
	if params.PageNum < 1 {
		params.PageNum = 1
	}
	response, err := java.OriginalPost("/distribution-center/admin/SaleList/queryCommonFidListTicketAndInvalidByPaging", params)
	if err != nil {
		err = szerrors.NewRemoteError(err)
		return
	}
	var res ResponseSaleSkuPaginate
	err = utils.JsonConvertor(response, &res)
	if err != nil {
		return nil, total, szerrors.NewRemoteError(err)
	}
	if res.Code != "200" {
		err = szerrors.NewRemoteError(errors.New(res.Message))
		return
	}
	if len(res.Data.Records) == 0 {
		return
	}
	list = res.Data.Records
	total = res.Data.Total

	return
}
