package admin

import (
	"errors"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/szerrors"
	"report-service/pkg/utils"
)

// DoQuerySaleSpuParams 定义了分销中心商品分页查询的参数集合。
type DoQuerySaleSpuParams struct {
	Channel          int      `json:"channel,omitempty"`          // 销售渠道，整数类型，取值范围 1-50。
	ChannelList      []int    `json:"channelList,omitempty"`      // 销售渠道列表，最多包含 5 个整数。
	CityCode         int      `json:"cityCode,omitempty"`         // 城市编码，用于筛选城市。
	FidList          []int    `json:"fidList,omitempty"`          // 分销商 ID 列表，非必须字段。
	LandTitleLike    string   `json:"landTitleLike,omitempty"`    // 景区名称模糊搜索，最大长度 255 字符。
	Level            int      `json:"level,omitempty"`            // 产品级别（如酒店星级），取值范围 1-5。
	Lid              int      `json:"lid,omitempty"`              // 景区 ID，最小值为 1。
	LidList          []int    `json:"lidList,omitempty"`          // 景区 ID 列表，最多包含 50 个整数。
	NotCityCode      int      `json:"notCityCode,omitempty"`      // 排除的城市编码，最小值为 1。
	NotLidList       []int    `json:"notLidList,omitempty"`       // 排除的景区 ID 列表，最多包含 50 个整数。
	NotPidList       []int    `json:"notPidList,omitempty"`       // 排除的商品 ID 列表，最多包含 200 个整数。
	NotSubType       int      `json:"notSubType,omitempty"`       // 排除的产品自类型过滤参数。
	NotType          string   `json:"notType,omitempty"`          // 排除的产品类型，最多一个字符。
	NotTypeList      []string `json:"notTypeList,omitempty"`      // 排除的产品类型列表，最多包含 10 个字符串。
	OnlyTransfer     int      `json:"onlyTransfer,omitempty"`     // 是否仅查询转分销，0 表示查全部，1 表示仅查转分销。
	OrderFlag        int      `json:"orderFlag,omitempty"`        // 报团标识，0 表示非报团，1 表示报团。
	Oversea          int      `json:"oversea,omitempty"`          // 境内外标识，0 表示境内，1 表示境外。
	PageNum          int      `json:"pageNum,omitempty"`          // 页码，必填项，最小值为 1。
	PageSize         int      `json:"pageSize,omitempty"`         // 每页条数，必填项，取值范围 1-50。
	Pid              int      `json:"pid,omitempty"`              // 商品 ID，最小值为 1。
	PidList          []int    `json:"pidList,omitempty"`          // 商品 ID 列表，最多包含 100 个整数。
	PlayDate         string   `json:"playDate,omitempty"`         // 游玩日期，格式为 yyyy-MM-dd。
	PoiIdList        []int    `json:"poiIdList,omitempty"`        // POI ID 列表，最多包含 50 个整数。
	PoiType          int      `json:"poiType,omitempty"`          // POI 类型，1 表示景区。
	ProductType      string   `json:"productType,omitempty"`      // 产品类型，最多一个字符，可选 A=景点，B=线路，C=酒店等。
	ProductTypeList  []string `json:"productTypeList,omitempty"`  // 产品类型列表，最多包含 10 个字符串。
	ProvinceCode     int      `json:"provinceCode,omitempty"`     // 省份编码，最小值为 1。
	QueryExpiration  bool     `json:"queryExpiration,omitempty"`  // 是否显示过期门票，false 不显示，true 显示。
	QueryTotal       int      `json:"queryTotal"`                 // 分页总数查询类型，0 表示不返回总数，1 表示只返回第一页的总数，2 表示每一页都返回总数。
	ResourceId       int      `json:"resourceId,omitempty"`       // 资源库 ID，最小值为 1。
	Sid              int      `json:"sid,omitempty"`              // 供应商 ID，最小值为 1。
	SidList          []int    `json:"sidList,omitempty"`          // 供应商 ID 列表，最多包含 20 个整数。
	SourceId         int      `json:"sourceId,omitempty"`         // 顶级供应商 ID，最小值为 1。
	SubType          int      `json:"subType,omitempty"`          // 产品子类型，计时类型：1=游船计时，2=滑雪场计时。
	SubTypeList      []int    `json:"subTypeList,omitempty"`      // 产品子类型列表，最多包含 5 个整数。
	SupplierName     string   `json:"supplierName,omitempty"`     // 供应商名称，最大长度 100 字符。
	SupplierNameLike string   `json:"supplierNameLike,omitempty"` // 供应商名称模糊搜索，最大长度 100 字符。
	TicketActive     int      `json:"ticketActive,omitempty"`     // 是否只显示在线支付产品，0 表示不允许向下分销，1 表示允许。
	TicketChannel    int      `json:"ticketChannel,omitempty"`    // 门票上的销售渠道，取值范围 1-50。
	TicketTitleLike  string   `json:"ticketTitleLike,omitempty"`  // 门票名称模糊搜索，最大长度 255 字符。
	Tid              int      `json:"tid,omitempty"`              // 门票 ID，最小值为 1。
	TidList          []int    `json:"tidList,omitempty"`          // 门票 ID 列表，最多包含 100 个整数。
	Topic            string   `json:"topic,omitempty"`            // 旅游主题，最大长度 600 字符。
}

// SaleSpuListResponseItem 销售列表的景区信息，合并不同供应商的景区数据
type SaleSpuListResponseItem struct {
	Fid         int    `json:"fid,omitempty"`         // 分销商ID
	LandTitle   string `json:"landTitle,omitempty"`   // 景区名称
	Lid         int    `json:"lid,omitempty"`         // 景区ID
	ProductType string `json:"productType,omitempty"` // 产品类型 A=景点，B=线路，C=酒店等
	SourceId    int    `json:"sourceId,omitempty"`    // 顶级供应商ID
}

type ResponseSaleSpuPaginate struct {
	java.ResponseCommonPaginateData
	Data ResponseSaleSpuPaginateData `json:"data"`
}

type ResponseSaleSpuPaginateData struct {
	Records []SaleSpuListResponseItem `json:"records"`
	Total   int                       `json:"total"`
	Size    int                       `json:"size"`
	Current int                       `json:"current"`
	Pages   int                       `json:"pages"`
}

// QuerySaleSpuListByMoreParamsPaging 多商户产品列表分页查询（包含下架产品） 接口文档：http://yapi.12301.test/project/245/interface/api/38886
func QuerySaleSpuListByMoreParamsPaging(params DoQuerySaleSpuParams) (list []SaleSpuListResponseItem, total int, err error) {
	list = make([]SaleSpuListResponseItem, 0)
	total = 0
	if params.PageSize > 50 {
		err = errors.New("分页大小超过限制[50]")
		return
	}
	if params.PageSize < 1 {
		params.PageSize = 10
	}
	if params.PageNum < 1 {
		params.PageNum = 1
	}
	response, err := java.OriginalPost("/distribution-center/admin/SaleList/queryCommonFidListDistinctLandAndInvalidByPaging", params)
	if err != nil {
		err = szerrors.NewRemoteError(err)
		return
	}
	var res ResponseSaleSpuPaginate
	err = utils.JsonConvertor(response, &res)
	if err != nil {
		err = szerrors.NewRemoteError(err)
		return
	}
	if res.Code != "200" {
		err = szerrors.NewRemoteError(errors.New(res.Message))
		return
	}
	if len(res.Data.Records) == 0 {
		return
	}
	list = res.Data.Records
	total = res.Data.Total

	return
}
