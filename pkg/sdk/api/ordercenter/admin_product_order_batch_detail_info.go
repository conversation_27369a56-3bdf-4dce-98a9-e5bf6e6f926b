package ordercenter

import (
	"errors"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// ProductOrderDetailDTO 产品订单详情数据
type ProductOrderDetailDTO struct {
	BizOrderStatus    int                    `json:"bizOrderStatus"`    // 业务订单状态
	BusinessInfo      map[string]interface{} `json:"businessInfo"`      // 订单业务信息
	BuyerId           int                    `json:"buyerId"`           // 购买用户ID
	Chains            []ProductOrderChain    `json:"chains"`            // 订单销售链
	EndDate           string                 `json:"endDate"`           // 有效期-结束时间
	Extra             string                 `json:"extra"`             // 附加信息
	FirstSaleChannel  int                    `json:"firstSaleChannel"`  // 一级销售渠道
	FirstSupplierId   int                    `json:"firstSupplierId"`   // 一级(顶级)供应商ID
	Id                int                    `json:"id"`                // ID
	IsCombineTrade    string                 `json:"isCombineTrade"`    // 是否为合并交易订单,0:否,1:是
	ItemNum           int                    `json:"itemNum"`           // 商品购买数量
	ItemSku           int                    `json:"itemSku"`           // 商品SKU_ID
	ItemSpu           int                    `json:"itemSpu"`           // 商品SPU_ID
	ItemStoreId       int                    `json:"itemStoreId"`       // 商品STORE_ID
	ItemType          string                 `json:"itemType"`          // 商品类型
	MainOrderId       string                 `json:"mainOrderId"`       // 主订单号（子票才有）
	OrderMethod       int                    `json:"orderMethod"`       // 下单方式
	OrderStatus       int                    `json:"orderStatus"`       // 中台订单状态
	OrderTime         string                 `json:"orderTime"`         // 下单时间
	OrderType         int                    `json:"orderType"`         // 订单类型：0.主单 1.子单
	PayAmount         int                    `json:"payAmount"`         // 实付金额(分)
	PayId             string                 `json:"payId"`             // 支付号
	PayStatus         int                    `json:"payStatus"`         // 支付状态
	PayTime           string                 `json:"payTime"`           // 支付时间
	Peoples           []OrderRelationPeople  `json:"peoples"`           // 订单关联人员
	PlayDate          string                 `json:"playDate"`          // 游玩时间
	ProductOrderId    string                 `json:"productOrderId"`    // 产品订单号
	SecondSaleChannel int                    `json:"secondSaleChannel"` // 二级销售渠道
	SellerId          int                    `json:"sellerId"`          // 卖方用户ID
	SkuTime           string                 `json:"skuTime"`           // 商品价格时间
	StartDate         string                 `json:"startDate"`         // 有效期-开始时间
	TradeOrderId      string                 `json:"tradeOrderId"`      // 交易订单号
}

// ProductOrderChain 产品订单销售链
type ProductOrderChain struct {
	BuyerId            int        `json:"buyerId"`            // 买家ID
	GroupId            int        `json:"groupId"`            // 分组ID
	Id                 int        `json:"id"`                 // ID
	Lvl                int        `json:"lvl"`                // 所处的级别
	MemberRelationship int        `json:"memberRelationship"` // 订单会员关系
	OrderId            string     `json:"orderId"`            // 订单号
	PayMode            int        `json:"payMode"`            // 支付方式
	Prices             []SkuPrice `json:"prices"`             // 日历价格
	SellerId           int        `json:"sellerId"`           // 卖家ID
}

// SkuPrice 产品SKU日历价格
type SkuPrice struct {
	CostPrice int    `json:"costPrice"` // 成本价(分)
	SalePrice int    `json:"salePrice"` // 出售价(分)
	SkuTime   string `json:"skuTime"`   // 时间
}

// OrderRelationPeople 订单关联人员
type OrderRelationPeople struct {
	Extra          map[string]interface{} `json:"extra"`          // 附加信息
	Id             int                    `json:"id"`             // 表id
	IdentityCode   string                 `json:"identityCode"`   // 证件号
	IdentityName   string                 `json:"identityName"`   // 证件姓名
	IdentityType   int                    `json:"identityType"`   // 证件类型
	MobileArea     string                 `json:"mobileArea"`     // 手机区号
	PeopleType     int                    `json:"peopleType"`     // 人员类型
	Phone          string                 `json:"phone"`          // 联系电话
	ProductOrderId string                 `json:"productOrderId"` // 产品订单号
}

// SimpleResult 简单结果
type SimpleResult struct {
	Code    string                  `json:"code"`    // 状态，200 成功 非200 都是失败
	Data    []ProductOrderDetailDTO `json:"data"`    // 数据
	Message string                  `json:"message"` // 错误消息，非200都有消息
}

// AdminProductOrderBatchDetailInfo 批量获取产品订单详情
func AdminProductOrderBatchDetailInfo(centreOrderNos []string) (*SimpleResult, error) {
	res, err := java.OriginalPost("/order-center/admin/product-order/batch-detail-info", centreOrderNos)
	if err != nil {
		return nil, err
	}

	var result SimpleResult
	err = utils.JsonConvertor(res, &result)
	if err != nil {
		return nil, err
	}

	// 增加错误判断
	if result.Code != "200" {
		return nil, errors.New(result.Message)
	}

	return &result, nil
}

// GetProductOrderToTradeOrderMap 获取产品订单号到交易订单号的映射
func GetProductOrderToTradeOrderMap(productOrderIds []string) (map[string]string, error) {
	// 调用批量获取产品订单详情接口
	result, err := AdminProductOrderBatchDetailInfo(productOrderIds)
	if err != nil {
		return nil, err
	}

	// 创建映射
	orderMap := make(map[string]string)
	for _, orderDetail := range result.Data {
		orderMap[orderDetail.ProductOrderId] = orderDetail.TradeOrderId
	}

	return orderMap, nil
}
