package ticketservice

import "report-service/pkg/utils"

type UuJqTicketDTO struct {
	Id     int    `json:"id"`
	LandId int    `json:"landid"`
	Title  string `json:"title"`
	Status int    `json:"status"`
}

type UuLandDTO struct {
	Id       int    `json:"id"`
	Title    string `json:"title"`
	PType    string `json:"pType"`
	SubType  int    `json:"subType"`
	SalerId  int    `json:"salerid"`
	ApplyDid int    `json:"applyDid"`
}

func QueryTicketTitleByIds(ticketIds []int) (map[int]string, error) {
	ticketIds = utils.RemoveDuplicate(ticketIds)
	var result []UuJqTicketDTO
	var err error
	result, err = QueryUuJqTicketByIds(ticketIds)
	if err != nil {
		return nil, err
	}
	var data = make(map[int]string)
	for _, item := range result {
		data[item.Id] = item.Title
	}
	return data, nil
}
