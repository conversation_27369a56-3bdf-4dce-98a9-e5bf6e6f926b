package ticketservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type TicketData struct {
	Confs                    interface{} `json:"confs"`
	ThridTicketAttributesDTO interface{} `json:"thridTicketAttributesDTO"`
	UuJqTicketDTO            interface{} `json:"uuJqTicketDTO"`
	UuLandDTO                interface{} `json:"uuLandDTO"`
	UuLandFDTO               interface{} `json:"uuLandFDTO"`
	UuProductDTO             interface{} `json:"uuProductDTO"`
}

func QueryTicketInfoByIds(ticketIds []int) ([]TicketData, error) {
	data, err := java.BatchPost("/web/v1/product/ticketService/queryTicketInfoByIds", map[string]interface{}{
		"ticketIds": ticketIds,
	}, "ticketIds", 200, true)
	if err != nil {
		return nil, err
	}

	var result []TicketData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func QueryLandInfoByTicketIds(ticketIds []int) ([]UuLandDTO, error) {
	ticketIds = utils.RemoveDuplicate(ticketIds)
	var result []TicketData
	var err error
	result, err = QueryTicketInfoByIds(ticketIds)
	if err != nil {
		return nil, err
	}
	var data []UuLandDTO
	for _, item := range result {
		var itemUuLandDTO UuLandDTO
		err = utils.JsonConvertor(item.UuLandDTO, &itemUuLandDTO)
		if err != nil {
			continue
		}
		data = append(data, itemUuLandDTO)
	}

	return data, nil
}
