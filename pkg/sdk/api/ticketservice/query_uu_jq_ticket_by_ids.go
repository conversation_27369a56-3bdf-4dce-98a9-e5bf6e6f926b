package ticketservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

func QueryUuJqTicketByIds(ticketIds []int) ([]UuJqTicketDTO, error) {
	ticketIds = utils.RemoveDuplicate(ticketIds)
	data, err := java.BatchPost("/web/v1/product/ticketService/queryUuJqTicketByIds", map[string]interface{}{
		"ticketIds": ticketIds,
	}, "ticketIds", 200, true)
	if err != nil {
		return nil, err
	}
	var result []UuJqTicketDTO
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
