package ordertrackqueryservice

//	{
//	   "code": 200,
//	   "msg": "success",
//	   "data": {
//	       "action": 4,
//	       "applyDid": 6970,
//	       "branchterminal": 0,
//	       "extContent": "{\"can_take\":\"1\",\"can_refund\":\"1\"}",
//	       "id": 66985187,
//	       "idCard": "",
//	       "inserttime": "2024-03-06 17:07:26",
//	       "leftNum": 1,
//	       "msg": "",
//	       "operMember": 0,
//	       "orderMonth": 3,
//	       "orderTime": 1709716037,
//	       "ordernum": "70971603714885",
//	       "salerid": 0,
//	       "source": 7,
//	       "syncState": 1,
//	       "terminal": 0,
//	       "tid": 110035973,
//	       "tnum": 1,
//	       "updateTime": 0
//	   },
//	   "inside_error": ""
//	}
type OrderTrackInfoData struct {
	Action         int    `json:"action"`
	ApplyDid       int    `json:"applyDid"`
	BranchTerminal int    `json:"branchterminal"`
	ExtContent     string `json:"extContent"`
	ID             int    `json:"id"`
	IDCard         string `json:"idCard"`
	InsertTime     string `json:"inserttime"`
	LeftNum        int    `json:"leftNum"`
	Msg            string `json:"msg"`
	OperMember     int    `json:"operMember"`
	OrderMonth     int    `json:"orderMonth"`
	OrderTime      int    `json:"orderTime"`
	OrderNum       string `json:"ordernum"`
	SalerID        int    `json:"salerid"`
	Source         int    `json:"source"`
	SyncState      int    `json:"syncState"`
	Terminal       int    `json:"terminal"`
	TID            int    `json:"tid"`
	TNum           int    `json:"tnum"`
	UpdateTime     int    `json:"updateTime"`
}

type OrderTrackInfoDataPage struct {
	List  []OrderTrackInfoData `json:"list"`
	Total int                  `json:"total"`
}
