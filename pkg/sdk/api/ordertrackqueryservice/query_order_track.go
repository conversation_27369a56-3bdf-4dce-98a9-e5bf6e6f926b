package ordertrackqueryservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

// 请求参数
// page  string[] //页码
// size  string[] //页大小
// forceMainDb  string[] //是否查询主库
// ordernumIn  string[] //订单号
// actionIn  int[] //变更状态不在
// sourceIn  string[] //操作来源
// actionNotIn  int[] //变更状态不在
// operMemberIn  int[] //操作人
// tidIn  int[] //票id
// minInsertTime  date //插入时间起始
// maxInsertTime  date //插入时间截止
// tnumGreaterEqThan  int //tnum 大于等于
// sortBy string[] 排序参数
// desc bool 是否倒序排序
func QueryOrderTrackByParamsPage(javaParams map[string]interface{}) (*OrderTrackInfoDataPage, error) {
	data, err := java.Post("/web/v1/orderquery/orderTrackQueryService/findByParamsPage", javaParams)
	if err != nil {
		return nil, err
	}

	var result *OrderTrackInfoDataPage
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
