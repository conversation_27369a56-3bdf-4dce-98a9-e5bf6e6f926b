package ordertrackqueryservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

func QueryOrderTrackFirstInfo(orderNo string, action int) (*OrderTrackInfoData, error) {
	var javaParams = map[string]interface{}{
		"ordernumIn": []string{orderNo},
		"actionIn":   []int{action},
	}
	data, err := java.Post("/web/v1/orderquery/orderTrackQueryService/findFirstByParams", javaParams)
	if err != nil {
		return nil, err
	}

	var result OrderTrackInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
