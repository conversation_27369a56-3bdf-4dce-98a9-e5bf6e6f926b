package orderqueryservice

import (
	"encoding/json"
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"report-service/internal/global"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
	"strings"
)

type OrderInfoData struct {
	Addon         interface{} `json:"addon"`
	Aid           int         `json:"aid"`
	ApplyDid      int         `json:"applyDid"`
	ApplyInfo     interface{} `json:"applyInfo"`
	BeginTime     string      `json:"begintime"`
	EndTime       string      `json:"endtime"`
	Code          int         `json:"code"`
	ContactTel    string      `json:"contacttel"`
	FxDetails     interface{} `json:"fxDetails"`
	Id            int         `json:"id"`
	SpuId         int         `json:"lid"`
	Member        int         `json:"member"`
	OrderTimeDate string      `json:"ordertime"`
	OrderTime     int         `json:"orderTime"`
	OrderMode     int         `json:"ordermode"`
	OrderName     string      `json:"ordername"`
	OrderNum      string      `json:"ordernum"`
	OrderTel      string      `json:"ordertel"`
	PayStatus     int         `json:"payStatus"`
	PayTime       int         `json:"payTime"`
	PayMode       int         `json:"paymode"`
	PersonId      string      `json:"personid"`
	Pid           int         `json:"pid"`
	Playtime      string      `json:"playtime"`
	ProductType   string      `json:"productType"`
	RemoteNum     string      `json:"remotenum"`
	ReMsg         int         `json:"remsg"`
	SalerId       int         `json:"salerid"`
	Status        int         `json:"status"`
	SkuId         int         `json:"tid"`
	TNum          int         `json:"tnum"`
	TotalMoney    int         `json:"totalmoney"`
	TPrice        int         `json:"tprice"`
	TradeOrderId  string      `json:"tradeOrderId"`
}

type AddonData struct {
	ApplyDid     int    `json:"applyDid"`
	Id           int    `json:"id"`
	IfPack       int    `json:"ifpack"`
	IfPrint      int    `json:"ifprint"`
	LinkOrderNum string `json:"linkOrdernum"`
	OrderId      string `json:"orderid"`
	PackOrder    string `json:"packOrder"`
	TorderNum    string `json:"tordernum"`
}

type ApplyInfoData struct {
	AddTime      string `json:"addtime"`
	ApplyId      int    `json:"applyId"`
	APrice       int    `json:"aprice"`
	CanRefund    int    `json:"canRefund"`
	CanTake      int    `json:"canTake"`
	CounterPrice int    `json:"counterPrice"`
	CurrentNum   int    `json:"currentNum"`
	Flag         int    `json:"flag"`
	Id           int    `json:"id"`
	LPrice       int    `json:"lprice"`
	OrderTime    int    `json:"orderTime"`
	OrderId      string `json:"orderid"`
	OriginNum    int    `json:"originNum"`
	Pid          int    `json:"pid"`
	Playtime     string `json:"playtime"`
	RefundNum    int    `json:"refundNum"`
	SalePrice    int    `json:"salePrice"`
	VerifiedNum  int    `json:"verifiedNum"`
}

type FxDetailsData struct {
	Aids       string `json:"aids"`
	AidsMoney  string `json:"aidsMoney"`
	AidsPrice  string `json:"aidsPrice"`
	ApplyDid   int    `json:"applyDid"`
	ConcatId   string `json:"concatId"`
	ExtContent string `json:"extContent"`
	Id         int    `json:"id"`
	Memo       string `json:"memo"`
	NoticeExt  string `json:"noticeExt"`
	OfflineExt string `json:"offlineExt"`
	OrderId    string `json:"orderid"`
	Origin     string `json:"origin"`
	PartnerExt string `json:"partnerExt"`
	ProductExt string `json:"productExt"`
	Series     string `json:"series"`
}

type OrderDetailInfo struct {
	Playtime      string              `json:"playtime"`
	RemoteNum     string              `json:"remotenum"`
	OrderNum      string              `json:"ordernum"`
	BeginTime     string              `json:"begintime"`
	EndTime       string              `json:"endtime"`
	Status        int                 `json:"status"`
	OrderName     string              `json:"ordername"`
	OrderTel      string              `json:"ordertel"`
	PersonId      string              `json:"personid"`
	Memo          string              `json:"memo"`
	TorderNum     string              `json:"tordernum"`
	ExtContent    FxDetailsExtContent `json:"extContent"`
	OrderTimeDate string              `json:"ordertime"`
}
type FxDetailsExtContent struct {
	SectionTimeStr string `json:"sectionTimeStr"`
	FirstDTime     string `json:"firstDtime"`
	PSubType       int    `json:"pSubType"`
}

func CompleteOrderByOrderNos(orderNos []string) ([]OrderInfoData, error) {
	orderNos = utils.RemoveDuplicate(orderNos)
	data, err := java.BatchPost("/web/v1/orderquery/orderQueryService/completeOrder", map[string]interface{}{
		"orderNum": orderNos,
	}, "orderNum", 50, true)
	if err != nil {
		return nil, err
	}
	var result []OrderInfoData
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetOrderDetail(orderNos []string) (map[string]OrderDetailInfo, error) {
	res, err := CompleteOrderByOrderNos(orderNos)
	if err != nil {
		return nil, err
	}
	if res == nil || len(res) == 0 {
		return nil, nil
	}
	results := make(map[string]OrderDetailInfo)
	for _, info := range res {
		orderNo := info.OrderNum
		var fxDetail FxDetailsData
		err = utils.JsonConvertor(info.FxDetails, &fxDetail)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("order decode fx detail is error, order_no: %s, err:%s", orderNo, err))
			continue
		}
		var addonInfo AddonData
		err = utils.JsonConvertor(info.Addon, &addonInfo)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("order decode addon is error, order_no: %s, err:%s", orderNo, err))
			continue
		}
		var extContent FxDetailsExtContent
		//扩展信息json解析
		if fxDetail.ExtContent != "" {
			jsonErr := json.Unmarshal([]byte(fxDetail.ExtContent), &extContent)
			if jsonErr != nil {
				continue
			}
		}
		//解析三方订单号
		segments := strings.SplitN(addonInfo.TorderNum, "&", 2)
		torderNum := addonInfo.TorderNum
		if len(segments) > 1 {
			torderNum = segments[0]
		}
		results[info.OrderNum] = OrderDetailInfo{
			Playtime:      carbon.Parse(info.Playtime).ToDateString(),
			RemoteNum:     info.RemoteNum,
			OrderNum:      info.OrderNum,
			BeginTime:     carbon.Parse(info.BeginTime).ToDateString(),
			EndTime:       carbon.Parse(info.EndTime).ToDateString(),
			Status:        info.Status,
			OrderName:     info.OrderName,
			OrderTel:      info.OrderTel,
			PersonId:      info.PersonId,
			Memo:          fxDetail.Memo,
			ExtContent:    extContent,
			TorderNum:     torderNum,
			OrderTimeDate: info.OrderTimeDate,
		}
	}
	return results, nil
}
