package ordertouristtrackqueryservice

import (
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

func QueryCompositeTouristTrackByParam(actionIn []int, applyIds []int, insertTimeBegin int64, insertTimeEnd int64, page int, size int) (*CompositeTouristTrackResponse, error) {
	actionIn = utils.RemoveDuplicate(actionIn)
	applyIds = utils.RemoveDuplicate(applyIds)
	javaParams := make(map[string]interface{})
	javaParams["insertTimeBegin"] = insertTimeBegin
	javaParams["insertTimeEnd"] = insertTimeEnd
	if len(applyIds) > 0 {
		javaParams["applyIds"] = applyIds
	}
	if page > 0 && size > 0 {
		javaParams["page"] = page
		javaParams["size"] = size
	}
	data, err := java.Post("/web/v1/orderquery/orderTouristTrackQueryService/queryCompositeTouristTrackByParam", javaParams)
	if err != nil {
		return nil, err
	}
	var result CompositeTouristTrackResponse
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
