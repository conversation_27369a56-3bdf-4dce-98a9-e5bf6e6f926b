package ordertouristtrackqueryservice

type CompositeTouristTrackResponse struct {
	List     []TouristTrackListResponse `json:"list"`
	PageNum  int                        `json:"pageNum"`
	PageSize int                        `json:"pageSize"`
	Pages    int                        `json:"pages"`
	Total    int                        `json:"total"`
}

type OrderTouristTrackResponse struct {
	List     []TouristTrackListOrderTouristTrackResponse `json:"list"`
	PageNum  int                                         `json:"pageNum"`
	PageSize int                                         `json:"pageSize"`
	Pages    int                                         `json:"pages"`
	Total    int                                         `json:"total"`
}

type TouristTrackListResponse struct {
	OrderTouristInfo   *TouristTrackListOrderTouristInfoResponse   `json:"orderTouristInfo"`
	OrderTouristTrack  *TouristTrackListOrderTouristTrackResponse  `json:"orderTouristTrack"`
	OrderTouristExtend *TouristTrackListOrderTouristExtendResponse `json:"orderTouristExtend"`
}

type TouristTrackListOrderTouristInfoResponse struct {
	ApplyDid    int    `json:"applyDid"`
	CheckState  int    `json:"checkState"`
	CheckTime   int    `json:"checkTime"`
	ChkCode     string `json:"chkCode"`
	Id          int    `json:"id"`
	IdCard      string `json:"idcard"`
	Idx         int    `json:"idx"`
	IsChecked   int    `json:"isChecked"`
	Lid         int    `json:"lid"`
	MemberId    int    `json:"memberId"`
	Mobile      string `json:"mobile"`
	OrderId     string `json:"orderid"`
	PrintState  int    `json:"printState"`
	SyncState   int    `json:"syncState"`
	Tourist     string `json:"tourist"`
	VoucherType int    `json:"voucherType"`
}

type TouristTrackListOrderTouristTrackResponse struct {
	ApplyDid       int    `json:"applyDid"`
	BranchTerminal int    `json:"branchTerminal"`
	ExtContent     string `json:"extContent"`
	Id             int    `json:"id"`
	Idx            int    `json:"idx"`
	InsertTime     int64  `json:"insertTime"` // 假设是Unix时间戳
	LeftNum        int    `json:"leftNum"`
	Msg            string `json:"msg"`
	OperMember     int    `json:"operMember"`
	OrderNum       string `json:"ordernum"`
	SalerId        int    `json:"salerId"`
	Source         int    `json:"source"`
	Terminal       int    `json:"terminal"`
	Tid            int    `json:"tid"`
	Tnum           int    `json:"tnum"`
	TrackAction    int    `json:"trackAction"`
}

type TouristTrackListOrderTouristTrackExtContent struct {
	IsFirstUsed *int `json:"is_first_used"`
}

type TouristTrackListOrderTouristExtendResponse struct {
	ExtInfo struct {
		Age  *string `json:"age"`
		Sex  *string `json:"sex"`
		Area struct {
			Region   *string `json:"region"`
			Country  *string `json:"country"`
			Province *string `json:"province"`
			City     *string `json:"city"`
			District *string `json:"district"`
		} `json:"area"`
	} `json:"extInfo"`
}
