package ordertouristtrackqueryservice

import (
	"fmt"
	"gitee.com/golang-module/carbon/v2"
	"math"
	"report-service/internal/domain/enum"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
	"time"
)

func QueryOrderTrackByParam(orderNumIn []string, idxIn []int, trackAction int, branchTerminalIn []int, page int, size int) (*OrderTouristTrackResponse, error) {
	orderNumIn = utils.RemoveDuplicate(orderNumIn)
	idxIn = utils.RemoveDuplicate(idxIn)
	branchTerminalIn = utils.RemoveDuplicate(branchTerminalIn)
	javaParams := make(map[string]interface{})
	if len(orderNumIn) > 0 {
		javaParams["ordernumIn"] = orderNumIn
	}
	if len(idxIn) > 0 {
		javaParams["idxs"] = idxIn
	}
	if trackAction > 0 {
		javaParams["action"] = trackAction
	}
	if len(branchTerminalIn) > 0 {
		javaParams["branchTerminalIn"] = branchTerminalIn
	}
	if page > 0 && size > 0 {
		javaParams["page"] = page
		javaParams["size"] = size
	}
	data, err := java.Post("/web/v1/orderquery/orderTouristTrackQueryService/queryOrderTrackByParam", javaParams)
	if err != nil {
		return nil, err
	}
	var result OrderTouristTrackResponse
	err = utils.JsonConvertor(data, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func QueryOrderTrackIdByOrderNos(orderNumIn []string, trackAction int) (map[string]int, error) {
	if len(orderNumIn) == 0 {
		return nil, nil
	}
	orderNumIn = utils.RemoveDuplicate(orderNumIn)
	data := make(map[string]int)
	size := 100
	if len(orderNumIn) > 0 && len(orderNumIn) < size {
		size = len(orderNumIn)
	}
	for len(orderNumIn) > 0 {
		if len(orderNumIn) < size {
			size = len(orderNumIn)
		}
		chunk := orderNumIn[:size]
		orderNumIn = orderNumIn[size:]
		result, err := QueryOrderTrackByParam(chunk, []int{}, trackAction, []int{}, 1, size)
		if err != nil {
			return nil, err
		}
		total := result.Total
		firstPagelist := result.List
		totalPage := int(math.Ceil(float64(total) / float64(size)))
		for pageNum := 1; pageNum <= totalPage; pageNum++ {
			var list []TouristTrackListOrderTouristTrackResponse
			if pageNum != 1 {
				result, err = QueryOrderTrackByParam(chunk, []int{}, trackAction, []int{}, pageNum, size)
				if err != nil {
					return nil, err
				}
				list = result.List
			} else {
				list = firstPagelist
			}
			for _, item := range list {
				orderIdxKey := item.GetOrderIdxKey()
				if existingId, ok := data[orderIdxKey]; ok {
					if item.Id < existingId {
						data[orderIdxKey] = item.Id
					}
				} else {
					data[orderIdxKey] = item.Id
				}
			}
		}
	}
	return data, nil
}

func (t TouristTrackListOrderTouristTrackResponse) GetOrderIdxKey() string {
	return fmt.Sprintf("%s_%d", t.OrderNum, t.Idx)
}

func (c TouristTrackListOrderTouristTrackResponse) GetChangeTime() carbon.Carbon {
	nanoseconds := c.InsertTime * int64(time.Second)
	t := time.Unix(0, nanoseconds)
	date := t.Format(enum.TimestampLayoutMake)
	return carbon.Parse(date)
}
