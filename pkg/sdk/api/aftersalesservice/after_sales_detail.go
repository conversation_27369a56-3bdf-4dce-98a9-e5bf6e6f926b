package aftersalesservice

import (
	"errors"
	"fmt"
	"report-service/pkg/sdk/api/java"
	"report-service/pkg/utils"
)

type AfterSaleResponse struct {
	Status    int           `json:"status"`
	Code      string        `json:"code"`
	Message   string        `json:"message"`
	Rows      []interface{} `json:"rows"`
	Timestamp int           `json:"timestamp"`
	Success   bool          `json:"success"`
}

type AfterSaleData struct {
	OrderId      string `json:"orderId"`
	AfterSalesNo string `json:"afterSalesNo"`
	SellerId     int    `json:"sellerId"`
	BuyerId      int    `json:"buyerId"`
	RefundAmount int    `json:"refundAmount"`
	RefundMethod int    `json:"refundMethod"`
}

func QueryAfterSalesDetail(afterSaleCode []string) ([]AfterSaleData, error) {
	afterSaleCode = utils.RemoveDuplicate(afterSaleCode)
	response, err := java.OriginalPost("/order-center/feign-client/after-sales/detail", afterSaleCode)
	if err != nil {
		return nil, err
	}
	var data AfterSaleResponse
	err = utils.JsonConvertor(response, &data)
	if err != nil {
		return nil, err
	}

	if data.Code != "200" {
		return nil, errors.New(fmt.Sprintf("请求服务失败，错误信息：%s", data.Message))
	}

	var result []AfterSaleData
	err = utils.JsonConvertor(data.Rows, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func QueryAfterSalesPriceMapByCode(afterSaleCode string) (map[int]int, map[int]int, error) {
	detail, err := QueryAfterSalesDetail([]string{afterSaleCode})
	if err != nil {
		return nil, nil, err
	}

	var incomeMap = make(map[int]int)
	var refundMap = make(map[int]int)
	for _, info := range detail {
		if info.AfterSalesNo != afterSaleCode {
			continue
		}
		incomeMap[info.BuyerId] = info.RefundAmount
		refundMap[info.SellerId] = info.RefundAmount
	}

	return incomeMap, refundMap, nil
}

func QueryAfterSalesInfoMapByCodeList(afterSaleCodes []string) (map[string][]AfterSaleData, error) {
	detail, err := QueryAfterSalesDetail(afterSaleCodes)
	if err != nil {
		return nil, err
	}

	var result = make(map[string][]AfterSaleData)
	for _, info := range detail {
		result[info.AfterSalesNo] = append(result[info.AfterSalesNo], info)
	}

	return result, nil
}
