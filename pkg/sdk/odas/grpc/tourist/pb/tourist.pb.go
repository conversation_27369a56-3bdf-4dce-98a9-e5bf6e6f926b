// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.2
// source: tourist.proto

// 定义包名

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 定义 SummaryInoutByGroups 请求参数和响应
type SummaryInoutByGroupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GIds    string                 `protobuf:"bytes,1,opt,name=gIds,proto3" json:"gIds,omitempty"`
	Start   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start,proto3" json:"start,omitempty"`
	End     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end,proto3" json:"end,omitempty"`
	NoAmend bool                   `protobuf:"varint,4,opt,name=noAmend,proto3" json:"noAmend,omitempty"`
}

func (x *SummaryInoutByGroupsRequest) Reset() {
	*x = SummaryInoutByGroupsRequest{}
	mi := &file_tourist_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SummaryInoutByGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummaryInoutByGroupsRequest) ProtoMessage() {}

func (x *SummaryInoutByGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tourist_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummaryInoutByGroupsRequest.ProtoReflect.Descriptor instead.
func (*SummaryInoutByGroupsRequest) Descriptor() ([]byte, []int) {
	return file_tourist_proto_rawDescGZIP(), []int{0}
}

func (x *SummaryInoutByGroupsRequest) GetGIds() string {
	if x != nil {
		return x.GIds
	}
	return ""
}

func (x *SummaryInoutByGroupsRequest) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *SummaryInoutByGroupsRequest) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *SummaryInoutByGroupsRequest) GetNoAmend() bool {
	if x != nil {
		return x.NoAmend
	}
	return false
}

type SummaryInoutByGroupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	In  int64 `protobuf:"varint,1,opt,name=in,proto3" json:"in,omitempty"`
	Out int64 `protobuf:"varint,2,opt,name=out,proto3" json:"out,omitempty"`
}

func (x *SummaryInoutByGroupsResponse) Reset() {
	*x = SummaryInoutByGroupsResponse{}
	mi := &file_tourist_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SummaryInoutByGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummaryInoutByGroupsResponse) ProtoMessage() {}

func (x *SummaryInoutByGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tourist_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummaryInoutByGroupsResponse.ProtoReflect.Descriptor instead.
func (*SummaryInoutByGroupsResponse) Descriptor() ([]byte, []int) {
	return file_tourist_proto_rawDescGZIP(), []int{1}
}

func (x *SummaryInoutByGroupsResponse) GetIn() int64 {
	if x != nil {
		return x.In
	}
	return 0
}

func (x *SummaryInoutByGroupsResponse) GetOut() int64 {
	if x != nil {
		return x.Out
	}
	return 0
}

// 定义summary的请求参数和响应
type GroupListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid int64 `protobuf:"varint,1,opt,name=sid,proto3" json:"sid,omitempty"`
}

func (x *GroupListRequest) Reset() {
	*x = GroupListRequest{}
	mi := &file_tourist_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupListRequest) ProtoMessage() {}

func (x *GroupListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tourist_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupListRequest.ProtoReflect.Descriptor instead.
func (*GroupListRequest) Descriptor() ([]byte, []int) {
	return file_tourist_proto_rawDescGZIP(), []int{2}
}

func (x *GroupListRequest) GetSid() int64 {
	if x != nil {
		return x.Sid
	}
	return 0
}

type GroupListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GroupListResponseDTO `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GroupListResponse) Reset() {
	*x = GroupListResponse{}
	mi := &file_tourist_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupListResponse) ProtoMessage() {}

func (x *GroupListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tourist_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupListResponse.ProtoReflect.Descriptor instead.
func (*GroupListResponse) Descriptor() ([]byte, []int) {
	return file_tourist_proto_rawDescGZIP(), []int{3}
}

func (x *GroupListResponse) GetList() []*GroupListResponseDTO {
	if x != nil {
		return x.List
	}
	return nil
}

type GroupListResponseDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sid       int64    `protobuf:"varint,2,opt,name=sid,proto3" json:"sid,omitempty"`
	SerialNo  string   `protobuf:"bytes,3,opt,name=serialNo,proto3" json:"serialNo,omitempty"`
	Name      string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Gates     []string `protobuf:"bytes,5,rep,name=gates,proto3" json:"gates,omitempty"`
	CreatedAt string   `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt string   `protobuf:"bytes,7,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
}

func (x *GroupListResponseDTO) Reset() {
	*x = GroupListResponseDTO{}
	mi := &file_tourist_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupListResponseDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupListResponseDTO) ProtoMessage() {}

func (x *GroupListResponseDTO) ProtoReflect() protoreflect.Message {
	mi := &file_tourist_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupListResponseDTO.ProtoReflect.Descriptor instead.
func (*GroupListResponseDTO) Descriptor() ([]byte, []int) {
	return file_tourist_proto_rawDescGZIP(), []int{4}
}

func (x *GroupListResponseDTO) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupListResponseDTO) GetSid() int64 {
	if x != nil {
		return x.Sid
	}
	return 0
}

func (x *GroupListResponseDTO) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *GroupListResponseDTO) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupListResponseDTO) GetGates() []string {
	if x != nil {
		return x.Gates
	}
	return nil
}

func (x *GroupListResponseDTO) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *GroupListResponseDTO) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

var File_tourist_proto protoreflect.FileDescriptor

var file_tourist_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x74, 0x6f, 0x75, 0x72, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x01, 0x0a, 0x1b, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x49, 0x6e, 0x6f, 0x75, 0x74, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x49, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x2c, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x6f, 0x41, 0x6d, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6e, 0x6f,
	0x41, 0x6d, 0x65, 0x6e, 0x64, 0x22, 0x40, 0x0a, 0x1c, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x49, 0x6e, 0x6f, 0x75, 0x74, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6f, 0x75, 0x74, 0x22, 0x24, 0x0a, 0x10, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x69, 0x64, 0x22, 0x44, 0x0a,
	0x11, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x14, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x67,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x32, 0xb5, 0x01, 0x0a, 0x0e, 0x54, 0x6f, 0x75, 0x72, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x61, 0x0a, 0x14, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e,
	0x6f, 0x75, 0x74, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x22, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x6f, 0x75, 0x74,
	0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49,
	0x6e, 0x6f, 0x75, 0x74, 0x42, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x40, 0x0a, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x74, 0x6f,
	0x75, 0x72, 0x69, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tourist_proto_rawDescOnce sync.Once
	file_tourist_proto_rawDescData = file_tourist_proto_rawDesc
)

func file_tourist_proto_rawDescGZIP() []byte {
	file_tourist_proto_rawDescOnce.Do(func() {
		file_tourist_proto_rawDescData = protoimpl.X.CompressGZIP(file_tourist_proto_rawDescData)
	})
	return file_tourist_proto_rawDescData
}

var file_tourist_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_tourist_proto_goTypes = []any{
	(*SummaryInoutByGroupsRequest)(nil),  // 0: proto.SummaryInoutByGroupsRequest
	(*SummaryInoutByGroupsResponse)(nil), // 1: proto.SummaryInoutByGroupsResponse
	(*GroupListRequest)(nil),             // 2: proto.GroupListRequest
	(*GroupListResponse)(nil),            // 3: proto.GroupListResponse
	(*GroupListResponseDTO)(nil),         // 4: proto.GroupListResponseDTO
	(*timestamppb.Timestamp)(nil),        // 5: google.protobuf.Timestamp
}
var file_tourist_proto_depIdxs = []int32{
	5, // 0: proto.SummaryInoutByGroupsRequest.start:type_name -> google.protobuf.Timestamp
	5, // 1: proto.SummaryInoutByGroupsRequest.end:type_name -> google.protobuf.Timestamp
	4, // 2: proto.GroupListResponse.list:type_name -> proto.GroupListResponseDTO
	0, // 3: proto.TouristService.SummaryInoutByGroups:input_type -> proto.SummaryInoutByGroupsRequest
	2, // 4: proto.TouristService.GroupList:input_type -> proto.GroupListRequest
	1, // 5: proto.TouristService.SummaryInoutByGroups:output_type -> proto.SummaryInoutByGroupsResponse
	3, // 6: proto.TouristService.GroupList:output_type -> proto.GroupListResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_tourist_proto_init() }
func file_tourist_proto_init() {
	if File_tourist_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tourist_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tourist_proto_goTypes,
		DependencyIndexes: file_tourist_proto_depIdxs,
		MessageInfos:      file_tourist_proto_msgTypes,
	}.Build()
	File_tourist_proto = out.File
	file_tourist_proto_rawDesc = nil
	file_tourist_proto_goTypes = nil
	file_tourist_proto_depIdxs = nil
}
