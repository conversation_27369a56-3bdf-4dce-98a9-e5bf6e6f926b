// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: tourist.proto

// 定义包名

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TouristService_SummaryInoutByGroups_FullMethodName = "/proto.TouristService/SummaryInoutByGroups"
	TouristService_GroupList_FullMethodName            = "/proto.TouristService/GroupList"
)

// TouristServiceClient is the client API for TouristService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 定义游客服务
type TouristServiceClient interface {
	// 批量查询出入园
	SummaryInoutByGroups(ctx context.Context, in *SummaryInoutByGroupsRequest, opts ...grpc.CallOption) (*SummaryInoutByGroupsResponse, error)
	// 获取统计组
	GroupList(ctx context.Context, in *GroupListRequest, opts ...grpc.CallOption) (*GroupListResponse, error)
}

type touristServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTouristServiceClient(cc grpc.ClientConnInterface) TouristServiceClient {
	return &touristServiceClient{cc}
}

func (c *touristServiceClient) SummaryInoutByGroups(ctx context.Context, in *SummaryInoutByGroupsRequest, opts ...grpc.CallOption) (*SummaryInoutByGroupsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SummaryInoutByGroupsResponse)
	err := c.cc.Invoke(ctx, TouristService_SummaryInoutByGroups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *touristServiceClient) GroupList(ctx context.Context, in *GroupListRequest, opts ...grpc.CallOption) (*GroupListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GroupListResponse)
	err := c.cc.Invoke(ctx, TouristService_GroupList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TouristServiceServer is the server API for TouristService service.
// All implementations should embed UnimplementedTouristServiceServer
// for forward compatibility.
//
// 定义游客服务
type TouristServiceServer interface {
	// 批量查询出入园
	SummaryInoutByGroups(context.Context, *SummaryInoutByGroupsRequest) (*SummaryInoutByGroupsResponse, error)
	// 获取统计组
	GroupList(context.Context, *GroupListRequest) (*GroupListResponse, error)
}

// UnimplementedTouristServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTouristServiceServer struct{}

func (UnimplementedTouristServiceServer) SummaryInoutByGroups(context.Context, *SummaryInoutByGroupsRequest) (*SummaryInoutByGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SummaryInoutByGroups not implemented")
}
func (UnimplementedTouristServiceServer) GroupList(context.Context, *GroupListRequest) (*GroupListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupList not implemented")
}
func (UnimplementedTouristServiceServer) testEmbeddedByValue() {}

// UnsafeTouristServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TouristServiceServer will
// result in compilation errors.
type UnsafeTouristServiceServer interface {
	mustEmbedUnimplementedTouristServiceServer()
}

func RegisterTouristServiceServer(s grpc.ServiceRegistrar, srv TouristServiceServer) {
	// If the following call pancis, it indicates UnimplementedTouristServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TouristService_ServiceDesc, srv)
}

func _TouristService_SummaryInoutByGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SummaryInoutByGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TouristServiceServer).SummaryInoutByGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TouristService_SummaryInoutByGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TouristServiceServer).SummaryInoutByGroups(ctx, req.(*SummaryInoutByGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TouristService_GroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TouristServiceServer).GroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TouristService_GroupList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TouristServiceServer).GroupList(ctx, req.(*GroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TouristService_ServiceDesc is the grpc.ServiceDesc for TouristService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TouristService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.TouristService",
	HandlerType: (*TouristServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SummaryInoutByGroups",
			Handler:    _TouristService_SummaryInoutByGroups_Handler,
		},
		{
			MethodName: "GroupList",
			Handler:    _TouristService_GroupList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tourist.proto",
}
