syntax = "proto3";

//path 表示生成的go文件的存放地址，会自动生成目录的。
//name 表示生成的go文件所属的包名
option go_package = ".;tourist";
// 定义包名
package proto;

// 定义游客服务
service TouristService {
  // 批量查询出入园
  rpc SummaryInoutByGroups (SummaryInoutByGroupsRequest) returns (SummaryInoutByGroupsResponse) {}
  // 获取统计组
  rpc GroupList (GroupListRequest) returns (GroupListResponse) {}
}

import "google/protobuf/timestamp.proto";

// 定义 SummaryInoutByGroups 请求参数和响应
message SummaryInoutByGroupsRequest {
  string  gIds = 1;
  google.protobuf.Timestamp start = 2;
  google.protobuf.Timestamp end = 3;
  bool noAmend = 4;
}

message SummaryInoutByGroupsResponse{
  int64 in = 1;
  int64 out = 2;
}

// 定义summary的请求参数和响应
message GroupListRequest {
  int64 sid = 1;
}

message GroupListResponse {
  repeated GroupListResponseDTO list = 1;
}

message GroupListResponseDTO {
  int64 id = 1;
  int64 sid = 2;
  string serialNo = 3;
  string name = 4;
  repeated string gates = 5;
  string createdAt = 6;
  string updatedAt = 7;
}