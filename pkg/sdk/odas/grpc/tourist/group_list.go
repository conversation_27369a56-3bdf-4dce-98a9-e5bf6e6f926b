package tourist

import (
	"report-service/pkg/sdk/odas/grpc/client"
	touristpb "report-service/pkg/sdk/odas/grpc/tourist/pb"
	"report-service/pkg/szerrors"
)

func GroupList(sid int64) (*touristpb.GroupListResponse, error) {
	req := touristpb.GroupListRequest{
		Sid: sid,
	}
	ctx, err := client.GetSignedContext(touristpb.TouristService_GroupList_FullMethodName, &req)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}
	res, err := client.TouristServiceClient.GroupList(ctx, &req)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}
	return res, nil
}
