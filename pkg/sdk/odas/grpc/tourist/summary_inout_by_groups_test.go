package tourist

import (
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestSummaryInoutByGroups(t *testing.T) {
	type args struct {
		gIds    string
		start   time.Time
		end     time.Time
		noAmend bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				gIds:    "34,46,49",
				start:   time.Date(2024, 10, 1, 0, 0, 0, 0, time.Local),
				end:     time.Date(2024, 12, 31, 0, 0, 0, 0, time.Local),
				noAmend: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := SummaryInoutByGroups(tt.args.gIds, tt.args.start, tt.args.end, tt.args.noAmend)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
		})
	}
}
