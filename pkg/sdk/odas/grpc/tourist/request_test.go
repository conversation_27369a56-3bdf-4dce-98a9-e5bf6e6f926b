package tourist

import (
	"go.uber.org/zap"
	"report-service/pkg/sdk/odas/grpc/client"
	"testing"
)

func TestMain(m *testing.M) {
	l, _ := zap.NewDevelopment()
	err := client.Register(&client.ConfigType{
		Addr:      "10.53.0.14:23081",
		AppId:     "rklXMeLpnwK2fz1B",
		AppSecret: "Oyr4nbXc67TYFZ8VISLfHsK9JCjgRiDM",
		Nonce:     "report_nonce",
	}, l)
	if err != nil {
		panic(err)
	}

	m.Run()

	err = client.Close()
	if err != nil {
		panic(err)
	}
}
