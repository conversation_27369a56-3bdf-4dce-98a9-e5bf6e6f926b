package tourist

import (
	"google.golang.org/protobuf/types/known/timestamppb"
	"report-service/pkg/sdk/odas/grpc/client"
	touristpb "report-service/pkg/sdk/odas/grpc/tourist/pb"
	"report-service/pkg/szerrors"
	"time"
)

func SummaryInoutByGroups(gIds string, start, end time.Time, noAmend bool) (*touristpb.SummaryInoutByGroupsResponse, error) {
	req := touristpb.SummaryInoutByGroupsRequest{
		GIds:    gIds,
		Start:   timestamppb.New(start),
		End:     timestamppb.New(end),
		NoAmend: noAmend,
	}
	ctx, err := client.GetSignedContext(touristpb.TouristService_SummaryInoutByGroups_FullMethodName, &req)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}
	res, err := client.TouristServiceClient.SummaryInoutByGroups(ctx, &req)
	if err != nil {
		return nil, szerrors.NewRemoteError(err)
	}
	return res, nil
}
