package tourist

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGroupList(t *testing.T) {
	type args struct {
		sid int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "TestGroupList",
			args: args{
				sid: 3385,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := GroupList(tt.args.sid)
			if !tt.wantErr(t, err, fmt.Sprintf("GroupList(%v)", tt.args.sid)) {
				return
			}
		})
	}
}
