package client

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

func LoggingInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		if logger == nil {
			return invoker(ctx, method, req, reply, cc, opts...)
		}

		startTime := time.Now()

		// Get metadata from context
		var md metadata.MD
		if outMD, ok := metadata.FromOutgoingContext(ctx); ok {
			md = outMD
		}

		// Marshal request for logging
		reqBytes, _ := json.Marshal(req)
		var reqBody interface{}
		_ = json.Unmarshal(reqBytes, &reqBody)

		// Call the RPC method
		err := invoker(ctx, method, req, reply, cc, opts...)

		// Calculate duration
		duration := time.Since(startTime)

		// Marshal response for logging
		var resBody interface{}
		if err == nil {
			resBytes, _ := json.Marshal(reply)
			_ = json.Unmarshal(resBytes, &resBody)
		}

		// Create zap fields
		fields := []zap.Field{
			zap.String("target", cc.Target()),
			zap.String("method", method),
			zap.Duration("duration", duration),
			zap.Any("request", reqBody),
			zap.Any("response", resBody),
			zap.Any("metadata", map[string]string{
				"appid":     firstValue(md.Get("appid")),
				"nonce":     firstValue(md.Get("nonce")),
				"timestamp": firstValue(md.Get("timestamp")),
				"signature": firstValue(md.Get("signature")),
			}),
		}

		if err != nil {
			fields = append(fields, zap.Error(err))
			logger.Error("grpc_request", fields...)
		} else {
			logger.Info("grpc_request", fields...)
		}

		return err
	}
}

// firstValue returns the first value from a string slice or empty string if slice is empty
func firstValue(values []string) string {
	if len(values) > 0 {
		return values[0]
	}
	return ""
}
