package client

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"google.golang.org/grpc/metadata"
	"net/url"
	"strconv"
	"time"
)

type SignType struct {
	AppId     string
	Nonce     string
	Timestamp string
	Signature string
}

func GetSignedContext(fullMethodName string, req interface{}) (ctx context.Context, err error) {
	sign, err := getSign(fullMethodName, req)
	if err != nil {
		return nil, err
	}
	ctx = metadata.NewOutgoingContext(context.Background(), metadata.New(map[string]string{
		"appid":     sign.AppId,
		"nonce":     sign.Nonce,
		"timestamp": sign.Timestamp,
		"signature": sign.Signature,
	}))

	return ctx, nil
}

func getSign(fullMethodName string, req interface{}) (sign *SignType, err error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	appId := Config.AppId
	nonce := Config.Nonce

	reqByte, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrapf(err, "请求参数转为byte失败，请确保可转换为json格式")
	}

	values := url.Values{}
	values.Add("method", fullMethodName)
	values.Add("req", string(reqByte))
	values.Add("appid", appId)
	values.Add("nonce", nonce)
	values.Add("timestamp", timestamp)

	encode := values.Encode()
	sum := md5.Sum([]byte(encode))
	signature := fmt.Sprintf("%x", sum[:])

	sign = &SignType{
		AppId:     appId,
		Nonce:     nonce,
		Timestamp: timestamp,
		Signature: signature,
	}
	return
}
