package client

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"report-service/pkg/sdk/odas/grpc/tourist/pb"
)

var (
	conn                 *grpc.ClientConn
	TouristServiceClient pb.TouristServiceClient
)

func Register(config *ConfigType, logger *zap.Logger) (err error) {
	Config = config
	SetLogger(logger)

	conn, err = grpc.NewClient(
		Config.Addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),                             // 无证书连接
		grpc.WithUnaryInterceptor(LoggingInterceptor()),                                      // 日志拦截器
		grpc.WithDefaultCallOptions(grpc.WaitForReady(false)),                                // 不等待就绪
		grpc.WithDefaultServiceConfig(`{"methodConfig": [{"name": [{}],"timeout": "10s"}]}`), // 默认10秒超时
	)
	if err != nil {
		return err
	}

	TouristServiceClient = pb.NewTouristServiceClient(conn)
	return nil
}

func Close() (err error) {
	if conn != nil {
		err = conn.Close()
		if err != nil {
			return err
		}
	}
	return nil
}
