package notice

import (
	"errors"
	"report-service/internal/global"
	"report-service/pkg/sdk/infrastructure"
	"report-service/pkg/utils/httputil"

	"github.com/spf13/cast"
	"go.uber.org/zap"
)

func NewSend(strategyId string, content string) (err error) {
	if infrastructure.Config.Addr == "" || strategyId == "" {
		global.LOG.Info("飞书通知配置不完整，无法发送通知", zap.String("content", content))
		return nil
	}

	var res map[string]interface{}
	err = httputil.RequestClient{
		Method: "POST",
		Url:    infrastructure.Config.Addr + "/v1/index/send",
		Headers: map[string]string{
			"app_id":     infrastructure.Config.AppId,
			"app_secret": infrastructure.Config.AppSecret,
		},
		Body: map[string]interface{}{
			"content":     content,
			"strategy_id": strategyId,
		},
		Response:   &res,
		DisableLog: true, // 飞书本就会通知，不用重复打印日志
	}.Request()
	if err != nil {
		return
	}
	if cast.ToInt(res["code"]) != 200 {
		err = errors.New(cast.ToString(res["msg"]))
		return
	}
	return
}
