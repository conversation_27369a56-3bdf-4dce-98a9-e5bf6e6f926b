package utils

import (
	"bytes"
	"encoding/json"
	"errors"
)

func JsonConvertor(originStruct, targetStruct interface{}) (err error) {
	marshal, err := json.Marshal(originStruct)
	if err != nil {
		return errors.New("marshal from originStruct error: " + err.<PERSON><PERSON>r())
	}

	err = json.Unmarshal(marshal, &targetStruct)
	if err != nil {
		return errors.New("unmarshal to targetStruct error: " + err.<PERSON>rror())
	}

	return
}

func JsonConvertOrPanic(originStruct, targetStruct interface{}) {
	err := JsonConvertor(originStruct, targetStruct)
	if err != nil {
		panic(err)
	}
}

// JsonDecode 解析JSON响应，防止数字精度丢失
// 适用于 JSON 解析的目标结构体不确定、为泛型的情况
func JsonDecode(b []byte, targetStruct interface{}) (err error) {
	// 创建一个 io.Reader 来读取 JSON 数据
	r := bytes.NewReader(b)
	dec := json.NewDecoder(r)

	// 使用 Number 类型解析 JSON，避免数字精度丢失
	dec.UseNumber()

	// 解析到目标结构体
	err = dec.Decode(&targetStruct)
	if err != nil {
		return err
	}

	return nil
}
