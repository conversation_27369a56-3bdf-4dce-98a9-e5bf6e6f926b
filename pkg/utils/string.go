package utils

import "strings"

// EnsureLeadingChar 确保字符串以指定字符开头
// 如果字符串为空，则返回指定的字符
// 如果字符串不以指定字符开头，则在前面添加该字符
// 如果字符串已经以指定字符开头，则直接返回原字符串
//
// 参数:
//
//	s: 需要处理的字符串
//	char: 指定的开头字符
//
// 返回值:
//
//	string: 以指定字符开头的字符串
func EnsureLeadingChar(s string, char string) string {
	// 检查字符参数是否有效
	if char == "" {
		return s
	}
	// 处理空字符串的情况
	if s == "" {
		return char
	}
	// 如果字符串不以指定字符开头，则添加该字符
	if !strings.HasPrefix(s, char) {
		return char + s
	}
	// 字符串已经以指定字符开头，直接返回
	return s
}
