package zip

import (
	"archive/zip"
	"fmt"
	"go.uber.org/zap"
	"io"
	"os"
	"path/filepath"
	"report-service/pkg/utils/common"
	"strings"
)

// ZipFile 将单个文件压缩为ZIP文件
func ZipFile(srcFile, dstZip string) error {
	// 创建目标ZIP文件
	zipFile, err := os.Create(dstZip)
	if err != nil {
		return fmt.Errorf("创建ZIP文件失败: %v", err)
	}
	defer func() {
		if closeErr := zipFile.Close(); closeErr != nil {
			// 记录关闭错误，但不覆盖原错误
			common.LOG.Error("关闭ZIP文件时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 创建zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer func() {
		if closeErr := zipWriter.Close(); closeErr != nil {
			// 记录关闭错误，但不覆盖原错误
			common.LOG.Error("关闭ZIP写入器时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 添加文件到ZIP
	err = addFileToZip(zipWriter, srcFile, filepath.Base(srcFile))
	if err != nil {
		return fmt.Errorf("添加文件到ZIP失败: %v", err)
	}
	return nil
}

// ZipFiles 将多个文件压缩为一个ZIP文件
func ZipFiles(srcFiles []string, dstZip string) error {
	// 创建目标ZIP文件
	zipFile, err := os.Create(dstZip)
	if err != nil {
		return fmt.Errorf("创建ZIP文件失败: %v", err)
	}
	defer func() {
		if closeErr := zipFile.Close(); closeErr != nil {
			common.LOG.Error("关闭ZIP文件时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 创建zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer func() {
		if closeErr := zipWriter.Close(); closeErr != nil {
			common.LOG.Error("关闭ZIP写入器时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 逐个添加文件到ZIP
	for _, srcFile := range srcFiles {
		var fileInfo os.FileInfo
		fileInfo, err = os.Stat(srcFile)
		if err != nil {
			return fmt.Errorf("获取文件信息失败 %s: %v", srcFile, err)
		}
		// 添加文件到ZIP
		err = addFileToZip(zipWriter, srcFile, fileInfo.Name())
		if err != nil {
			return fmt.Errorf("添加文件 %s 到ZIP失败: %v", srcFile, err)
		}
	}
	return nil
}

// ZipDirectory 将整个目录压缩为ZIP文件
func ZipDirectory(srcDir, dstZip string) error {
	// 创建目标ZIP文件
	zipFile, err := os.Create(dstZip)
	if err != nil {
		return fmt.Errorf("创建ZIP文件失败: %v", err)
	}
	defer func() {
		if closeErr := zipFile.Close(); closeErr != nil {
			common.LOG.Error("关闭ZIP文件时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 创建zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer func() {
		if closeErr := zipWriter.Close(); closeErr != nil {
			common.LOG.Error("关闭ZIP写入器时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 遍历目录并添加文件到ZIP
	err = filepath.Walk(srcDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 获取相对路径
		var relPath string
		relPath, err = filepath.Rel(srcDir, path)
		if err != nil {
			return err
		}
		// 跳过根目录
		if relPath == "." {
			return nil
		}
		// 构造ZIP中的文件路径
		zipPath := filepath.ToSlash(relPath)
		// 添加文件或目录到ZIP
		if info.IsDir() {
			zipPath += "/"
			_, err = zipWriter.Create(zipPath)
			return err
		}
		return addFileToZip(zipWriter, path, zipPath)
	})
	if err != nil {
		return fmt.Errorf("压缩目录失败: %v", err)
	}
	return nil
}

// Unzip 解压ZIP文件到指定目录
func Unzip(srcZip, dstDir string) error {
	// 打开ZIP文件
	reader, err := zip.OpenReader(srcZip)
	if err != nil {
		return fmt.Errorf("打开ZIP文件失败: %v", err)
	}
	defer func() {
		if closeErr := reader.Close(); closeErr != nil {
			common.LOG.Error("关闭ZIP读取器时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 创建目标目录
	err = os.MkdirAll(dstDir, 0755)
	if err != nil {
		return fmt.Errorf("创建目标目录失败: %v", err)
	}
	// 遍历ZIP中的文件
	for _, file := range reader.File {
		err = extractFile(file, dstDir)
		if err != nil {
			return fmt.Errorf("解压文件 %s 失败: %v", file.Name, err)
		}
	}
	return nil
}

// addFileToZip 将文件添加到ZIP中
func addFileToZip(zipWriter *zip.Writer, srcFile, zipPath string) error {
	// 打开源文件
	file, err := os.Open(srcFile)
	if err != nil {
		return err
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			common.LOG.Error("关闭源文件时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 获取文件信息
	var info os.FileInfo
	info, err = file.Stat()
	if err != nil {
		return err
	}
	// 创建文件头
	var header *zip.FileHeader
	header, err = zip.FileInfoHeader(info)
	if err != nil {
		return err
	}
	header.Name = zipPath
	// 如果是目录，确保以/结尾
	if info.IsDir() {
		header.Name += "/"
	}
	// 创建写入器
	var writer io.Writer
	writer, err = zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}
	// 如果是目录，直接返回
	if info.IsDir() {
		return nil
	}
	// 复制文件内容
	_, err = io.Copy(writer, file)
	return err
}

// extractFile 从ZIP中提取单个文件
func extractFile(file *zip.File, dstDir string) error {
	// 构造目标文件路径
	filePath := filepath.Join(dstDir, file.Name)
	// 检查路径遍历漏洞
	if !strings.HasPrefix(filePath, filepath.Clean(dstDir)+string(os.PathSeparator)) {
		return fmt.Errorf("非法文件路径: %s", filePath)
	}
	// 如果是目录，创建目录
	if file.FileInfo().IsDir() {
		err := os.MkdirAll(filePath, file.Mode())
		if err != nil {
			return fmt.Errorf("创建目录失败 %s: %v", filePath, err)
		}
		return nil
	}
	// 创建目录（如果不存在）
	err := os.MkdirAll(filepath.Dir(filePath), 0755)
	if err != nil {
		return fmt.Errorf("创建父目录失败 %s: %v", filepath.Dir(filePath), err)
	}
	// 打开ZIP中的文件
	var zipFile io.ReadCloser
	zipFile, err = file.Open()
	if err != nil {
		return fmt.Errorf("打开ZIP文件失败 %s: %v", file.Name, err)
	}
	defer func() {
		if closeErr := zipFile.Close(); closeErr != nil {
			common.LOG.Error("关闭ZIP文件时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 创建目标文件
	var targetFile *os.File
	targetFile, err = os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, file.Mode())
	if err != nil {
		return fmt.Errorf("创建目标文件失败 %s: %v", filePath, err)
	}
	defer func() {
		if closeErr := targetFile.Close(); closeErr != nil {
			common.LOG.Error("关闭目标文件时出错", zap.Any("close_err", closeErr))
		}
	}()
	// 复制文件内容
	_, err = io.Copy(targetFile, zipFile)
	if err != nil {
		return fmt.Errorf("复制文件内容失败 %s: %v", filePath, err)
	}
	return nil
}
