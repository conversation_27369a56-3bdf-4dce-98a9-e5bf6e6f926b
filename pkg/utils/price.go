package utils

import (
	"strconv"
)

func FormatPrice(price int, prefix ...string) string {
	str := ""
	if len(prefix) > 0 {
		str = prefix[0]
	}
	return str + strconv.FormatFloat(float64(price)/100, 'f', 2, 64)
}

// ConvertCentToYuan 将分转换为元，保留小数点后2位
func ConvertCentToYuan(cent int) float64 {
	formatted := FormatPrice(cent)
	result, _ := strconv.ParseFloat(formatted, 64)
	return result
}

// ConvertCentToYuanByFloat64 将分转换为元，保留小数点后2位
func ConvertCentToYuanByFloat64(cent float64, prefix ...string) string {
	str := ""
	if len(prefix) > 0 {
		str = prefix[0]
	}
	return str + strconv.FormatFloat(cent/100, 'f', 2, 64)
}
