package utils

import (
	"github.com/spf13/cast"
)

func RemoveDuplicate[T any](s []T) []T {
	res := make([]T, 0, len(s))
	mySet := make(map[any]struct{})
	for _, t := range s {
		if _, ok := mySet[t]; !ok {
			res = append(res, t)
			mySet[t] = struct{}{}
		}
	}
	return res
}

// Deprecated: Use Container instead
func IsInSlice[T comparable](s []T, t T) bool {
	for _, v := range s {
		if v == t {
			return true
		}
	}
	return false
}

// Container 判断某个元素是否在切片中
func Container[T comparable](s []T, t T) bool {
	for _, v := range s {
		if v == t {
			return true
		}
	}
	return false
}

func ExistDuplicate(s interface{}) bool {
	slice := cast.ToSlice(s)
	seen := make(map[interface{}]bool)
	for _, v := range slice {
		if _, ok := seen[v]; ok {
			return true // 发现重复，返回true
		}
		seen[v] = true
	}
	return false // 没有重复，返回false
}

func MergeSlice[T comparable](slice01 []T, slice02 []T) []T {
	var result []T
	result = append(result, slice01...)
	result = append(result, slice02...)
	return result
}

func ChunkSlice[T any](slice []T, chunkSize int) [][]T {
	var chunks [][]T
	if chunkSize <= 0 {
		chunks = append(chunks, slice)
		return chunks
	}
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func RemoveIfEqual[T comparable](s []T, elem T) []T {
	result := s[:0]
	for _, v := range s {
		if v != elem {
			result = append(result, v)
		}
	}
	return result
}

// Intersection 取两个切片的交集，并返回交集
func Intersection[T comparable](slice01 []T, slice02 []T) []T {
	var intersect []T
	if len(slice01) == 0 || len(slice02) == 0 {
		return intersect
	}
	m := make(map[T]bool)
	for _, item := range slice01 {
		m[item] = true
	}
	for _, item := range slice02 {
		if _, found := m[item]; found {
			intersect = append(intersect, item)
		}
	}
	return intersect
}

func SliceToMap[T comparable](s []T) map[int]T {
	m := make(map[int]T)
	for index, value := range s {
		m[index] = value
	}
	return m
}
