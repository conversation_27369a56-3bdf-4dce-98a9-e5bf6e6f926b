package validator

import (
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhtranslations "github.com/go-playground/validator/v10/translations/zh"
	"reflect"
	"regexp"
	"report-service/pkg/utils/common"
)

var (
	Validator *validator.Validate
	Trans     ut.Translator
)

// 初始化验证器和翻译器
func init() {
	// 创建验证器实例
	Validator = validator.New()

	// 创建翻译器实例
	translator := zh.New()
	uni := ut.New(translator, translator)
	Trans, _ = uni.GetTranslator("zh")

	// 注册翻译器
	_ = zhtranslations.RegisterDefaultTranslations(Validator, Trans)

	// 自定义错误消息处理
	Validator.RegisterTagNameFunc(func(fld reflect.StructField) string {
		label := fld.Tag.Get("label")
		if label != "" {
			return label + "(" + fld.Tag.Get("json") + ")"
		}
		return fld.Name
	})

	// 注册自定义验证函数
	registerValidations()

	// 注册额外的验证函数的翻译器
	registerTranslations()
}

func registerValidations() {
	registerPhoneValidation()
}

func registerPhoneValidation() {
	// 自定义手机号或固定电话的正则表达式
	var phoneRegex = regexp.MustCompile(`^(1[3456789]\d{9}|(\d{3,4}-)?\d{7,8}(-\d{1,4})?)$`)

	// 自定义联系方式验证函数
	validatePhone := func(fl validator.FieldLevel) bool {
		contact := fl.Field().String()
		return phoneRegex.MatchString(contact)
	}

	// 注册自定义验证函数
	err := Validator.RegisterValidation("phone", validatePhone)
	if err != nil {
		common.LOG.Error("注册自定义验证函数失败: " + err.Error())
		return
	}

	// 注册自定义验证函数的翻译器
	err = Validator.RegisterTranslation("phone", Trans, func(ut ut.Translator) error {
		return ut.Add("phone", "{0}格式不正确，请填写正确的手机号或固定电话", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("phone", fe.Field())
		return t
	})
	if err != nil {
		common.LOG.Error("注册自定义验证函数的翻译器失败: " + err.Error())
		return
	}
}

func registerTranslations() {
	// 注册 oneof 验证函数的翻译器
	err := Validator.RegisterTranslation("oneof", Trans, func(ut ut.Translator) error {
		return ut.Add("oneof", "{0}必须是[{1}]中的一个", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("oneof", fe.Field(), fe.Param())
		return t
	})
	if err != nil {
		common.LOG.Error("注册自定义验证函数的翻译器失败: " + err.Error())
		return
	}
}
