package utils

import (
	"fmt"
	"strings"
)

type DuplicateUpdateField struct {
	Name        string
	Value       interface{}
	IsIncrement bool // 增量标识，如果为true，则更新时使用 value=value+增量数
}

type ItemWithDuplicateUpdate interface {
	TableName() string
	InsertFieldValueMap() ([]DuplicateUpdateField, error)
	UpdateFieldValueMap() ([]DuplicateUpdateField, error)
}

// 注意：如果是表的部分字段更新，需要注意，表设计是不是使用了默认值，如果没有默认值，不能做部分更新
// 问题示例：mysql 表结构 `payload` json NOT NULL COMMENT '扩展数据',json字段不存在默认值，
// 这时候部分更新字段里没有`payload`字段，就会导致“SQL 错误 [1364] [HY000]: Field 'payload' doesn't have a default value” 报错
func ParseDuplicateUpdateSqlAndValues[T ItemWithDuplicateUpdate](data []T) (string, []interface{}, error) {
	var tableName string
	var insertFields []string
	var insertPlaceHolders []string
	var updateFields []string
	var insertValues []interface{}
	for index, item := range data {
		insertFieldSlice, err := item.InsertFieldValueMap()
		if err != nil || len(insertFieldSlice) == 0 {
			return "", nil, err
		}
		updateFieldSlice, err := item.UpdateFieldValueMap()
		if err != nil {
			return "", nil, err
		}
		//字段信息和占位信息取第一个的值
		if index == 0 {
			tableName = item.TableName()
			for _, field := range insertFieldSlice {
				insertFields = append(insertFields, field.Name)
				insertPlaceHolders = append(insertPlaceHolders, "?")
			}
			for _, field := range updateFieldSlice {
				if field.IsIncrement {
					// 如果是增量更新，使用 value=value+VALUES(value) 的形式
					updateFields = append(updateFields, fmt.Sprintf("%s=%s+VALUES(%s)", field.Name, field.Name, field.Name))
				} else {
					// 普通更新，直接使用 VALUES(value)
					updateFields = append(updateFields, fmt.Sprintf("%s=VALUES(%s)", field.Name, field.Name))
				}
			}
		}
		for _, field := range insertFieldSlice {
			insertValues = append(insertValues, field.Value)
		}
	}

	placeholder := strings.Repeat(fmt.Sprintf("(%s),", strings.Join(insertPlaceHolders, ", ")), len(data))
	placeholder = placeholder[:len(placeholder)-1]

	return fmt.Sprintf("INSERT INTO %s (%s) VALUES %s ON DUPLICATE KEY UPDATE %s",
		tableName,
		strings.Join(insertFields, ", "),
		placeholder,
		strings.Join(updateFields, ", ")), insertValues, nil
}
