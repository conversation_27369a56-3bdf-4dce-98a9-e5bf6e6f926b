package utils

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"io"
)

// GetServerPath 获取当前服务的地址
func GetServerPath(c *gin.Context) string {
	// gin并不能获取http or https，但我们所有环境都是http，暂时写死
	return "http://" + c.Request.Host
}

// GetRequestBody 获取请求体
func GetRequestBody(c *gin.Context) ([]byte, error) {
	body, err := c.GetRawData()
	if err != nil {
		return nil, err
	}
	c.Request.Body = io.NopCloser(bytes.NewReader(body))
	return body, nil
}
