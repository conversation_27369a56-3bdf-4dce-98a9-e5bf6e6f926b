package utils

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
)

func MD5V(str []byte, b ...byte) string {
	h := md5.New()
	h.Write(str)
	return hex.EncodeToString(h.Sum(b))
}

func SHA256(input string) string {
	// 创建一个SHA-256哈希对象
	hash := sha256.New()

	// 将字符串转换为字节数组并计算哈希值
	hash.Write([]byte(input))
	hashBytes := hash.Sum(nil)

	// 将哈希值转换为十六进制字符串
	hashString := hex.EncodeToString(hashBytes)

	return hashString
}
