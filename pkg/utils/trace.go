package utils

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"report-service/pkg/utils/snowflake"
)

const TraceIdKey = "trace_id"

func SetTraceId(parent context.Context) context.Context {
	traceId := snowflake.GetId()
	return context.WithValue(parent, TraceIdKey, traceId)
}

func GetTraceId(ctx context.Context) string {
	return cast.ToString(ctx.Value(TraceIdKey))
}

func SetTraceIdToGin(c *gin.Context) {
	c.Request = c.Request.WithContext(SetTraceId(c.Request.Context()))
}

func GetTraceIdFromGin(c *gin.Context) string {
	return GetTraceId(c.Request.Context())
}
