package excel

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"strings"
)

// CellStyle 定义单元格样式
type CellStyle struct {
	FontSize            float64 // 字体大小
	Bold                bool    // 是否加粗
	BgColor             string  // 背景色，如 "#FF0000"
	Horizontal          string  // 水平对齐方式：left/center/right
	Vertical            string  // 垂直对齐方式：top/center/bottom
	MergeCellHorizontal bool
	MergeCellVertical   bool
	FontColor           string // 字体颜色
	Border              *bool  // 是否有边框
}

// HeaderRow 定义表头行
type HeaderRow struct {
	Values []interface{} // 表头内容
	Height float64       // 行高（可选）
	Style  *CellStyle    // 行样式（可选）
}

// ExcelData 定义Excel数据
type ExcelData struct {
	SheetName   string          // 工作表名称
	Title       string          // 主标题（可选）
	Headers     []HeaderRow     // 多行表头
	Data        [][]interface{} // 数据（二维数组）
	DataStyles  [][]CellStyle   // 对应数据的样式（可选）
	MergeTitles bool            // 是否合并主标题单元格
	TitleStyle  *CellStyle      // 主标题样式
}

// GenerateExcelWithMerge 生成带合并单元格的Excel文件
func GenerateExcelWithMerge(filePath string, data ExcelData) error {
	// 创建文件
	f := excelize.NewFile()
	// 创建工作表
	index, sheetName, err := createSheetName(f, &data)
	if err != nil {
		return err
	}
	currentRow := 1
	// 写入主标题（如果有）
	currentRow, err = writeTitle(f, sheetName, &data, currentRow)
	if err != nil {
		return err
	}
	// 写入多行表头
	currentRow, err = writeHeaders(f, sheetName, &data, currentRow)
	if err != nil {
		return err
	}
	// 写入数据
	err = writeListData(f, sheetName, &data, currentRow)
	if err != nil {
		return err
	}
	// 跨行自动合并相同值的单元格
	mergeCellsRow(f, sheetName, data, currentRow)
	// 跨列自动合并相同值的单元格
	mergeCellsCol(f, sheetName, data, currentRow)
	// 设置活动工作表并保存
	f.SetActiveSheet(index)
	// 保存文件
	if saveErr := f.SaveAs(filePath); saveErr != nil {
		return fmt.Errorf("保存文件失败: %v", saveErr)
	}
	return nil
}

// createSheetName 创建工作表
func createSheetName(f *excelize.File, data *ExcelData) (int, string, error) {
	if data == nil {
		return 0, "", fmt.Errorf("参数异常")
	}
	sheetName := data.SheetName
	if sheetName == "" {
		sheetName = "Sheet1"
	}
	// 创建工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return index, sheetName, fmt.Errorf("创建工作表失败: %v", err)
	}
	// 删除默认工作表
	if sheetName != "Sheet1" {
		err = f.DeleteSheet("Sheet1")
		if err != nil {
			return 0, "", fmt.Errorf("删除默认工作表失败: %v", err)
		}
	}
	return index, sheetName, err
}

// writeTitle 写入主标题
func writeTitle(f *excelize.File, sheetName string, data *ExcelData, currentRow int) (int, error) {
	title := data.Title
	if title != "" {
		style := getTitleStyle()
		if data.TitleStyle != nil {
			style = *data.TitleStyle
		}
		styleID, styleErr := createStyle(f, style)
		if styleErr != nil {
			return currentRow, fmt.Errorf("创建标题样式失败: %v", styleErr)
		}
		//计算列数
		endCol := getColumnCount(data)
		// 计算合并范围（合并所有列）
		startCell, _ := excelize.CoordinatesToCellName(1, currentRow)
		endCell, _ := excelize.CoordinatesToCellName(endCol, currentRow)
		if setErr := f.SetCellValue(sheetName, startCell, title); setErr != nil {
			return currentRow, fmt.Errorf("设置单元格值失败: %v", setErr)
		}
		if setErr := f.SetCellStyle(sheetName, startCell, endCell, styleID); setErr != nil {
			return currentRow, fmt.Errorf("设置单元格样式失败: %v", setErr)
		}
		if data.MergeTitles {
			if MergeErr := f.MergeCell(sheetName, startCell, endCell); MergeErr != nil {
				return currentRow, fmt.Errorf("合并单元格失败: %v", MergeErr)
			}
		}
		if setErr := f.SetRowHeight(sheetName, currentRow, 30); setErr != nil {
			return currentRow, fmt.Errorf("设置单元格行高失败: %v", setErr)
		}
		currentRow++
	}
	return currentRow, nil
}

// writeHeaders 写入表头
func writeHeaders(f *excelize.File, sheetName string, data *ExcelData, currentRow int) (int, error) {
	for _, header := range data.Headers {
		style := getHeaderStyle()
		if header.Style != nil {
			style = header.Style
		}
		styleID, styleErr := createStyle(f, *style)
		if styleErr != nil {
			return currentRow, fmt.Errorf("创建表头样式失败: %v", styleErr)
		}
		// 设置行高
		if header.Height > 0 {
			if setErr := f.SetRowHeight(sheetName, currentRow, header.Height); setErr != nil {
				return currentRow, fmt.Errorf("设置单元格行高失败: %v", setErr)
			}
		}
		// 写入表头内容
		var prevValue interface{}
		var startMergeCol int
		for colIdx, value := range header.Values {
			var currentValue interface{}
			if colIdx < len(header.Values)-1 {
				currentValue = value
			} else {
				currentValue = nil
			}
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, currentRow)
			if setErr := f.SetCellValue(sheetName, cell, value); setErr != nil {
				return currentRow, fmt.Errorf("设置单元格值失败: %v", setErr)
			}
			if setErr := f.SetCellStyle(sheetName, cell, cell, styleID); setErr != nil {
				return currentRow, fmt.Errorf("设置单元格样式失败: %v", setErr)
			}
			if currentValue != prevValue || colIdx == len(header.Values) {
				// 检查是否需要合并（连续相同值且允许合并）
				if startMergeCol < colIdx-1 {
					colIdxNew := colIdx
					// 处理最后一列
					if currentValue == nil {
						colIdxNew = colIdx + 1
					}
					startCell, _ := excelize.CoordinatesToCellName(startMergeCol+1, currentRow)
					endCell, _ := excelize.CoordinatesToCellName(colIdxNew, currentRow)
					if mergeErr := f.MergeCell(sheetName, startCell, endCell); mergeErr != nil {
						return currentRow, fmt.Errorf("合并单元格失败: %v", mergeErr)
					}
				}
				startMergeCol = colIdx
				prevValue = currentValue
			}
		}
		currentRow++
	}
	return currentRow, nil
}

// writeListData 写入列表数据
func writeListData(f *excelize.File, sheetName string, data *ExcelData, currentRow int) error {
	for rowIdx, rowData := range data.Data {
		rowNum := currentRow + rowIdx
		for colIdx, cellValue := range rowData {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowNum)
			// 设置单元格值，且处理值的格式
			if setErr := setCellValueWithFormat(f, sheetName, cell, cellValue); setErr != nil {
				return fmt.Errorf("设置单元格值失败: %v", setErr)
			}
			// 应用样式
			if len(data.DataStyles) > rowIdx && len(data.DataStyles[rowIdx]) > colIdx {
				style := data.DataStyles[rowIdx][colIdx]
				styleID, styleErr := createStyle(f, style)
				if styleErr == nil {
					if setErr := f.SetCellStyle(sheetName, cell, cell, styleID); setErr != nil {
						return fmt.Errorf("设置单元格样式失败: %v", setErr)
					}
				}
			}
		}
	}
	return nil
}

// mergeCellsRow 按行合并单元格
func mergeCellsRow(f *excelize.File, sheetName string, data ExcelData, dataStartRow int) {
	if len(data.Data) == 0 {
		return
	}
	var columns []int
	// 按列跨行合并
	for col := 0; col < len(data.Data[0]); col++ {
		var prevValue interface{}
		var preMerge bool
		var startMergeRow int
		columns = append(columns, col)
		for row := 0; row <= len(data.Data); row++ {
			var currentValue interface{}
			lastRowMerge := false
			isMerge := true
			if row < len(data.Data) {
				var colVal []interface{}
				for _, column := range columns {
					if data.Data[row][column] != nil {
						colVal = append(colVal, data.Data[row][column])
					}
				}
				// 列合并，当前列及之前的列联合比对
				var sb strings.Builder
				for i, v := range colVal {
					if i > 0 {
						sb.WriteString("|")
					}
					sb.WriteString(fmt.Sprintf("%v", v))
				}
				currentValue = sb.String()
				if len(data.DataStyles) > row && len(data.DataStyles[row]) > col {
					if !data.DataStyles[row][col].MergeCellVertical {
						isMerge = false
					}
				}
			} else {
				currentValue = nil // 最后一行之后强制结束
				if row == len(data.Data) {
					lastRowMerge = true
					if len(data.DataStyles) > row-1 && len(data.DataStyles[row-1]) > col {
						if data.DataStyles[row-1][col].MergeCellVertical {
							isMerge = true
						}
					}
				}
			}
			if currentValue != prevValue || lastRowMerge {
				if startMergeRow < row-1 && preMerge {
					// 需要合并
					startCell, _ := excelize.CoordinatesToCellName(col+1, startMergeRow+dataStartRow)
					endCell, _ := excelize.CoordinatesToCellName(col+1, row+dataStartRow-1)
					if mergeErr := f.MergeCell(sheetName, startCell, endCell); mergeErr != nil {
						return
					}
					// 默认合并样式-水平垂直居中
					style := getTextStyle()
					styleID, _ := createStyle(f, style)
					//自定义合并样式
					if len(data.DataStyles) > row-1 && len(data.DataStyles[row-1]) > col {
						style = data.DataStyles[row-1][col]
						styleID, _ = createStyle(f, style)
					}
					if setErr := f.SetCellStyle(sheetName, startCell, endCell, styleID); setErr != nil {
						return
					}
				}
				startMergeRow = row
				prevValue = currentValue
				preMerge = isMerge
			}
		}
	}
}

// mergeCellsCol 按列合并单元格
func mergeCellsCol(f *excelize.File, sheetName string, data ExcelData, dataStartRow int) {
	if len(data.Data) == 0 {
		return
	}
	// 按行跨列合并
	for row := 0; row < len(data.Data); row++ {
		var prevValue interface{}
		var preMerge bool
		var startMergeCol int

		// 确保行数据不为空
		if len(data.Data[row]) == 0 {
			continue
		}

		for col := 0; col <= len(data.Data[row]); col++ {
			var currentValue interface{}
			lastColMerge := false
			isMerge := true
			if col < len(data.Data[row]) {
				currentValue = data.Data[row][col]
				if len(data.DataStyles) > row && len(data.DataStyles[row]) > col {
					if !data.DataStyles[row][col].MergeCellHorizontal {
						isMerge = false
					}
				}
			} else {
				currentValue = nil
				if col == len(data.Data[row]) {
					lastColMerge = true
					if len(data.DataStyles) > row && len(data.DataStyles[row]) > col-1 {
						if data.DataStyles[row][col-1].MergeCellHorizontal {
							isMerge = true
						}
					}
				}
			}
			if currentValue != prevValue || lastColMerge {
				if startMergeCol < col-1 && preMerge {
					startCell, _ := excelize.CoordinatesToCellName(startMergeCol+1, row+dataStartRow)
					endCell, _ := excelize.CoordinatesToCellName(col, row+dataStartRow)
					if mergeErr := f.MergeCell(sheetName, startCell, endCell); mergeErr != nil {
						return
					}
					style := getTextStyle()
					styleID, _ := createStyle(f, style)
					if len(data.DataStyles) > row && len(data.DataStyles[row]) > col {
						style = data.DataStyles[row][col]
						styleID, _ = createStyle(f, style)
					}
					if setErr := f.SetCellStyle(sheetName, startCell, endCell, styleID); setErr != nil {
						return
					}
				}
				startMergeCol = col
				prevValue = currentValue
				preMerge = isMerge
			}
		}
	}
}

// getColumnCount 获取文件列数
func getColumnCount(data *ExcelData) int {
	endCol := 1
	if data == nil {
		return endCol
	}
	if len(data.Headers) > 0 {
		endCol = len(data.Headers[0].Values)
	} else if len(data.Data) > 0 {
		endCol = len(data.Data[0])
	}
	return endCol
}

// createStyle 根据样式配置创建Excel样式
func createStyle(f *excelize.File, style CellStyle) (int, error) {
	return f.NewStyle(convertToExcelizeStyle(&style))
}

// getTitleStyle 获取主标题默认样式
func getTitleStyle() CellStyle {
	border := true
	return CellStyle{
		Bold:       true,
		FontSize:   18,
		Horizontal: "center",
		Vertical:   "center",
		BgColor:    "#F5F5F5",
		FontColor:  "#000000",
		Border:     &border,
	}
}

// getHeaderStyle 获取表头默认样式
func getHeaderStyle() *CellStyle {
	border := true
	return &CellStyle{
		Bold:       true,
		FontSize:   12,
		Horizontal: "center",
		Vertical:   "center",
		Border:     &border,
		FontColor:  "#000000",
	}
}

// getTextStyle 获取文本默认样式
func getTextStyle() CellStyle {
	border := true
	return CellStyle{
		Bold:       false,
		FontSize:   12,
		Horizontal: "center",
		Vertical:   "center",
		Border:     &border,
		FontColor:  "#000000",
	}
}

// convertToExcelizeStyle 将自定义样式转换为excelize样式
func convertToExcelizeStyle(style *CellStyle) *excelize.Style {
	excelStyle := &excelize.Style{
		Font: &excelize.Font{
			Size: style.FontSize,
			Bold: style.Bold,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	}
	if style.FontColor != "" {
		excelStyle.Font.Color = style.FontColor
	}
	if style.Horizontal != "" {
		excelStyle.Alignment.Horizontal = style.Horizontal
	}
	if style.Vertical != "" {
		excelStyle.Alignment.Vertical = style.Vertical
	}
	if style.BgColor != "" {
		excelStyle.Fill = excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{style.BgColor},
		}
	}
	if style.Border == nil || (style.Border != nil && *style.Border) {
		excelStyle.Border = []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		}
	}

	return excelStyle
}

// setCellValueWithFormat 根据值的类型自动设置单元格值
func setCellValueWithFormat(f *excelize.File, sheetName, cell string, value interface{}) error {
	switch v := value.(type) {
	case int, int8, int16, int32, int64:
		return f.SetCellValue(sheetName, cell, v)
	case uint, uint8, uint16, uint32, uint64:
		return f.SetCellValue(sheetName, cell, v)
	case float32, float64:
		return f.SetCellValue(sheetName, cell, v)
	case string:
		return f.SetCellValue(sheetName, cell, v)
	case bool:
		return f.SetCellValue(sheetName, cell, v)
	case nil:
		return f.SetCellValue(sheetName, cell, "")
	default:
		// 对于其他类型，转换为字符串
		return f.SetCellValue(sheetName, cell, fmt.Sprintf("%v", v))
	}
}
