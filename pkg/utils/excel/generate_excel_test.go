package excel

import (
	"fmt"
	"testing"
)

func TestGenerateExcelWithMerge(t *testing.T) {

	data := ExcelData{
		SheetName:   "销售报表-样表样式",
		Title:       "2025年度销售数据汇总",
		MergeTitles: true,
		Headers: []HeaderRow{
			{
				Values: []interface{}{"日期2025.07.01", "日期2025.07.01", "日期2025.07.01", "日期2025.07.01", "日期2025.07.01", "日期2025.07.01", "日期2025.07.01", "日期2025.07.01", "日期2025.07.01"},
				Height: 20,
				Style: &CellStyle{
					FontSize:   12,
					Bold:       false,
					BgColor:    "#FFFFFF",
					Horizontal: "left",
					Vertical:   "center",
				},
			},
			{
				Values: []interface{}{"", "", "", "第一季度", "第一季度", "第一季度", "第二季度", "第二季度", "第二季度"},
				Height: 25,
				Style: &CellStyle{
					FontSize:   14,
					Bold:       true,
					BgColor:    "#E6E6FA",
					Horizontal: "center",
					Vertical:   "center",
				},
			},
			{
				Values: []interface{}{"地区", "产品类别", "产品类别", "1月", "2月", "3月", "4月", "5月", "6月"},
				Height: 20,
				Style: &CellStyle{
					FontSize:   12,
					Bold:       true,
					BgColor:    "#ADD8E6",
					Horizontal: "center",
					Vertical:   "center",
				},
			},
			{
				Values: []interface{}{"", "", "", "销售额(万)", "销售额(万)", "销售额(万)", "销售额(万)", "销售额(万)", "销售额(万)"},
				Style: &CellStyle{
					FontSize:   11,
					Bold:       false,
					BgColor:    "#F0F8FF",
					Horizontal: "center",
					Vertical:   "center",
				},
			},
		},
		Data: [][]interface{}{
			{"华东", "电子产品", "电子产品", 120, 150, 180, 200, 220, 240},
			{"华东", "日用品", "进口", 80, 90, 100, 110, 120, 130},
			{"华东", "日用品", "出口", 88, 90, 100, 110, 120, 130},
			{"华北", "日用品", "电子产品", 100, 110, 130, 140, 160, 180},
			{"华北", "电子产品", "电子产品", 70, 75, 80, 85, 90, 95},
			{"华北", "电子产品", "电子产品", 70, 75, 80, 85, 90, 95},
		},
		DataStyles: [][]CellStyle{
			{
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
			},
			{
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
			},
			{
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
			},
			{
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
			},
			{
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
			},
			{
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   true,
					MergeCellHorizontal: true,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
				{
					MergeCellVertical:   false,
					MergeCellHorizontal: false,
				},
			},
		},
	}

	realFilePath := "销售报表-样表样式22.xlsx"
	////系统临时文件目录存储
	//tmpDir := filepath.Join(os.TempDir(), "report")
	//if _, err := os.Stat(tmpDir); os.IsNotExist(err) {
	//	if err = os.MkdirAll(tmpDir, os.ModePerm); err != nil {
	//		fmt.Printf("创建临时目录失败: %v", err)
	//	}
	//}
	//realFilePath = filepath.Join(tmpDir, realFilePath)
	//defer func(name string) {
	//	//结束删除临时文件
	//	time.Sleep(5 * time.Second)
	//	err := os.Remove(name)
	//	if err != nil {
	//		fmt.Printf("删除临时文件失败: %v", err)
	//	}
	//}(realFilePath)
	err := GenerateExcelWithMerge(realFilePath, data)
	if err != nil {
		fmt.Printf("生成Excel失败: %v\n", err)
	} else {
		fmt.Println("Excel文件生成成功")
	}
}
