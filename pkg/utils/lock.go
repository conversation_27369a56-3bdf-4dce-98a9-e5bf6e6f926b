package utils

import (
	"context"
	"report-service/pkg/utils/common"
	"time"

	"errors"
)

func GetSpinLock(key string, expiration time.Duration) error {
	ctx := context.Background()
	start := time.Now()
	for {
		ok, err := common.Redis.SetNX(ctx, key, "locked", expiration).Result()
		if err != nil {
			return errors.New("获取锁失败")
		}
		if ok {
			return nil
		}
		time.Sleep(100 * time.Millisecond)
		if time.Since(start) > expiration {
			return errors.New("系统繁忙，请稍后再试")
		}
	}
}

func ReleaseSpinLock(key string) {
	ctx := context.Background()
	common.Redis.Del(ctx, key)
}

func Lock(key string, expiration time.Duration, lockError error, fc func() error) (err error) {
	ctx := context.Background()
	ok, err := common.Redis.SetNX(ctx, key, "locked", expiration).Result()
	if err != nil || !ok {
		if lockError == nil {
			return errors.New("系统繁忙，请稍后再试")
		}
		return lockError
	}
	defer common.Redis.Del(ctx, key)
	err = fc()
	return
}
