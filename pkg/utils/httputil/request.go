package httputil

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"report-service/pkg/utils"
	"report-service/pkg/utils/common"
	"time"

	"go.uber.org/zap"
)

const TimeoutDuration = 10 * time.Second

type RequestClient struct {
	Method         string            // 请求方法
	Url            string            // 请求地址
	Headers        map[string]string // 请求头
	Body           interface{}       // 请求体
	Response       interface{}       // 响应体
	Timeout        time.Duration     // 请求超时时间
	DisableLog     bool              // 关闭日志输出
	LogRawResponse bool              // 记录原始响应
}

func (rc RequestClient) Request() (err error) {
	// 记录日志
	var rawResponse string
	operateStartTime := time.Now()
	if !rc.DisableLog && common.LOG != nil {
		defer func() {
			fields := []zap.Field{
				zap.Any("method", rc.Method),
				zap.Any("uri", rc.Url),
				zap.Any("operationDuration", time.Since(operateStartTime).Seconds()),
				zap.Any("headers", rc.Headers),
				zap.Any("body", rc.Body),
				zap.Any("response", rc.Response),
				zap.Any("error", err),
			}
			if rc.LogRawResponse {
				fields = append(fields, zap.Any("rawResponse", rawResponse))
			}
			common.LOG.Info("request", fields...)
		}()
	}

	// 序列化请求体为JSON
	requestBody, err := json.Marshal(rc.Body)
	if err != nil {
		return err
	}

	// 创建HTTP请求
	req, err := http.NewRequest(rc.Method, rc.Url, bytes.NewBuffer(requestBody))
	if err != nil {
		return err
	}
	// 默认设置
	for key, value := range rc.Headers {
		req.Header.Set(key, value)
	}
	timeout := TimeoutDuration
	if rc.Timeout > 0 {
		timeout = rc.Timeout
	}
	client := &http.Client{
		Timeout: timeout,
	}

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 读取响应体
	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return err
	}
	rawResponse = buf.String()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		rc.LogRawResponse = true
		return errors.New("http request error, error code: " + resp.Status)
	}

	// 解析JSON响应
	err = utils.JsonDecode(buf.Bytes(), &rc.Response)
	if err != nil {
		return err
	}

	return nil
}

// Request 发送HTTP请求并获取JSON响应
func Request(method, uri string, headers map[string]string, body interface{}, response interface{}) (err error) {
	rc := RequestClient{
		Method:   method,
		Url:      uri,
		Headers:  headers,
		Body:     body,
		Response: response,
	}
	return rc.Request()
}
