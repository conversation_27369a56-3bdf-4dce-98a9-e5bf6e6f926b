package httputil

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"report-service/pkg/utils"
	"report-service/pkg/utils/common"
	"time"

	"go.uber.org/zap"
)

type RequestSubmitFormClient struct {
	Method         string                       // 请求方法
	Url            string                       // 请求地址
	Headers        map[string]string            // 请求头
	Body           map[string]interface{}       // 请求体
	Files          []RequestSubmitFormFilesItem // 请求多文件
	Response       interface{}                  // 响应体
	Timeout        time.Duration                // 请求超时时间
	DisableLog     bool                         // 关闭日志输出
	LogRawResponse bool                         // 记录原始响应
}

type RequestSubmitFormFilesItem struct {
	FilePath  string
	FileField string
}

type RequestSubmitFormClientOption func(*RequestSubmitFormClient)

func WithTimeout(second time.Duration) RequestSubmitFormClientOption {
	return func(rp *RequestSubmitFormClient) {
		if second > 0 {
			rp.Timeout = second
		}
	}
}

func WithFiles(files ...RequestSubmitFormFilesItem) RequestSubmitFormClientOption {
	return func(rp *RequestSubmitFormClient) {
		for _, file := range files {
			rp.Files = append(rp.Files, RequestSubmitFormFilesItem{
				FilePath:  file.FilePath,
				FileField: file.FileField,
			})
		}
	}
}

// RequestSubmitForm 发送带文件的HTTP请求
func RequestSubmitForm(method, uri string, headers map[string]string, body map[string]interface{}, response interface{}, opts ...RequestSubmitFormClientOption) (err error) {
	rc := RequestSubmitFormClient{
		Method:   method,
		Url:      uri,
		Headers:  headers,
		Body:     body,
		Response: response,
	}
	for _, opt := range opts {
		opt(&rc)
	}
	return rc.RequestSubmitForm()
}

// RequestSubmitForm 发送带文件的HTTP请求（RequestClient的方法）
func (rc RequestSubmitFormClient) RequestSubmitForm() (err error) {
	// 记录日志
	var rawResponse string
	operateStartTime := time.Now()
	if !rc.DisableLog && common.LOG != nil {
		defer func() {
			fields := []zap.Field{
				zap.Any("method", rc.Method),
				zap.Any("uri", rc.Url),
				zap.Any("operationDuration", time.Since(operateStartTime).Seconds()),
				zap.Any("headers", rc.Headers),
				zap.Any("body", rc.Body),
				zap.Any("filePath", rc.getFilePaths()),
				zap.Any("fileField", rc.getFileFields()),
				zap.Any("response", rc.Response),
				zap.Any("error", err),
			}
			if rc.LogRawResponse {
				fields = append(fields, zap.Any("rawResponse", rawResponse))
			}
			common.LOG.Info("request with file", fields...)
		}()
	}

	// 创建multipart表单
	var bodyBuffer bytes.Buffer
	writer := multipart.NewWriter(&bodyBuffer)

	// 添加JSON数据部分
	if rc.Body != nil {
		// 将每个字段作为表单字段添加
		for key, value := range rc.Body {
			var fieldValue string
			switch v := value.(type) {
			case string:
				fieldValue = v
			case int, int32, int64:
				fieldValue = fmt.Sprintf("%d", v)
			case float32, float64:
				fieldValue = fmt.Sprintf("%g", v)
			case bool:
				fieldValue = fmt.Sprintf("%t", v)
			default:
				// 对于复杂类型，转换为JSON字符串
				var jsonBytes []byte
				if jsonBytes, err = json.Marshal(value); err == nil {
					fieldValue = string(jsonBytes)
				} else {
					fieldValue = fmt.Sprintf("%v", value)
				}
			}
			if err = writer.WriteField(key, fieldValue); err != nil {
				return err
			}
		}
	}
	// 添加文件数据部分
	if rc.Files != nil {
		for _, file := range rc.Files {
			if err = rc.writerCreateFormFile(file.FileField, file.FilePath, writer); err != nil {
				return err
			}
		}
	}
	// 关闭multipart writer
	err = writer.Close()
	if err != nil {
		return err
	}
	// 创建HTTP请求
	req, err := http.NewRequest(rc.Method, rc.Url, &bodyBuffer)
	if err != nil {
		return err
	}
	// 设置请求头
	for key, value := range rc.Headers {
		req.Header.Set(key, value)
	}
	// 设置multipart内容类型
	req.Header.Set("Content-Type", writer.FormDataContentType())
	// 设置超时时间
	timeout := TimeoutDuration
	if rc.Timeout > 0 {
		timeout = rc.Timeout
	}
	client := &http.Client{
		Timeout: timeout,
	}
	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 读取响应体
	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return err
	}
	rawResponse = buf.String()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		rc.LogRawResponse = true
		return errors.New("http request error, error code: " + resp.Status)
	}

	// 解析JSON响应
	err = utils.JsonDecode(buf.Bytes(), &rc.Response)
	if err != nil {
		return err
	}

	return nil
}

// writerCreateFormFile 创建文件表单字段
func (rc RequestSubmitFormClient) writerCreateFormFile(fileField, filePath string, writer *multipart.Writer) error {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)
	// 创建文件表单字段
	fileWriter, err := writer.CreateFormFile(fileField, filepath.Base(filePath))
	if err != nil {
		return err
	}
	// 复制文件内容到表单
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		return err
	}
	return nil
}

func (rc RequestSubmitFormClient) getFilePaths() (filePaths string) {
	if rc.Files == nil {
		return
	}
	for _, file := range rc.Files {
		filePaths += file.FilePath + ";"
	}
	return
}

func (rc RequestSubmitFormClient) getFileFields() (fileFields string) {
	if rc.Files == nil {
		return
	}
	for _, file := range rc.Files {
		fileFields += file.FileField + ";"
	}
	return
}
