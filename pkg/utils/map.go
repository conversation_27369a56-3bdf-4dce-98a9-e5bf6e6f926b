package utils

import (
	"encoding/json"
	"sort"
)

func SortMapValuesIntoSortedSlice[T any](m map[int]T) []T {
	var keys []int
	for k := range m {
		keys = append(keys, k)
	}
	sort.Ints(keys)

	var values []T
	for _, k := range keys {
		values = append(values, m[k])
	}
	return values
}

func MapToSlice[T1 comparable, T2 any](m map[T1]T2) []T2 {
	var slice []T2
	for _, value := range m {
		slice = append(slice, value)
	}
	return slice
}

func StructToMapViaJSON(obj interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	// 先转换为JSON
	jsonData, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}
	// 再转换为map
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
