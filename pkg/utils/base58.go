package utils

import (
	"github.com/btcsuite/btcutil/base58"
)

// EncodeBase58 使用Base58进行加密
func EncodeBase58(number int) string {
	// 将数字转换为字节数组
	bytes := intToBytes(number)

	// 使用Base58编码
	encoded := base58.Encode(bytes)

	return encoded
}

func EncodeStringToBase58(str string) string {
	return base58.Encode([]byte(str))
}

// DecodeBase58 使用Base58进行解密
func DecodeBase58(encoded string) int {
	// 使用Base58解码
	decodedBytes := base58.Decode(encoded)

	// 将字节数组转换为数字
	decoded := bytesToInt(decodedBytes)

	return decoded
}

// 将数字转换为字节数组
func intToBytes(number int) []byte {
	result := make([]byte, 0)

	for number > 0 {
		result = append([]byte{byte(number % 256)}, result...)
		number /= 256
	}

	return result
}

// 将字节数组转换为数字
func bytesToInt(bytes []byte) int {
	result := 0

	for _, b := range bytes {
		result = result*256 + int(b)
	}

	return result
}

func EncodeBase58String(s string) string {
	bytes := []byte(s)
	encoded := base58.Encode(bytes)
	return encoded
}

func DecodeBase58String(encoded string) string {
	decodedBytes := base58.Decode(encoded)
	decoded := string(decodedBytes)
	return decoded
}
