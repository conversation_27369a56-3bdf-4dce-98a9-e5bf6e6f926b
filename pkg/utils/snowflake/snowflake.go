package snowflake

import (
	"errors"
	"sync"
	"time"
)

// 默认的 Snowflake 参数
const (
	DefaultStartTime    = 1701014400000 // 默认起始时间戳，单位为毫秒（2023-11-27 00:00:00）
	DefaultMachineBits  = 10            // 默认机器ID位数，即最大机器ID为1023
	DefaultSequenceBits = 12            // 默认序列号位数，即每毫秒可生成的ID序号数为4095
)

// Snowflake 结构体
type Snowflake struct {
	mu           sync.Mutex
	startTime    int // 起始时间戳，单位为毫秒
	machineId    int // 机器ID
	sequence     int // 序列号
	lastGenTime  int // 上一次生成ID的时间戳，单位为毫秒
	sequenceBits int // 序列号位数
	machineBits  int // 机器ID位数
}

var GlobalSnowflake *Snowflake

func GetId() int {
	id, err := GlobalSnowflake.NextId()
	if err != nil {
		panic("生成ID失败")
	}
	return id
}

// NewSnowflake 创建一个Snowflake实例
func NewSnowflake(machineId int, startTime int, machineBits int, sequenceBits int) (*Snowflake, error) {
	if sequenceBits <= 0 || machineBits <= 0 || sequenceBits+machineBits > 22 {
		return nil, errors.New("invalid sequenceBits or machineBits")
	}

	return &Snowflake{
		startTime:    startTime,
		machineId:    machineId,
		sequence:     0,
		lastGenTime:  -1,
		sequenceBits: sequenceBits,
		machineBits:  machineBits,
	}, nil
}

// NewDefaultSnowflake 创建一个使用默认值的Snowflake实例
func NewDefaultSnowflake() (*Snowflake, error) {
	machineId, err := getMachineId()
	if err != nil {
		return nil, err
	}
	return NewSnowflake(machineId, DefaultStartTime, DefaultMachineBits, DefaultSequenceBits)
}

// NextId 生成下一个唯一ID
func (s *Snowflake) NextId() (int, error) {
	if s == nil {
		return int(time.Now().UnixNano() / 1e6), nil
	}
	s.mu.Lock()
	defer s.mu.Unlock()

	// 获取当前时间戳
	now := int(time.Now().UnixNano() / 1e6) // 毫秒级时间戳

	// 检查系统时间是否发生回拨
	if now < s.lastGenTime {
		return 0, errors.New("system time is moving backwards")
	}

	// 在同一毫秒内生成多个ID，需要进行序列号递增
	if now == s.lastGenTime {
		s.sequence = (s.sequence + 1) & ((1 << s.sequenceBits) - 1)
		if s.sequence == 0 {
			// 当前毫秒内的序列号用完，等待下一毫秒
			for now <= s.lastGenTime {
				now = int(time.Now().UnixNano() / 1e6)
			}
		}
	} else {
		// 新的毫秒，序列号重置为0
		s.sequence = 0
	}

	// 更新上一次生成ID的时间戳
	s.lastGenTime = now

	// 生成ID，共64位（1位标识位，41位时间戳，10位机器ID，12位序列号）
	// 时间戳41位，往后69年内可用，使用时注意计算起始时间
	id := ((now - s.startTime) << (s.machineBits + s.sequenceBits)) |
		(s.machineId << s.sequenceBits) |
		s.sequence

	return id, nil
}
