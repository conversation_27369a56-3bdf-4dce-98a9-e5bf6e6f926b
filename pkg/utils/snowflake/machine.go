package snowflake

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"report-service/pkg/utils/common"
)

func getMachineId() (int, error) {
	return getMachineIdByRedis()
}

// 根据redis获取机器ID
func getMachineIdByRedis() (int, error) {
	key := "snowflake:machine_id"
	tx := context.Background()
	val, err := common.Redis.Incr(tx, key).Result()
	if err != nil {
		return 0, err
	}
	// 取模，防止超过机器ID最大值
	maxValue := cast.ToInt64(1 << DefaultMachineBits)
	val = val % maxValue
	common.LOG.Info(fmt.Sprintf("snowflake get machine_id: %d", val))
	return int(val), nil
}
