#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

agent:
  # Service name is showed in UI.
  service_name: ${SW_AGENT_NAME:hotel_service}
  # To obtain the environment variable key for the instance name, if it cannot be obtained, an instance name will be automatically generated.
  instance_env_name: SW_AGENT_INSTANCE_NAME
  # Sampling rate of tracing data, which is a floating-point value that must be between 0 and 1.
  sampler: ${SW_AGENT_SAMPLE:1}
  meter:
    # The interval of collecting metrics, in seconds.
    collect_interval: ${SW_AGENT_METER_COLLECT_INTERVAL:20}
  correlation:
    max_key_count: ${SW_AGENT_CORRELATION_MAX_KEY_COUNT:3}
    max_value_size: ${SW_AGENT_CORRELATION_MAX_VALUE_SIZE:128}
  # If the suffix obtained by splitting the operation name by the last index of "." in this set, this segment should be ignored.(multiple split by ",").
  ignore_suffix: ${SW_AGENT_IGNORE_SUFFIX:.jpg,.jpeg,.js,.css,.png,.bmp,.gif,.ico,.mp3,.mp4,.html,.svg}
  # If the operation name of the first span is matching, this segment should be ignored.(multiple split by ",").
  # Matching rules follow Ant Path match style, like /path/*, /path/**, /path/?.
  #       "/path/*" means matching any path that starts with "/path/".  
  #       "/path/**" means matching any path that starts with "/path/" and includes its subpaths.
  #       "/path/?" means matching any path that starts with "/path/" and has any single character as a wildcard.
  trace_ignore_path: ${SW_AGENT_TRACE_IGNORE_PATH:}

reporter:
  discard: ${SW_AGENT_REPORTER_DISCARD:false}
  grpc:
    # The gRPC server address of the backend service.
    backend_service: ${SW_AGENT_REPORTER_GRPC_BACKEND_SERVICE:127.0.0.1:11800}
    # The maximum count of segment for reporting tracing data.
    max_send_queue: ${SW_AGENT_REPORTER_GRPC_MAX_SEND_QUEUE:5000}
    # The interval(s) of checking service and backend service
    check_interval: ${SW_AGENT_REPORTER_GRPC_CHECK_INTERVAL:20}
    # The authentication string for communicate with backend.
    authentication: ${SW_AGENT_REPORTER_GRPC_AUTHENTICATION:}
    # The interval(s) of fetching dynamic configuration from backend.
    cds_fetch_interval: ${SW_AGENT_REPORTER_GRPC_CDS_FETCH_INTERVAL:20}
    tls:
      # Whether to enable TLS with backend.
      enable: ${SW_AGENT_REPORTER_GRPC_TLS_ENABLE:false}
      # The file path of ca.crt. The config only works when opening the TLS switch.
      ca_path: ${SW_AGENT_REPORTER_GRPC_TLS_CA_PATH:}
      # The file path of client.pem. The config only works when mTLS.
      client_key_path: ${SW_AGENT_REPORTER_GRPC_TLS_CLIENT_KEY_PATH:}
      # The file path of client.crt. The config only works when mTLS.
      client_cert_chain_path: ${SW_AGENT_REPORTER_GRPC_TLS_CLIENT_CERT_CHAIN_PATH:}
      # Controls whether a client verifies the server's certificate chain and host name.
      insecure_skip_verify: ${SW_AGENT_REPORTER_GRPC_TLS_INSECURE_SKIP_VERIFY:false}

log:
  # The type determines which logging type is currently used by the system.
  # The Go agent wourld use this log type to generate custom logs. It supports: "auto", "logrus", or "zap".
  # auto: Automatically identifies the source of the log.
  #       If logrus is present in the project, it wourld automatically use logrus.
  #       If zap has been initialized in the project, it would use the zap framework.
  #       By default, it would use std errors to output log content.
  # logrus: Specifies that the Agent should use the logrus framework.
  # zap: Specifies that the Agent should use the zap framework.
  # The system must have already been initialized through methods such as "zap.New", "zap.NewProduction", etc.
  type: ${SW_AGENT_LOG_TYPE:auto}
  tracing:
    # Whether to automatically integrate Tracing information into the logs.
    enable: ${SW_AGENT_LOG_TRACING_ENABLE:false}
    # If tracing information is enabled, the tracing information would be stored in the current Key in each log.
    key: ${SW_AGENT_LOG_TRACING_KEY:SW_CTX}
  reporter:
    # Whether to upload logs to the backend.
    enable: ${SW_AGENT_LOG_REPORTER_ENABLE:true}
    # The fields name list that needs to added to the label of the log.(multiple split by ",")
    label_keys: ${SW_AGENT_LOG_REPORTER_LABEL_KEYS:}

plugin:
  # List the names of excluded plugins, multiple plugin names should be splitted by ","
  # NOTE: This parameter only takes effect during the compilation phase.
  excluded: ${SW_AGENT_PLUGIN_EXCLUDES:rocketmq}
  config:
    http:
      # Collect the parameters of the HTTP request on the server side
      server_collect_parameters: ${SW_AGENT_PLUGIN_CONFIG_HTTP_SERVER_COLLECT_PARAMETERS:false}
    mongo:
      # Collect the statement of the MongoDB request
      collect_statement: ${SW_AGENT_PLUGIN_CONFIG_MONGO_COLLECT_STATEMENT:false}
    sql:
      # Collect the parameter of the SQL request
      collect_parameter: ${SW_AGENT_PLUGIN_CONFIG_SQL_COLLECT_PARAMETER:false}
    redis:
      # Limit the bytes size of redis args request
      max_args_bytes: ${SW_AGENT_PLUGIN_CONFIG_REDIS_MAX_ARGS_BYTES:1024}
    gin:
      # Collect the http header of gin request
      collect_request_headers: ${SW_AGENT_PLUGIN_CONFIG_GIN_COLLECT_REQUEST_HEADERS:}
      # Controlling the length limitation of all header values
      header_length_threshold: ${SW_AGENT_PLUGIN_CONFIG_GIN_HEADER_LENGTH_THRESHOLD:2048}