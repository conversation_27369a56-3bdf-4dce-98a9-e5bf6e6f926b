version: '3'
services:
  mysql:
    restart: always
    image: mysql:5.7
    container_name: hotel-mysql
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: hotel
      MYSQL_CHARSET: utf8mb4
    ports:
      - "3306:3306"
    platform: linux/x86_64
#    command:
#      --sql-mode="NO_ZERO_IN_DATE,NO_ZERO_DATE"
    volumes:
      - ../../../mysql/data:/var/lib/mysql
      - ./my.cnf:/etc/mysql/my.cnf  # 添加这一行来挂载自定义配置文件
  redis:
    restart: always
    image: redis:latest
    container_name: hotel-redis
    volumes:
      - ../../../redis/data:/data
    ports:
      - "6379:6379"
